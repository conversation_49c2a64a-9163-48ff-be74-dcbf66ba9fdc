import React, { useState, useEffect, useRef, useLayoutEffect, } from "react";
import { StyleSheet, View, Text, Dimensions, useWindowDimensions, ActivityIndicator, Alert, TouchableOpacity, Animated, InteractionManager, Platform, } from "react-native";
import firebase from 'firebase/app';
import AppNavigator from "./navigation/AppNavigator";
import FontAwesomeTTF from 'react-native-vector-icons/Fonts/FontAwesome.ttf';
import SimpleLineIconsTTF from 'react-native-vector-icons/Fonts/SimpleLineIcons.ttf';
import EntypoTTF from 'react-native-vector-icons/Fonts/Entypo.ttf';
import IoniconsTTF from 'react-native-vector-icons/Fonts/Ionicons.ttf';
import FeatherTTF from 'react-native-vector-icons/Fonts/Feather.ttf';
import AntDesignTTF from 'react-native-vector-icons/Fonts/AntDesign.ttf';
import FontistoTTF from 'react-native-vector-icons/Fonts/Fontisto.ttf';
import MaterialIconsTTF from 'react-native-vector-icons/Fonts/MaterialIcons.ttf';
import MaterialCommunityIconsTTF from "react-native-vector-icons/Fonts/MaterialCommunityIcons.ttf";
import NunitoSansBoldTTF from './assets/fonts/NunitoSans-Bold.ttf';
import NunitoSansRegularTTF from './assets/fonts/NunitoSans-Regular.ttf';
import NunitoSansSemiBoldTTF from './assets/fonts/NunitoSans-SemiBold.ttf';
import { CommonStore } from "../src/store/commonStore";
import AwesomeAlert from 'react-native-awesome-alerts';
import {
  // listenToUserChanges,
  // listenToSelectedOutletChanges,

  // listenToSelectedOutletTagChanges,
  // listenToSearchOutletTextChanges,
  // listenToSearchOutletMerchantIdChanges,

  requestNotificationsPermission,
  getOutletById,
  isMobile,

  listenToAccountEmailChangesMerchant,

  listenToSelectedOutletItemChanges,
  listenToCommonChangesMerchant,
  listenToSelectedOutletTableIdChanges,
  listenToUserChangesMerchant,
  listenToUserOrderHistoricalChanges,
  listenToMerchantIdChangesMerchant,
  listenToCurrOutletIdChangesWaiter,
  listenToCurrOutletIdChangesMerchant,
  listenToSelectedOrderToPayUserIdChanges,
  listenToSelectedCustomerChangesMerchant,
  listenToSelectedCustomerApplicableVoucherIdChangesMerchant,

  listenToCurrOutletIdReservationChanges,

  sliceUnicodeStringV2WithDots,
  logToFile,
  showAlertForFailedPrinting,
} from "./util/common";
import Ionicons from 'react-native-vector-icons/Ionicons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Draggable from 'react-native-draggable';
import Colors from '../src/constant/Colors';
import { useNavigation } from '@react-navigation/native';
import { DataStore } from '../src/store/dataStore';
import { UserStore } from '../src/store/userStore';
// import messaging from '@react-native-firebase/messaging';
import { Collections } from '../src/constant/firebase';
import ApiClient from '../src/util/ApiClient';
import API from '../src/constant/API';
import { parseMessages } from '../src/util/notifications';
import { NotificationStore } from '../src/store/notificationStore';
import { PROMOTION_TYPE, PROMOTION_TYPE_VARIATION } from '../src/constant/promotions';
import { prefix } from "./constant/env";
import Styles from "./constant/Styles";
import { OutletStore } from "./store/outletStore";
import { MerchantStore } from "./store/merchantStore";
import { idbGet, idbSet } from "./util/db";
import WJsonStream from "w-json-stream";
import { KD_FONT_SIZE, KD_PRINT_VARIATION, REPORT_DISPLAY_TYPE, ROLE_TYPE, PRIVILEGES_NAME, APP_ENV, ORDER_TYPE, USER_ORDER_STATUS, } from "./constant/common";
import moment from "moment";
import { useLinkTo } from "@react-navigation/native";
import { useNetInfo } from '@react-native-community/netinfo';
import Feather from 'react-native-vector-icons/Feather';
import Ionicon from 'react-native-vector-icons/Ionicons';
import { ReactComponent as CashRegister } from "./assets/svg/CashRegister.svg";
import { checkPrinterTasksLocking, connectToPrinter, disconnectPrinter, openCashDrawer, printDocket, printDocketForKD, printKDSummaryCategoryWrapper, printUserOrder, } from './util/printer';
import DropDownPicker from 'react-native-dropdown-picker';
import { PRINTER_TASK_TYPE, PRINTER_USAGE_TYPE } from "./constant/printer";
import APILocal from "./util/apiLocalReplacers";

import "react-native-get-random-values";
import { v4 as uuidv4 } from "uuid";

// Look at public/index.html!

const firebaseConfig = require('./config/firebase-web.json');


////////////////////////////////////////////////////

global.firstStartDict = {};

////////////////////////////////////////////////////

global.checkMerchantIdTimerId = null;

// 2024-09-26 - optimization related

global.reportTableTimerId = null;
global.reportTableTimerId2 = null;

global.reportChartTimerId = null;

global.listenerTimerDuration = 100;

////////////////////////////////////////////////////

global.usbLabelPrintersDict = {};

global.isBlePrinterTestPrinted = false;
global.isBlePrinterInited = false;

global.currOutletShiftStatus = false;

global.blePrintersDict = {};

global.sunmiSerialNo = '';
global.sunmiPrinterModal = '';
global.sunmiPrinterVersion = '';
global.sunmiPrinters = [];
global.sunmiPrintersDict = {};

global.detectSunmiPrintersInterval = null;

global.iminSerialNo = '';
global.iminPrinterModal = '';
global.iminPrinterVersion = '';
global.iminPrinters = [];
global.iminPrintersDict = {};

global.detectIminPrintersInterval = null;

////////////////////////////////////////////////////

global.mutateUserOrdersInterval = null;

global.timeCheckItemTableInterval = null;

global.merchantLogoUrlOnline = '';

global.appEnv = APP_ENV.LIVE;

global.checkClosedTablesInterval = null;

global.forceShowApp = null; // for xiaomi pad 6 auto sign out issue fixes

global.sendDebugTextTimer = null;

global.funcSwitchShowApp = () => { };

global.signInAlready = false;

global.udiData = {};

global.noSignoutFN = undefined;
global.noSignoutC = undefined;
global.noSignoutI = undefined;

global.outletSupplyItemsDict = {};
global.outletSupplyItemsSkuDict = {};

global.cacheActualUserOrderDict = {};

// global.requestAnimationFrame = (cb) => {
//   cb && cb();
// };

global.refreshRateTimer = null;

global.isUserActiveNow = false;
global.uanTimerId = null;
global.uanI = 0; // user active now interval (second)

global.reservationConfig = {
  printKdOsBeforeMinutes: 60,
};

global.queueConfig = {

};

// global.checkingOutTakeawayTimestampUse = null;

global.crmUsersDtUse = null;
global.userOrdersUse = null;

global.crmUsersDt = Date.now();
global.userOrdersDt = Date.now();

global.ssUserOrderMetadataId = null;
global.ssUserOrderActiveId = null;
global.ssUserOrderMonthlyId = null;

global.viewTableOrderModalPrev = false;

global.pinUnlockModalShowing = false;
global.pinUnlockCallback = async () => { };

global.getRazerPayoutTransactionsParsedTimer = null;

global.isLoadedFromLocalDB = false;

global.ptBatchPairs = [];
global.pteBatchPairs = [];

global.ptStartDatePrev = null;
global.ptEndDatePrev = null;

global.ptDateTimestamp = Date.now();
// global.ptSavedTimestamp = Date.now();
global.payoutTransactions = [];
global.payoutTransactionsExtend = [];

global.ptDict = {};
global.pteDict = {};

global.printerPendingTasksPrioritizedNum = 0;
global.printerPendingTasksNum = 0;
global.printerObjList = [];
global.outletItemsDict = {};

global.isPrintingNow = false;
global.isSnapshotChanging = true;
global.isCheckingIncomingOrders = false;
global.isAuthorizingTakeawayOrders = false;

/////////////////////////////////////////////////////////////

global.outletKdEventTypes = [];
global.outletKdVariation = KD_PRINT_VARIATION.SUMMARY;
global.outletKdFontSize = KD_FONT_SIZE.NORMAL;
global.outletKdHeaderFontSize = KD_FONT_SIZE.EXTRA_LARGE;
global.outletKdPrintSku = false;
global.outletKdPrintUserInfo = false;
global.outletPrintReceiptWhenPaidOnline = false;
global.outletToggleOfflineMode = false;

global.outletToggleDisableAutoPrint = false;

global.outletAutoPrintPaySlip = true;

global.allOutletsCategoriesDict = {};
global.outletCategoriesDict = {}; // for print KD twice
global.outletItemsInfoDict = {};

/////////////////////////////////////////////////////////////

global.privileges = [];
global.privileges_state = [];

/////////////////////////////////////////////////////////////

global.warningOutletItemList = [];
global.warningOutletSupplyItemList = [];

/////////////////////////////////////////////////////////////

global.tokenFcm = '';

/////////////////////////////////////////////////////////////

// global.dropdownPickerDict = {};

/////////////////////////////////////////////////////////////

global.isPlacingOrder = false;
global.isAddingToCart = false;

global.isPayingAndCheckingOutOrder = false;
global.isCheckingOutOrder = false;

/////////////////////////////////////////////////////////////

global.availablePromotions = [];
// global.merchantPromotionsAppliedDict = {};

/////////////////////////////////////////////////////////////

global.currUserRole = 'admin';

/////////////////////////////////////////////////////////////

global.employees = [];

global.currOutlet = {
  reportDisplayType: REPORT_DISPLAY_TYPE.DAY,

  icitnTime: 3,
};

/////////////////////////////////////////////////////////////

global.isPaymentLoading = false;
global.isSplitAndJoinUserOrderRunning = false;

/////////////////////////////////////////////////////////////

global.logFileInterval = null;
global.reprintInterval = null;

/////////////////////////////////////////////////////////////

global.subscriberListenToUserChangesMerchant = () => { };
global.subscriberListenToMerchantIdChangesMerchant = () => { };
global.subscriberListenToCurrOutletIdChangesWaiter = () => { };
global.subscriberListenToCurrOutletIdChangesMerchant = () => { };
global.subscriberListenToSelectedOutletItemChanges = () => { };
global.subscriberListenToSelectedOrderToPayUserIdChanges = () => { };
global.subscriberListenToSelectedOutletTableIdChanges = () => { };
global.subscriberListenToCommonChangesMerchant = () => { };
global.subscriberListenToSelectedCustomerChangesMerchant = () => { };
global.subscriberListenToSelectedCustomerApplicableVoucherIdChangesMerchant = () => { };
global.subscriberListenToUserOrderHistoricalChanges = () => { };

global.subscriberListenToOutletReviewHistoricalChanges = () => { };

/////////////////////////////////////////////////////////////

// interval based instead of while loop to create non-blocking experience

const taskQueueIntervalTime = Platform.OS === 'ios' ? 200 : 200; // 500

const printerTaskQueueInterval = setInterval(() => {
  startPrinterTaskQueueV2();
}, taskQueueIntervalTime); // 5000

/////////////////////////////////////////////////////////////

global.snapshots = {};
// global.emitter = new EventEmitter();
// global.emitter = new NativeEventEmitter(NativeModules);
// const emitter = new EventEmitter();

const runBackgroundTasks = async () => {
  const printerTaskQueueInterval = setInterval(() => {
    startPrinterTaskQueueV2();
  }, taskQueueIntervalTime); // 5000

  // while (BackgroundService.isRunning()) {
  // }

  // 2024-04-02 - no need keeping snapshot listener caller alive

  // startSnapshotListeners();

  // await new Promise(() => { // Never resolve
  //   // // Listen to Something1 to start a task
  //   // Something1.addEventListener('event', () => {
  //   //   // Starts a new "task"
  //   // };
  //   // // Listen to Something2 to start another task
  //   // Something2.addEventListener('event', () => {
  //   //   // Starts a new "task"
  //   // };

  //   try {
  //     // console.log('========================');
  //     // console.log('global.emitter');
  //     // console.log(global.emitter);
  //     // console.log('========================');

  //     startSnapshotListeners();
  //   }
  //   catch (ex) {
  //     console.error('=======================');
  //     console.error(ex);
  //     console.error('=======================');
  //   }
  // });
}

// BackgroundService.start(runBackgroundTasks, {
//   taskName: 'KooDoo Services',
//   taskTitle: 'KooDoo Services',
//   taskDesc: 'Monitoring printer status...',
//   taskIcon: {
//     name: 'ic_launcher',
//     type: 'mipmap',
//   },
//   color: '##4E9F7D',
//   // linkingURI: 'yourSchemeHere://chat/jane', // See Deep Linking for more info
//   parameters: {
//     delay: 1000,
//   },
// });

if (!firebase.apps.length) {
  firebase.initializeApp(firebaseConfig);
} else {
  firebase.app(); // if already initialized, use this one
}

///////////////////////////////////////////////////////

// 2022-09-29 - Printer queue-based flow

const baseMillisecondToWait = Platform.OS === 'ios' ? 500 : 500; // 500

const startPrinterTaskQueueV2 = async () => {
  // choose the one that have most tasks OR receipt first OR queue date (in future can add as selection in settings for user to choose)

  // console.log(`[pr] global.isPrintingNow: ${global.isPrintingNow}`);
  // console.log(`[pr] global.isSnapshotChanging: ${global.isSnapshotChanging}`);
  // console.log(`[pr] global.isAuthorizingTakeawayOrders: ${global.isAuthorizingTakeawayOrders}`);

  // console.log(`[pr] global.printerPendingTasksNum: ${global.printerPendingTasksNum}`);

  // console.log(`[pr] global.printerPendingTasksPrioritizedNum: ${global.printerPendingTasksPrioritizedNum}`);

  if (
    !global.isCheckingInterruptedTasksNow &&
    global.printerPendingTasksNum > 0 &&
    !global.isPrintingNow &&
    (
      global.printerPendingTasksPrioritizedNum > 0 // prioritize tasks even snapshot changing
      ||
      (
        !global.isSnapshotChanging &&
        !global.isAuthorizingTakeawayOrders
      )
    )
    &&
    !global.isReconnectingToTimeoutPrinter // might happened to certain printers, when connected at 13:00pm, but executed tasks at 13:01pm (over 30 seconds)
    &&
    // 2024-03-29 - added 1 more configurable checking, if user active (interacting with screen) now, no need print first
    (
      global.uanI > 0
        ?
        (!global.isUserActiveNow)
        :
        true
    )
  ) {
    let waitingTime = baseMillisecondToWait; // 0

    if (global.currOutlet.bmtwIos && global.currOutlet.bmtwAnd) {
      if (Platform.OS === 'ios') {
        waitingTime = global.currOutlet.bmtwIos;
      }
      else {
        waitingTime = global.currOutlet.bmtwAnd;
      }
    }

    global.printerObjList = global.printerObjList.sort((curr, prev) => {
      return (curr.priority >= prev.priority || curr.frequency >= prev.frequency || curr.tasks.find(task => task.isPrioritized)) ? -1 : 1;
    }).sort((curr, prev) => {
      return curr.userPriority >= prev.userPriority ? -1 : 1;
    });

    console.log(`[pr] global.printerObjList`);
    console.log(global.printerObjList);

    let printerObj = global.printerObjList.reduce((prev, curr) => {
      if (curr.tasks.length > prev.tasks.length) {
        return curr;
      }
      else {
        return prev;
      }
    }, {
      tasks: [],
      priority: 0,
      frequency: 0,
    });

    console.log(`[pr] printerObj`);
    console.log(printerObj);

    if (printerObj && printerObj.tasks && printerObj.tasks.length > 0) {
      // connect to printer first

      global.isPrintingNow = true;

      let tasks = [
        ...printerObj.tasks,
      ];

      // console.log(`[pr] connect to printer: ${printerObj.ip} at ${moment().format('hh:mm:ss')}`);

      // logToFile(`[pr] connect to printer: ${printerObj.ip} at ${moment().format('hh:mm:ss')}`);

      // await disconnectPrinter(printerObj);

      let result = false;

      let tasksDirectNum = tasks.filter(task => {
        if (task.taskType === PRINTER_TASK_TYPE.PRINT_USER_ORDER_RECEIPT ||
          task.taskType === PRINTER_TASK_TYPE.OPEN_CASH_DRAWER) {
          return true;
        }
        else {
          return false;
        }
      }).length;

      // console.log(`printer scheduler: connecting to: ${printerObj.ip}`);

      // logToFile(`printer scheduler: connecting to: ${printerObj.ip}`);

      InteractionManager.runAfterInteractions(async () => {
        // if (tasksDirectNum === tasks.length) {
        //   // means all is print receipt and cash drawer, retry times set to 1 can d

        // 2024-12-03 - here conmmented first, as unable to print via lan now, on web
        //   result = await connectToPrinter(printerObj.ip,
        //     printerObj.rtIosD ? printerObj.rtIosD : 4,
        //     printerObj.rtAndD ? printerObj.rtAndD : 4);
        // }
        // else {
        //   result = await connectToPrinter(printerObj.ip,
        //     printerObj.rtIosId ? printerObj.rtIosId : 5,
        //     printerObj.rtAndId ? printerObj.rtAndId : 5);
        // }

        global.currPrinterConnectedTime = Date.now();

        // console.log(`[pr] ${printerObj.ip} printer result: ${result} at ${moment().format('hh:mm:ss')}`);
        // logToFile(`[pr] ${printerObj.ip} printer result: ${result} at ${moment().format('hh:mm:ss')}`);

        let printerObjIndex = global.printerObjList.findIndex(obj => obj.uniqueId === printerObj.uniqueId);

        if (true) {
          const lastJobCooldownTime = (printerObj.cooldownTime ? printerObj.cooldownTime : 650) + 50;

          setTimeout(async () => {
            for (let i = 0; i < tasks.length; i++) {
              if (global.currOutlet && global.currOutlet.prevWTAlg) {
                // old algo

                waitingTime += (i *
                  (printerObj.cooldownTime ? printerObj.cooldownTime : 650)
                ); // 500 || baseMillisecondToWait
              }
              else {
                // new algo

                // 2023-10-21 - Fixes for printing time (instead of accumulating)
                waitingTime = (i *
                  (printerObj.cooldownTime ? printerObj.cooldownTime : 650)
                ); // 500 || baseMillisecondToWait       
              }

              setTimeout(async () => {
                InteractionManager.runAfterInteractions(async () => {
                  await tasks[i].callback();

                  if (tasks[i].isPrioritized) {
                    global.printerPendingTasksPrioritizedNum--;
                  }

                  // global.printerPendingTasksNum--;

                  (function (index, delay) {
                    console.log('[pr] compare if last index');
                    console.log(`index: ${index}`);

                    logToFile(`[pr] compare if last index: ${printerObj.ip}`);
                    logToFile(`index: ${index}, ${printerObj.ip}`);

                    console.log(`tasks.length - 1: ${tasks.length - 1}`);

                    /////////////////////////////////////////////////////////

                    // 2023-10-28 - Here can mark an 'update' to the order, to indicate this order been printed

                    /////////////////////////////////////////////////////////

                    if (index === tasks.length - 1) {
                      // means last one

                      console.log(`[pr] setTimeout at ${moment().format('hh:mm:ss')}, for ${((printerObj.cooldownTime ? printerObj.cooldownTime : 650) + 1000)} ms`);
                      logToFile(`[pr] setTimeout at ${moment().format('hh:mm:ss')}, for ${((printerObj.cooldownTime ? printerObj.cooldownTime : 650) + 1000)} ms, ${printerObj.ip}`);

                      // setTimeout(() => {
                      //   console.log(`[pr] done one printer cycle at ${moment().format('hh:mm:ss')}`);

                      //   global.isPrintingNow = false;
                      // }, ((printerObj.cooldownTime ? printerObj.cooldownTime : 650) + 50)); // original is 1000

                      setTimeout(() => {
                        console.log(`[pr] done one printer cycle at ${moment().format('hh:mm:ss')}`);
                        logToFile(`[pr] done one printer cycle at ${moment().format('hh:mm:ss')}, lastJobCooldownTime: ${lastJobCooldownTime} ms, ${printerObj.ip}`);

                        global.isPrintingNow = false;
                      }, lastJobCooldownTime); // original is 1000
                    }

                    /////////////////////////////////////////////////////////

                    if (!global.supportCodeData && global.currOutlet && !global.currOutlet.reprintOff) {
                      if (tasks[i].orderUniqueId && tasks[i].taskInfo && tasks[i].taskInfo.orderId) {
                        // const orderPTasks = storageMMKV.getString(`${tasks[i].orderUniqueId}.pTasks`);
                        // if (orderPTasks) {
                        //   // means got already

                        //   storageMMKV.set(`${tasks[i].orderUniqueId}.pTasks`, (parseInt(orderPTasks) - 1).toFixed(0));
                        // }
                        // else {
                        //   storageMMKV.set(`${tasks[i].orderUniqueId}.pTasks`, (-1).toFixed(0));
                        // }

                        // storageMMKV.set(`${tasks[i].orderUniqueId}.pTasksPopup`, '0');

                        try {
                          firebase.firestore()
                            .collection(Collections.UserOrderMetadata)
                            .doc(tasks[i].orderUniqueId)
                            .update({
                              pTasks: firebase.firestore.FieldValue.increment(-1),
                              // [`pTasks.${taskId}`]: true,
                              // pPrinting: '1', // 1 = in progress, 2 = done

                              pTasksPopup: '0',

                              // uniqueId: msgData.orderData.uniqueId,

                              updatedAt: Date.now(),
                            });
                        }
                        catch (ex) {
                          logToFile(ex);
                        }

                        // firestore()
                        //   .collection(Collections.UserOrder)
                        //   .doc(tasks[i].orderUniqueId)
                        //   .update({
                        //     pTasks: firestore.FieldValue.increment(-1),
                        //     // [`pTasks.${taskId}`]: true,
                        //     // pPrinting: '1', // 1 = in progress, 2 = done

                        //     pTasksPopup: '0',
                        //   });

                        logToFile(`[pr] pTasks: firestore.FieldValue.increment(-1) | ${tasks[i].taskInfo.orderId} | ${tasks[i].orderUniqueId}`);
                        logToFile(`[pr] pTasksPopup: 0`);
                      }
                    }
                  })(i, waitingTime);
                });
              }, waitingTime);

              // tasks[i].callback();
              // await waitForSeconds(1);

              global.printerObjList[printerObjIndex].frequency++;
            }
          }, waitingTime);
        }
        else {
          // global.printerPendingTasksNum--;

          console.log(`set isPrintingNow to false`);

          global.isPrintingNow = false;

          // try requeue the printer tasks

          // 2023-06-30

          if (global.routerIpv4 && !global.outletToggleDisablePrintingAlert) {
            showAlertForFailedPrinting(
              printerObj,
              global.routerIpv4,
              tasks,
            );
          }
        }

        result && global.printerObjList[printerObjIndex].priority++;

        /////////////////////////////////

        // clear the tasks after done

        // note: during we print the current tasks, new task might got added        

        // should add a checking first, before remove (or remove by pop?)

        checkPrinterTasksLocking(() => {
          global.printerObjList[printerObjIndex].tasks = global.printerObjList[printerObjIndex].tasks.filter(task => {
            for (var i = 0; i < tasks.length; i++) {
              if (tasks[i].taskId === task.taskId) {
                // means this task already executed, can remove it

                return false;
              }
            }

            return true;
          });

          global.printerObjList[printerObjIndex].priority = result ? 1 : -1;

          global.printerPendingTasksNum -= tasks.length;

          setTimeout(() => {
            // 2023-07-04 - Move to outside first
            // global.printerPendingTasksNum -= tasks.length;

            if (global.printerPendingTasksNum < 0) {
              // just in case

              global.printerPendingTasksNum = 0;
            }

            if (global.printerPendingTasksPrioritizedNum < 0) {
              // just in case

              global.printerPendingTasksPrioritizedNum = 0;
            }
          }, waitingTime);
        });

        // try execute every second (wait longer if after printed)

        // waitingTime += 2000; // 1000

        // setTimeout(() => {
        //   global.isPrintingNow = false;
        // }, waitingTime);

        // await waitForSeconds(1);
        // global.isPrintingNow = false;
      });
    }
    else {
      // try execute every second (wait longer if after printed)

      // waitingTime += 1000; // 100

      // setTimeout(() => {
      //   global.isPrintingNow = false;
      // }, waitingTime);

      // await waitForSeconds(0.5);
      // global.isPrintingNow = false;

      // if (global.printerPendingTasksNum > 0) {
      //   console.log('[pr] help auto deduct');

      //   global.printerPendingTasksNum -= 1;
      // }
    }
  }
};

const App = () => {
  const linkTo = useLinkTo();
  const {
    height: windowHeight,
    width: windowWidth,
  } = useWindowDimensions();

  const merchantId = UserStore.useState((s) => s.merchantId);
  const role = UserStore.useState((s) => s.role);
  const currOutletId = MerchantStore.useState((s) => s.currOutletId);

  const reportOutletIdList = CommonStore.useState((s) => s.reportOutletIdList);

  const historyStartDate = CommonStore.useState(s => s.historyStartDate);
  const historyEndDate = CommonStore.useState(s => s.historyEndDate);
  const selectedCalendarData = CommonStore.useState(s => s.selectedCalendarData);

  const selectedCustomerEdit = CommonStore.useState(
    (s) => s.selectedCustomerEdit
  );

  const outletTables = OutletStore.useState(s => s.outletTables);

  const allOutletsItems = OutletStore.useState((s) => s.allOutletsItems);


  const allOutletsUserOrdersRealTime = OutletStore.useState((s) => s.allOutletsUserOrdersRealTime);
  const allOutletsUserOrdersDoneRealTime = OutletStore.useState((s) => s.allOutletsUserOrdersDoneRealTime);
  const allOutletsUserOrdersDoneCache = OutletStore.useState((s) => s.allOutletsUserOrdersDoneCache);
  const allOutletsUserOrdersCache = OutletStore.useState((s) => s.allOutletsUserOrdersCache);

  const selectedCustomerApplicableVoucherIdList = OutletStore.useState(
    (s) => s.selectedCustomerApplicableVoucherIdList
  );

  const reportingApiLoading = OutletStore.useState(
    (s) => s.reportingApiLoading
  );

  const selectedOrderToPayUserId = CommonStore.useState(
    (s) => s.selectedOrderToPayUserId
  );

  const selectedOutletTable = CommonStore.useState(
    (s) => s.selectedOutletTable
  );

  const isMasterAccount = UserStore.useState((s) => s.isMasterAccount);

  // const closeIt = async (event) => {
  //   // await AsyncStorage.setItem('refreshed', '1');    

  //   event.stopImmediatePropagation();
  //   event.preventDefault();
  //   return event.returnValue = "Are you sure you want to exit?";

  //   // return "Any string value here forces a dialog box to \n" + "appear before closing the window.";
  // }

  // // set the refreshed string before refresh
  // window.onbeforeunload = closeIt;

  // when loading up check if refresh string is 1 to read from storage
  document.addEventListener('readystatechange', async (event) => {
    console.log('page is readystatechange');

    await readFromStorage();

    // const reload = await AsyncStorage.getItem('refreshed');

    // if (reload === '1') {
    //   await readFromStorage();

    // } else {
    // }

    // await AsyncStorage.setItem('refreshed', '0');
  });

  const readFromStorage = async () => {
    console.log('\n Reading from asyncStorage \n')

    global.isLoadedFromLocalDB = true;

    // const commonStoreDataRaw = await AsyncStorage.getItem('@commonStore');
    const commonStoreDataRaw = await idbGet('@commonStore');

    if (commonStoreDataRaw !== null && commonStoreDataRaw !== undefined) {
      await WJsonStream.parse(commonStoreDataRaw).then(async res => {
        // const commonStoreData = JSON.parse(commonStoreDataRaw);
        // console.log(commonStoreData, typeof commonStoreData);

        // CommonStore.replace({
        //   ...commonStoreData,

        //   isLoading: false,
        //   isOrdering: false,
        //   isAuthenticating: false,
        // });

        CommonStore.replace({
          ...res,

          isLoading: false,
          isOrdering: false,
          isAuthenticating: false,

          historyStartDate: moment().startOf('month').startOf('day').valueOf(),
          historyEndDate: moment().endOf('month').endOf('day').valueOf(),
        });
      });
    }


    // const userStoreDataRaw = await AsyncStorage.getItem('@userStore');
    const userStoreDataRaw = await idbGet('@userStore');
    if (userStoreDataRaw !== null && userStoreDataRaw !== undefined) {
      const userStoreData = JSON.parse(userStoreDataRaw);
      UserStore.replace({
        ...userStoreData,

        ...window.location.href.includes('/login') && {
          role: '',
          merchantId: '',
        },
      });

      if (!window.location.href.includes('/login') && userStoreData.role) {
        global.currUserRole = userStoreData.role;
      }

      if (userStoreData.name === '' && window.location.pathname !== `${prefix}/login`) {
        window.location.href = `${prefix}/login`;
      }
    }

    // const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
    const dataStoreDataRaw = await idbGet('@dataStore');
    if (dataStoreDataRaw !== null && dataStoreDataRaw !== undefined) {
      const dataStoreData = JSON.parse(dataStoreDataRaw);
      DataStore.replace(dataStoreData);
    }

    // const merchantStoreDataRaw = await AsyncStorage.getItem('@merchantStore');
    const merchantStoreDataRaw = await idbGet('@merchantStore');
    if (merchantStoreDataRaw !== null && merchantStoreDataRaw !== undefined) {
      const merchantStoreData = JSON.parse(merchantStoreDataRaw);
      MerchantStore.replace({
        ...merchantStoreData,

        ...window.location.href.includes('/login') && {
          currOutletId: '',
        },
      });
    }
    else {
      linkToFunc && linkToFunc(`${prefix}/login`);
    }

    // const outletStoreDataRaw = await AsyncStorage.getItem('@outletStore');
    const outletStoreDataRaw = await idbGet('@outletStore');
    if (outletStoreDataRaw !== null && outletStoreDataRaw !== undefined) {
      await WJsonStream.parse(outletStoreDataRaw).then(async res => {
        // const outletStoreData = JSON.parse(outletStoreDataRaw);
        OutletStore.replace({
          ...res,
          allOutletUserOrderDoneProcessed: [],
          ...res.allOutletUserOrderDoneProcessed === undefined && {
            reportingApiLoading: false,
          }
        });
      });
    }
  }

  const [cartIcon, setCartIcon] = useState(false);

  const alertObj = CommonStore.useState(s => s.alertObj);

  const firebaseUid = UserStore.useState(s => s.firebaseUid);

  const lat = CommonStore.useState(s => s.lat);
  const lng = CommonStore.useState(s => s.lng);

  const selectedOutlet = CommonStore.useState(s => s.selectedOutlet);
  const selectedOutletItem = CommonStore.useState(s => s.selectedOutletItem);

  const selectedOutletTag = CommonStore.useState(s => s.selectedOutletTag);

  const searchOutletText = CommonStore.useState(s => s.searchOutletText);
  const searchOutletMerchantId = CommonStore.useState(s => s.searchOutletMerchantId);

  const cartItems = CommonStore.useState(s => s.cartItems);

  const debugText = CommonStore.useState(s => s.debugText);

  const selectedOutletTableId = CommonStore.useState(s => s.selectedOutletTableId);

  const navigationObj = DataStore.useState(s => s.navigationObj);

  // const beerDockets = CommonStore.useState(s => s.beerDockets);
  // const selectedUserBeerDocket = CommonStore.useState(s => s.selectedUserBeerDocket);
  // const beerDocketsRedemptions = CommonStore.useState(s => s.beerDocketsRedemptions);
  // const beerDocketsRedemptionsBDDict = CommonStore.useState(s => s.beerDocketsRedemptionsBDDict);

  const linkToFunc = DataStore.useState(s => s.linkToFunc);

  const currPage = CommonStore.useState(s => s.currPage);

  const nPromotionNotificationManual = NotificationStore.useState(s => s.nPromotionNotificationManual);
  const nPromotionNotificationAuto = NotificationStore.useState(s => s.nPromotionNotificationAuto);
  const nUserOrderCourierAction = NotificationStore.useState(s => s.nUserOrderCourierAction);

  const userGroups = UserStore.useState(s => s.userGroups);
  const email = UserStore.useState(s => s.email);

  const currOutlet = MerchantStore.useState(s => s.currOutlet);

  // const selectedOutletPromotions = CommonStore.useState(s => s.selectedOutletPromotions);
  // const availablePromotions = CommonStore.useState(s => s.availablePromotions);

  const selectedOutletPointsRedeemPackages = CommonStore.useState(s => s.selectedOutletPointsRedeemPackages);
  // const availablePointsRedeemPackages = CommonStore.useState(s => s.availablePointsRedeemPackages);

  const selectedOutletCRMTagsDict = CommonStore.useState(s => s.selectedOutletCRMTagsDict);
  const selectedOutletCRMUser = CommonStore.useState(s => s.selectedOutletCRMUser);

  const userName = UserStore.useState((s) => s.name);

  // useEffect(() => {
  //   const fetchData = async () => {
  //     const emailParsed = await readFromStorage2();
  //     console.log('AHHHHH', emailParsed);

  //     if (emailParsed === '' && window.location.pathname !== `${prefix}/login`) {
  //       window.location.href = `${prefix}/login`;
  //     }
  //   };

  //   fetchData();
  // }, []);

  ////////////////////////////////////////////////////////////////////

  // 2025-05-20 - to redirect user to login screen, if after 5s no merchant id found

  useEffect(() => {
    if (!merchantId &&
      !window.location.href.includes('/login')
    ) {
      global.checkMerchantIdTimerId = setTimeout(() => {
        window.location.href = `${prefix}/login`;
      }, 10000);
    }

    if (merchantId &&
      !window.location.href.includes('/login')
    ) {
      if (global.checkMerchantIdTimerId) {
        clearTimeout(global.checkMerchantIdTimerId);
      }
    }
  }, [
    merchantId,
  ]);

  ////////////////////////////////////////////////////////////////////


  useEffect(() => {
    console.log('useEffect - App - 8');

    // NetPrinter.init();

    // BLEPrinter.init();

    // initSunmiPrinters();

    // initIminPrinters();

    // setTimeout(() => {
    //   initPrinterBle();
    // }, 10000);

    return () => {
      // BackgroundService.stop();
    };
  }, []);

  const loadAsyncStorage = async () => {
    // await AsyncStorage.removeItem(`${firebaseUid}.cartItems`);

    const cartItemsRaw = await AsyncStorage.getItem(`${firebaseUid}.cartItems`);
    const cartOutletId = await AsyncStorage.getItem(`${firebaseUid}.cartOutletId`);

    if (cartItemsRaw) {
      DataStore.update(s => {
        s.cartItems = [
          // ...s.cartItems,
          ...JSON.parse(cartItemsRaw),
        ];
      });
    }

    if (cartOutletId) {
      CommonStore.update(s => {
        s.cartOutletId = cartOutletId;
      });
    }
  };

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      var outletItems = [];
      var outletItemsDict = {};
      var outletItemsSkuDict = {};

      // var allOutletsItems = [];
      // var allOutletsItemsSkuDict = {};

      if (isMasterAccount) {
        const allOutletsItemsCurrOutlet = allOutletsItems.filter(item => item.outletId === currOutletId);

        outletItems = allOutletsItemsCurrOutlet;
      }
      else if (isMasterAccount === false || isMasterAccount === undefined) {
        outletItems = allOutletsItems;
      }

      global.outletItemsInfoDict = outletItems.reduce((obj, item) => {
        return Object.assign(obj, {
          [item.uniqueId]: {
            categoryId: item.categoryId,
            name: item.name,
            skuMerchant: item.skuMerchant,
          },
        })
      }, {});

      /////////////////////////////////////////

      OutletStore.update((s) => {
        s.outletItems = outletItems;
        // s.outletItemsDict = outletItemsDict;
        // s.outletItemsSkuDict = outletItemsSkuDict;

        // s.allOutletsItems = allOutletsItems;
        // s.allOutletsItemsSkuDict = allOutletsItemsSkuDict;
      });
    });
  }, [currOutletId, allOutletsItems, isMasterAccount]);

  useEffect(async () => {
    setTimeout(() => {
      const unsubscribeCommonStore = CommonStore.subscribe(
        (s) => s,
        async (commonStore) => {
          // AsyncStorage.setItem('@commonStore', JSON.stringify(commonStore));

          // await idbSet('@commonStore', JSON.stringify(commonStore));

          if (global.isLoadedFromLocalDB) {
            await WJsonStream.stringify(commonStore).then(async res => {
              await idbSet('@commonStore', res);
            });
          }
        },
      );

      const unsubscribeDataStore = DataStore.subscribe(
        (s) => s,
        async (dataStore) => {
          // AsyncStorage.setItem('@dataStore', JSON.stringify(dataStore));

          if (global.isLoadedFromLocalDB) {
            await idbSet('@dataStore', JSON.stringify(dataStore));
          }
        },
      );

      const unsubscribeUserStore = UserStore.subscribe(
        (s) => s,
        async (userStore) => {
          // AsyncStorage.setItem('@userStore', JSON.stringify(userStore));

          if (global.isLoadedFromLocalDB) {
            await idbSet('@userStore', JSON.stringify(userStore));
          }
        },
      );

      const unsubscribeOutletStore = OutletStore.subscribe(
        (s) => s,
        async (outletStore) => {
          // AsyncStorage.setItem('@outletStore', JSON.stringify(outletStore));

          if (global.isLoadedFromLocalDB) {
            await WJsonStream.stringify(outletStore).then(async res => {
              await idbSet('@outletStore', res);
            });
          }

          // await idbSet('@outletStore', JSON.stringify({
          //   ...outletStore,
          //   allOutletsUserOrdersDoneCache: [],
          //   allOutletsUserOrdersCache: [],
          //   allOutletsUserOrdersDoneRealTime: [],
          //   allOutletsUserOrdersRealTime: [],
          //   // allOutletsUserOrdersDone: [],
          //   // allOutletsUserOrders: [],

          //   // outletItems: [],
          //   // outletItemsDict: {},
          //   // outletItemsSkuDict: {},
          //   // outletCategories: [],
          //   // outletCategoriesDict: {},
          // }));
        },
      );

      const unsubscribeMerchantStore = MerchantStore.subscribe(
        (s) => s,
        async (merchantStore) => {
          // AsyncStorage.setItem('@merchantStore', JSON.stringify(merchantStore));

          if (global.isLoadedFromLocalDB) {
            await idbSet('@merchantStore', JSON.stringify(merchantStore));
          }
        },
      );

      return () => {
        unsubscribeCommonStore();
        unsubscribeDataStore();
        unsubscribeUserStore();
        unsubscribeOutletStore();
        unsubscribeMerchantStore();
      };
    }, 0);
  }, []);

  useEffect(() => {
    global.ptDateTimestamp = Date.now();

  }, [historyStartDate, historyEndDate]);

  //////////////////////////////////////////////////////////////////

  // 2024-12-03 - printing tasks

  /////////////////////////////////////////////////////////////////////////////////////  

  // for printing orders

  // const userOrdersPrinting = OutletStore.useState(s => s.userOrdersPrinting.filter(record => {
  //   return record.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED ||
  //     record.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED;
  // }));

  //////////////////////////////////////////////////////////////

  // for new printing methods

  const [tokenFcm, setTokenFcm] = useState('');
  const [tokenDevice, setTokenDevice] = useState('');

  const [printedOrderIdList, setPrintedOrderIdList] = useState([]);

  const supportCodeData = CommonStore.useState(s => s.supportCodeData);

  /////////////////////////////////////////////////////////////////////////////////////

  // 2024-12-04 - set token for this device

  const readTokenDevice = async () => {
    const tokenDeviceRaw = await AsyncStorage.getItem('tokenDevice');

    if (tokenDeviceRaw) {
      // means existed

      setTokenDevice(tokenDeviceRaw);
    }
    else {
      var tokenDeviceTemp = uuidv4();
      setTokenDevice(tokenDeviceTemp);

      await AsyncStorage.setItem('tokenDevice', tokenDeviceTemp);
    }
  };

  useEffect(() => {
    readTokenDevice();
  }, []);

  /////////////////////////////////////////////////////////////////////////////////////

  const userOrdersPrinting = OutletStore.useState(s => s.userOrdersPrinting);

  // duplicated printing might caused by simultaneous changes of watch states, need to make them synchronous
  // use the useLayoutEffect instead of useEffect?
  // use the waiting queue to block?
  useLayoutEffect(() => { // remove async
    if (userOrdersPrinting.length > 0 && (tokenFcm || tokenDevice)) {
      // var userOrdersPrintingTemp = [
      //   ...userOrdersPrinting,
      // ];

      if (!global.outletToggleDisableAutoPrint) {
        InteractionManager.runAfterInteractions(async () => {
          ///////////////////////////////////////////////////

          // 2023-05-26 - Extra code for queue waiting

          // await new Promise((resolve) => {
          //   let intervalTimer = setInterval(() => {
          //     console.log('waiting');

          //     if (!global.isCheckingPrintKdOsNow) {
          //       console.log('done waiting');

          //       clearInterval(intervalTimer);
          //       resolve();
          //     }
          //   }, 10); // 100 ms
          // });

          // global.isCheckingPrintKdOsNow = true;

          ///////////////////////////////////////////////////

          var userOrdersPrintingTemp = userOrdersPrinting.filter(order => {
            if (order.printedTokenList &&
              // (
              //   (tokenFcm && !order.printedTokenList.includes(tokenFcm))
              //   ||
              //   (tokenDevice && !order.printedTokenList.includes(tokenDevice))
              // )
              (
                (!supportCodeData && order.printedTokenList.length === 0) // only print if empty token
                ||
                (supportCodeData &&
                  (
                    order.printedTokenListSupport === undefined ||
                    (
                      order.printedTokenListSupport &&
                      (
                        (tokenFcm && !order.printedTokenListSupport.includes(tokenFcm))
                        ||
                        (tokenDevice && !order.printedTokenListSupport.includes(tokenDevice))
                      )
                    )
                  )
                )
              )
              &&
              !global.printedOrderListLocal.includes(order.uniqueId)) {
              // console.log('order.printedTokenList');
              // console.log(order.printedTokenList);
              // console.log('order.printedTokenListSupport');
              // console.log(order.printedTokenListSupport);
              // console.log('tokenFcm');
              // console.log(tokenFcm);
              // console.log('tokenDevice');
              // console.log(tokenDevice);

              let validStatus = true;

              // 2024-08-16 - authorize dine-in orders support

              if (currOutlet.dineInRequiredAuthorization && order.orderType === ORDER_TYPE.DINEIN
                &&
                (
                  order.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED
                  ||
                  order.autoAuthorized // means this order is auto authorized
                )
              ) {
                validStatus = false;
              }

              return validStatus;
            }
            else {
              return false;
            }
          });

          if (userOrdersPrintingTemp.length > 0) {
            // setPrintedOrderIdList([
            //   ...printedOrderIdList,
            //   userOrdersPrintingTemp.map(order => order.uniqueId),
            // ]);

            // global.isCheckingIncomingOrders = true;

            global.printedOrderListLocal = [
              ...global.printedOrderListLocal,
              ...userOrdersPrintingTemp.map(order => order.uniqueId),
            ];

            printUserOrdersTrigger(userOrdersPrintingTemp);

            if (!supportCodeData) {
              updateUserOrdersTrigger(userOrdersPrintingTemp);
            }
            else {
              updateUserOrdersTriggerSupport(userOrdersPrintingTemp);
            }

            seatUserOrders(userOrdersPrintingTemp.filter(order => order.tableId));

            // setTimeout(() => {
            //   global.isCheckingIncomingOrders = false;
            // }, 500);

            ///////////////////////////////////////////////////////////////////

            // force sequential

            // var isPrintingKDAndOS = await AsyncStorage.getItem('isPrintingKDAndOS');

            // console.log(`[batch] ${userOrdersPrintingTemp[0].finalPrice}: isPrintingKDAndOS checking: ${isPrintingKDAndOS}`);

            // while (isPrintingKDAndOS === '1') {
            //   await waitForSeconds(1); // before print another set

            //   isPrintingKDAndOS = await AsyncStorage.getItem('isPrintingKDAndOS');

            //   console.log(`[batch] ${userOrdersPrintingTemp[0].finalPrice}: isPrintingKDAndOS looping: ${isPrintingKDAndOS}`);
            // }

            // if (isPrintingKDAndOS === '0') {
            //   await AsyncStorage.setItem('isPrintingKDAndOS', '1');

            //   console.log(`[batch] ${userOrdersPrintingTemp[0].finalPrice}: isPrintingKDAndOS now printing: ${isPrintingKDAndOS}`);

            //   printUserOrdersTrigger(userOrdersPrintingTemp);
            // }

            ///////////////////////////////////////////////////////////////////                

            // clean up
            // printedOrderListLocal = [];
          }

          // global.isCheckingPrintKdOsNow = false;
        });
      }
    }
  }, [
    userOrdersPrinting, // use length better?
    tokenFcm,
    tokenDevice,

    currOutlet,
  ]);

  const updateUserOrdersTrigger = async (userOrdersPrintingTemp) => {
    if (tokenFcm || tokenDevice) {
      const userOrderBatch = firebase.firestore().batch();

      for (let i = 0; i < userOrdersPrintingTemp.length; i++) {
        let printedTokenList = userOrdersPrintingTemp[i].printedTokenList || ['koodoo'];

        if (!printedTokenList.includes(tokenFcm ? tokenFcm : tokenDevice)) {
          userOrderBatch.update(
            firebase.firestore().collection(Collections.UserOrder).doc(userOrdersPrintingTemp[i].uniqueId),
            {
              // triggerPrintingTimes: 1,
              printedTokenList: [
                ...printedTokenList,
                tokenFcm ? tokenFcm : tokenDevice,
              ],

              updatedAt: Date.now(),
            },
          );
        }
      }

      userOrderBatch.commit();
    }
  };

  const updateUserOrdersTriggerSupport = async (userOrdersPrintingTemp) => {
    if (tokenFcm || tokenDevice) {
      const userOrderBatch = firebase.firestore().batch();

      for (let i = 0; i < userOrdersPrintingTemp.length; i++) {
        let printedTokenListSupport = userOrdersPrintingTemp[i].printedTokenListSupport || ['koodoo'];

        if (!printedTokenListSupport.includes(tokenFcm ? tokenFcm : tokenDevice)) {
          userOrderBatch.update(
            firebase.firestore().collection(Collections.UserOrder).doc(userOrdersPrintingTemp[i].uniqueId),
            {
              // triggerPrintingTimes: 1,
              printedTokenListSupport: [
                ...printedTokenListSupport,
                tokenFcm ? tokenFcm : tokenDevice,
              ],

              updatedAt: Date.now(),
            },
          );
        }
      }

      userOrderBatch.commit();
    }
  };

  const printUserOrdersTrigger = async (userOrdersPrintingTemp) => {
    let printKdOsBeforeMinutes = 60;
    if (global.reservationConfig && typeof global.reservationConfig.printKdOsBeforeMinutes === 'number') {
      printKdOsBeforeMinutes = global.reservationConfig.printKdOsBeforeMinutes;
    }

    for (let i = 0; i < userOrdersPrintingTemp.length; i++) {
      // userOrderBatch.update(
      //   firestore().collection(Collections.UserOrder).doc(userOrdersPrintingTemp[i].uniqueId),
      //   {
      //     triggerPrintingTimes: 1,

      //     updatedAt: Date.now(),
      //   },
      // );

      let order = userOrdersPrintingTemp[i];

      // console.log(`${order.finalPrice}: global.blockingKDAndOS checking: ${global.blockingKDAndOS}`);

      // while (global.blockingKDAndOS === true) {
      //   await waitForSeconds(1); // before print another set

      //   console.log(`${order.finalPrice}: global.blockingKDAndOS looping: ${global.blockingKDAndOS}`);
      // }

      // if (global.blockingKDAndOS === false) {
      //   global.blockingKDAndOS = true;

      //   console.log(`${order.finalPrice}: global.blockingKDAndOS now printing: ${global.blockingKDAndOS}`);
      // }

      if (
        (moment().diff(order.orderDate, 'minute') <= 15 ||
          moment().diff(order.createdAt, 'minute') <= 15)
        ||
        (order.isReservationOrder &&
          (
            moment(order.reservationTime).diff(Date.now(), 'minute') <=
            (printKdOsBeforeMinutes)
          )
        )
      ) {
        if (order.orderType === ORDER_TYPE.DINEIN) {
          // if ((isPrintingReceipt === '0' || isPrintingReceipt === null)) {
          //   NetPrinter.closeConn();
          // }

          // NetPrinter.closeConn(); // no need anymore

          printUserOrder(
            {
              orderData: order,
            },
            false,
            [PRINTER_USAGE_TYPE.ORDER_SUMMARY],
            false,
            false,
            false,
            { isInternetReachable: true, isConnected: true },
          );

          printUserOrder(
            {
              orderData: order,
            },
            false,
            [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
            false,
            false,
            false,
            { isInternetReachable: true, isConnected: true },
          );

          printKDSummaryCategoryWrapper(
            {
              orderData: order,
            },
          );

          for (let bdIndex = 0; bdIndex < order.cartItems.length; bdIndex++) {
            if (!order.cartItems[bdIndex].isDocket) {
              printDocketForKD(
                {
                  userOrder: order,
                  cartItem: order.cartItems[bdIndex],
                },
                // true,
                [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
              );
            }
          }

          ///////////////////////////////////

          // for beer docket

          if (order && order.cartItems) {
            for (let index = 0; index < order.cartItems.length; index++) {
              if (order.cartItems[index].isDocket) {
                await printDocket(
                  {
                    userOrder: order,
                    cartItem: order.cartItems[index],
                  },
                  // true,
                  [PRINTER_USAGE_TYPE.RECEIPT],
                );
              }
            }
          }

          ///////////////////////////////////

          // setTimeout(async () => {
          //   global.blockingKDAndOS = false;

          //   console.log(`${order.finalPrice}: global.blockingKDAndOS reset: ${global.blockingKDAndOS}`);
          // }, 3000);
        }
        else if (order.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED) {
          // if received don't print first, wait until authorized
        }
        else {
          // if (isPrintingReceipt === '0' || isPrintingReceipt === null) {
          //   NetPrinter.closeConn();
          // }

          // NetPrinter.closeConn(); // no need anymore

          // NetPrinter.closeConn();

          printUserOrder(
            {
              orderData: order,
            },
            false,
            [PRINTER_USAGE_TYPE.ORDER_SUMMARY],
            false,
            false,
            false,
            { isInternetReachable: true, isConnected: true },
          );

          printUserOrder(
            {
              orderData: order,
            },
            false,
            [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
            false,
            false,
            false,
            { isInternetReachable: true, isConnected: true },
          );

          printKDSummaryCategoryWrapper(
            {
              orderData: order,
            },
          );

          for (let bdIndex = 0; bdIndex < order.cartItems.length; bdIndex++) {
            if (!order.cartItems[bdIndex].isDocket) {
              printDocketForKD(
                {
                  userOrder: order,
                  cartItem: order.cartItems[bdIndex],
                },
                // true,
                [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
              );
            }
          }

          // if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY) {
          //   await printUserOrder(
          //     {
          //       orderData: order,
          //     },
          //     false,
          //     [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
          //     false,
          //     false,
          //     false,
          //     { isInternetReachable: true, isConnected: true },
          //   );
          // }
          // else if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY_CATEGORY) {
          //   await printKDSummaryCategoryWrapper(
          //     {
          //       orderData: order,
          //     },
          //   );
          // }
          // else if (global.outletKdVariation === KD_PRINT_VARIATION.INDIVIDUAL) {
          //   for (let bdIndex = 0; bdIndex < order.cartItems.length; bdIndex++) {
          //     await printDocketForKD(
          //       {
          //         userOrder: order,
          //         cartItem: order.cartItems[bdIndex],
          //       },
          //       // true,
          //       [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
          //       // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
          //     );
          //   }
          // }

          // NetPrinter.closeConn();            

          ///////////////////////////////////

          // for beer docket

          if (order && order.cartItems) {
            for (let index = 0; index < order.cartItems.length; index++) {
              if (order.cartItems[index].isDocket) {
                await printDocket(
                  {
                    userOrder: order,
                    cartItem: order.cartItems[index],
                  },
                  // true,
                  [PRINTER_USAGE_TYPE.RECEIPT],
                );
              }
            }
          }

          ///////////////////////////////////

          // setTimeout(async () => {
          //   global.blockingKDAndOS = false;

          //   console.log(`${order.finalPrice}: global.blockingKDAndOS reset: ${global.blockingKDAndOS}`);
          // }, 3000);
        }
      }
      else {
        // setTimeout(async () => {

        //   global.blockingKDAndOS = false;

        //   console.log(`${order.finalPrice}: global.blockingKDAndOS reset: ${global.blockingKDAndOS}`);
        // }, 1000);
      }
    }
  };

  const seatUserOrders = async (userOrdersTable) => {
    var tableIdList = [...new Set(userOrdersTable.map(order => order.tableId))];

    for (let i = 0; i < tableIdList.length; i++) {
      const currTable = outletTables.find(table => table.uniqueId === tableIdList[i]);

      if (currTable && currTable.uniqueId && currTable.seated <= 0) {
        // if (currTable && currTable.uniqueId) {
        // try to seat the table to show the order

        var paxTotal = userOrdersTable.filter(order => order.tableId === currTable.uniqueId).reduce((accum, order) => accum + order.tablePax, 0);

        var body = {
          tableId: currTable.uniqueId,
          pax: paxTotal,
          outletId: currOutletId,
        };

        APILocal.addCustomer({ body }).then((result) => {
          if (result && result.status === 'success') {
            console.log('auto seated');
          }
        });
      }
    }
  };

  /////////////////////////////////////////////////////////////////////////////////////  

  // 2023-01-03 - Print receipt when user paid orders online (paid when order placed or after order placed)

  useEffect(async () => {
    if (allOutletsUserOrdersRealTime.length > 0 && (tokenFcm || tokenDevice) && global.outletPrintReceiptWhenPaidOnline) {
      // var userOrdersPrintingTemp = [
      //   ...userOrdersPrinting,
      // ];

      if (!global.outletToggleDisableAutoPrint) {
        InteractionManager.runAfterInteractions(() => {
          var allOutletsUserOrdersRealTimeTemp = allOutletsUserOrdersRealTime.filter(order =>
            order.paymentDate &&
            order.paymentDetails &&
            (order.paymentDetails.txn_ID !== undefined ||
              order.paymentDetails.txnId !== undefined)
            &&
            (
              (order.printedTokenPaidOnlineList === undefined || (order.printedTokenPaidOnlineList && order.printedTokenPaidOnlineList.length === 0)) // only print if empty token
              ||
              (supportCodeData &&
                (
                  order.printedTokenPaidOnlineListSupport === undefined ||
                  (
                    order.printedTokenPaidOnlineListSupport &&
                    (
                      (tokenFcm && !order.printedTokenPaidOnlineListSupport.includes(tokenFcm))
                      ||
                      (tokenDevice && !order.printedTokenPaidOnlineListSupport.includes(tokenDevice))
                    )
                  )
                )
              )
            )

            // (order.printedTokenPaidOnlineList === undefined ||
            //   (
            //     order.printedTokenPaidOnlineList &&
            //     // !order.printedTokenPaidOnlineList.includes(tokenFcm)
            //     (
            //       (tokenFcm && !order.printedTokenPaidOnlineList.includes(tokenFcm))
            //       ||
            //       (tokenDevice && !order.printedTokenPaidOnlineList.includes(tokenDevice))
            //     )
            //     // (order.printedTokenPaidOnlineList.length === 0)
            //   )
            // )
            &&
            !global.printedOrderPaidOnlineListLocal.includes(order.uniqueId)
          );

          if (allOutletsUserOrdersRealTimeTemp.length > 0) {
            // setPrintedOrderIdList([
            //   ...printedOrderIdList,
            //   userOrdersPrintingTemp.map(order => order.uniqueId),
            // ]);

            // global.isCheckingIncomingOrders = true;

            global.printedOrderPaidOnlineListLocal = [
              ...global.printedOrderPaidOnlineListLocal,
              ...allOutletsUserOrdersRealTimeTemp.map(order => order.uniqueId),
            ];

            //printUserOrdersTriggerPaidOnline(allOutletsUserOrdersRealTimeTemp);

            if (!supportCodeData) {
              updateUserOrdersTriggerPaidOnline(allOutletsUserOrdersRealTimeTemp);
            }
            else {
              updateUserOrdersTriggerPaidOnlineSupport(allOutletsUserOrdersRealTimeTemp);
            }

            // setTimeout(() => {
            //   global.isCheckingIncomingOrders = false;
            // }, 500);

            ///////////////////////////////////////////////////////////////////

            // force sequential

            // var isPrintingKDAndOS = await AsyncStorage.getItem('isPrintingKDAndOS');

            // console.log(`[batch] ${userOrdersPrintingTemp[0].finalPrice}: isPrintingKDAndOS checking: ${isPrintingKDAndOS}`);

            // while (isPrintingKDAndOS === '1') {
            //   await waitForSeconds(1); // before print another set

            //   isPrintingKDAndOS = await AsyncStorage.getItem('isPrintingKDAndOS');

            //   console.log(`[batch] ${userOrdersPrintingTemp[0].finalPrice}: isPrintingKDAndOS looping: ${isPrintingKDAndOS}`);
            // }

            // if (isPrintingKDAndOS === '0') {
            //   await AsyncStorage.setItem('isPrintingKDAndOS', '1');

            //   console.log(`[batch] ${userOrdersPrintingTemp[0].finalPrice}: isPrintingKDAndOS now printing: ${isPrintingKDAndOS}`);

            //   printUserOrdersTrigger(userOrdersPrintingTemp);
            // }

            ///////////////////////////////////////////////////////////////////                

            // clean up
            // printedOrderListLocal = [];
          }
        });
      }
    }
  }, [allOutletsUserOrdersRealTime, tokenFcm, tokenDevice]);

  const updateUserOrdersTriggerPaidOnline = async (userOrdersPrintingTemp) => {
    if (tokenFcm || tokenDevice) {
      const userOrderBatch = firebase.firestore().batch();

      for (let i = 0; i < userOrdersPrintingTemp.length; i++) {
        let printedTokenPaidOnlineList = userOrdersPrintingTemp[i].printedTokenPaidOnlineList || ['koodoo'];

        userOrderBatch.update(
          firebase.firestore().collection(Collections.UserOrder).doc(userOrdersPrintingTemp[i].uniqueId),
          {
            // triggerPrintingTimes: 1,
            printedTokenPaidOnlineList: [
              ...printedTokenPaidOnlineList,
              tokenFcm ? tokenFcm : tokenDevice,
            ],

            updatedAt: Date.now(),
          },
        );
      }

      userOrderBatch.commit();
    }
  };

  const updateUserOrdersTriggerPaidOnlineSupport = async (userOrdersPrintingTemp) => {
    if (tokenFcm || tokenDevice) {
      const userOrderBatch = firebase.firestore().batch();

      for (let i = 0; i < userOrdersPrintingTemp.length; i++) {
        let printedTokenPaidOnlineListSupport = userOrdersPrintingTemp[i].printedTokenPaidOnlineListSupport || ['koodoo'];

        userOrderBatch.update(
          firebase.firestore().collection(Collections.UserOrder).doc(userOrdersPrintingTemp[i].uniqueId),
          {
            // triggerPrintingTimes: 1,
            printedTokenPaidOnlineListSupport: [
              ...printedTokenPaidOnlineListSupport,
              tokenFcm ? tokenFcm : tokenDevice,
            ],

            updatedAt: Date.now(),
          },
        );
      }

      userOrderBatch.commit();
    }
  };

  //////////////////////////////////////////////////////////////////

  useEffect(() => {
    if (reportOutletIdList) {
      InteractionManager.runAfterInteractions(() => {
        // console.log('======================');
        // console.log('======================');
        // console.log('======================');
        // console.log('test records');

        OutletStore.update(s => {
          s.allOutletsUserOrdersDone = allOutletsUserOrdersDoneCache.concat(allOutletsUserOrdersDoneRealTime).filter(order => {
            if (reportOutletIdList.includes(order.outletId)) {
              // console.log('----------------');
              // console.log(order.uniqueId);
              // console.log(order.orderId);
              // console.log(order.finalPrice)
            }

            return reportOutletIdList.includes(order.outletId);
          });
          s.allOutletsUserOrders = allOutletsUserOrdersCache.concat(allOutletsUserOrdersRealTime).filter(order => {
            return reportOutletIdList.includes(order.outletId);
          });
        });

        // console.log('======================');
        // console.log('======================');
        // console.log('======================');
      });
    }
  }, [
    allOutletsUserOrdersDoneCache,
    allOutletsUserOrdersCache,
    allOutletsUserOrdersDoneRealTime,
    allOutletsUserOrdersRealTime,

    reportOutletIdList,
    currOutletId,
  ]);

  useEffect(() => {
    if (currOutlet && currOutlet.uniqueId) {
      listenToAccountEmailChangesMerchant(currOutlet.uniqueId);
    }
    else {
      listenToAccountEmailChangesMerchant('');
    }
  }, [currOutlet]);

  ////////////////////////////////////////////////////////////////////

  // 2023-02-07 - Standardize listeners

  useEffect(() => {
    if (
      selectedOutletItem !== null &&
      selectedOutletItem !== undefined &&
      selectedOutletItem.uniqueId
    ) {
      // InteractionManager.runAfterInteractions(() => {
      //   listenToSelectedOutletItemChanges(selectedOutletItem);
      // });

      typeof global.subscriberListenToSelectedOutletItemChanges === 'function' && global.subscriberListenToSelectedOutletItemChanges();
      global.subscriberListenToSelectedOutletItemChanges = () => { };

      let subscriber = listenToSelectedOutletItemChanges(selectedOutletItem);

      global.subscriberListenToSelectedOutletItemChanges = subscriber;

      return () => {
        typeof subscriber === 'function' && subscriber();
      };
    }
  }, [selectedOutletItem, role]);

  useEffect(() => {
    // InteractionManager.runAfterInteractions(() => {
    //   listenToSelectedOrderfToPayUserIdChanges(selectedOrderToPayUserId || "default-user");

    //   // if (selectedOrderToPayUserId && selectedOrderToPayUserId !== "") {
    //   //   listenToSelectedOrderToPayUserIdChanges(selectedOrderToPayUserId);
    //   // }
    //   // else {
    //   //   // OutletStore.update((s) => {
    //   //   //   s.selectedCustomerOrders = [];
    //   //   //   s.selectedCustomerDineInOrders = [];

    //   //   //   s.selectedCustomerAddresses = [];
    //   //   //   s.selectedCustomerPointsTransactions = [];
    //   //   //   s.selectedCustomerPointsBalance = 0;

    //   //   //   s.selectedCustomerVoucherRedemptions = [];

    //   //   //   s.selectedCustomerUserBeerDockets = [];

    //   //   //   s.selectedCustomerLCCTransactionsEmail = [];
    //   //   //   s.selectedCustomerLCCBalanceEmail = 0;
    //   //   //   s.selectedCustomerLCCTransactionsPhone = [];
    //   //   //   s.selectedCustomerLCCBalancePhone = 0;

    //   //   //   s.selectedCustomerLCCTransactions = [];
    //   //   //   s.selectedCustomerLCCBalance = 0;

    //   //   //   s.selectedCustomerUserLoyaltyCampaigns = [];

    //   //   //   s.selectedCustomerUserTaggableVouchers = [];

    //   //   //   // s.availableTaggableVouchers = [];
    //   //   // });
    //   // }
    // });

    typeof global.subscriberListenToSelectedOrderToPayUserIdChanges === 'function' && global.subscriberListenToSelectedOrderToPayUserIdChanges();
    global.subscriberListenToSelectedOrderToPayUserIdChanges = () => { };

    let subscriber = listenToSelectedOrderToPayUserIdChanges(selectedOrderToPayUserId || 'default-user');

    global.subscriberListenToSelectedOrderToPayUserIdChanges = subscriber;

    return () => {
      typeof subscriber === 'function' && subscriber();
    };
  }, [selectedOrderToPayUserId]);

  useEffect(() => {
    if (
      firebaseUid.length > 0 &&
      selectedOutletTable &&
      selectedOutletTable.uniqueId &&
      currOutletId
    ) {
      // InteractionManager.runAfterInteractions(() => {
      //   listenToSelectedOutletTableIdChanges(
      //     firebaseUid,
      //     selectedOutletTable.uniqueId,
      //     currOutletId,
      //   );
      // });

      typeof global.subscriberListenToSelectedOutletTableIdChanges === 'function' && global.subscriberListenToSelectedOutletTableIdChanges();
      global.subscriberListenToSelectedOutletTableIdChanges = () => { };

      let subscriber = listenToSelectedOutletTableIdChanges(
        firebaseUid,
        selectedOutletTable.uniqueId,
        currOutletId,
      );

      global.subscriberListenToSelectedOutletTableIdChanges = subscriber;

      return () => {
        typeof subscriber === 'function' && subscriber();
      };
    }
  }, [firebaseUid, selectedOutletTable, currOutletId]);

  useEffect(() => {
    // console.log('screen height' + windowHeight);
    // console.log('screen width' + windowWidth);

    // InteractionManager.runAfterInteractions(() => {
    //   listenToCommonChangesMerchant();
    // });        

    requestNotificationsPermission();

    // messaging().onMessage(async (msg) => {
    //   // console.log('message from foreground!');
    //   // console.log(msg);

    //   parseMessages(msg);
    // });

    //////////////////////////////////////////////

    typeof global.subscriberListenToCommonChangesMerchant === 'function' && global.subscriberListenToCommonChangesMerchant();
    global.subscriberListenToCommonChangesMerchant = () => { };

    let subscriber = listenToCommonChangesMerchant();

    global.subscriberListenToCommonChangesMerchant = subscriber;

    return () => {
      typeof subscriber === 'function' && subscriber();
    };

    //////////////////////////////////////////////
  }, []);

  useEffect(() => {
    if (
      selectedCustomerEdit &&
      currOutletId
      // selectedCustomerEdit.firebaseUid &&
      // selectedCustomerEdit.firebaseUid.length > 0
    ) {
      // OutletStore.update((s) => {
      //   s.selectedCustomerOrders = [];
      //   s.selectedCustomerDineInOrders = [];

      //   s.selectedCustomerAddresses = [];
      //   s.selectedCustomerPointsTransactions = [];
      //   s.selectedCustomerPointsBalance = 0;

      //   s.selectedCustomerVoucherRedemptions = [];

      //   s.selectedCustomerUserBeerDockets = [];

      //   s.selectedCustomerLCCTransactions = [];
      //   s.selectedCustomerLCCBalance = 0;

      //   s.selectedCustomerUserLoyaltyCampaigns = [];
      // });

      // InteractionManager.runAfterInteractions(() => {
      //   listenToSelectedCustomerChangesMerchant(selectedCustomerEdit, currOutletId);
      // });      

      typeof global.subscriberListenToSelectedCustomerChangesMerchant === 'function' && global.subscriberListenToSelectedCustomerChangesMerchant();
      global.subscriberListenToSelectedCustomerChangesMerchant = () => { };

      let subscriber = listenToSelectedCustomerChangesMerchant(selectedCustomerEdit, currOutletId);

      global.subscriberListenToSelectedCustomerChangesMerchant = subscriber;

      return () => {
        typeof subscriber === 'function' && subscriber();
      };
    } else {
      OutletStore.update((s) => {
        s.selectedCustomerOrders = [];
        s.selectedCustomerDineInOrders = [];

        s.selectedCustomerAddresses = [];
        s.selectedCustomerPointsTransactions = [];
        s.selectedCustomerPointsBalance = 0;

        s.selectedCustomerVoucherRedemptions = [];

        s.selectedCustomerUserBeerDockets = [];

        s.selectedCustomerLCCTransactionsEmail = [];
        s.selectedCustomerLCCBalanceEmail = 0;
        s.selectedCustomerLCCTransactionsPhone = [];
        s.selectedCustomerLCCBalancePhone = 0;

        s.selectedCustomerLCCTransactions = [];
        s.selectedCustomerLCCBalance = 0;

        s.selectedCustomerPointsTransactionsEmail = [];
        s.selectedCustomerPointsBalanceEmail = 0;
        s.selectedCustomerPointsTransactionsPhone = [];
        s.selectedCustomerPointsBalancePhone = 0;
        s.selectedCustomerPointsTransactions = [];
        s.selectedCustomerPointsBalance = 0;

        s.selectedCustomerUserLoyaltyCampaigns = [];

        s.selectedCustomerUserTaggableVouchersView = [];

        // s.availableTaggableVouchers = [];
      });
    }
  }, [selectedCustomerEdit, currOutletId]);

  useEffect(async () => {
    if (
      selectedCustomerEdit && selectedCustomerEdit.email &&
      selectedCustomerApplicableVoucherIdList.length > 0
      // selectedCustomerEdit.firebaseUid &&
      // selectedCustomerEdit.firebaseUid.length > 0
    ) {
      // OutletStore.update((s) => {
      //   s.selectedCustomerOrders = [];
      //   s.selectedCustomerDineInOrders = [];

      //   s.selectedCustomerAddresses = [];
      //   s.selectedCustomerPointsTransactions = [];
      //   s.selectedCustomerPointsBalance = 0;

      //   s.selectedCustomerVoucherRedemptions = [];

      //   s.selectedCustomerUserBeerDockets = [];

      //   s.selectedCustomerLCCTransactions = [];
      //   s.selectedCustomerLCCBalance = 0;

      //   s.selectedCustomerUserLoyaltyCampaigns = [];
      // });

      // InteractionManager.runAfterInteractions(() => {
      //   listenToSelectedCustomerApplicableVoucherIdChangesMerchant(selectedCustomerEdit, selectedCustomerApplicableVoucherIdList);
      // });

      typeof global.subscriberListenToSelectedCustomerApplicableVoucherIdChangesMerchant === 'function' && global.subscriberListenToSelectedCustomerApplicableVoucherIdChangesMerchant();
      global.subscriberListenToSelectedCustomerApplicableVoucherIdChangesMerchant = () => { };

      let subscriber = await listenToSelectedCustomerApplicableVoucherIdChangesMerchant(selectedCustomerEdit, selectedCustomerApplicableVoucherIdList);

      global.subscriberListenToSelectedCustomerApplicableVoucherIdChangesMerchant = subscriber;

      return () => {
        typeof subscriber === 'function' && subscriber();
      };
    } else {
      OutletStore.update((s) => {
        // s.selectedCustomerUserLoyaltyCampaigns = [];

        s.selectedCustomerUserTaggableVouchers = [];

        // s.availableTaggableVouchers = [];
      });
    }
  }, [selectedCustomerEdit, selectedCustomerApplicableVoucherIdList]);

  useEffect(() => {
    if (firebaseUid !== "") {
      if (
        role === ROLE_TYPE.ADMIN ||
        role === ROLE_TYPE.LEGACY ||
        role === ROLE_TYPE.STORE_MANAGER ||
        role === ROLE_TYPE.FRONTLINER
      ) {
        // InteractionManager.runAfterInteractions(() => {
        //   listenToUserChangesMerchant(firebaseUid, currOutletId);
        // });

        typeof global.subscriberListenToUserChangesMerchant === 'function' && global.subscriberListenToUserChangesMerchant();
        global.subscriberListenToUserChangesMerchant = () => { };

        let subscriber = listenToUserChangesMerchant(firebaseUid, currOutletId);

        global.subscriberListenToUserChangesMerchant = subscriber;

        return () => {
          typeof subscriber === 'function' && subscriber();
        };
      }
    }
  }, [firebaseUid, role]);

  useEffect(() => {
    if (merchantId !== '' && currOutletId !== '' && isMasterAccount !== undefined) {
      if (
        role === ROLE_TYPE.ADMIN ||
        role === ROLE_TYPE.LEGACY ||
        role === ROLE_TYPE.STORE_MANAGER ||
        role === ROLE_TYPE.FRONTLINER
      ) {
        // InteractionManager.runAfterInteractions(() => {
        //   listenToMerchantIdChangesMerchant(merchantId);
        // });

        // listenToMerchantIdChangesMerchant(merchantId);

        typeof global.subscriberListenToMerchantIdChangesMerchant === 'function' && global.subscriberListenToMerchantIdChangesMerchant();
        global.subscriberListenToMerchantIdChangesMerchant = () => { };

        let subscriberSummary = listenToMerchantIdChangesMerchant(merchantId, currOutletId, isMasterAccount);

        global.subscriberListenToMerchantIdChangesMerchant = subscriberSummary;

        return () => {
          typeof subscriberSummary === 'function' && subscriberSummary();
        };
      }
    }
  }, [
    merchantId,
    role,

    currOutletId,
    isMasterAccount,
  ]);

  useEffect(() => {
    if (currOutletId !== '') {
      if (
        role === ROLE_TYPE.ADMIN ||
        role === ROLE_TYPE.LEGACY ||
        role === ROLE_TYPE.STORE_MANAGER ||
        role === ROLE_TYPE.FRONTLINER
      ) {
        // InteractionManager.runAfterInteractions(() => {
        //   listenToCurrOutletIdChangesWaiter(role, currOutletId, false);

        //   listenToCurrOutletIdChangesMerchant(merchantId, currOutletId);
        // });

        // typeof subscriberListenToCurrOutletIdChangesWaiter === 'function' && subscriberListenToCurrOutletIdChangesWaiter();
        // setSubscriberListenToCurrOutletIdChangesWaiter(() => { });

        typeof global.subscriberListenToCurrOutletIdChangesWaiter === 'function' && global.subscriberListenToCurrOutletIdChangesWaiter();
        global.subscriberListenToCurrOutletIdChangesWaiter = () => { };

        let subscriber = listenToCurrOutletIdChangesWaiter(role, currOutletId, false, '', currOutlet.toggleOpenOrder);

        global.subscriberListenToCurrOutletIdChangesWaiter = subscriber;

        return () => {
          typeof subscriber === 'function' && subscriber();
        };
      }
    }
  }, [currOutletId, role, currOutlet.toggleOpenOrder]);

  useEffect(() => {
    if (merchantId !== '' && currOutletId !== '') {
      if (
        role === ROLE_TYPE.ADMIN ||
        role === ROLE_TYPE.LEGACY ||
        role === ROLE_TYPE.STORE_MANAGER ||
        role === ROLE_TYPE.FRONTLINER
      ) {
        // InteractionManager.runAfterInteractions(() => {
        //   listenToCurrOutletIdChangesWaiter(role, currOutletId, false);

        //   listenToCurrOutletIdChangesMerchant(merchantId, currOutletId);
        // });

        typeof global.subscriberListenToCurrOutletIdChangesMerchant === 'function' && global.subscriberListenToCurrOutletIdChangesMerchant();
        global.subscriberListenToCurrOutletIdChangesMerchant = () => { };

        let subscriber = listenToCurrOutletIdChangesMerchant(merchantId, currOutletId, isMasterAccount);

        global.subscriberListenToCurrOutletIdChangesMerchant = subscriber;

        return () => {
          typeof subscriber === 'function' && subscriber();
        };
      }
    }
  }, [merchantId, currOutletId, role, isMasterAccount]);

  useEffect(() => {
    if (merchantId && currOutlet && currOutlet.uniqueId
      && reportOutletIdList && reportOutletIdList.length >= 0) {
      typeof global.subscriberListenToUserOrderHistoricalChanges === 'function' && global.subscriberListenToUserOrderHistoricalChanges();
      global.subscriberListenToUserOrderHistoricalChanges = () => { };

      let subscriberSummary = listenToUserOrderHistoricalChanges(merchantId, historyStartDate, historyEndDate, currOutlet.reportDataSizeLimit ? currOutlet.reportDataSizeLimit : 500, currOutlet.reportBatchSize ? currOutlet.reportBatchSize : 31, currOutlet.uniqueId, reportOutletIdList, isMasterAccount);

      global.subscriberListenToUserOrderHistoricalChanges = subscriberSummary;

      return () => {
        typeof subscriberSummary === 'function' && subscriberSummary();
      };
    }
  }, [merchantId, historyStartDate, historyEndDate, currOutlet, reportOutletIdList, isMasterAccount]);

  ////////////////////////////////////////////////////////////////////

  useEffect(() => {
    if (currOutletId !== '') {
      if (
        role === ROLE_TYPE.ADMIN ||
        role === ROLE_TYPE.LEGACY ||
        role === ROLE_TYPE.STORE_MANAGER ||
        role === ROLE_TYPE.FRONTLINER
      ) {
        typeof global.subscriberListenToReservationChanges === 'function' && global.subscriberListenToReservationChanges();
        global.subscriberListenToReservationChanges = () => { };

        let subscriber = listenToCurrOutletIdReservationChanges(role, currOutletId, false, '', selectedCalendarData, historyStartDate, historyEndDate);

        global.subscriberListenToReservationChanges = subscriber;

        return () => {
          typeof subscriber === 'function' && subscriber();
        };
      }
    }
  }, [currOutletId, role, selectedCalendarData, historyStartDate, historyEndDate]);

  ////////////////////////////////////////////////////////////////////

  // useEffect(() => {
  //   // AsyncStorage.getItem('refreshed')

  //   // readFromStorage();

  //   // CommonStore.subscribe(
  //   //   (s) => s,
  //   //   async (commonStore) => {
  //   //     await AsyncStorage.setItem('@commonStore', JSON.stringify(commonStore));
  //   //   },
  //   // );

  //   // DataStore.subscribe(
  //   //   (s) => s,
  //   //   async (dataStore) => {
  //   //     await AsyncStorage.setItem('@dataStore', JSON.stringify(dataStore));
  //   //   },
  //   // );

  //   // UserStore.subscribe(
  //   //   (s) => s,
  //   //   async (userStore) => {
  //   //     await AsyncStorage.setItem('@userStore', JSON.stringify(userStore));
  //   //   },
  //   // );

  //   console.log('isMobile');
  //   console.log(isMobile());
  // }, []);

  // useEffect(() => {
  //   if (firebaseUid !== '') {
  //     listenToUserChanges(firebaseUid);

  //     // loadAsyncStorage();
  //   }
  // }, [firebaseUid]);

  // useEffect(() => {
  //   if (selectedOutlet !== null
  //     // && email
  //   ) {
  //     listenToSelectedOutletChanges(selectedOutlet, email);
  //   }
  // }, [selectedOutlet, email]);

  // useEffect(() => {
  //   if (selectedOutletTag !== null &&
  //     selectedOutletTag !== undefined &&
  //     selectedOutletTag.uniqueId) {
  //     listenToSelectedOutletTagChanges(selectedOutletTag);
  //   }
  // }, [selectedOutletTag]);

  // useEffect(() => {
  //   if (searchOutletText && searchOutletText.length > 0) {
  //     listenToSearchOutletTextChanges(searchOutletText);
  //   }
  // }, [searchOutletText]);

  // useEffect(() => {
  //   if (searchOutletMerchantId && searchOutletMerchantId.length > 0) {
  //     listenToSearchOutletMerchantIdChanges(searchOutletMerchantId);
  //   }
  // }, [searchOutletMerchantId]);

  // useEffect(() => {
  //   // combine merchant's beer docket with user's redeemed beer docket record, to merge user's action changes (redeemed mug, extended days, etc)

  //   var userBeerDocketsTemp = [];
  //   var selectedUserBeerDocketTemp = {};

  //   for (var i = 0; i < beerDockets.length; i++) {
  //     var record = {
  //       ...beerDockets[i],
  //       beerDocketId: beerDockets[i].uniqueId,
  //     };

  //     if (beerDocketsRedemptionsBDDict[record.beerDocketId]) {
  //       record = {
  //         ...record,
  //         ...beerDocketsRedemptionsBDDict[record.uniqueId], // extend the docket default data with user's own data
  //         userBeerDocketId: beerDocketsRedemptionsBDDict[record.uniqueId].uniqueId,
  //       };
  //     }

  //     userBeerDocketsTemp.push(record);

  //     if (record.beerDocketId === selectedUserBeerDocket.beerDocketId) {
  //       selectedUserBeerDocketTemp = record;
  //     }
  //   };

  //   console.log('changed userBeerDockets!');
  //   console.log(userBeerDocketsTemp);

  //   CommonStore.update(s => {
  //     s.userBeerDockets = userBeerDocketsTemp;

  //     if (selectedUserBeerDocket && selectedUserBeerDocket.beerDocketId) {
  //       s.selectedUserBeerDocket = selectedUserBeerDocketTemp;
  //     }
  //   });
  // }, [beerDockets, beerDocketsRedemptionsBDDict]);

  /////////////////////////////////////////////////////  

  useEffect(() => {
    if (cartItems && cartItems.length > 0) {
      setCartIcon(true);
    }
    else {
      setCartIcon(false);
    }
  }, [cartItems]);

  useEffect(() => {
    var promotionOutletId = '';

    if (nPromotionNotificationManual && nPromotionNotificationManual.type) {
      // if (nUserOrder.orderType === ORDER_TYPE.DINEIN) {
      //     navigation.navigate('Order');
      // }
      // else {
      //     navigation.navigate('Takeaway');
      // }

      promotionOutletId = nPromotionNotificationManual.outletId;
    }
    else if (nPromotionNotificationAuto && nPromotionNotificationAuto.type) {
      promotionOutletId = nPromotionNotificationAuto.outletId;
    }

    if (promotionOutletId) {
      redirectToPromotionOutlet(promotionOutletId);
    }
  }, [nPromotionNotificationManual, nPromotionNotificationAuto]);

  const redirectToPromotionOutlet = async (outletId) => {
    const outletSnapshot = await firebase.firestore()
      .collection(Collections.Outlet)
      .where('uniqueId', '==', outletId)
      .limit(1)
      .get();

    var outlet = null;
    if (!outletSnapshot.empty) {
      outlet = outletSnapshot.docs[0].data();
    }

    CommonStore.update(s => {
      s.selectedOutlet = outlet;
    }, () => {
      navigationObj.navigate('Outlet', { outletData: outlet });
    });
  };

  useEffect(() => {
    if (nUserOrderCourierAction && nUserOrderCourierAction.type) {
      redirectToOrderHistoryDetails(nUserOrderCourierAction.orderId);
    }
  }, [nUserOrderCourierAction]);

  const redirectToOrderHistoryDetails = async (orderId) => {
    const userOrderSnapshot = await firebase.firestore()
      .collection(Collections.UserOrder)
      .where('uniqueId', '==', orderId)
      .limit(1)
      .get();

    var userOrder = null;
    if (!userOrderSnapshot.empty) {
      userOrder = userOrderSnapshot.docs[0].data();
    }

    CommonStore.update(s => {
      s.selectedUserOrder = userOrder;
    });

    navigationObj.navigate("OrderHistoryDetail", { orderId: userOrder.uniqueId });
  };

  ////////////////////////////////////////////////////////

  useEffect(() => {
    // listenToCommonChangesMerchant();

    CommonStore.update(s => {
      s.gmvStartDate = moment().startOf('day').valueOf();
      s.gmvEndDate = moment().endOf("day").valueOf();
    });

    CommonStore.update(s => {
      // s.ptStartDate = moment().add(-2, 'day').startOf('day').valueOf();
      s.ptStartDate = moment().startOf('day').valueOf();
      s.ptEndDate = moment().endOf("day").valueOf();
    });
  }, []);

  const [openO, setOpenO] = useState(false);
  const iconWifiOpacity = useRef(new Animated.Value(0)).current;
  const networkNotStable = CommonStore.useState(s => s.networkNotStable);
  // const netInfo = useNetInfo();
  const allOutletsRaw = MerchantStore.useState((s) => s.allOutlets);
  const [allOutlets, setAllOutlets] = useState([]);
  const isLoading = CommonStore.useState((s) => s.isLoading);
  const currOutletShiftStatus = OutletStore.useState(
    (s) => s.currOutletShiftStatus,
  );
  const privileges_state = UserStore.useState((s) => s.privileges);
  const [privileges, setPrivileges] = useState([]);
  const pinNo = UserStore.useState(s => s.pinNo);

  useEffect(() => {
    setAllOutlets(allOutletsRaw.filter(outlet => {
      if (outlet.uniqueId === currOutletId || isMasterAccount) {
        return true;
      }
      else {
        return false;
      }
    }));
  }, [allOutletsRaw, currOutletId, isMasterAccount]);

  const startAnimation = () => {
    Animated.sequence([
      Animated.timing(iconWifiOpacity, {
        toValue: 1,
        duration: 1000, // Adjust the duration as needed
        useNativeDriver: true,
      }),
      Animated.timing(iconWifiOpacity, {
        toValue: 0,
        duration: 1000, // Adjust the duration as needed
        useNativeDriver: true,
      }),
    ]).start(() => {
      startAnimation();
    });
  };
  // Interpolate the node from 0 to 1 without clamping
  // const opacityIconWifi = mix(animationIconWifi, 0.1, 1);

  useEffect(() => {
    startAnimation();
  }, []);

  // useEffect(() => {
  //   if (netInfo.isInternetReachable === true || netInfo.isInternetReachable === null) {
  //     CommonStore.update(s => {
  //       s.networkNotStable = false;
  //     });
  //   }
  //   else {
  //     CommonStore.update(s => {
  //       s.networkNotStable = true;
  //     });
  //   }
  // }, [netInfo]);

  useEffect(async () => {
    // admin full access

    // const enteredPinNo = await AsyncStorage.getItem('enteredPinNo');
    //const enteredPinNo = storageMMKV.getString('enteredPinNo');

    //if (role === ROLE_TYPE.ADMIN && pinNo === enteredPinNo) {
    if (role === ROLE_TYPE.ADMIN) {
      setPrivileges([
        "EMPLOYEES",
        "OPERATION",
        "PRODUCT",
        "INVENTORY",
        "INVENTORY_COMPOSITE",
        "DOCKET",
        "VOUCHER",
        "PROMOTION",
        "CRM",
        "LOYALTY",
        "TRANSACTIONS",
        "REPORT",
        "RESERVATIONS",

        // for action
        'REFUND_ORDER',

        'SETTINGS',

        'QUEUE',

        'OPEN_CASH_DRAWER',

        'KDS',

        'UPSELLING',

        // for Kitchen

        'REJECT_ITEM',
        'CANCEL_ORDER',
        //'REFUND_tORDER',
      ]);
    } else {
      setPrivileges(privileges_state || []);
    }
  }, [role, privileges_state, pinNo]);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      //// console.log(moment().utc().tz(TIMEZONE.KUALA_LUMPUR).format());
      //// console.log('haloooo');
      //// console.log(moment(moment(Date.now)));
      // setTargetOutletDropdownList(allOutlets.map(outlet => ({ label: outlet.name, value: outlet.uniqueId })));

      // // console.log('targetOutletDropdownList');
      // // console.log(targetOutletDropdownList);

      // if (allOutlets.length > 0) {
      //     setSelectedTargetOutletId(currOutletId);
      // }

      // useEffect(() => {
      //     setMessages()
      // }, []);

      // const onSend = useCallback((messages = []) => {
      //     setMessages(previousMessages => GiftedChat.append(previousMessages, messages))
      // }, []);

      ////////////////////////////////////////////////////////

      var targetOutletDropdownListTemp = allOutlets.filter(outlet => {
        if (currOutletId === outlet.uniqueId) {
          return true;
        }

        if (isMasterAccount) {
          return true;
        }
      }).map((outlet) => ({
        label: sliceUnicodeStringV2WithDots(outlet.name, 20),
        value: outlet.uniqueId,
      }));

      // setExpandLineSelection(false);

      CommonStore.update((s) => {
        s.outletSelectDropdownView = () => {
          return (
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                // borderWidth:1,
              }}>
              {/* <TouchableOpacity
              style={{
                // backgroundColor: 'red',
                marginRight: 20,
              }}
              onPress={() => {
                if (currOutletShiftStatus === OUTLET_SHIFT_STATUS.OPENED) {
                  navigation.navigate('Table');

                  setTimeout(() => {
                    navigation.navigate('OutletMenu', {
                      params: {
                        outletData: currOutlet,
                        orderType: 0,
                        test: {},
                        navFrom: ORDER_TYPE.DINEIN,
                      },
                    });
                    // setSelectedTab(1);
                    // expandAction(1);
                    // setCurrPage('Table');
                    CommonStore.update((s) => {
                      // s.currPage = 'OutletMenu';
                      // s.currPageStack = [...currPageStack, 'OutletMenu'];

                      s.orderType = ORDER_TYPE.DINEIN;

                      // 2022-06-10 Fixes

                      // s.isCounterOrdering = true;

                      s.selectedOutletTable = {};
                    });
                  }, 100);
                } else {
                  // console.log('Close: ', currOutletShiftStatus);
                  // setShiftClosedModal(true);

                  // CommonStore.update(s => {
                  //   s.shiftClosedModal = true;
                  // });

                  Alert.alert('Info', 'Shift is currently closed \nPlease open shift to proceed');
                }
              }}>
              <MaterialCommunityIcons
                name="silverware-fork-knife"
                size={switchMerchant ? 18 : 22}
                style={{ color: Colors.whiteColor }}
              />
            </TouchableOpacity> */}

              {/*<TouchableOpacity
              style={{
                // backgroundColor: 'red',
                marginRight: 10,
              }}S
              onPress={() => {
                CommonStore.update((s) => {
                  s.chatbotModalVisibility = true;
                });
              }}>
              <MaterialIcons
                name="support-agent"
                size={switchMerchant ? 20 : 25}
                style={{ color: Colors.whiteColor }}
              />
            </TouchableOpacity>*/}

              {
                // networkNotStable
                false
                  ?
                  <Animated.View
                    style={{
                      // width: 200,
                      // height: 200,
                      // backgroundColor: 'red',
                      opacity: iconWifiOpacity,
                    }}
                  >
                    <TouchableOpacity
                      style={{
                        // backgroundColor: 'red',
                        marginRight: 10,
                      }}
                      onPress={async () => {
                        // await openCashDrawer();

                        window.confirm('Info. Network is not stable or unavailable right now, please check your internet router again.');
                      }}>
                      <Feather
                        name="wifi-off"
                        size={22}
                        style={{
                          // color: Colors.lightRed,
                          color: '#eb676a',
                        }}
                      />
                    </TouchableOpacity>
                  </Animated.View>
                  :
                  <></>
              }

              {
                (
                  (currOutlet && currOutlet.privileges &&
                    currOutlet.privileges.includes(PRIVILEGES_NAME.OPEN_CASH_DRAWER))
                  && privileges && privileges.includes(PRIVILEGES_NAME.OPEN_CASH_DRAWER))
                // ?
                // <TouchableOpacity
                //   style={{
                //     // backgroundColor: 'red',
                //     marginRight: 10,
                //   }}
                //   onPress={async () => {
                //     await openCashDrawer();
                //   }}>
                //   <CashRegister
                //     width={25}
                //     height={25}
                //     style={{ color: Colors.whiteColor }}
                //   />
                // </TouchableOpacity>
                // :
                // <></>
              }

              {/* {console.log('@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@')}
              {console.log(currOutletId)}
              {console.log(allOutlets)}
              {console.log('@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@')} */}

              {currOutletId.length > 0 &&
                allOutlets.find((item) => item.uniqueId === currOutletId) ? (
                <DropDownPicker
                  style={{
                    backgroundColor: Colors.fieldtBgColor,
                    width: 200,
                    height: 40,
                    borderRadius: 10,
                    borderWidth: 1,
                    borderColor: "#E5E5E5",
                    flexDirection: "row",
                  }}
                  dropDownContainerStyle={{
                    width: 200,
                    backgroundColor: Colors.fieldtBgColor,
                    borderColor: "#E5E5E5",
                  }}
                  labelStyle={{
                    marginLeft: 5,
                    flexDirection: "row",
                  }}
                  textStyle={{
                    fontSize: 14,
                    fontFamily: 'NunitoSans-Regular',

                    marginLeft: 5,
                    paddingVertical: 10,
                    flexDirection: "row",
                  }}
                  selectedItemContainerStyle={{
                    flexDirection: "row",
                  }}

                  showArrowIcon={true}
                  ArrowDownIconComponent={({ style }) => (
                    <Ionicon
                      size={25}
                      color={Colors.fieldtTxtColor}
                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                      name="chevron-down-outline"
                    />
                  )}
                  ArrowUpIconComponent={({ style }) => (
                    <Ionicon
                      size={25}
                      color={Colors.fieldtTxtColor}
                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                      name="chevron-up-outline"
                    />
                  )}

                  showTickIcon={true}
                  TickIconComponent={({ press }) => (
                    <Ionicon
                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                      color={
                        press ? Colors.fieldtBgColor : Colors.primaryColor
                      }
                      name={'md-checkbox'}
                      size={25}
                    />
                  )}
                  placeholderStyle={{
                    color: Colors.fieldtTxtColor,
                    // marginTop: 15,
                  }}
                  dropDownDirection="BOTTOM"
                  placeholder="Choose Outlet"
                  items={targetOutletDropdownListTemp}
                  value={currOutletId}
                  onSelectItem={(item) => {
                    if (item) { // if choose the same option again, value = ''
                      MerchantStore.update((s) => {
                        s.currOutletId = item.value;
                        s.currOutlet =
                          allOutlets.find(
                            (outlet) => outlet.uniqueId === item.value
                          ) || {};
                      });

                      if (currOutletId !== item.value) {
                        if (window.location.href.includes('sale-by-shift-report') ||
                          window.location.href.includes("sales-by-pay-in-n-out-shift-report")) {
                          setTimeout(() => {
                            window.location.reload();
                          }, 2000);
                        }
                        else {
                          // linkToFunc && linkToFunc('/dashboard');
                          // navigationObj && navigationObj.navigate("Dashboard - KooDoo BackOffice")

                          setTimeout(() => {
                            window.location.reload();
                          }, 2000);
                        }
                      }
                    }

                    CommonStore.update((s) => {
                      s.shiftClosedModal = false;
                    });
                  }}
                  open={openO}
                  setOpen={setOpenO}
                />
              ) : (
                <ActivityIndicator size={"small"} color={Colors.whiteColor} />
              )}
            </View>
          );
        };
      });
    });
  }, [allOutlets, currOutletId, isLoading, currOutletShiftStatus, currOutlet, privileges, isMasterAccount,
    networkNotStable, iconWifiOpacity, openO, navigationObj]);

  const gmvStartDate = CommonStore.useState(s => s.gmvStartDate);
  const gmvEndDate = CommonStore.useState(s => s.gmvEndDate);

  // useEffect(() => {
  //   if (gmvStartDate !== gmvEndDate) {
  //     // temp fix for strange bugs for same date

  //     listenToOrderChangesMerchant(gmvStartDate, gmvEndDate);
  //   }
  // }, [gmvStartDate, gmvEndDate]);

  const ptStartDate = CommonStore.useState(s => s.ptStartDate);
  const ptEndDate = CommonStore.useState(s => s.ptEndDate);

  // 2023-05-29 - No need first, can use from merchant-app porting listeners
  // useEffect(() => {
  //   if (ptStartDate !== ptEndDate) {
  //     // temp fix for strange bugs for same date

  //     // listenToPayoutTransactionsChangesMerchant(ptStartDate, ptEndDate);

  //     typeof global.subscriberListenToPayoutTransactionsChangesMerchant === 'function' && global.subscriberListenToPayoutTransactionsChangesMerchant();
  //     global.subscriberListenToPayoutTransactionsChangesMerchant = () => { };

  //     let subscriber = listenToPayoutTransactionsChangesMerchant(ptStartDate, ptEndDate);

  //     global.subscriberListenToPayoutTransactionsChangesMerchant = subscriber;

  //     return () => {
  //       typeof subscriber === 'function' && subscriber();
  //     };
  //   }
  // }, [ptStartDate, ptEndDate]);

  ////////////////////////////////////////////////////////

  const updateTokenFcm = async () => {
    const tokenFcm = await AsyncStorage.getItem('tokenFcm');

    if (tokenFcm) {
      const body = {
        tokenFcm: tokenFcm,
        userId: firebaseUid,
      };

      ApiClient.POST(API.updateTokenFcm, body).then((result) => {
        console.log('updated token fcm');
      });
    }
  };

  /////////////////////////////////////////////////////////

  // useEffect(() => {
  //   var availablePromotionsTemp = [];
  //   var availablePointsRedeemPackagesTemp = [];

  //   for (var i = 0; i < selectedOutletPromotions.length; i++) {
  //     var isValid = false;

  //     if (userGroups.includes(selectedOutletPromotions[i].targetUserGroup)) {
  //       isValid = true;
  //     }

  //     if (selectedOutletCRMTagsDict[selectedOutletPromotions[i].targetUserGroup]) {
  //       const currCrmUserTag = selectedOutletCRMTagsDict[selectedOutletPromotions[i].targetUserGroup];

  //       if (currCrmUserTag.emailList.includes(email)) {
  //         // means got

  //         isValid = true;
  //       }
  //     }

  //     if (isValid) {
  //       availablePromotionsTemp.push(selectedOutletPromotions[i]);
  //     }
  //   }

  //   for (var i = 0; i < selectedOutletPointsRedeemPackages.length; i++) {
  //     var isValid = false;

  //     if (userGroups.includes(selectedOutletPointsRedeemPackages[i].targetUserGroup)) {
  //       isValid = true;
  //     }

  //     if (selectedOutletCRMTagsDict[selectedOutletPointsRedeemPackages[i].targetUserGroup]) {
  //       const currCrmUserTag = selectedOutletCRMTagsDict[selectedOutletPointsRedeemPackages[i].targetUserGroup];

  //       if (currCrmUserTag.emailList.includes(email)) {
  //         // means got

  //         isValid = true;
  //       }
  //     }

  //     //////////////////////////////////

  //     if (selectedOutletCRMUser && selectedOutletCRMUser.pointsRedeemPackageDisableDict) {
  //       if (selectedOutletCRMUser.pointsRedeemPackageDisableDict[selectedOutletPointsRedeemPackages[i].uniqueId]) {
  //         isValid = false;
  //       }
  //     }

  //     //////////////////////////////////

  //     if (isValid) {
  //       availablePointsRedeemPackagesTemp.push(selectedOutletPointsRedeemPackages[i]);
  //     }
  //   }

  //   CommonStore.update(s => {
  //     s.availablePromotions = availablePromotionsTemp;
  //     s.availablePointsRedeemPackages = availablePointsRedeemPackagesTemp;
  //   });
  // }, [
  //   selectedOutletPromotions,
  //   selectedOutletPointsRedeemPackages,
  //   userGroups,
  //   email,

  //   selectedOutletCRMTagsDict,
  //   selectedOutletCRMUser,
  // ]);

  // useEffect(() => {
  //   var overrideItemPriceSkuDictTemp = {};
  //   var amountOffItemSkuDictTemp = {};
  //   var percentageOffItemSkuDictTemp = {};

  //   var overrideCategoryPriceNameDictTemp = {};
  //   var amountOffCategoryNameDictTemp = {};
  //   var percentageOffCategoryNameDictTemp = {};

  //   for (var i = 0; i < availablePromotions.length; i++) {
  //     if (availablePromotions[i].promotionType === PROMOTION_TYPE.OVERRIDE_EXISTING_PRICE) {
  //       for (var j = 0; j < availablePromotions[i].criteriaList.length; j++) {
  //         const criteria = availablePromotions[i].criteriaList[j];

  //         if (criteria.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
  //           for (var k = 0; k < criteria.variationItemsSku.length; k++) {
  //             overrideItemPriceSkuDictTemp[criteria.variationItemsSku[k]] = criteria.priceBeforeTax;
  //           }
  //         }
  //         else if (criteria.variation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
  //           for (var k = 0; k < criteria.variationItemsSku.length; k++) {
  //             overrideCategoryPriceNameDictTemp[criteria.variationItemsSku[k]] = criteria.priceBeforeTax;
  //           }
  //         }
  //       }
  //     }
  //     else if (availablePromotions[i].promotionType === PROMOTION_TYPE.TAKE_AMOUNT_OFF) {
  //       for (var j = 0; j < availablePromotions[i].criteriaList.length; j++) {
  //         const criteria = availablePromotions[i].criteriaList[j];

  //         if (criteria.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
  //           for (var k = 0; k < criteria.variationItemsSku.length; k++) {
  //             amountOffItemSkuDictTemp[criteria.variationItemsSku[k]] = {
  //               amountOff: criteria.amountOff,
  //               maxQuantity: criteria.maxQuantity,
  //               minQuantity: criteria.minQuantity,

  //               quantityMin: criteria.quantityMin,
  //               quantityMax: criteria.quantityMax,
  //               priceMin: criteria.priceMin,
  //               priceMax: criteria.priceMax,
  //             };
  //           }
  //         }
  //         else if (criteria.variation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
  //           for (var k = 0; k < criteria.variationItemsSku.length; k++) {
  //             amountOffCategoryNameDictTemp[criteria.variationItemsSku[k]] = {
  //               amountOff: criteria.amountOff,
  //               maxQuantity: criteria.maxQuantity,
  //               minQuantity: criteria.minQuantity,

  //               quantityMin: criteria.quantityMin,
  //               quantityMax: criteria.quantityMax,
  //               priceMin: criteria.priceMin,
  //               priceMax: criteria.priceMax,
  //             };
  //           }
  //         }
  //       }
  //     }
  //     else if (availablePromotions[i].promotionType === PROMOTION_TYPE.TAKE_PERCENTAGE_OFF) {
  //       for (var j = 0; j < availablePromotions[i].criteriaList.length; j++) {
  //         const criteria = availablePromotions[i].criteriaList[j];

  //         if (criteria.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
  //           for (var k = 0; k < criteria.variationItemsSku.length; k++) {
  //             percentageOffItemSkuDictTemp[criteria.variationItemsSku[k]] = {
  //               percentageOff: criteria.percentageOff,
  //               maxQuantity: criteria.maxQuantity,
  //               minQuantity: criteria.minQuantity,

  //               quantityMin: criteria.quantityMin,
  //               quantityMax: criteria.quantityMax,
  //               priceMin: criteria.priceMin,
  //               priceMax: criteria.priceMax,
  //             };
  //           }
  //         }
  //         else if (criteria.variation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
  //           for (var k = 0; k < criteria.variationItemsSku.length; k++) {
  //             percentageOffCategoryNameDictTemp[criteria.variationItemsSku[k]] = {
  //               percentageOff: criteria.percentageOff,
  //               maxQuantity: criteria.maxQuantity,
  //               minQuantity: criteria.minQuantity,

  //               quantityMin: criteria.quantityMin,
  //               quantityMax: criteria.quantityMax,
  //               priceMin: criteria.priceMin,
  //               priceMax: criteria.priceMax,
  //             };
  //           }
  //         }
  //       }
  //     }
  //   }

  //   CommonStore.update(s => {
  //     s.overrideItemPriceSkuDict = overrideItemPriceSkuDictTemp;
  //     s.amountOffItemSkuDict = amountOffItemSkuDictTemp;
  //     s.percentageOffItemSkuDict = percentageOffItemSkuDictTemp;

  //     s.overrideCategoryPriceNameDict = overrideCategoryPriceNameDictTemp;
  //     s.amountOffCategoryNameDict = amountOffCategoryNameDictTemp;
  //     s.percentageOffCategoryNameDict = percentageOffCategoryNameDictTemp;
  //   });
  // }, [availablePromotions]);

  // useEffect(() => {
  //   var pointsRedeemItemSkuDict = {};
  //   var pointsRedeemCategoryNameDict = {};

  //   for (var i = 0; i < availablePointsRedeemPackages.length; i++) {
  //     const pointsRedeemPackage = availablePointsRedeemPackages[i];

  //     if (pointsRedeemPackage.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
  //       for (var k = 0; k < pointsRedeemPackage.variationItemsSku.length; k++) {
  //         pointsRedeemItemSkuDict[pointsRedeemPackage.variationItemsSku[k]] = {
  //           packageId: pointsRedeemPackage.uniqueId,
  //           limitRedemptionPerUser: pointsRedeemPackage.limitRedemptionPerUser,
  //           conversionCurrency: pointsRedeemPackage.conversionCurrency,
  //           conversionCurrencyTo: pointsRedeemPackage.conversionCurrencyTo,
  //           conversionPointsFrom: pointsRedeemPackage.conversionPointsFrom,
  //         };
  //       }
  //     }
  //     else if (pointsRedeemPackage.variation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
  //       for (var k = 0; k < pointsRedeemPackage.variationItemsSku.length; k++) {
  //         pointsRedeemCategoryNameDict[pointsRedeemPackage.variationItemsSku[k]] = {
  //           packageId: pointsRedeemPackage.uniqueId,
  //           limitRedemptionPerUser: pointsRedeemPackage.limitRedemptionPerUser,
  //           conversionCurrency: pointsRedeemPackage.conversionCurrency,
  //           conversionCurrencyTo: pointsRedeemPackage.conversionCurrencyTo,
  //           conversionPointsFrom: pointsRedeemPackage.conversionPointsFrom,
  //         };
  //       }
  //     }
  //   }

  //   CommonStore.update(s => {
  //     s.pointsRedeemItemSkuDict = pointsRedeemItemSkuDict;
  //     s.pointsRedeemCategoryNameDict = pointsRedeemCategoryNameDict;
  //   });
  // }, [availablePointsRedeemPackages]);

  /////////////////////////////////////////////////////////

  const onCartClicked = () => {
    if (cartItems.length > 0) {
      navigationObj && navigationObj.navigate("Cart", { test: null, outletData: selectedOutlet });
    } else {
      alert("Info: No item in your cart at the moment")
    }
  };

  /////////////////////////////////////////////////////////

  // Block access of mobile devices
  if (isMobile()) {
    return (
      <View style={{
        width: windowWidth,
        height: windowHeight,
        alignSelf: 'center',
        justifyContent: 'center',
        padding: 20
      }}>
        <Text style={{
          fontFamily: 'NunitoSans-Regular',
          fontSize: 18,
          textAlign: 'center'
        }}>
          Please use a desktop computer to access this application. Mobile devices are not supported.
        </Text>
      </View>
    );
  }
  
  return (
    <View style={{
      width: windowWidth,
      height: windowHeight,
      alignSelf: 'center',
      justifyContent: 'center'
    }}>
      <style type="text/css">{`
        @font-face {
          font-family: 'FontAwesome';
          src: url(${FontAwesomeTTF}) format('truetype');
        }

        @font-face {
          font-family: 'SimpleLineIcons';
          src: url(${SimpleLineIconsTTF}) format('truetype');
        }

        @font-face {
          font-family: 'Entypo';
          src: url(${EntypoTTF}) format('truetype');
        }

        @font-face {
          font-family: 'Ionicons';
          src: url(${IoniconsTTF}) format('truetype');
        }

        @font-face {
          font-family: 'Feather';
          src: url(${FeatherTTF}) format('truetype');
        }

        @font-face {
          font-family: 'AntDesign';
          src: url(${AntDesignTTF}) format('truetype');
        }

        @font-face {
          font-family: 'MaterialIcons';
          src: url(${MaterialIconsTTF}) format('truetype');
        }

        @font-face {
          font-family: 'MaterialCommunityIcons';
          src: url(${MaterialCommunityIconsTTF}) format('truetype');
        }

        @font-face {
          font-family: 'Fontisto';
          src: url(${FontistoTTF}) format('truetype');
        }

        @font-face {
          font-family: 'NunitoSans-Bold';
          src: url(${NunitoSansBoldTTF}) format('truetype');
        }

        @font-face {
          font-family: 'NunitoSans-SemiBold';
          src: url(${NunitoSansSemiBoldTTF}) format('truetype');
        }

        @font-face {
          font-family: 'NunitoSans-Regular';
          src: url(${NunitoSansRegularTTF}) format('truetype');
        }
      `}</style>

      {reportingApiLoading && (
        <View
          style={{
            position: 'absolute',
            top: 100,
            right: 20,
            width: isMobile() ? '30%' : '15%',
            background: 'white',
            borderColor: Colors.primaryColor,
            padding: 10,
            borderRadius: 8,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 9999,
            // Add shadow properties
            shadowColor: '#000',
            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowOpacity: 0.25,
            shadowRadius: 3.84,
            elevation: 5, // for Android

            ...isMobile() && {
              flexDirection: 'column',
            },
          }}
        >
          <ActivityIndicator color={Colors.primaryColor} size="small" style={{
            ...isMobile() && {
              marginBottom: 8,
            },
            ...!isMobile() && {
              marginRight: 8,
            },

          }} />
          <Text
            style={{
              color: Colors.primaryColor,
              fontSize: 14,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}
          >
            Getting report data...
          </Text>
        </View>
      )
      }

      <AppNavigator />

    </View>
  );
};

const styles = StyleSheet.create({
  cartCount: {
    position: 'absolute',
    top: -12,
    right: -10,
    backgroundColor: Colors.primaryColor,
    width: 30,
    height: 30,
    borderRadius: 30 / 2,
    alignContent: 'center',
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default App;
