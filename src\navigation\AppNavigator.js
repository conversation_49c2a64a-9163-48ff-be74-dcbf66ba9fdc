import React, { Component, useEffect, useState, useRef, Suspense } from 'react';
import {
  View,
  Text,
  // useWindowDimensions,
  StyleSheet,
  Dimensions,
  useWindowDimensions,
  ActivityIndicator,
} from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator, useHeaderHeight } from '@react-navigation/stack';
// import NewOrderScreen from '../screen/AuthScreen';
// import NewOrderGenericScreen from '../screen/NewOrderGenericScreen';
// import TakeawayScreen from '../screen/TakeawayScreen.js.bak';
// import ScanScreen from '../screen/ErrorScreen';
import { prefix } from '../constant/env';
import Colors from '../constant/Colors';
// import HomeScreen from '../screen/HomeScreen';
// import Guests from '../screen/Guests';
// import CreateGuests from '../screen/CreateGuests';

// import LoginScreen from '../screen/LoginScreen';
// import DashboardScreen from '../screen/DashboardScreen';
// import AllTransaction from '../screen/AllTransaction';
// import ProductScreen from '../screen/ProductScreen';
// import ProductCategoryScreen from '../screen/ProductCategoryScreen';
// import ProductAddScreen from '../screen/ProductAddScreen';
// import ProductMenuScreen from '../screen/ProductMenuScreen';
// import EmployeeScreen from '../screen/EmployeeScreen';
// import CrmScreen from '../screen/CrmScreen';
// import SegmentScreen from '../screen/SegmentScreen';
// import ReportSalesOvertime from '../screen/ReportSalesOvertime';
// import ReportSalesAov from '../screen/ReportSalesAov';
// import ReportSalesProduct from '../screen/ReportSalesProduct';
// import ReportSalesCategory from '../screen/ReportSalesCategory';
// import ReportCategoryProduct from '../screen/ReportCategoryProduct';
// import ReportSalesVariant from '../screen/ReportSalesVariant';
// import ReportSalesAddOns from '../screen/ReportSalesAddOns';
// import ReportSalesTransaction from '../screen/ReportSalesTransaction';
// import ReportSalesPaymentMethod from '../screen/ReportSalesPaymentMethod';
// import ReportSaleByShift from '../screen/ReportSaleByShift';
// import ReportSalesRepeat from '../screen/ReportSalesRepeat';
// import ReportShiftPayInOut from '../screen/ReportShiftPayInOut';
// import ReportPayout from '../screen/ReportPayout';
// import PreorderPackageScreen from '../screen/PreorderPackageScreen';
// import NewCustomer from '../screen/NewCustomer';
// import SettingsReservation from '../screen/SettingsReservationScreen';
// import SettingShiftScreen from '../screen/SettingShiftScreen';
// import SettingReceiptScreen from '../screen/SettingReceiptScreen';
// import SettingOrderScreen from '../screen/SettingOrderScreen';
// import SettingPaymentScreen from '../screen/SettingPaymentScreen';
// import EInvoiceScreen from '../screen/EInvoiceScreen';
// import SettingsWhatsappScreen from '../screen/SettingsWhatsappScreen';
// import NewLoyaltyCampaignScreen from '../screen/NewLoyaltyCampaignScreen';
// import SettingCredit from '../screen/SettingCredit';
// import LoyaltyStampScreen from '../screen/LoyaltyStampScreen';
// import VoucherReport from '../screen/VoucherReport';
// import SettingsScreen from '../screen/SettingsScreen';
// import VoucherScreen from '../screen/VoucherScreen';
// import TaggableVoucherListScreen from '../screen/TaggableVoucherListScreen';
// import TaggableVoucherReportScreen from '../screen/TaggableVoucherReport';
// import NewTaggableVoucherScreen from '../screen/NewTaggableVoucherScreen';
// import PromotionListScreen from '../screen/PromotionListScreen';
// import PromotionReport from '../screen/PromotionReport';
// import LoyaltyPointsRate from '../screen/LoyaltyPointsRate';
// import SettingSetCredit from '../screen/SettingSetCredit';
// import RedemptionRedeemedScreen from '../screen/RedemptionRedeemedScreen';
// import RedemptionExpiredScreen from '../screen/RedemptionExpiredScreen';
// import RedemptionScreen from '../screen/RedemptionScreen';
// import PurchaseOrderScreen from '../screen/PurchaseOrderScreen';
// import SettingLoyaltyScreen from '../screen/SettingLoyaltyScreen';
// import SettingRedemptionScreen from '../screen/SettingRedemptionScreen';
// import StockTakeScreen from '../screen/StockTakeScreen';
// import StockTransferScreen from '../screen/StockTransferScreen';
// import SupplierScreen from '../screen/SupplierScreen';
// import InventoryScreen from '../screen/InventoryScreen';
// import NewCampaignScreen from '../screen/NewCampaignScreen'
// import SettingPrinterScreen from '../screen/SettingPrinterScreen'
// import LoyaltyReport from '../screen/LoyaltyReport';
// import LoyaltySettingScreen from '../screen/LoyaltySettingScreen';
// import ReportActivityLog from '../screen/ReportActivityLog';
// import ReportSalesRefund from '../screen/ReportSalesRefund';
// import EmployeeTimeSheet from '../screen/EmployeeTimeSheet';
// import TopUpCreditReport from '../screen/TopUpCreditReport';
// import LoyaltySignUpCampaignScreen from '../screen/LoyaltySignUpCampaignScreen';
// import LoyaltyPayEarn from '../screen/LoyaltyPayEarn';
// import LoyaltyRewardRedemption from '../screen/LoyaltyRewardRedemption';
// import TopupCreditTypeScreen from '../screen/TopupCreditTypeScreen';
// import NewTopupCreditTypeScreen from '../screen/NewTopupCreditTypeScreen';
// import InventoryProductScreen from '../screen/InventoryProductScreen';
// import StockTakeProductScreen from '../screen/StockTakeProductScreen';
// import StockTransferProductScreen from '../screen/StockTransferProductScreen';
// import SupplierProductScreen from '../screen/SupplierProductScreen';
// import PurchaseOrderProductScreen from '../screen/PurchaseOrderProductScreen';
// import SettingMerchantPaymentScreen from '../screen/SettingMerchantPaymentScreen';
// import StockReturnProductScreen from '../screen/StockReturnProductScreen';
// import SettingCreditSMSScreen from '../screen/SettingCreditSMSScreen';
// import SettingCreditWhatsappScreen from '../screen/SettingCreditWhatsappScreen';
// import VariantAddOnScreen from '../screen/VariantAddOnScreen';
// import TableScreen from '../screen/TableScreen';
// import OrderScreen from '../screen/OrderScreen';
// import TakeawayScreen from '../screen/TakeawayScreen';
// import OtherDeliveryScreen from '../screen/OtherDeliveryScreen';
// import HistoryScreen from '../screen/HistoryScreen';
// import UpsellingCampaignScreen from '../screen/UpsellingCampaignScreen';
// import UpsellingListScreen from '../screen/UpsellingListScreen';
// import ReservationAnalyticScreen from '../screen/ReservationAnalyticScreen';
// import SettingDepositScreen from '../screen/SettingDepositScreen';
// import ReportSalesOrderCount from '../screen/ReportSalesOrderCount';
// import KitchenScreen from '../screen/KitchenScreen';
// import DetailsScreen from '../screen/DetailsScreen';
// import CalendarScreen from '../screen/CalendarScreen';
// import ReportSalesRevisitCount from '../screen/ReportSalesRevisitCount';
// import ReportSalesUpselling from '../screen/ReportSalesUpselling';
// import ReportSalesUpsellingRevenue from '../screen/ReportSalesUpsellingRevenue';
// import MoCartScreen from '../screen/MoCartScreen';
// import MenuOrderingScreen from '../screen/MenuOrderingScreen';
// import MoTableScreen from '../screen/MoTableScreen';
// import MoOutletMenuScreen from '../screen/MoOutletMenuScreen';
// import MoMenuItemDetailsScreen from '../screen/MoMenuItemDetailsScreen';
// import WorkOrderItemScreen from '../screen/WorkOrderItemList';
// import WorkOrderScreen from '../screen/WorkOrderList';
// import CompositeReportScreen from '../screen/CompositeReport';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

const config = {
  screens: {
    HomeStack: {
      path: `${prefix}`,
      initialRouteName: 'Login - KooDoo BackOffice',
      screens: {
        // 'KooDoo BackOffice': {
        //   path: '/',
        // },
        'Login - KooDoo BackOffice': {
          path: '/login',
        },
        'Error - KooDoo BackOffice': {
          path: '/error',
        },
        'Dashboard - KooDoo BackOffice': {
          path: '/dashboard',
        },
        'All Transaction - KooDoo BackOffice': {
          path: '/all-transaction',
        },
        'Ordering - KooDoo BackOffice': {
          path: '/ordering',
        },
        'MoTable - KooDoo BackOffice': {
          path: '/motable',
        },
        'Product - KooDoo BackOffice': {
          path: '/product',
        },
        'Product Add - KooDoo BackOffice': {
          path: '/product-add',
        },
        'Product Category - KooDoo BackOffice': {
          path: '/product-category',
        },
        'Product Menu - KooDoo BackOffice': {
          path: '/product-menu',
        },
        'Employee List - KooDoo BackOffice': {
          path: '/employee-list',
        },
        'Promotion List - KooDoo BackOffice': {
          path: '/promotion-list'
        },
        'Promotion Report - KooDoo BackOffice': {
          path: '/promotion-report'
        },
        'Customer List - KooDoo BackOffice': {
          path: '/customers-list',
        },
        'Segment List - KooDoo BackOffice': {
          path: '/segment-list',
        },
        'Overview Report - KooDoo BackOffice': {
          path: '/overview-report'
        },
        'AOV Report - KooDoo BackOffice': {
          path: '/aov-report'
        },
        'Product Report - KooDoo BackOffice': {
          path: '/sales-by-product-report'
        },
        'Category Report - KooDoo BackOffice': {
          path: '/sales-by-category-report'
        },
        'Product Category Report - KooDoo BackOffice': {
          path: '/sales-by-product-category-report'
        },
        'Variant Report - KooDoo BackOffice': {
          path: '/sales-by-variant-report'
        },
        'Add Ons Report - KooDoo BackOffice': {
          path: '/sales-by-addons-report'
        },
        'Transaction Report - KooDoo BackOffice': {
          path: '/sales-by-transaction-report'
        },
        'Payment Method Report - KooDoo BackOffice': {
          path: '/sales-by-payment-method-report'
        },
        'Shift Report - KooDoo BackOffice': {
          path: '/sale-by-shift-report'
        },
        'ReportSalesRepeat': {
          path: '/report-sales-repeat'
        },
        'Pay In & Out Shift Report - KooDoo BackOffice': {
          path: '/sales-by-pay-in-n-out-shift-report'
        },
        'PayOut Report - KooDoo BackOffice': {
          path: '/payout-report'
        },
        'Preorder Package - KooDoo BackOffice': {
          path: '/preorder-package'
        },
        'Customer - KooDoo BackOffice': {
          path: '/customer'
        },
        'Settings Reservation - KooDoo BackOffice': {
          path: '/setting-reservation'
        },
        'Payment Setting - KooDoo BackOffice': {
          path: '/payment-settings'
        },
        'Order Settings - KooDoo BackOffice': {
          path: '/order-settings'
        },
        //old unused voucher page comment by 1/11
        // 'VoucherReport - KooDoo BackOffice': {
        //   path: '/voucherreport'
        // },
        // 'VoucherScreen - KooDoo BackOffice': {
        //   path: '/voucherscreen'
        // },
        'Voucher List - KooDoo BackOffice': {
          path: '/voucher-list'
        },
        'Voucher - KooDoo BackOffice': {
          path: '/voucher'
        },
        'Voucher Report - KooDoo BackOffice': {
          path: '/voucher-report'
        },
        'Receipt Settings - KooDoo BackOffice': {
          path: '/receipt-settings'
        },
        'Loyalty Campaign - KooDoo BackOffice': {
          path: '/loyalty-campaign'
        },
        'Loyalty Points - KooDoo BackOffice': {
          path: '/loyalty-point-list'
        },
        'Loyalty Stamps - KooDoo BackOffice': {
          path: '/loyalty-stamps-list'
        },
        // 'Guests - KooDoo BackOffice': {
        //   path: '/guests'
        // },
        'Shift Settings - KooDoo BackOffice': {
          path: '/shift-settings'
        },
        'General Settings - KooDoo BackOffice': {
          path: '/general-settings'
        },
        'Redemption Package - KooDoo BackOffice': {
          path: '/redemption-package'
        },
        'Rate Settings - KooDoo BackOffice': {
          path: '/rate-settings'
        },
        'Redeemed Docket - KooDoo BackOffice': {
          path: '/redeemed-docket'
        },
        'Expired Docket - KooDoo BackOffice': {
          path: '/expired-docket'
        },
        'Active Docket - KooDoo BackOffice': {
          path: '/active-docket'
        },
        'Promotion - KooDoo BackOffice': {
          path: '/promotion'
        },
        'PurchaseOrder - KooDoo BackOffice': {
          path: '/purchaseorder'
        },
        'Setting Loyalty - KooDoo BackOffice': {
          path: '/setting-loyalty'
        },
        'Docket List - KooDoo BackOffice': {
          path: '/docket-list'
        },
        'StockTake - KooDoo BackOffice': {
          path: '/stocktake'
        },
        'Stock Transfer - KooDoo BackOffice': {
          path: '/stock-transfer'
        },
        'Supplier - KooDoo BackOffice': {
          path: 'supplier'
        },
        'Inventory - KooDoo BackOffice': {
          path: 'inventory'
        },
        'Printer Settings - KooDoo BackOffice': {
          path: 'printer-settings'
        },
        'Loyalty Report - KooDoo BackOffice': {
          path: 'loyalty-report'
        },
        'Loyalty Setting - KooDoo BackOffice': {
          path: 'loyalty-setting'
        },
        'Activity-Log - KooDoo BackOffice': {
          path: 'activity-log'
        },
        'Refund Report - KooDoo BackOffice': {
          path: 'sales-by-refund-report'
        },
        'Employee Timesheet - KooDoo BackOffice': {
          path: 'employee-timesheet'
        },
        'Pay & Earn - KooDoo BackOffice': {
          path: 'loyalty-pay-n-earn'
        },
        'Credit Type Report - KooDoo BackOffice': {
          path: 'credit-type-report'
        },
        'Sign Up Reward - KooDoo BackOffice': {
          path: 'sign-up-reward'
        },
        'Reward & Redemption - KooDoo BackOffice': {
          path: 'loyalty-reward-n-redemption'
        },
        'Credit Type List - KooDoo BackOffice': {
          path: 'credit-type-list'
        },
        'Credit Type - KooDoo BackOffice': {
          path: 'credit-type'
        },
        'StockTake Product - KooDoo BackOffice': {
          path: '/stocktake-product'
        },
        'Stock Transfer Product - KooDoo BackOffice': {
          path: '/stock-transfer-product'
        },
        'Supplier Product - KooDoo BackOffice': {
          path: 'supplier-product'
        },
        'Inventory Product - KooDoo BackOffice': {
          path: 'inventory-product'
        },
        'PurchaseOrder Product - KooDoo BackOffice': {
          path: '/purchaseorder-product'
        },
        'Stock Return Product - KooDoo BackOffice': {
          path: '/stock-return-product'
        },

        'Merchant Payment - KooDoo BackOffice': {
          path: '/mp-settings'
        },

        'SMS Credit - KooDoo BackOffice': {
          path: '/sms-credit-settings'
        },

        'Whatsapp Credit - KooDoo BackOffice': {
          path: '/whatsapp-credit-settings'
        },


        "Payment Success - KooDoo BackOffice": {
          path: "payment/:subdomain?/:amount?/:appCode?/:channel?/:checksum?/:mpSecuredVerfified?/:msgType?/:orderId?/:pInstruction?/:statusCode?/:txnId?",
        },

        'Table - KooDoo BackOffice': {
          path: '/table',
        },
        'Kitchen - KooDoo BackOffice': {
          path: '/kitchen',
        },
        'Order - KooDoo BackOffice': {
          path: '/order'
        },
        'Takeaway - KooDoo BackOffice': {
          path: '/takeaway'
        },
        'Other Delivery - KooDoo BackOffice': {
          path: '/other-delivery'
        },
        'History - KooDoo BackOffice': {
          path: '/history'
        },
        'Manage Reservation - KooDoo BackOffice': {
          path: '/manage-reservations'
        },
        'Calendar - KooDoo BackOffice': {
          path: '/calendar'
        },

        'Upselling List - KooDoo BackOffice': {
          path: '/upselling-list'
        },
        'Upselling Campaign - KooDoo BackOffice': {
          path: '/upselling-campaign'
        },
        'Reservation Analytic - KooDoo BackOffice': {
          path: '/reservation-analytic'
        },
        'Revisit Count Report - KooDoo BackOffice': {
          path: '/revisit-count-report'
        },
        'Order Count Report - KooDoo BackOffice': {
          path: '/order-count-report'
        },
        'Upselling Report - KooDoo BackOffice': {
          path: '/upselling-report'
        },
        'Upselling Revenue Report - KooDoo BackOffice': {
          path: '/upselling-revenue-report'
        },

        'Reservation Setting Deposit - KooDoo BackOffice': {
          path: '/reservation-setting-deposit'
        },
        'E Invoice - KooDoo BackOffice': {
          path: '/e-invoice'
        },
        'WhatsApp Settings - KooDoo BackOffice': {
          path: '/whatsapp-settings'
        },

        'Variant Add-on - KooDoo BackOffice': {
          path: '/variant-addon'
        },
        'Work Order - KooDoo BackOffice': {
          path: '/work-order'
        },
        'Work Order Item - KooDoo BackOffice': {
          path: '/work-order-item'
        },
        'Composite Report - KooDoo BackOffice': {
          path: '/composite-report'
        }
      },
    },

    // screens: {
    //   MainScreen: {
    //     path: 'qr/:outletId/:tableId/:tableCode/:waiterId?',
    //   },
    // },
    // NewOrderStack: {
    //   path: "/web/:outletId/:tableId/:tableCode/:tablePax/:waiterId",
    //   initialRouteName: "neworder",
    //   screens: {
    //     Login: {
    //       path: 'new-order',
    //     },
    //   },
    // },
    // NewOrderStack: {
    //   path: `${prefix}`,
    //   // initialRouteName: "NewOrder",
    //   screens: {
    //     NewOrder: {
    //       path: 'new-order/:outletId?/:tableId?/:tableCode?/:tablePax?/:waiterId?',
    //     },
    //     NewOrderGeneric: {
    //       path: 'new-order-generic/:outletId?/:tableId?/:tableCode?',
    //     },
    //     Takeaway: {
    //       path: 'takeaway',
    //     },
    //     Scan: {
    //       path: 'scan',
    //     },
    //     Error: {
    //       path: 'error',
    //     },
    //     PaymentSuccess: {
    //       path: 'payment/:amount?/:appCode?/:channel?/:checksum?/:mpSecuredVerfified?/:msgType?/:orderId?/:pInstruction?/:statusCode?/:txnId?',
    //     },
    //   },
    // },
    // OrderStack: {
    //   path: `${prefix}/outlet`,
    //   initialRouteName: "OutletMenu",
    //   screens: {
    //     OutletMenu: {
    //       path: 'menu',
    //     },
    //     MenuItemDetailsScreen: {
    //       path: 'menu/item',
    //     },
    //     CartScreen: {
    //       path: 'cart',
    //     },
    //   },
    // },
  },
};

const linking = {
  prefixes: [
    /* your linking prefixes */
  ],
  config: config,
};

// const NewOrderStack = ({ navigation }) => {
//   navigation.setOptions({ tabBarVisible: false });

//   return (
//     <Stack.Navigator
//       screenOptions={{
//         headerShown: false,
//       }}
//     >
//       <Stack.Screen
//         name='NewOrder'
//         component={NewOrderScreen}
//       />

//       <Stack.Screen
//         name='NewOrderGeneric'
//         component={NewOrderGenericScreen}
//       />

//       <Stack.Screen
//         name='Takeaway'
//         component={TakeawayScreen}
//       />

//       <Stack.Screen
//         name='PaymentSuccess'
//         component={PaymentSuccessScreen}
//       />

//       <Stack.Screen
//         name='Scan'
//         component={ScanScreen}
//       />

//       <Stack.Screen
//         name='Error'
//         component={ErrorScreen}
//       />
//     </Stack.Navigator>
//   );
// }

// const OrderStack = ({ navigation }) => {
//   navigation.setOptions({ tabBarVisible: false });

//   return (
//     <Stack.Navigator
//       screenOptions={{
//         // headerShown: false,
//       }}
//     >
//       <Stack.Screen
//         name='OutletMenu'
//         component={OutletMenuScreen}
//       />
//       <Stack.Screen
//         name='MenuItemDetailsScreen'
//         component={MenuItemDetailsScreen}
//       />
//       <Stack.Screen
//         name='CartScreen'
//         component={CartScreen}
//       />
//     </Stack.Navigator>
//   );
// }

// Loading component for Suspense fallback
const LoadingScreen = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color={Colors.darkBgColor} />
  </View>
);

// Dynamic imports
const ErrorScreen = React.lazy(() => import('../screen/ErrorScreen.takeaway'));
const PaymentSuccessScreen = React.lazy(() => import('../screen/PaymentSuccessScreen'));
const LoginScreen = React.lazy(() => import('../screen/LoginScreen'));
const DashboardScreen = React.lazy(() => import('../screen/DashboardScreen'));
const AllTransaction = React.lazy(() => import('../screen/AllTransaction'));
const ProductScreen = React.lazy(() => import('../screen/ProductScreen'));
const ProductCategoryScreen = React.lazy(() => import('../screen/ProductCategoryScreen'));
const ProductAddScreen = React.lazy(() => import('../screen/ProductAddScreen'));
const ProductMenuScreen = React.lazy(() => import('../screen/ProductMenuScreen'));
const EmployeeScreen = React.lazy(() => import('../screen/EmployeeScreen'));
const CrmScreen = React.lazy(() => import('../screen/CrmScreen'));
const SegmentScreen = React.lazy(() => import('../screen/SegmentScreen'));
const ReportSalesOvertime = React.lazy(() => import('../screen/ReportSalesOvertime'));
const ReportSalesAov = React.lazy(() => import('../screen/ReportSalesAov')); //hide first 2025-05-23
const ReportSalesProduct = React.lazy(() => import('../screen/ReportSalesProductV2'));
const ReportSalesCategory = React.lazy(() => import('../screen/ReportSalesCategory'));
const ReportCategoryProduct = React.lazy(() => import('../screen/ReportCategoryProduct'));
const ReportSalesVariant = React.lazy(() => import('../screen/ReportSalesVariant')); //hide first 2025-05-23
const ReportSalesAddOns = React.lazy(() => import('../screen/ReportSalesAddOns')); //hide first 2025-05-23
// const ReportSalesTransaction = React.lazy(() => import('../screen/ReportSalesTransaction')); //hide first 2025-05-23
// const ReportSalesPaymentMethod = React.lazy(() => import('../screen/ReportSalesPaymentMethod')); //hide first 2025-05-23
const ReportSaleByShift = React.lazy(() => import('../screen/ReportSaleByShift'));
const ReportSalesRepeat = React.lazy(() => import('../screen/ReportSalesRepeat'));
const ReportShiftPayInOut = React.lazy(() => import('../screen/ReportShiftPayInOut'));
const ReportPayout = React.lazy(() => import('../screen/ReportPayout'));
const PreorderPackageScreen = React.lazy(() => import('../screen/PreorderPackageScreen'));
const NewCustomer = React.lazy(() => import('../screen/NewCustomer'));
const SettingsReservation = React.lazy(() => import('../screen/SettingsReservationScreen'));
const SettingShiftScreen = React.lazy(() => import('../screen/SettingShiftScreen'));
const SettingReceiptScreen = React.lazy(() => import('../screen/SettingReceiptScreen'));
const SettingOrderScreen = React.lazy(() => import('../screen/SettingOrderScreen'));
const SettingPaymentScreen = React.lazy(() => import('../screen/SettingPaymentScreen'));
const EInvoiceScreen = React.lazy(() => import('../screen/EInvoiceScreen'));
const SettingsWhatsappScreen = React.lazy(() => import('../screen/SettingsWhatsappScreen'));
const NewLoyaltyCampaignScreen = React.lazy(() => import('../screen/NewLoyaltyCampaignScreen'));
const SettingCredit = React.lazy(() => import('../screen/SettingCredit'));
const LoyaltyStampScreen = React.lazy(() => import('../screen/LoyaltyStampScreen'));
const VoucherReport = React.lazy(() => import('../screen/VoucherReport'));
const SettingsScreen = React.lazy(() => import('../screen/SettingsScreen'));
const VoucherScreen = React.lazy(() => import('../screen/VoucherScreen'));
const TaggableVoucherListScreen = React.lazy(() => import('../screen/TaggableVoucherListScreen'));
const TaggableVoucherReportScreen = React.lazy(() => import('../screen/TaggableVoucherReport'));
const NewTaggableVoucherScreen = React.lazy(() => import('../screen/NewTaggableVoucherScreen'));
const PromotionListScreen = React.lazy(() => import('../screen/PromotionListScreen'));
const PromotionReport = React.lazy(() => import('../screen/PromotionReport'));
const LoyaltyPointsRate = React.lazy(() => import('../screen/LoyaltyPointsRate'));
const SettingSetCredit = React.lazy(() => import('../screen/SettingSetCredit'));
const RedemptionRedeemedScreen = React.lazy(() => import('../screen/RedemptionRedeemedScreen'));
const RedemptionExpiredScreen = React.lazy(() => import('../screen/RedemptionExpiredScreen'));
const RedemptionScreen = React.lazy(() => import('../screen/RedemptionScreen'));
const PurchaseOrderScreen = React.lazy(() => import('../screen/PurchaseOrderScreen'));
const SettingLoyaltyScreen = React.lazy(() => import('../screen/SettingLoyaltyScreen'));
const SettingRedemptionScreen = React.lazy(() => import('../screen/SettingRedemptionScreen'));
const StockTakeScreen = React.lazy(() => import('../screen/StockTakeScreen'));
const StockTransferScreen = React.lazy(() => import('../screen/StockTransferScreen'));
const SupplierScreen = React.lazy(() => import('../screen/SupplierScreen'));
const InventoryScreen = React.lazy(() => import('../screen/InventoryScreen'));
const NewCampaignScreen = React.lazy(() => import('../screen/NewCampaignScreen'));
const SettingPrinterScreen = React.lazy(() => import('../screen/SettingPrinterScreen'));
const LoyaltyReport = React.lazy(() => import('../screen/LoyaltyReport'));
const LoyaltySettingScreen = React.lazy(() => import('../screen/LoyaltySettingScreen'));
const ReportActivityLog = React.lazy(() => import('../screen/ReportActivityLog'));
// const ReportSalesRefund = React.lazy(() => import('../screen/ReportSalesRefund')); 
const EmployeeTimeSheet = React.lazy(() => import('../screen/EmployeeTimeSheet'));
const TopUpCreditReport = React.lazy(() => import('../screen/TopUpCreditReport'));
const LoyaltySignUpCampaignScreen = React.lazy(() => import('../screen/LoyaltySignUpCampaignScreen'));
const LoyaltyPayEarn = React.lazy(() => import('../screen/LoyaltyPayEarn'));
const LoyaltyRewardRedemption = React.lazy(() => import('../screen/LoyaltyRewardRedemption'));
const TopupCreditTypeScreen = React.lazy(() => import('../screen/TopupCreditTypeScreen'));
const NewTopupCreditTypeScreen = React.lazy(() => import('../screen/NewTopupCreditTypeScreen'));
const InventoryProductScreen = React.lazy(() => import('../screen/InventoryProductScreen'));
const StockTakeProductScreen = React.lazy(() => import('../screen/StockTakeProductScreen'));
const StockTransferProductScreen = React.lazy(() => import('../screen/StockTransferProductScreen'));
const SupplierProductScreen = React.lazy(() => import('../screen/SupplierProductScreen'));
const PurchaseOrderProductScreen = React.lazy(() => import('../screen/PurchaseOrderProductScreen'));
const SettingMerchantPaymentScreen = React.lazy(() => import('../screen/SettingMerchantPaymentScreen'));
const StockReturnProductScreen = React.lazy(() => import('../screen/StockReturnProductScreen'));
const SettingCreditSMSScreen = React.lazy(() => import('../screen/SettingCreditSMSScreen'));
const SettingCreditWhatsappScreen = React.lazy(() => import('../screen/SettingCreditWhatsappScreen'));
const VariantAddOnScreen = React.lazy(() => import('../screen/VariantAddOnScreen'));
const TableScreen = React.lazy(() => import('../screen/TableScreen'));
const OrderScreen = React.lazy(() => import('../screen/OrderScreen'));
const TakeawayScreen = React.lazy(() => import('../screen/TakeawayScreen'));
const OtherDeliveryScreen = React.lazy(() => import('../screen/OtherDeliveryScreen'));
const HistoryScreen = React.lazy(() => import('../screen/HistoryScreen'));
const UpsellingCampaignScreen = React.lazy(() => import('../screen/UpsellingCampaignScreen'));
const UpsellingListScreen = React.lazy(() => import('../screen/UpsellingListScreen'));
const ReservationAnalyticScreen = React.lazy(() => import('../screen/ReservationAnalyticScreen'));
const SettingDepositScreen = React.lazy(() => import('../screen/SettingDepositScreen'));
// const ReportSalesOrderCount = React.lazy(() => import('../screen/ReportSalesOrderCount')); //hide first 2025-05-23
const KitchenScreen = React.lazy(() => import('../screen/KitchenScreen'));
const DetailsScreen = React.lazy(() => import('../screen/DetailsScreen'));
const CalendarScreen = React.lazy(() => import('../screen/CalendarScreen'));
// const ReportSalesRevisitCount = React.lazy(() => import('../screen/ReportSalesRevisitCount')); //hide first 2025-05-23
const ReportSalesUpselling = React.lazy(() => import('../screen/ReportSalesUpselling')); //hide first 2025-05-23
const ReportSalesUpsellingRevenue = React.lazy(() => import('../screen/ReportSalesUpsellingRevenue')); //hide first 2025-05-23
const MoCartScreen = React.lazy(() => import('../screen/MoCartScreen'));
const MenuOrderingScreen = React.lazy(() => import('../screen/MenuOrderingScreen'));
const MoTableScreen = React.lazy(() => import('../screen/MoTableScreen'));
const MoOutletMenuScreen = React.lazy(() => import('../screen/MoOutletMenuScreen'));
const MoMenuItemDetailsScreen = React.lazy(() => import('../screen/MoMenuItemDetailsScreen'));
const WorkOrderItemScreen = React.lazy(() => import('../screen/WorkOrderItemList'));
const WorkOrderScreen = React.lazy(() => import('../screen/WorkOrderList'));
const CompositeReportScreen = React.lazy(() => import('../screen/CompositeReport'));

const HomeStack = ({ navigation }) => {
  navigation.setOptions({ tabBarVisible: false });

  const { height: windowHeight, width: windowWidth } = useWindowDimensions();
  const headerOption = {
    headerTitleStyle: { color: Colors.whiteColor, marginLeft: windowWidth * 0.3, fontFamily: 'NunitoSans-Bold', fontSize: 32 },
    headerTintColor: Colors.darkBgColor,
    headerStyle: {
      backgroundColor: Colors.darkBgColor,
      elevation: 0,
      shadowOpacity: 0,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.22,
      shadowRadius: 3.22,
      elevation: 3,
      height: windowHeight * 0.08
    },
    tabBarVisible: false,
  };

  return (
    <Suspense fallback={<LoadingScreen />}>
      <Stack.Navigator
        screenOptions={{
          headerShown: true,
        }}
      >
        <Stack.Screen
          name='Login - KooDoo BackOffice'
          component={LoginScreen}
        />
        <Stack.Screen
          name='Error - KooDoo BackOffice'
          component={ErrorScreen}
        />
        <Stack.Screen
          name='Dashboard - KooDoo BackOffice'
          component={DashboardScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='All Transaction - KooDoo BackOffice'
          component={AllTransaction}
          options={headerOption}
        />
        <Stack.Screen
          name="Ordering - KooDoo BackOffice"
          component={MenuOrderingScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="MoTable - KooDoo BackOffice"
          component={MoTableScreen}
          options={headerOption}
        />
        <Stack.Screen
          name="Outlet Menu - KooDoo BackOffice"
          component={MoOutletMenuScreen}
          options={headerOption}
        />
        <Stack.Screen
          name="Menu Item Details - KooDoo BackOffice"
          component={MoMenuItemDetailsScreen}
          options={headerOption}
        />
        <Stack.Screen
          name="Cart - KooDoo BackOfffice"
          component={MoCartScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Product - KooDoo BackOffice'
          component={ProductScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Product Add - KooDoo BackOffice'
          component={ProductAddScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Product Category - KooDoo BackOffice'
          component={ProductCategoryScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Product Menu - KooDoo BackOffice'
          component={ProductMenuScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Employee List - KooDoo BackOffice'
          component={EmployeeScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Customer List - KooDoo BackOffice'
          component={CrmScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Promotion List - KooDoo BackOffice'
          component={PromotionListScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Promotion Report - KooDoo BackOffice'
          component={PromotionReport}
          options={headerOption}
        />
        <Stack.Screen
          name='Segment List - KooDoo BackOffice'
          component={SegmentScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Overview Report - KooDoo BackOffice'
          component={ReportSalesOvertime}
          options={headerOption}
        />
        {/* hide first 2025-05-23 */}
        <Stack.Screen
          name='AOV Report - KooDoo BackOffice'
          component={ReportSalesAov}
          options={headerOption}
        />
        <Stack.Screen
          name='Product Report - KooDoo BackOffice'
          component={ReportSalesProduct}
          options={headerOption}
        />
        <Stack.Screen
          name='Category Report - KooDoo BackOffice'
          component={ReportSalesCategory}
          options={headerOption}
        />
        <Stack.Screen
          name='Product Category Report - KooDoo BackOffice'
          component={ReportCategoryProduct}
          options={headerOption}
        />
        <Stack.Screen
          name='Variant Report - KooDoo BackOffice'
          component={ReportSalesVariant}
          options={headerOption}
        />
        <Stack.Screen
          name='Add Ons Report - KooDoo BackOffice'
          component={ReportSalesAddOns}
          options={headerOption}
        />
        {/* <Stack.Screen
          name='Transaction Report - KooDoo BackOffice'
          component={ReportSalesTransaction}
          options={headerOption}
        />
        <Stack.Screen
          name='Payment Method Report - KooDoo BackOffice'
          component={ReportSalesPaymentMethod}
          options={headerOption}
        /> */}
        <Stack.Screen
          name='Shift Report - KooDoo BackOffice'
          component={ReportSaleByShift}
          options={headerOption}
        />
        <Stack.Screen
          name='Repeat Sales Report - KooDoo BackOffice'
          component={ReportSalesRepeat}
          options={headerOption}
        />
        <Stack.Screen
          name='Pay In & Out Shift Report - KooDoo BackOffice'
          component={ReportShiftPayInOut}
          options={headerOption}
        />
        <Stack.Screen
          name='PayOut Report - KooDoo BackOffice'
          component={ReportPayout}
          options={headerOption}
        />
        <Stack.Screen
          name='Preorder Package - KooDoo BackOffice'
          component={PreorderPackageScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Customer - KooDoo BackOffice'
          component={NewCustomer}
          options={headerOption}
        />
        <Stack.Screen
          name='Settings Reservation - KooDoo BackOffice'
          component={SettingsReservation}
          options={headerOption}
        />
        <Stack.Screen
          name='Receipt Settings - KooDoo BackOffice'
          component={SettingReceiptScreen}
          options={headerOption}
        />
        <Stack.Screen
          name="Order Settings - KooDoo BackOffice"
          component={SettingOrderScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Payment Setting - KooDoo BackOffice'
          component={SettingPaymentScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='E Invoice - KooDoo BackOffice'
          component={EInvoiceScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='WhatsApp Settings - KooDoo BackOffice'
          component={SettingsWhatsappScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='VoucherScreen - KooDoo BackOffice'
          component={VoucherScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Loyalty Campaign - KooDoo BackOffice'
          component={NewLoyaltyCampaignScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Loyalty Points - KooDoo BackOffice'
          component={SettingCredit}
          options={headerOption}
        />
        <Stack.Screen
          name='Loyalty Stamps - KooDoo BackOffice'
          component={LoyaltyStampScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='VoucherReport - KooDoo BackOffice'
          component={VoucherReport}
        />
        <Stack.Screen
          name='Voucher List - KooDoo BackOffice'
          component={TaggableVoucherListScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Voucher - KooDoo BackOffice'
          component={NewTaggableVoucherScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Voucher Report - KooDoo BackOffice'
          component={TaggableVoucherReportScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Shift Settings - KooDoo BackOffice'
          component={SettingShiftScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='General Settings - KooDoo BackOffice'
          component={SettingsScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Rate Settings - KooDoo BackOffice'
          component={LoyaltyPointsRate}
          options={headerOption}
        />
        <Stack.Screen
          name='Redemption Package - KooDoo BackOffice'
          component={SettingSetCredit}
          options={headerOption}
        />
        <Stack.Screen
          name='Redeemed Docket - KooDoo BackOffice'
          component={RedemptionRedeemedScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Expired Docket - KooDoo BackOffice'
          component={RedemptionExpiredScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Active Docket - KooDoo BackOffice'
          component={RedemptionScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Promotion - KooDoo BackOffice'
          component={NewCampaignScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='PurchaseOrder - KooDoo BackOffice'
          component={PurchaseOrderScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Setting Loyalty - KooDoo BackOffice'
          component={SettingLoyaltyScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Docket List - KooDoo BackOffice'
          component={SettingRedemptionScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='StockTake - KooDoo BackOffice'
          component={StockTakeScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Stock Transfer - KooDoo BackOffice'
          component={StockTransferScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Supplier - KooDoo BackOffice'
          component={SupplierScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Inventory - KooDoo BackOffice'
          component={InventoryScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Printer Settings - KooDoo BackOffice'
          component={SettingPrinterScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Loyalty Report - KooDoo BackOffice'
          component={LoyaltyReport}
          options={headerOption}
        />
        <Stack.Screen
          name='Loyalty Setting - KooDoo BackOffice'
          component={LoyaltySettingScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Activity-Log - KooDoo BackOffice'
          component={ReportActivityLog}
          options={headerOption}
        />
        {/* <Stack.Screen
          name='Refund Report - KooDoo BackOffice'
          component={ReportSalesRefund}
          options={headerOption}
        /> */}
        <Stack.Screen
          name='Employee Timesheet - KooDoo BackOffice'
          component={EmployeeTimeSheet}
          options={headerOption}
        />
        <Stack.Screen
          name='Pay & Earn - KooDoo BackOffice'
          component={LoyaltyPayEarn}
          options={headerOption}
        />
        <Stack.Screen
          name='Credit Type Report - KooDoo BackOffice'
          component={TopUpCreditReport}
          options={headerOption}
        />
        <Stack.Screen
          name='Sign Up Reward - KooDoo BackOffice'
          component={LoyaltySignUpCampaignScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Reward & Redemption - KooDoo BackOffice'
          component={LoyaltyRewardRedemption}
          options={headerOption}
        />
        <Stack.Screen
          name='Credit Type List - KooDoo BackOffice'
          component={TopupCreditTypeScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Credit Type - KooDoo BackOffice'
          component={NewTopupCreditTypeScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='StockTake Product - KooDoo BackOffice'
          component={StockTakeProductScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Stock Transfer Product - KooDoo BackOffice'
          component={StockTransferProductScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Supplier Product - KooDoo BackOffice'
          component={SupplierProductScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Inventory Product - KooDoo BackOffice'
          component={InventoryProductScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='PurchaseOrder Product - KooDoo BackOffice'
          component={PurchaseOrderProductScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Stock Return Product - KooDoo BackOffice'
          component={StockReturnProductScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Merchant Payment - KooDoo BackOffice'
          component={SettingMerchantPaymentScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='SMS Credit - KooDoo BackOffice'
          component={SettingCreditSMSScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Whatsapp Credit - KooDoo BackOffice'
          component={SettingCreditWhatsappScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Payment Success - KooDoo BackOffice'
          component={PaymentSuccessScreen}
        />
        <Stack.Screen
          name='Table - KooDoo BackOffice'
          component={TableScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Order - KooDoo BackOffice'
          component={OrderScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Kitchen - KooDoo BackOffice'
          component={KitchenScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Takeaway - KooDoo BackOffice'
          component={TakeawayScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Other Delivery - KooDoo BackOffice'
          component={OtherDeliveryScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='History - KooDoo BackOffice'
          component={HistoryScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Manage Reservation - KooDoo BackOffice'
          component={DetailsScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Calendar - KooDoo BackOffice'
          component={CalendarScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Upselling List - KooDoo BackOffice'
          component={UpsellingListScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Upselling Campaign - KooDoo BackOffice'
          component={UpsellingCampaignScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Reservation Analytic - KooDoo BackOffice'
          component={ReservationAnalyticScreen}
          options={headerOption}
        />
        {/* hide first 2025-05-23 */}
        {/* <Stack.Screen
          name='Revisit Count Report - KooDoo BackOffice'
          component={ReportSalesRevisitCount}
          options={headerOption}
        />
        <Stack.Screen
          name='Order Count Report - KooDoo BackOffice'
          component={ReportSalesOrderCount}
          options={headerOption}
        /> */}
        <Stack.Screen
          name='Upselling Revenue Report - KooDoo BackOffice'
          component={ReportSalesUpsellingRevenue}
          options={headerOption}
        />
        <Stack.Screen
          name='Upselling Report - KooDoo BackOffice'
          component={ReportSalesUpselling}
          options={headerOption}
        />
        <Stack.Screen
          name='Reservation Setting Deposit - KooDoo BackOffice'
          component={SettingDepositScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Variant Add-on - KooDoo BackOffice'
          component={VariantAddOnScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Work Order - KooDoo BackOffice'
          component={WorkOrderScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Work Order Item - KooDoo BackOffice'
          component={WorkOrderItemScreen}
          options={headerOption}
        />
        <Stack.Screen
          name='Composite Report - KooDoo BackOffice'
          component={CompositeReportScreen}
          options={headerOption}
        />
      </Stack.Navigator>
    </Suspense>
  );
}

const AppNavigator = () => {
  return (
    <NavigationContainer linking={linking} fallback={<LoadingScreen />}>
      <Suspense fallback={<LoadingScreen />}>
        <Tab.Navigator tabBar={() => null}>
          <Tab.Screen
            name='HomeStack'
            component={HomeStack}
            screenOptions={({ route }) => ({
              tabBarVisible: null,
            })}
          />
        </Tab.Navigator>
      </Suspense>
    </NavigationContainer>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  menuContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-evenly',
  },
  menuItemsCard: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
  },
  circleContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    padding: 10,
  },
});

export default AppNavigator;