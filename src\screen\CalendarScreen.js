import React, { Component, useReducer, useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Dimensions,
  Alert,
  Button,
  Modal,
  TextInput,
  KeyboardAvoidingView,
  ActivityIndicator,
  useWindowDimensions,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import Footer from './Footer';
import Icon from 'react-native-vector-icons/Ionicons';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Plus from 'react-native-vector-icons/Feather';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Feather from 'react-native-vector-icons/Feather';
import PlusSvg from '../assets/svg/Plus.svg';
import DropDownPicker from 'react-native-dropdown-picker';
import Close from 'react-native-vector-icons/AntDesign';
import ApiClient from '../util/ApiClient';
import AntDesign from 'react-native-vector-icons/AntDesign';
import API from '../constant/API';
import * as User from '../util/User';
import * as Cart from '../util/Cart';
// import Dash from 'react-native-dash';
import moment from 'moment';
import Styles from '../constant/Styles';
// import QRCode from 'react-native-qrcode-svg';
import EIcon from 'react-native-vector-icons/Entypo';
// import CurrencyInput, { formatValue } from 'react-currency-input-field';
import AIcon from 'react-native-vector-icons/AntDesign';
import OrderModal from './components/OrderModal';
// import Barcode from 'react-native-barcode-builder';
// import molpay from 'molpay-mobile-xdk-reactnative-beta';
import {
  getTransformForScreenInsideNavigation,
  // isTablet
} from '../util/common';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
// import Swipeable from 'react-native-gesture-handler/Swipeable';
import {
  DELAY_LONG_PRESS_TIME,
  MODE_ADD_CART,
  OFFLINE_BILL_TYPE,
  OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST,
  OFFLINE_PAYMENT_METHOD_TYPE,
  ORDER_TYPE,
  USER_ORDER_PRIORITY,
  USER_ORDER_STATUS,
  USER_RESERVATION_STATUS,
  EXPAND_TAB_TYPE,
} from '../constant/common';
// import { useKeyboard } from '../hooks';
// import RNPickerSelect from 'react-native-picker-select';
import {
  getObjectDiff,
  isObjectEqual,
  listenToCurrOutletIdChangesWaiter,
  naturalCompare,
  sliceUnicodeStringV2WithDots,
} from '../util/common';
import { qrUrl } from '../constant/env';
import { printUserOrder } from '../util/printer';
import { Collections } from '../constant/firebase';
// import firestore from '@react-native-firebase/firestore';
// import CheckBox from 'react-native-check-box';
import AsyncImage from '../components/asyncImage';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import { customAlphabet } from 'nanoid';

// import CheckBox from '@react-native-community/checkbox';
// import { DIMENTIONS } from 'react-native-numeric-input';
import { color } from 'react-native-reanimated';
// import ProgressCircle from 'react-native-progress-circle';
import { Calendar, CalendarList, Agenda } from 'react-native-calendars';

import PropTypes from 'prop-types';
import Moment from 'moment';
// import jMoment from 'moment-jalaali';
// import { format, subHours, startOfMonth } from 'date-fns';
// import {
//   MonthlyBody,
//   MonthlyDay,
//   MonthlyCalendar,
//   MonthlyNav,
//   DefaultMonthlyEventItem,
// } from '@zach.codes/react-calendar';
import { useFocusEffect } from '@react-navigation/native';
// import UserIdleWrapper from '../components/userIdleWrapper';

import { prefix } from "../constant/env";
import { useLinkTo } from "@react-navigation/native";
import MultiSelect from "react-multiple-select-dropdown-lite";
import personicon from "../assets/image/default-profile.png";
import headerLogo from "../assets/image/logo.png";


const alphabet = '0123456789';
const nanoid = customAlphabet(alphabet, 12);

const CalendarScreen = (props) => {
  // Common useState for every page
  const { navigation } = props;
  
  const linkTo = useLinkTo();

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [switchMerchant, setSwitchMerchant] = useState(false);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  // Dummy data

  // useState
  let [currentMonth, setCurrentMonth] = useState(Date.now());
  const calendarDataArray = CommonStore.useState((s) => s.calendarDataArray);
  const selectedCalendarData = CommonStore.useState(
    (s) => s.selectedCalendarData,
  );

  // Header use data
  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );
  const currOutletShiftStatus = OutletStore.useState(
    (s) => s.currOutletShiftStatus
  );

  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const allOutlets = MerchantStore.useState((s) => s.allOutlets);
  const isLoading = CommonStore.useState((s) => s.isLoading);
  
  var targetOutletDropdownListTemp = allOutlets.map((outlet) => ({
    label: sliceUnicodeStringV2WithDots(outlet.name, 20),
    value: outlet.uniqueId,
  }));

  const userName = UserStore.useState((s) => s.name);

  const userReservations = OutletStore.useState((s) => s.userReservations);

  const [openO, setOpenO] = useState(false);

  // useEffect(() => {
  //   CommonStore.update((s) => {
  //     s.outletSelectDropdownView = () => {
  //       return (
  //         <View
  //           style={{
  //             flexDirection: "row",
  //             alignItems: "center",
  //             borderRadius: 8,
  //             width: 200,
  //             backgroundColor: "white",
  //           }}
  //         >
  //           {currOutletId.length > 0 &&
  //             allOutlets.find((item) => item.uniqueId === currOutletId) ? (
  //               <DropDownPicker
  //               style={{
  //                 backgroundColor: Colors.fieldtBgColor,
  //                 width: 200,
  //                 height: 40,
  //                 borderRadius: 10,
  //                 borderWidth: 1,
  //                 borderColor: "#E5E5E5",
  //                 flexDirection: "row",
  //               }}
  //               dropDownContainerStyle={{
  //                 width: 200,
  //                 backgroundColor: Colors.fieldtBgColor,
  //                 borderColor: "#E5E5E5",
  //               }}
  //               labelStyle={{
  //                 marginLeft: 5,
  //                 flexDirection: "row",
  //               }}
  //               textStyle={{
  //                 fontSize: 14,
  //                 fontFamily: 'NunitoSans-Regular',

  //                 marginLeft: 5,
  //                 paddingVertical: 10,
  //                 flexDirection: "row",
  //               }}
  //               selectedItemContainerStyle={{
  //                 flexDirection: "row",
  //               }}

  //               showArrowIcon={true}
  //               ArrowDownIconComponent={({ style }) => (
  //                 <Ionicon
  //                   size={25}
  //                   color={Colors.fieldtTxtColor}
  //                   style={{ paddingHorizontal: 5, marginTop: 5 }}
  //                   name="chevron-down-outline"
  //                 />
  //               )}
  //               ArrowUpIconComponent={({ style }) => (
  //                 <Ionicon
  //                   size={25}
  //                   color={Colors.fieldtTxtColor}
  //                   style={{ paddingHorizontal: 5, marginTop: 5 }}
  //                   name="chevron-up-outline"
  //                 />
  //               )}

  //               showTickIcon={true}
  //               TickIconComponent={({ press }) => (
  //                 <Ionicon
  //                   style={{ paddingHorizontal: 5, marginTop: 5 }}
  //                   color={
  //                     press ? Colors.fieldtBgColor : Colors.primaryColor
  //                   }
  //                   name={'md-checkbox'}
  //                   size={25}
  //                 />
  //               )}
  //               placeholderStyle={{
  //                 color: Colors.fieldtTxtColor,
  //                 // marginTop: 15,
  //               }}
  //               dropDownDirection="BOTTOM"
  //               placeholder="Choose Outlet"
  //               items={targetOutletDropdownListTemp}
  //               value={currOutletId}
  //               onSelectItem={(item) => {
  //                 if (item) { // if choose the same option again, value = ''
  //                   MerchantStore.update((s) => {
  //                     s.currOutletId = item.value;
  //                     s.currOutlet =
  //                       allOutlets.find(
  //                         (outlet) => outlet.uniqueId === item.value
  //                       ) || {};
  //                   });
  //                 }

  //                 CommonStore.update((s) => {
  //                   s.shiftClosedModal = false;
  //                 });
  //               }}
  //               open={openO}
  //               setOpen={setOpenO}
  //             />
  //           ) : (
  //             <ActivityIndicator size={"small"} color={Colors.whiteColor} />
  //           )}

  //           {/* <Select

  //             placeholder={"Choose Outlet"}
  //             onChange={(items) => {
  //               setSelectedOutletList(items);
  //             }}
  //             options={outletDropdownList}
  //             isMulti
  //           /> */}
  //         </View>
  //       );
  //     };
  //   });
  // }, [allOutlets, currOutletId, isLoading, currOutletShiftStatus]);

  // Header of the page
  navigation.setOptions({
    headerLeft: () => (
      <View
        style={[
          styles.headerLeftStyle,
          {
            width: windowWidth * 0.17,
          },
        ]}
      >
        <img src={headerLogo} width={124} height={26} />
        {/* <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        /> */}
      </View>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            justifyContent: "center",
            alignItems: "center",
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            //width:  "55%",
          },
          Dimensions.get("screen").width <= 768
            ? { right: Dimensions.get("screen").width * 0.12 }
            : {},
        ]}
      >
        <Text
          style={{
            fontSize: 24,
            // lineHeight: 25,
            textAlign: "center",
            fontFamily: "NunitoSans-Bold",
            color: Colors.whiteColor,
            opacity: 1,
          }}
        >
          Calendar
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        {/* {console.log('edward test')} */}
        {/* {console.log(outletSelectDropdownView)} */}
        {outletSelectDropdownView && outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: "white",
            width: 0.5,
            height: Dimensions.get("screen").height * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
            // borderWidth: 1
          }}
        ></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate("General Settings - KooDoo BackOffice")
            }
          }}
          style={{ flexDirection: "row", alignItems: "center" }}
        >
          <Text
            style={{
              fontFamily: "NunitoSans-SemiBold",
              fontSize: 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }}
          >
            {userName}
          </Text>
          <View
            style={{
              //backgroundColor: 'red',
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "white",
            }}
          >
            <img
              src={personicon}
              width={windowHeight * 0.035}
              height={windowHeight * 0.035}
            />
            {/* <Image
              style={{
                width: windowHeight * 0.05,
              height: windowHeight * 0.05,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            /> */}
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  useEffect(() => {
    if (userReservations) {
      var tempArr = [];
      for (var i = 0; i < userReservations.length; i++) {
        var isExist = false;
        for (let j = 0; j < tempArr.length; j++) {
          if (moment(tempArr[j].date).format('MM-DD-YYYY') == moment(userReservations[i].reservationTime).format('MM-DD-YYYY')) {
            isExist = true;
            if (
              userReservations[i].status === USER_RESERVATION_STATUS.SEATED ||
              userReservations[i].status === USER_RESERVATION_STATUS.ACCEPTED ||
              userReservations[i].status === USER_RESERVATION_STATUS.PENDING
            ) {
              tempArr[j].pax += userReservations[i].pax;
              tempArr[j].rsv += 1;
            } else if (
              userReservations[i].status === USER_RESERVATION_STATUS.CANCELED
            ) {
              tempArr[j].cancel += 1;
            } else if (
              userReservations[i].status === USER_RESERVATION_STATUS.NO_SHOW
            ) {
              tempArr[j].noShow += 1;
            }
            break;
          } else {
            isExist = false;
          }
        }
        if (!isExist) {
          if (
            userReservations[i].status === USER_RESERVATION_STATUS.SEATED ||
            userReservations[i].status === USER_RESERVATION_STATUS.ACCEPTED ||
            userReservations[i].status === USER_RESERVATION_STATUS.PENDING
          ) {
            tempArr.push({
              id: tempArr.length,
              date: moment(userReservations[i].reservationTime),
              pax: userReservations[i].pax,
              rsv: 1,
              cancel: 0,
              noShow: 0,
            });
          } else if (
            userReservations[i].status === USER_RESERVATION_STATUS.CANCELED
          ) {
            tempArr.push({
              id: tempArr.length,
              date: moment(userReservations[i].reservationTime),
              pax: userReservations[i].pax,
              rsv: 0,
              cancel: 1,
              noShow: 0,
            });
          } else if (
            userReservations[i].status === USER_RESERVATION_STATUS.NO_SHOW
          ) {
            tempArr.push({
              id: tempArr.length,
              date: moment(userReservations[i].reservationTime),
              pax: userReservations[i].pax,
              rsv: 0,
              cancel: 0,
              noShow: 1,
            });
          }
        }
      }
    }
    CommonStore.update((s) => {
      s.calendarDataArray = tempArr;
    });
    // console.log(userReservations)
    // // console.log(tempArr)
    // console.log('refreshed');
  }, [userReservations]);

  useEffect(() => {
    var tempArr = [];
    if (userReservations) {
      userReservations.forEach((item) => {
        if (
          moment(item.reservationTime).format('YYYY-MM-DD') ==
          moment(selectedCalendarData).format('YYYY-MM-DD')
        ) {
          tempArr.push(item);
        }
      });
    }
    CommonStore.update((s) => {
      s.selectedCalendarArray = tempArr;
    })
  }, [selectedCalendarData]);

  // Main return
  return (
    // Screen View
    // <UserIdleWrapper disabled={!isMounted}>
      <View
        style={[
          styles.container,
          {
            ...getTransformForScreenInsideNavigation(),
          },
          // {borderWidth:1}
        ]}>
        {/* Modal start */}

        {/* Modal end */}
        {/* Side bar content */}
        <View
          style={[
            styles.sidebar,
            switchMerchant
              ? {
                // width: '10%'
              }
              : {},
            {
              width: windowWidth * 0.08,
            }
            // {borderWidth:1}
          ]}>
          <SideBar
            navigation={props.navigation}
            selectedTab={1}
            expandOperation
          />
        </View>
        {/* <View
          style={{
            flex: 1,
            position: 'absolute',
            bottom:
              Platform.OS == 'ios'
                ? windowHeight * 0.075
                : !switchMerchant && windowWidth < 1000
                  ? windowHeight * 0.115
                  : windowHeight * 0.1,
          }}>
          <Footer />
        </View> */}
        {/* Calender page content */}
        <View
          style={{
            width: windowWidth * 0.92,
            height: windowHeight * 0.8,
            // borderWidth: 10,
            // margin: 10,
            padding: 16,
          }}>
          <View style={{ position: 'absolute', bottom: -30, left: 0 - windowWidth * 0.08 }}>
            <Footer />
          </View>
          <ScrollView>
            <Calendar
              // pressDay={}
              data={calendarDataArray}
              reservation={false}
              // pax={2}
              // rsv={3}
              // cancel={1}
              // noShow={2}
              onDayPress={(day) => {
                console.log('DAY', day)
                // go to reservation details
                CommonStore.update((s) => {
                  s.selectedCalendarData = moment(day[0]).format('YYYY-MM-DD');
                });

                calendarDataArray.forEach((item) => {
                  // // console.log(moment(item.date).format('YYYY-MM-DD'))
                  if (
                    moment(item.date).format('YYYY-MM-DD') ===
                    moment(day[0]).format('YYYY-MM-DD')
                  ) {
                    CommonStore.update((s) => {
                      s.selectedCalendarData = moment(item.date).format(
                        'YYYY-MM-DD',
                      );
                    });
                  }
                });
                // navigation.navigate('DetailsScreen');
                linkTo && linkTo(`${prefix}/manage-reservations`);
              }}
              // Month format in calendar title. Formatting values: http://arshaw.com/xdate/#Formatting
              monthFormat={'yyyy MMM'}
              // Handler which gets executed when visible month changes in calendar. Default = undefined
              onMonthChange={(month) => {
                console.log('month changed', month);

                CommonStore.update((s) => {
                  s.selectedCalendarData = month.timestamp;
                });
              }}
              // If firstDay=1 week starts from Monday. Note that dayNames and dayNamesShort should still start from Sunday
              firstDay={1}
              hideExtraDays
              onPressArrowLeft={(subtractMonth) => subtractMonth()}
              onPressArrowRight={(addMonth) => addMonth()}
              // Disable all touch events for disabled days. can be override with disableTouchEvent in markedDates
              disableAllTouchEventsForDisabledDays
              enableSwipeMonths
              theme={{
                // backgroundColor: 'blue',
                // calendarBackground: '#ffffff',
                //   textSectionTitleColor: '#b6c1cd',
                // textSectionTitleDisabledColor: '#d9e1e8',
                // selectedDayBackgroundColor: 'blue',
                // selectedDayTextColor: 'green',
                todayTextColor: 'blue',
                // arrowColor: 'orange',
                // disabledArrowColor: '#d9e1e8',
                // monthTextColor: 'blue',
                // indicatorColor: 'orange',
                //   textDayFontFamily: 'monospace',
                //   textMonthFontFamily: 'monospace',
                // textDayHeaderFontFamily: 'monospace',
                // textDayFontWeight: '800',
                textMonthFontWeight: 'bold',
              }}
            />
          </ScrollView>
        </View>
      </View>
    // </UserIdleWrapper>
  );
};

// Style sheet
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },

  listItem: {
    backgroundColor: Colors.whiteColor,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: '#c4c4c4',
    borderRadius: 20,
    paddingVertical: 20,
    paddingHorizontal: 20,
    marginRight: 10,
    marginBottom: 10,
    width: (Dimensions.get('window').width - 150) / 2,
  },

  tablebox: {
    backgroundColor: Colors.whiteColor,
    shadowColor: '#c4c4c4',
    shadowOffset: {
      width: 8,
      height: 8,
    },
    shadowOpacity: 0.55,
    shadowRadius: 10.32,
    width: 100,
    height: 100,
    marginRight: 25,
    borderRadius: 10,
    marginBottom: 30,
    marginTop: 10,
    marginHorizontal: 20,
  },

  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center',
  },
  headerLeftStyle: {
    width: Dimensions.get('window').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  confirmBox: {
    width: Dimensions.get('window').width * 0.4,
    minHeight: Dimensions.get('window').height * 0.1,
    borderRadius: 12,
    backgroundColor: 'rgb(209, 212, 212)',
    // borderWidth:1,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default CalendarScreen;
