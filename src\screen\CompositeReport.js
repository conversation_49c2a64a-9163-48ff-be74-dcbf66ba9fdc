// import { Text } from "react-native-fast-text";
import React, {
  useState,
  useEffect,
  useRef,
  useMemo,
  useCallback,
} from 'react';
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Alert,
  TouchableOpacity,
  Dimensions,
  Switch,
  Modal,
  KeyboardAvoidingView,
  Platform,
  TextInput,
  ActivityIndicator,
  useWindowDimensions,
  Text,
} from 'react-native';
// import firestore from '@react-native-firebase/firestore';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Feather';
import Ionicons from 'react-native-vector-icons/Ionicons';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Entypo from 'react-native-vector-icons/Entypo';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import DropDownPicker from 'react-native-dropdown-picker';
import { FlatList } from 'react-native-gesture-handler';
import personicon from "../assets/image/default-profile.png";
import headerLogo from "../assets/image/logo.png";
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage';
// import CheckBox from 'react-native-check-box';
import moment from 'moment';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import BigNumber from 'bignumber.js';
import Styles from '../constant/Styles';
import { getOrderDiscountInfo, getOrderDiscountInfoInclOrderBased, getTransformForModalInsideNavigation, getTransformForScreenInsideNavigation, getCartItemPriceWithoutAddOn, getAddOnChoiceQuantity, getAddOnChoicePrice, logEventAnalytics, } from '../util/common';
// import FusionCharts from 'react-native-fusioncharts';
import { OutletStore } from '../store/outletStore';
import { MerchantStore } from '../store/merchantStore';
import { UserStore } from '../store/userStore';
import Upload from '../assets/svg/Upload.svg';
import Download from '../assets/svg/Download.svg';
import GCalendar from '../assets/svg/GCalendar.svg';
// import RNFetchBlob from 'rn-fetch-blob';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {
  listenToUserChangesMerchant,
  listenToMerchantIdChangesMerchant,
  listenToCurrOutletIdChangesWaiter,
  listenToAllOutletsChangesMerchant,
  listenToCommonChangesMerchant,
  listenToSelectedOutletItemChanges,
  convertArrayToCSV,
  listenToSelectedOutletTableIdChanges,
  requestNotificationsPermission,
  sortReportDataList,
  generateEmailReport,
} from '../util/common';
import {
  filterChartItems,
  getDataForChartReportProductSales,
} from '../util/chart';
import {
  CHART_DATA,
  CHART_TYPE,
  FS_LIBRARY_PATH,
  CHART_Y_AXIS_DROPDOWN_LIST,
  CHART_FIELD_COMPARE_DROPDOWN_LIST,
  CHART_FIELD_NAME_DROPDOWN_LIST,
  CHART_FIELD_TYPE,
  CHART_FIELD_COMPARE_DICT,
  CHART_PERIOD,
  CHART_X_AXIS_DROPDOWN_LIST,
} from '../constant/chart';
import {
  EMAIL_REPORT_TYPE,
  REPORT_SORT_FIELD_TYPE,
  TABLE_PAGE_SIZE_DROPDOWN_LIST,
  ORDER_TYPE,
  EXPAND_TAB_TYPE,
  OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST,
  ORDER_TYPE_DROP_DOWN_LIST,
  PRODUCT_PRICE_TYPE,
  UNIT_TYPE_SHORT,
  APP_TYPE
} from '../constant/common';
import XLSX from 'xlsx';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
// import RNPickerSelect from 'react-native-picker-select';
import Feather from 'react-native-vector-icons/Feather';
import { useKeyboard } from '../hooks';
import { CommonStore } from '../store/commonStore';
import AsyncImage from '../components/asyncImage';
// import Tooltip from 'react-native-walkthrough-tooltip';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import { printCategoryAndProductSalesReport } from '../util/printer';
import { PRINTER_USAGE_TYPE } from '../constant/printer';
import { Collections } from '../constant/firebase';
import { ANALYTICS, ANALYTICS_PARSED } from '../constant/analytics';
import Ionicon from 'react-native-vector-icons/Ionicons';

//Unexpected require().
import logoImage from '../assets/image/logo.png';
import profilePicImage from '../assets/image/profile-pic.jpg';

const isTablet = () => {
  return true;
};

const CompositeReportScreen = (props) => {
  const { navigation } = props;
  const { width: windowWidth, height: windowHeight } = useWindowDimensions();
  const switchMerchant = !isTablet();

  ///////////////////////////////////////////////////////////

  const isMounted = useRef(true);

  useFocusEffect(
    useCallback(() => {
      isMounted.current = true;
      return () => {
        isMounted.current = false;
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const [openPage, setOpenPage] = useState(false);

  const userName = UserStore.useState((s) => s.name);
  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);
  const expandTab = CommonStore.useState((s) => s.expandTab);

  const outletSupplyItemsDict = CommonStore.useState((s) => s.outletSupplyItemsDict);
  const outletSelectDropdownView = CommonStore.useState((s) => s.outletSelectDropdownView);

  const [keyboardHeight] = useKeyboard();

  const [perPage, setPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [currentDetailsPage, setCurrentDetailsPage] = useState(1);

  const [showingDetailPage, setShowingDetailPage] = useState(null);
  const [detailPageTitle, setDetailPageTitle] = useState('');

  const osit = CommonStore.useState(s => {
    return s.osit !== undefined ? s.osit : [];
  });
  const [reportSummarySearch, setReportSummarySearch] = useState('');
  const [reportSummarySorting, setReportSummarySorting] = useState({ key: null, direction: 'asc' });
  const [reportDetailsSorting, setReportDetailsSorting] = useState({ key: null, direction: 'asc' });

  // [{outletSupplyItemId, name, latestCreatedDate}]
  const ositUnique = useMemo(() => {
    const uniqueItems = Object.values(osit.reduce((acc, item) => {
      const { outletSupplyItemId, name, createdAt } = item;
      const currentDate = moment(createdAt).valueOf();

      if (!acc[outletSupplyItemId] || moment(currentDate).isAfter(moment(acc[outletSupplyItemId].latestCreatedDate))) {
        acc[outletSupplyItemId] = {
          outletSupplyItemId,
          name,
          latestCreatedDate: currentDate
        };
      }

      return acc;
    }, {}));

    const filteredItems = uniqueItems.filter(item =>
      (item.name ? item.name : '').toLowerCase().includes(reportSummarySearch.toLowerCase())
    );

    if (reportSummarySorting.key) {
      filteredItems.sort((a, b) => {
        if (a[reportSummarySorting.key] < b[reportSummarySorting.key]) {
          return reportSummarySorting.direction === 'asc' ? -1 : 1;
        }
        if (a[reportSummarySorting.key] > b[reportSummarySorting.key]) {
          return reportSummarySorting.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }

    return filteredItems;
  }, [osit, reportSummarySearch, reportSummarySorting]);

  // {outletSupplyItemId: [osit]}
  const ositDict = useMemo(() => {
    return osit.reduce((acc, item) => {
      const { outletSupplyItemId } = item;
      if (!acc[outletSupplyItemId]) {
        acc[outletSupplyItemId] = [];
      }
      acc[outletSupplyItemId].push(item);
      return acc;
    }, {});
  }, [osit]);

  // Count Pages
  const totalDetailSummaryPages = useMemo(() => {
    return ositUnique.length > 0 ? Math.max(1, Math.ceil(ositUnique.length / perPage)) : 0;
  }, [ositUnique, perPage]);

  const totalDetailRecordPages = useMemo(() => {
    if (showingDetailPage === null) return 0;
    const detailRecords = ositDict[showingDetailPage];
    return detailRecords && detailRecords.length > 0 ? Math.max(1, Math.ceil(detailRecords.length / perPage)) : 0;
  }, [ositDict, showingDetailPage, perPage]);

  //////////////////////////////////////////////////////////////

  // Export Modal - Functionality Waiting for Implement
  const [exportModalVisibility, setExportModalVisibility] = useState(false);
  const [emailToExport, setEmailToExport] = useState('');

  const [exportingCSV, setExportingCSV] = useState(false);
  const [exportingExcel, setExportingExcel] = useState(false);

  const exportAsCSV = async () => {
    setExportingCSV(true);

    setTimeout(() => {
      setExportingCSV(false);
    }, 5000);
  }

  const exportAsExcel = async () => {
    setExportingExcel(true);

    setTimeout(() => {
      setExportingExcel(false);
    }, 5000);
  }

  //////////////////////////////////////////////////////////////

  // Set Detail Page Title
  useEffect(() => {
    const itemName = showingDetailPage ? (outletSupplyItemsDict[showingDetailPage] ? outletSupplyItemsDict[showingDetailPage].name : '') : '';
    setDetailPageTitle(itemName);
  }, [showingDetailPage, outletSupplyItemsDict]);

  //////////////////////////////////////////////////////////////

  // Sorting Function
  const requestReportSummarySort = (key) => {
    let direction = 'asc';
    if (reportSummarySorting.key === key && reportSummarySorting.direction === 'asc') {
      direction = 'desc';
    }
    setReportSummarySorting({ key, direction });

    ositUnique.sort((a, b) => {
      if (a[key] < b[key]) {
        return direction === 'asc' ? -1 : 1;
      }
      if (a[key] > b[key]) {
        return direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  };

  const requestReportDetailsSort = (key) => {
    let direction = 'asc';
    if (reportDetailsSorting.key === key && reportDetailsSorting.direction === 'asc') {
      direction = 'desc';
    }
    setReportDetailsSorting({ key, direction });

    ositDict[showingDetailPage].sort((a, b) => {
      if (a[key] < b[key]) {
        return direction === 'asc' ? -1 : 1;
      }
      if (a[key] > b[key]) {
        return direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  };

  const renderGroupedOsit = ({ item, index }) => {
    return (
      <TouchableOpacity
        onPress={() => { setShowingDetailPage(item.outletSupplyItemId); }}
        style={{
          backgroundColor:
            (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
          paddingVertical: 10,
          borderColor: '#BDBDBD',
          borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
          borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
        }}>

        <View style={{ flexDirection: 'row' }}>
          {/* Index */}
          <Text style={{
            width: '5%',
            fontSize: switchMerchant ? 10 : 13,
            fontFamily: 'NunitoSans-Regular',
            textAlign: 'left',
            paddingLeft: 10,
          }}>
            {(index + 1) + (currentPage - 1) * perPage}
          </Text>

          {/* Name */}
          <Text
            style={{
              width: '50%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {item.name}
          </Text>

          {/* Latest Updated */}
          <Text style={{
            width: '15%',
            fontSize: switchMerchant ? 10 : 13,
            fontFamily: 'NunitoSans-Regular',
            textAlign: 'left',
            paddingLeft: 10,
          }}>
            {moment(item.latestCreatedDate).format('DD MMM YYYY')}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderOsitRecord = ({ item, index }) => {
    const ositTransactions = Object.entries(item.fields).map(
      ([key, value]) => ({ key, ...value })
    );

    // let stockType = ositTransactions.map(transaction => transaction.rm || '-');
    // stockType = [...new Set(stockType)];

    return (
      <>
        {
          ositTransactions.map((record, recordIndex) => {
            let stockType = [record.rm ? record.rm : '-'];

            return (
              <TouchableOpacity style={{
                backgroundColor: (recordIndex + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
                paddingVertical: 10,
                borderColor: '#BDBDBD',
                borderTopWidth: (recordIndex + 1) % 2 == 0 ? 0 : 0.5,
                borderBottomWidth: (recordIndex + 1) % 2 == 0 ? 0 : 0.5,
              }}>

                <View style={{ flexDirection: 'row' }}>
                  <Text style={{
                    width: '5%',
                    fontSize: switchMerchant ? 10 : 13,
                    fontFamily: 'NunitoSans-Regular',
                    textAlign: 'left',
                    paddingLeft: 10,
                  }}>
                    {(recordIndex + 1) + (currentDetailsPage - 1) * perPage}
                  </Text>

                  <Text
                    style={{
                      width: '20%',
                      fontSize: switchMerchant ? 10 : 13,
                      fontFamily: 'NunitoSans-Regular',
                      textAlign: 'left',
                      paddingLeft: 10,
                    }}>
                    {item.name}
                  </Text>

                  <Text
                    style={{
                      width: '15%',
                      fontSize: switchMerchant ? 10 : 13,
                      fontFamily: 'NunitoSans-Regular',
                      textAlign: 'left',
                      paddingLeft: 10,
                    }}>
                    {moment(record.dt).format('DD MMM YYYY')}
                  </Text>

                  <Text style={{
                    width: '15%',
                    fontSize: switchMerchant ? 10 : 13,
                    fontFamily: 'NunitoSans-Regular',
                    textAlign: 'left',
                    paddingLeft: 10,
                  }}>
                    {record.bf.toFixed(2)}
                  </Text>

                  <Text
                    style={{
                      width: '9%',
                      fontSize: switchMerchant ? 10 : 13,
                      fontFamily: 'NunitoSans-Regular',
                      textAlign: 'left',
                      paddingLeft: 10,
                    }}>
                    {Math.abs(record.am)}
                  </Text>

                  <Text
                    style={{
                      width: '15%',
                      fontSize: switchMerchant ? 10 : 13,
                      fontFamily: 'NunitoSans-Regular',
                      textAlign: 'left',
                      paddingLeft: 10,
                      // backgroundColor: '#E6E6FA', // Lavender
                    }}>
                    {record.af.toFixed(2)}
                  </Text>

                  {/* <Text
            style={{
              width: '12%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
              // backgroundColor: '#E6E6FA', // Lavender
            }}>
            {/* Batch ID *
            ID
          </Text> */}

                  <Text
                    style={{
                      width: '20%',
                      fontSize: switchMerchant ? 10 : 13,
                      fontFamily: 'NunitoSans-Regular',
                      textAlign: 'left',
                      paddingLeft: 10,
                    }}>
                    {stockType.join('\n')}
                  </Text>
                </View>
              </TouchableOpacity>
            );
          })
        }
      </>
    );
  };

  const nextPage = () => {
    if (showingDetailPage === null) {
      setCurrentPage(Math.min(Math.max(currentPage + 1, 1), totalDetailSummaryPages));
    }
    else {
      setCurrentDetailsPage(Math.min(Math.max(currentDetailsPage + 1, 1), totalDetailRecordPages));
    }
  }

  const prevPage = () => {
    if (showingDetailPage === null) {
      setCurrentPage(Math.min(Math.max(currentPage - 1, 1), totalDetailSummaryPages));
    }
    else {
      setCurrentDetailsPage(Math.min(Math.max(currentDetailsPage - 1, 1), totalDetailRecordPages));
    }
  }

  //////////////////////////////////////////////////////////////

  navigation.setOptions({
    headerLeft: () => (
      <View
        style={[
          styles.headerLeftStyle,
          {
            width: windowWidth * 0.17,
          },
        ]}
      >
        <img src={headerLogo} width={124} height={26} />
        {/* <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        /> */}
      </View>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            justifyContent: "center",
            alignItems: "center",
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            //width:  "55%",
          },
          Dimensions.get("screen").width <= 768
            ? { right: Dimensions.get("screen").width * 0.12 }
            : {},
        ]}
      >
        <Text
          style={{
            fontSize: 24,
            // lineHeight: 25,
            textAlign: "center",
            fontFamily: "NunitoSans-Bold",
            color: Colors.whiteColor,
            opacity: 1,
          }}
        >
          Composite Report
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        {/* {console.log('edward test')} */}
        {/* {console.log(outletSelectDropdownView)} */}
        {outletSelectDropdownView && outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: "white",
            width: 0.5,
            height: Dimensions.get("screen").height * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
            // borderWidth: 1
          }}
        ></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate("General Settings - KooDoo BackOffice")
            }
          }}
          style={{ flexDirection: "row", alignItems: "center" }}
        >
          <Text
            style={{
              fontFamily: "NunitoSans-SemiBold",
              fontSize: 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }}
          >
            {userName}
          </Text>
          <View
            style={{
              //backgroundColor: 'red',
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "white",
            }}
          >
            <img
              src={personicon}
              width={windowHeight * 0.035}
              height={windowHeight * 0.035}
            />
            {/* <Image
              style={{
                width: windowHeight * 0.05,
              height: windowHeight * 0.05,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            /> */}
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  return (
    <UserIdleWrapper disabled={!isMounted}>
      <View
        style={[
          styles.container,
          !isTablet()
            ? {
              transform: [{ scaleX: 1 }, { scaleY: 1 }],
            }
            : {},
          {
            ...getTransformForScreenInsideNavigation(),
          }
        ]}>
        {/* Sidebar */}
        <View style={[styles.sidebar,
        switchMerchant
          ? {
            width: windowWidth * 0.08,
          }
          : {},
        {
          width: windowWidth * 0.08,
        }
        ]}>
          <SideBar
            navigation={props.navigation}
            selectedTab={8}
            expandReport
          />
        </View>

        {/* Main Content */}
        <ScrollView horizontal>

          {/* Download Report */}
          <Modal
            style={{}}
            visible={exportModalVisibility}
            supportedOrientations={['portrait', 'landscape']}
            transparent
            animationType={'fade'}>
            <View style={{
              flex: 1,
              backgroundColor: Colors.modalBgColor,
              alignItems: 'center',
              justifyContent: 'center',
              top:
                Platform.OS === 'android'
                  ? 0
                  : keyboardHeight > 0
                    ? -keyboardHeight * 0.45
                    : 0,
            }}>
              <View style={{
                height: windowWidth * 0.3,
                width: windowWidth * 0.4,
                backgroundColor: Colors.whiteColor,
                borderRadius: 12,
                padding: windowWidth * 0.03,
                alignItems: 'center',
                justifyContent: 'center',
                ...getTransformForModalInsideNavigation(),
              }}>
                {/* Close Button */}
                <TouchableOpacity
                  disabled={exportingCSV || exportingExcel}
                  style={{
                    position: 'absolute',
                    right: windowWidth * 0.02,
                    top: windowWidth * 0.02,

                    elevation: 1000,
                    zIndex: 1000,
                  }}
                  onPress={() => { setExportModalVisibility(false) }}
                >
                  <AntDesign
                    name="closecircle"
                    size={switchMerchant ? 15 : 25}
                    color={Colors.fieldtTxtColor}
                  />
                </TouchableOpacity>

                <View
                  style={{
                    alignItems: 'center',
                    top: '20%',
                    position: 'absolute',
                  }}>
                  <Text
                    style={{
                      fontFamily: 'NunitoSans-Bold',
                      textAlign: 'center',
                      fontSize: switchMerchant ? 16 : 24,
                    }}>
                    Download Report
                  </Text>
                </View>

                <View style={{ top: switchMerchant ? '14%' : '10%' }}>
                  <Text
                    style={{
                      fontSize: switchMerchant ? 10 : 20,
                      fontFamily: 'NunitoSans-Bold',
                    }}>
                    Email Address:
                  </Text>

                  <TextInput
                    underlineColorAndroid={Colors.fieldtBgColor}
                    style={{
                      backgroundColor: Colors.fieldtBgColor,
                      width: switchMerchant ? 240 : 370,
                      height: switchMerchant ? 35 : 50,
                      borderRadius: 5,
                      padding: 5,
                      marginVertical: 5,
                      borderWidth: 1,
                      borderColor: '#E5E5E5',
                      paddingLeft: 10,
                      fontSize: switchMerchant ? 10 : 14,
                    }}
                    autoCapitalize="none"
                    placeholderStyle={{ padding: 5 }}
                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                    placeholder="Enter your email"
                    onChangeText={(text) => { setEmailToExport(text) }}
                    value={emailToExport}
                  />

                  <Text
                    style={{
                      fontSize: switchMerchant ? 10 : 20,
                      fontFamily: 'NunitoSans-Bold',
                      marginTop: 15,
                    }}>
                    Send As:
                  </Text>

                  <View style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                    flexDirection: 'row',
                    marginTop: 10,
                  }}>
                    <TouchableOpacity
                      disabled={exportingExcel}
                      style={{
                        justifyContent: 'center',
                        flexDirection: 'row',
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        backgroundColor: '#4E9F7D',
                        borderRadius: 5,
                        width: switchMerchant ? 100 : 100,
                        paddingHorizontal: 10,
                        height: switchMerchant ? 35 : 40,
                        alignItems: 'center',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,
                        marginRight: 15,
                      }}
                      onPress={() => { exportAsExcel() }}>
                      {exportingExcel ? (
                        <ActivityIndicator
                          size={'small'}
                          color={Colors.whiteColor}
                        />
                      ) : (
                        <Text
                          style={{
                            color: Colors.whiteColor,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          EXCEL
                        </Text>
                      )}
                    </TouchableOpacity>

                    <TouchableOpacity
                      disabled={exportingCSV}
                      style={{
                        justifyContent: 'center',
                        flexDirection: 'row',
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        backgroundColor: '#4E9F7D',
                        borderRadius: 5,
                        width: switchMerchant ? 100 : 100,
                        paddingHorizontal: 10,
                        height: switchMerchant ? 35 : 40,
                        alignItems: 'center',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,
                      }}
                      onPress={() => { exportAsCSV() }}>
                      {exportingCSV ? (
                        <ActivityIndicator
                          size={'small'}
                          color={Colors.whiteColor}
                        />
                      ) : (
                        <Text
                          style={{
                            color: Colors.whiteColor,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          CSV
                        </Text>
                      )}
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </View>
          </Modal>

          <View style={[styles.content,
          {
            width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
          },
          ]}>

            {/* Header & Search */}
            <View style={{ zindex: 2 }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  alignSelf: 'center',
                  justifyContent: 'space-between',
                  width: windowWidth * 0.87,
                  paddingLeft: 1,
                  zindex: 2,
                }}>

                {/* Display Back Button while Detail Page is Displayed */}
                {showingDetailPage !== null ? (
                  <TouchableOpacity
                    style={{
                      // marginLeft: switchMerchant
                      //   ? 0
                      //   : windowWidth <= 1823 && windowWidth >= 1820
                      //     ? 24
                      //     : 10,
                      justifyContent: 'center',
                      flexDirection: 'row',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: '#4E9F7D',
                      borderRadius: 5,
                      width: switchMerchant ? 90 : 120,
                      paddingHorizontal: 10,
                      height: switchMerchant ? 35 : 40,
                      alignItems: 'center',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                      opacity: showingDetailPage === null ? 0 : 100,
                    }}
                    onPress={() => { setShowingDetailPage(null) }}
                    disabled={showingDetailPage === null}>
                    <AntDesign
                      name="arrowleft"
                      size={switchMerchant ? 10 : 20}
                      color={Colors.whiteColor}
                    />
                    <Text
                      style={{
                        color: Colors.whiteColor,
                        marginLeft: 5,
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-Bold',
                        marginBottom: Platform.OS === 'ios' ? 0 : 2,
                      }}>
                      Summary
                    </Text>
                  </TouchableOpacity>
                ) : (
                  <View>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 20 : 26,
                        fontFamily: 'NunitoSans-Bold',
                      }}>
                      Composite Report
                    </Text>
                    <View
                      style={{
                        justifyContent: 'center',
                      }}>
                      <View
                        style={{ alignItems: 'center', flexDirection: 'row' }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 20 : 26,
                          }}>
                          {`${ositUnique.length} record`}
                        </Text>
                      </View>
                    </View>
                  </View>
                )}

                <View
                  style={{
                    flexDirection: 'row', zindex: 2,
                  }}>

                  <TouchableOpacity
                    style={{
                      justifyContent: 'center',
                      flexDirection: 'row',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: '#4E9F7D',
                      borderRadius: 5,
                      paddingHorizontal: 10,
                      height: switchMerchant ? 35 : 40,
                      alignItems: 'center',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                      marginRight: 10,
                    }}
                    onPress={() => { setExportModalVisibility(true) }}
                  >
                    <View
                      style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <Icon
                        name="download"
                        size={switchMerchant ? 10 : 20}
                        color={Colors.whiteColor}
                      />
                      <Text
                        style={{
                          color: Colors.whiteColor,
                          marginLeft: 5,
                          fontSize: switchMerchant ? 10 : 16,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        DOWNLOAD
                      </Text>
                    </View>
                  </TouchableOpacity>

                  <View
                    style={{
                      width: switchMerchant ? 200 : 250,
                      height: switchMerchant ? 35 : 40,
                      backgroundColor: 'white',
                      borderRadius: 5,
                      flexDirection: 'row',
                      alignContent: 'center',
                      alignItems: 'center',
                      shadowColor: '#000',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 3,
                      borderWidth: 1,
                      borderColor: '#E5E5E5',
                    }}>
                    <Icon
                      name="search"
                      size={switchMerchant ? 13 : 18}
                      color={Colors.primaryColor}
                      style={{ marginLeft: 15 }}
                    />
                    <TextInput
                      underlineColorAndroid={Colors.whiteColor}
                      style={{
                        width: switchMerchant ? 180 : 250,
                        fontSize: switchMerchant ? 10 : 15,
                        fontFamily: 'NunitoSans-Regular',
                        paddingLeft: 5,
                        height: 45,
                      }}
                      clearButtonMode="while-editing"
                      placeholder=" Search"
                      onChangeText={(text) => { setReportSummarySearch(text) }}
                      placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                      value={reportSummarySearch}
                    />
                  </View>
                </View>
              </View>
            </View>

            <View style={{ width: '100%', marginTop: 15 }}>
              <View style={{
                backgroundColor: Colors.whiteColor,
                width: windowWidth * 0.87,
                height:
                  Platform.OS == 'android'
                    ? windowHeight * 0.6
                    : windowHeight * 0.66,
                marginHorizontal: 30,
                marginBottom: 10,
                alignSelf: 'center',
                borderRadius: 5,
                shadowColor: '#000',
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 3,
              }}>

                {/* Details Title */}
                {showingDetailPage !== null ? (
                  <View style={{ flexDirection: 'row' }}>
                    <View
                      style={{
                        flexDirection: 'row',
                        width: windowWidth * 0.875,
                        alignSelf: 'flex-end',
                      }}>
                      <Text
                        style={{
                          marginLeft: 20,
                          marginTop: 10,
                          fontFamily: 'NunitoSans-Regular',
                          fontSize: switchMerchant ? 14 : 22,
                          alignSelf: 'center',
                        }}>
                        {detailPageTitle}
                      </Text>
                    </View>
                  </View>
                ) : null}

                {/* Header */}
                {showingDetailPage === null ? (
                  <View style={{ marginTop: 10, flexDirection: 'row', }}>
                    {/* Index */}
                    <View style={{
                      flexDirection: 'row',
                      width: '5%',
                      justifyContent: 'flex-start',
                      alignItems: 'center',
                      paddingLeft: 10,
                      borderRightWidth: 1,
                      borderRightColor: 'lightgrey',
                    }}>
                      <Text style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Bold',
                      }}>
                        {'No.'}
                      </Text>
                    </View>

                    {/* Name */}
                    <View style={{
                      flexDirection: 'row',
                      width: '50%',
                      justifyContent: 'flex-start',
                      alignItems: 'center',
                      paddingLeft: 10,
                      borderRightWidth: 1,
                      borderRightColor: 'lightgrey',
                    }}>
                      <TouchableOpacity onPress={() => { requestReportSummarySort('name') }}>
                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                          <Text style={{
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                            {'Name'}
                          </Text>

                          <View style={{ marginLeft: '3%' }}>
                            <Entypo
                              name="triangle-up"
                              size={switchMerchant ? 7 : 14}
                              color={
                                reportSummarySorting.key === 'name' && reportSummarySorting.direction === 'asc'
                                  ? Colors.secondaryColor
                                  : Colors.descriptionColor
                              } />

                            <Entypo
                              name="triangle-down"
                              size={switchMerchant ? 7 : 14}
                              color={
                                reportSummarySorting.key === 'name' && reportSummarySorting.direction === 'desc'
                                  ? Colors.secondaryColor
                                  : Colors.descriptionColor
                              } />
                          </View>
                        </View>
                      </TouchableOpacity>
                    </View>

                    {/* Last Update */}
                    <View
                      style={{
                        flexDirection: 'row',
                        width: '15%',
                        borderRightWidth: 1,
                        borderRightColor: 'lightgrey',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                        paddingLeft: 10,
                      }}>
                      <TouchableOpacity onPress={() => { requestReportSummarySort('latestCreatedDate') }}>
                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                          <Text style={{
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                            {'Latest Updated'}
                          </Text>

                          <View style={{ marginLeft: '3%' }}>
                            <Entypo
                              name="triangle-up"
                              size={switchMerchant ? 7 : 14}
                              color={
                                reportSummarySorting.key === 'latestCreatedDate' && reportSummarySorting.direction === 'asc'
                                  ? Colors.secondaryColor
                                  : Colors.descriptionColor
                              } />

                            <Entypo
                              name="triangle-down"
                              size={switchMerchant ? 7 : 14}
                              color={
                                reportSummarySorting.key === 'latestCreatedDate' && reportSummarySorting.direction === 'desc'
                                  ? Colors.secondaryColor
                                  : Colors.descriptionColor
                              } />
                          </View>
                        </View>
                      </TouchableOpacity>
                    </View>
                  </View>
                ) : (
                  <View style={{ marginTop: 10, flexDirection: 'row' }}>
                    {/* Index */}
                    <View style={{
                      flexDirection: 'row',
                      width: '5%',
                      borderRightWidth: 1,
                      borderRightColor: 'lightgrey',
                      alignItems: 'center',
                      justifyContent: 'flex-start',
                      paddingLeft: 10,
                    }}>
                      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <Text style={{
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                          {'No.'}
                        </Text>
                      </View>
                    </View>

                    {/* Name */}
                    <View style={{
                      flexDirection: 'row',
                      width: '20%',
                      borderRightWidth: 1,
                      borderRightColor: 'lightgrey',
                      alignItems: 'center',
                      justifyContent: 'flex-start',
                      paddingLeft: 10,
                    }}>
                      <TouchableOpacity onPress={() => { requestReportDetailsSort('name') }}>
                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                          <Text style={{
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                            {'Name'}
                          </Text>

                          <View style={{ marginLeft: '3%' }}>
                            <Entypo
                              name="triangle-up"
                              size={switchMerchant ? 7 : 14}
                              color={
                                reportDetailsSorting.key === 'name' && reportDetailsSorting.direction === 'asc'
                                  ? Colors.secondaryColor
                                  : Colors.descriptionColor
                              } />

                            <Entypo
                              name="triangle-down"
                              size={switchMerchant ? 7 : 14}
                              color={
                                reportDetailsSorting.key === 'name' && reportDetailsSorting.direction === 'desc'
                                  ? Colors.secondaryColor
                                  : Colors.descriptionColor
                              } />
                          </View>
                        </View>
                      </TouchableOpacity>
                    </View>

                    {/* Date */}
                    <View
                      style={{
                        flexDirection: 'row',
                        width: '15%',
                        borderRightWidth: 1,
                        borderRightColor: 'lightgrey',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                        paddingLeft: 10,
                      }}>
                      <TouchableOpacity onPress={() => { requestReportDetailsSort('createdAt') }}>
                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                          <Text style={{
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                            {'Date'}
                          </Text>

                          <View style={{ marginLeft: '3%' }}>
                            <Entypo
                              name="triangle-up"
                              size={switchMerchant ? 7 : 14}
                              color={
                                reportDetailsSorting.key === 'createdAt' && reportDetailsSorting.direction === 'asc'
                                  ? Colors.secondaryColor
                                  : Colors.descriptionColor
                              } />

                            <Entypo
                              name="triangle-down"
                              size={switchMerchant ? 7 : 14}
                              color={
                                reportDetailsSorting.key === 'createdAt' && reportDetailsSorting.direction === 'desc'
                                  ? Colors.secondaryColor
                                  : Colors.descriptionColor
                              } />
                          </View>
                        </View>
                      </TouchableOpacity>
                    </View>

                    {/* Before */}
                    <View
                      style={{
                        flexDirection: 'row',
                        width: '15%',
                        borderRightWidth: 1,
                        borderRightColor: 'lightgrey',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                        paddingLeft: 10,
                      }}>
                      <TouchableOpacity onPress={() => { requestReportDetailsSort('bf') }}>
                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                          <Text style={{
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                            {'Before'}
                          </Text>

                          <View style={{ marginLeft: '3%' }}>
                            <Entypo
                              name="triangle-up"
                              size={switchMerchant ? 7 : 14}
                              color={
                                reportDetailsSorting.key === 'bf' && reportDetailsSorting.direction === 'asc'
                                  ? Colors.secondaryColor
                                  : Colors.descriptionColor
                              } />

                            <Entypo
                              name="triangle-down"
                              size={switchMerchant ? 7 : 14}
                              color={
                                reportDetailsSorting.key === 'bf' && reportDetailsSorting.direction === 'desc'
                                  ? Colors.secondaryColor
                                  : Colors.descriptionColor
                              } />
                          </View>
                        </View>
                      </TouchableOpacity>
                    </View>

                    {/* Quantity */}
                    <View
                      style={{
                        flexDirection: 'row',
                        width: '9%',
                        borderRightWidth: 1,
                        borderRightColor: 'lightgrey',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                        paddingLeft: 10,
                      }}>
                      <TouchableOpacity onPress={() => { requestReportDetailsSort('am') }}>
                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                          <Text style={{
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                            {'Qty'}
                          </Text>

                          <View style={{
                            marginLeft: '3%',
                            justifyContent: 'space-between',
                          }}>
                            <Entypo
                              name="triangle-up"
                              size={switchMerchant ? 7 : 14}
                              color={
                                reportDetailsSorting.key === 'am' && reportDetailsSorting.direction === 'asc'
                                  ? Colors.secondaryColor
                                  : Colors.descriptionColor
                              } />

                            <Entypo
                              name="triangle-down"
                              size={switchMerchant ? 7 : 14}
                              color={
                                reportDetailsSorting.key === 'am' && reportDetailsSorting.direction === 'desc'
                                  ? Colors.secondaryColor
                                  : Colors.descriptionColor
                              } />
                          </View>
                        </View>
                      </TouchableOpacity>
                    </View>

                    {/* After */}
                    <View
                      style={{
                        flexDirection: 'row',
                        width: '15%',
                        borderRightWidth: 1,
                        borderRightColor: 'lightgrey',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                        paddingLeft: 10,
                      }}>
                      <TouchableOpacity onPress={() => { requestReportDetailsSort('af') }}>
                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                          <Text style={{
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                            {'After'}
                          </Text>
                          <View style={{ marginLeft: '3%' }}>
                            <Entypo
                              name="triangle-up"
                              size={switchMerchant ? 7 : 14}
                              color={
                                reportDetailsSorting.key === 'af' && reportDetailsSorting.direction === 'asc'
                                  ? Colors.secondaryColor
                                  : Colors.descriptionColor
                              } />

                            <Entypo
                              name="triangle-down"
                              size={switchMerchant ? 7 : 14}
                              color={
                                reportDetailsSorting.key === 'af' && reportDetailsSorting.direction === 'desc'
                                  ? Colors.secondaryColor
                                  : Colors.descriptionColor
                              } />
                          </View>
                        </View>
                      </TouchableOpacity>
                    </View>

                    {/* Batch ID */}
                    {/* <View style={{
                      flexDirection: 'row',
                      width: '12%',
                      borderRightWidth: 1,
                      borderRightColor: 'lightgrey',
                      alignItems: 'center',
                      justifyContent: 'flex-start',
                      paddingLeft: 10,
                    }}>
                      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <Text style={{
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                          {'Batch ID'}
                        </Text>
                      </View>
                    </View> */}

                    {/* Type */}
                    <View style={{
                      flexDirection: 'row',
                      width: '8%',
                      alignItems: 'center',
                      justifyContent: 'flex-start',
                      paddingLeft: 10,
                    }}>
                      <TouchableOpacity onPress={() => { requestReportDetailsSort('unit') }}>
                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                          <Text style={{
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                            {'Type'}
                          </Text>

                          <View style={{ marginLeft: '3%' }}>
                            <Entypo
                              name="triangle-up"
                              size={switchMerchant ? 7 : 14}
                              color={
                                reportDetailsSorting.key === 'unit' && reportDetailsSorting.direction === 'asc'
                                  ? Colors.secondaryColor
                                  : Colors.descriptionColor
                              } />

                            <Entypo
                              name="triangle-down"
                              size={switchMerchant ? 7 : 14}
                              color={
                                reportDetailsSorting.key === 'unit' && reportDetailsSorting.direction === 'desc'
                                  ? Colors.secondaryColor
                                  : Colors.descriptionColor
                              } />
                          </View>
                        </View>
                      </TouchableOpacity>
                    </View>
                  </View>
                )}

                {/* FlatList */}
                {showingDetailPage !== null ? (
                  <>
                    {ositDict[showingDetailPage].length > 0 ? (
                      <FlatList
                        data={ositDict[showingDetailPage].slice((currentDetailsPage - 1) * perPage, currentDetailsPage * perPage)}
                        renderItem={renderOsitRecord}
                        showsVerticalScrollIndicator={false}
                        keyExtractor={(item) => item.uniqueId}
                        style={{ marginTop: 10 }}
                        initialNumToRender={10}
                      />
                    ) : (
                      <View
                        style={{
                          alignItems: 'center',
                          justifyContent: 'center',
                          height: '71%',
                        }}>
                        <Text style={{ color: Colors.descriptionColor }}>
                          - No Data Available -
                        </Text>
                      </View>
                    )}
                  </>
                ) : (
                  <>
                    {ositUnique.length > 0 ? (
                      <FlatList
                        data={ositUnique.slice((currentPage - 1) * perPage, currentPage * perPage)}
                        renderItem={renderGroupedOsit}
                        showsVerticalScrollIndicator={false}
                        keyExtractor={(item) => item.outletSupplyItemId}
                        style={{ marginTop: 10 }}
                        initialNumToRender={10}
                      />
                    ) : (
                      <View
                        style={{
                          alignItems: 'center',
                          justifyContent: 'center',
                          height: '71%',
                        }}>
                        <Text style={{ color: Colors.descriptionColor }}>
                          - No Data Available -
                        </Text>
                      </View>
                    )}
                  </>
                )}
              </View>

              {/* Paging Settings */}
              <View style={{
                flexDirection: 'row',
                marginTop: 10,
                width: windowWidth * 0.87,
                alignItems: 'center',
                alignSelf: 'center',
                justifyContent: 'flex-end',
                top: Platform.OS === 'ios' && keyboardHeight > 0 ? -keyboardHeight * 0.9 : 0,
                paddingHorizontal: keyboardHeight > 0 ? 10 : 0,
                paddingBottom: 5,
              }}>
                <Text style={{
                  fontSize: switchMerchant ? 10 : 14,
                  fontFamily: 'NunitoSans-Bold',
                  marginRight: '1%',
                }}>
                  Items Showed
                </Text>

                <View
                  style={{
                    width: Platform.OS === 'ios' ? 65 : '13%', //65,
                    height: switchMerchant ? 20 : 35,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 10,
                    justifyContent: 'center',
                    paddingHorizontal: Platform.OS === 'ios' ? 0 : 0,
                    //paddingLeft:switchMerchant ? '4.5%': Platform.OS === 'ios' ? '2%' : '4%',
                    // paddingTop: '-60%',
                    borderWidth: 1,
                    borderColor: '#E5E5E5',
                    marginRight: '1%',
                  }}>
                  {/* <RNPickerSelect
                    placeholder={{}}
                    useNativeAndroidPickerStyle={false}
                    style={{
                      inputIOS: {
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                        textAlign: 'center',
                      },
                      inputAndroid: {
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                        justifyContent: 'center',
                        textAlign: 'center',
                        height: 40,
                        color: 'black',
                      },
                      inputAndroidContainer: { width: '100%' },
                      height: 35,

                      chevronContainer: {
                        display: 'none',
                      },
                      chevronDown: {
                        display: 'none',
                      },
                      chevronUp: {
                        display: 'none',
                      },
                    }}
                    items={TABLE_PAGE_SIZE_DROPDOWN_LIST}
                    value={perPage}
                    onValueChange={(value) => {
                      setPerPage(value);
                    }}
                  /> */}

                  <DropDownPicker
                    style={{
                      backgroundColor: Colors.fieldtBgColor,
                      width: '100%',
                      height: 40,
                      borderRadius: 10,
                      borderWidth: 1,
                      borderColor: "#E5E5E5",
                      flexDirection: "row",
                    }}
                    dropDownContainerStyle={{
                      width: '100%',
                      backgroundColor: Colors.fieldtBgColor,
                      borderColor: "#E5E5E5",
                    }}
                    labelStyle={{
                      marginLeft: 5,
                      flexDirection: "row",
                    }}
                    textStyle={{
                      fontSize: 14,
                      fontFamily: 'NunitoSans-Regular',

                      marginLeft: 5,
                      paddingVertical: 10,
                      flexDirection: "row",
                    }}
                    selectedItemContainerStyle={{
                      flexDirection: "row",
                    }}

                    showArrowIcon={true}
                    ArrowDownIconComponent={({ style }) => (
                      <Ionicon
                        size={25}
                        color={Colors.fieldtTxtColor}
                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                        name="chevron-down-outline"
                      />
                    )}
                    ArrowUpIconComponent={({ style }) => (
                      <Ionicon
                        size={25}
                        color={Colors.fieldtTxtColor}
                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                        name="chevron-up-outline"
                      />
                    )}

                    showTickIcon={true}
                    TickIconComponent={({ press }) => (
                      <Ionicon
                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                        color={
                          press ? Colors.fieldtBgColor : Colors.primaryColor
                        }
                        name={'md-checkbox'}
                        size={25}
                      />
                    )}
                    placeholder={'Select a Type'}
                    placeholderStyle={{
                      color: Colors.fieldtTxtColor,
                      // marginTop: 15,
                    }}
                    // searchable
                    // searchableStyle={{
                    //   paddingHorizontal: windowWidth * 0.0079,
                    // }}
                    value={perPage}
                    items={TABLE_PAGE_SIZE_DROPDOWN_LIST.concat({
                      label: 'All',
                      // value: !showDetails
                      //   ? dailySalesDetailsList.length
                      //   : transactionTypeSalesDetails.length,
                      value: showingDetailPage === null
                      ? ositUnique.length
                      : ositDict[showingDetailPage].length
                    })}
                    // multiple={true}
                    // multipleText={`${item.tagIdList.length} Tag(s)`}
                    onSelectItem={(item) => {
                      setPerPage(item.value);
                      // var currentPageTemp =
                      //   text.length > 0 ? parseInt(text) : 1;

                      // setCurrentPage(
                      //   currentPageTemp > pageCount
                      //     ? pageCount
                      //     : currentPageTemp < 1
                      //       ? 1
                      //       : currentPageTemp,
                      // );
                    }}
                    open={openPage}
                    setOpen={setOpenPage}
                    dropDownDirection="TOP"
                  />
                </View>

                <Text
                  style={{
                    fontSize: switchMerchant ? 10 : 14,
                    fontFamily: 'NunitoSans-Bold',
                    marginRight: '1%',
                  }}>
                  Page
                </Text>

                <View
                  style={{
                    width: switchMerchant ? 65 : 70,
                    height: switchMerchant ? 20 : 35,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 10,
                    justifyContent: 'center',
                    paddingHorizontal: 22,
                    borderWidth: 1,
                    borderColor: '#E5E5E5',
                  }}>

                  <TextInput
                    placeholder={showingDetailPage ? currentDetailsPage.toString() : currentPage.toString()}
                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                    style={{
                      color: 'black',
                      fontSize: switchMerchant ? 10 : 14,
                      fontFamily: 'NunitoSans-Regular',
                      marginTop: Platform.OS === 'ios' ? 0 : -15,
                      marginBottom: Platform.OS === 'ios' ? 0 : -15,
                      textAlign: 'center',
                      width: '100%',
                    }}
                    value={showingDetailPage ? currentDetailsPage.toString() : currentPage.toString()}
                    keyboardType={'numeric'}
                    onChangeText={(text) => {
                      const currentPageTemp = text.length > 0 ? parseInt(text, 10) : 1;
                      if (showingDetailPage) {
                        setCurrentDetailsPage(currentPageTemp);
                      } else {
                        setCurrentPage(currentPageTemp);
                      }
                    }}
                    onEndEditing={() => {
                      if (showingDetailPage) {
                        setCurrentDetailsPage(prev => Math.min(Math.max(prev, 1), totalDetailRecordPages));
                      } else {
                        setCurrentPage(prev => Math.min(Math.max(prev, 1), totalDetailSummaryPages));
                      }
                    }}
                  />
                </View>

                <Text
                  style={{
                    fontSize: switchMerchant ? 10 : 14,
                    fontFamily: 'NunitoSans-Bold',
                    marginLeft: '1%',
                    marginRight: '1%',
                  }}>
                  of {showingDetailPage ? totalDetailRecordPages : totalDetailSummaryPages}
                </Text>

                <TouchableOpacity
                  style={{
                    width: switchMerchant ? 30 : 45,
                    height: switchMerchant ? 20 : 28,
                    backgroundColor: Colors.primaryColor,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                  onPress={() => { prevPage() }}
                >
                  <MaterialIcons
                    name="keyboard-arrow-left"
                    size={switchMerchant ? 20 : 25}
                    style={{ color: Colors.whiteColor }}
                  />
                </TouchableOpacity>

                <TouchableOpacity
                  style={{
                    width: switchMerchant ? 30 : 45,
                    height: switchMerchant ? 20 : 28,
                    backgroundColor: Colors.primaryColor,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                  onPress={() => {
                    nextPage();
                  }}>
                  <MaterialIcons
                    name="keyboard-arrow-right"
                    size={switchMerchant ? 20 : 25}
                    style={{ color: Colors.whiteColor }}
                  />
                </TouchableOpacity>

              </View>
            </View>
          </View>
        </ScrollView>
      </View>
    </UserIdleWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
  },
  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
  },
  content: {
    padding: 20,
    paddingBottom: 80,
    width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
    backgroundColor: Colors.highlightColor,
    flex: 1,
    flexDirection: 'column',
  },
  modalSaveButton: {
    width: Dimensions.get('window').width * 0.15,
    backgroundColor: Colors.fieldtBgColor,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,

    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,

    marginVertical: 10,
  },
  headerLeftStyle: {
    width: Dimensions.get('screen').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default CompositeReportScreen;
