import React, {
  Component,
  useReducer,
  useState,
  useEffect,
  useCallback,
} from "react";
import {
  StyleSheet,
  Image,
  View,
  Text,
  Alert,
  Dimensions,
  TouchableOpacity,
  Switch,
  FlatList,
  Modal,
  TextInput,
  KeyboardAvoidingView,
  PermissionsAndroid,
  Platform,
  ActivityIndicator,
  PlatformColor,
  Picker,
  ScrollView,
  useWindowDimensions,
} from "react-native";
import Select from "react-select";
import Colors from "../constant/Colors";
import firebase from "firebase";
import SideBar from "./SideBar";
import Feather from "react-native-vector-icons/Feather";
import * as User from "../util/User";
import ApiClient from "../util/ApiClient";
import API from "../constant/API";
import Icon from "react-native-vector-icons/Feather";
import FontAwesome5 from "react-native-vector-icons/FontAwesome5";
import DropDownPicker from "react-native-dropdown-picker";
import AsyncStorage from "@react-native-async-storage/async-storage";
// import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
import Styles from "../constant/Styles";
// import { isTablet } from 'react-native-device-detection';
import { MerchantStore } from "../store/merchantStore";
import {
  uploadImageToFirebaseStorage,
  uploadImageToFirebaseStorage64,
  _base64ToArrayBuffer,
  getTransformForModalInsideNavigation,
  getTransformForScreenInsideNavigation,
} from "../util/common";
import "react-native-get-random-values";
import { v4 as uuidv4 } from "uuid";
import { UserStore } from "../store/userStore";
import { OutletStore } from "../store/outletStore";
import {
  EMAIL_REPORT_TYPE,
  ROLE_TYPE_PARSED,
  PRIVILEGES_NAME,
  ROLE_TYPE,
  EXPAND_TAB_TYPE,
  SCREEN_NAME,
  SCREEN_NAME_PARSED,
} from "../constant/common";
import { convertArrayToCSV, generateEmailReport } from "../util/common";
// import RNFetchBlob from 'rn-fetch-blob';
import moment from "moment";
// import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
// import RNPickerSelect from 'react-native-picker-select';
// import { useKeyboard } from '../hooks';
import XLSX from "xlsx";

//////////////////////////// Test clocked in time ////////////////////////////
import EvilIcons from "react-native-vector-icons/EvilIcons";
import DateTimePickerModal from "react-native-modal-datetime-picker";
import { CommonStore } from "../store/commonStore";
import AntDesign from "react-native-vector-icons/AntDesign";
import AsyncImage from "../components/asyncImage";
// import { firebase } from '@react-native-firebase/firestore';
import { Collections } from "../constant/firebase";
import APILocal from "../util/apiLocalReplacers";
import { useFocusEffect } from "@react-navigation/native";
import { ReactComponent as Upload } from "../assets/svg/Upload.svg";
import { ReactComponent as Download } from "../assets/svg/Download.svg";
import { ReactComponent as Dish } from "../assets/svg/Dish.svg";
import { ReactComponent as Coins } from "../assets/svg/Coins.svg";
import { ReactComponent as Hand } from "../assets/svg/Hand.svg";
import { ReactComponent as GCoin } from "../assets/svg/GCoin.svg";
import { ReactComponent as GCalendar } from "../assets/svg/GCalendar.svg";
import { ReactComponent as Magnify } from "../assets/svg/Magnify.svg";
import { CSVLink } from "react-csv";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import "../constant/datePicker.css";
import { ReactComponent as Time } from "../assets/svg/Times.svg";
import TimeKeeper from "react-timekeeper";
import { useFilePicker } from "use-file-picker";
import { file } from "jszip";
import personicon from "../assets/image/default-profile.png";
import headerLogo from "../assets/image/logo.png";
import { sliceUnicodeStringV2WithDots } from "../util/common";
import MultiSelect from "react-multiple-select-dropdown-lite";
import Ionicon from "react-native-vector-icons/Ionicons";
import "../constant/styles.css";

// import UserIdleWrapper from '../components/userIdleWrapper';

const EmployeeScreen = (props) => {
  //port til aug 11 changes
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  // const [isMounted, setIsMounted] = useState(true);

  // useFocusEffect(
  //   useCallback(() => {
  //     setIsMounted(true);
  //     return () => {
  //       setIsMounted(false);
  //     };
  //   }, [])
  // );

  ///////////////////////////////////////////////////////////

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  // const [keyboardHeight] = useKeyboard();

  const [employeeList, setEmployeeList] = useState([]);
  const [employeeListLength, setEmployeeListLength] = useState(0);
  const [lists, setLists] = useState([]);
  const [list1, setList1] = useState(true);
  const [searchList, setSearchList] = useState(false);
  const [addEmployee, setAddEmployee] = useState(false);
  const [addEmployeeItem, setAddEmployeeItem] = useState(true);
  const [position, setPosition] = useState("");
  // const [// outletId, set// outletId] = useState(1);
  const [outletId, setOutletId] = useState(User.getOutletId());
  const [waiter, setWaiter] = useState("");
  const [search, setSearch] = useState("");
  const [countries, setCountries] = useState(["uk"]);
  const [showDetail, setShowDetail] = useState(false);
  const [confirmRemove, setConfirmRemove] = useState(false);
  const [visible, setVisible] = useState(false);
  const [showEmployee, setShowEmployee] = useState([]);
  const [switchMerchant, setSwitchMerchant] = useState(false);
  const [loading, setLoading] = useState(false);
  const [role, setRole] = useState("frontliner");
  const [pin, setPin] = useState("");
  const [number, setNumber] = useState("");
  const [password, setPassword] = useState("");
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [username, setUsername] = useState("");
  const [privileges, setPrivileges] = useState([]);
  const [screensToBlock, setScreensToBlock] = useState([]);

  const [image, setImage] = useState("");
  const [imageType, setImageType] = useState("");
  const [isImageChanged, setIsImageChanged] = useState(false);
  const [
    openFileSelector,
    {
      plainFiles,
      filesContent,
      loading: loadingImageInput,
      clear: clearImageContainer,
      errors,
    },
  ] = useFilePicker({
    readAs: "DataURL",
    accept: ["image/*"],
    multiple: false,
  });

  // Handle selected image file
  useEffect(() => {
    if (plainFiles.length && filesContent.length && !loadingImageInput) {
      setImage(filesContent[0].content);
      setImageType(filesContent[0].name.slice(filesContent[0].name.lastIndexOf(".")));
      setIsImageChanged(true);
    }

    if (errors.length) console.error(errors);
  }, [plainFiles, filesContent, loadingImageInput, errors]);

  //////////////////////////// Test clocked in time ////////////////////////////
  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  const [pickDate, setPickDate] = useState("");
  const [dayOfWeek, setDayOfWeek] = useState("");

  const [showDateTimePicker1, setShowDateTimePicker1] = useState(false);
  const [pickTime, setPickTime] = useState("");
  const [regularTime, setRegularTime] = useState("");
  const [overTime, setOverTime] = useState("");

  //////////////////////////////////////////////////////////////

  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const allOutletsDict = MerchantStore.useState((s) => s.allOutletsDict);
  const allOutlets = MerchantStore.useState((s) => s.allOutlets);
  const selectedOutletEmployeeEdit = CommonStore.useState(
    (s) => s.selectedOutletEmployeeEdit
  );

  const [targetOutletDropdownList, setTargetOutletDropdownList] = useState([]);
  const [selectedTargetOutletId, setSelectedTargetOutletId] = useState("");
  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView
  );

  const [exportModalVisibility, setExportModalVisibility] = useState(false);
  const [exportEmail, setExportEmail] = useState("");
  const isLoading = CommonStore.useState((s) => s.isLoading);
  const [isLoadingExcel, setIsLoadingExcel] = useState(false);
  const [isLoadingCsv, setIsLoadingCsv] = useState(false);

  const [clockInModal, setClockInModal] = useState(false);
  const [clockOutModal, setClockOutModal] = useState(false);
  const [showShiftDatePicker, setShowShiftDatePicker] = useState(false);
  const [showShiftTimePicker, setShowShiftTimePicker] = useState(false);
  const [shiftDate, setShiftDate] = useState(moment());
  const [shiftTime, setShiftTime] = useState(moment());

  const [temp, setTemp] = useState("");

  const [currEditEmployee, setCurrEditEmployee] = useState({});
  // new date picker
  const [rev_date, setRev_date] = useState(moment(Date.now()));
  const [rev_time, setRev_time] = useState(
    moment(Date.now()).format("hh:mm A")
  );
  const [clockInTimePicker, setClockInTimePicker] = useState(false);
  const [clockOutTimePicker, setClockOutTimePicker] = useState(false);
  const [clockTimePicker, setClockTimePicker] = useState(false);
  useEffect(() => {
    setTargetOutletDropdownList(
      allOutlets.map((outlet) => ({
        label: outlet.name,
        value: outlet.uniqueId,
      }))
    );

    // console.log('targetOutletDropdownList');
    // console.log(targetOutletDropdownList);

    if (allOutlets.length > 0) {
      setSelectedTargetOutletId(currOutletId);
    }
  }, [allOutlets, currOutletId]);

  //////////////////////////////////////////////////////////////

  const [outletDropdownList, setOutletDropdownList] = useState([]);
  const [selectedOutletId, setSelectedOutletId] = useState("");

  const merchantId = UserStore.useState((s) => s.merchantId);

  const allOutletsEmployees = OutletStore.useState(
    (s) => s.allOutletsEmployees
  );

  const userId = UserStore.useState((s) => s.firebaseUid);
  const userName = UserStore.useState((s) => s.name);
  const userRole = UserStore.useState((s) => s.role);
  const merchantName = MerchantStore.useState((s) => s.name);

  const isMasterAccount = UserStore.useState((s) => s.isMasterAccount);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  // select image
  useEffect(() => {
    if (plainFiles.length && filesContent.length && !loadingImageInput) {
      setImage(filesContent[0].content);
      setImageType(
        filesContent[0].name.slice(filesContent[0].name.lastIndexOf("."))
      );
      setIsImageChanged(true);
    }

    if (errors.length) console.error(errors);
  }, [plainFiles, filesContent, loadingImageInput, errors]);

  useEffect(() => {
    setOutletDropdownList(
      allOutlets.filter(outlet => {
        if (outlet.uniqueId === currOutletId || isMasterAccount) {
          return true;
        }
      }).map((item) => ({ label: item.name, value: item.uniqueId })),
    );

    if (selectedOutletId === "" && allOutlets.length > 0) {
      setSelectedOutletId(allOutlets[0].uniqueId);
    }

    var selectedOutletIdTemp = '';

    if (selectedOutletId === '' && allOutlets.length > 0) {
      selectedOutletIdTemp = allOutlets[0].uniqueId;
    }

    if (selectedOutletEmployeeEdit && selectedOutletEmployeeEdit.outletId &&
      allOutlets.find(outlet => outlet.uniqueId && selectedOutletEmployeeEdit.outletId)) {
      selectedOutletIdTemp = selectedOutletEmployeeEdit.outletId;
    }

    setSelectedOutletId(selectedOutletIdTemp);
  }, [allOutlets, selectedOutletEmployeeEdit, currOutletId, isMasterAccount]);

  useEffect(() => {
    if (selectedOutletEmployeeEdit) {
      setName(selectedOutletEmployeeEdit.name);
      setUsername(selectedOutletEmployeeEdit.uniqueName);
      setRole(selectedOutletEmployeeEdit.role);
      // setSelectedOutletId(selectedOutletEmployeeEdit.selectedOutletId);
      setNumber(selectedOutletEmployeeEdit.number);
      setEmail(selectedOutletEmployeeEdit.email);
      // setPassword(selectedOutletEmployeeEdit.password);
      setPassword("password");
      setImage(selectedOutletEmployeeEdit.image);
      setIsImageChanged(false);
      setPin(selectedOutletEmployeeEdit.pinNo || "");
      setPrivileges(selectedOutletEmployeeEdit.privileges || []);
      setScreensToBlock(selectedOutletEmployeeEdit.screensToBlock || []);
    } else {
      setName("");
      setUsername("");
      setRole("frontliner");
      //setSelectedOutletId('');
      setNumber("");
      setEmail("");
      setPassword("");
      setImage("");
      setIsImageChanged(false);
      setPin("");
      setPrivileges([]);
      setScreensToBlock([]);
    }
  }, [selectedOutletEmployeeEdit, addEmployee, addEmployeeItem]);

  const setState = () => { };

  navigation.dangerouslyGetParent().setOptions({
    tabBarVisible: false,
  });

  const currOutletShiftStatus = OutletStore.useState(
    (s) => s.currOutletShiftStatus
  );

  // const [selectedOutletList, setSelectedOutletList] = useState([]); // multi-outlets

  const [openRole, setOpenRole] = useState(false);
  const [openOutlet, setOpenOutlet] = useState(false);
  const [openPriv, setOpenPriv] = useState(false);
  const [openSTB, setOpenSTB] = useState(false);

  // var outletNames = [];

  // for (var i = 0; i < allOutlets.length; i++) {
  //   for (var j = 0; j < selectedOutletList.length; j++) {
  //     if (selectedOutletList.includes(allOutlets[i].uniqueId)) {
  //       outletNames.push(allOutlets[i].name);
  //       break;
  //     }
  //   }
  // }

  // useEffect(() => {
  //   setOutletDropdownList(
  //     allOutlets.map((item) => {
  //       return { label: item.name, value: item.uniqueId };
  //     })
  //   );
  // }, [allOutlets]);

  var targetOutletDropdownListTemp = allOutlets.map((outlet) => ({
    label: sliceUnicodeStringV2WithDots(outlet.name, 20),
    value: outlet.uniqueId,
  }));

  const [openO, setOpenO] = useState(false);

  useEffect(() => {
    CommonStore.update((s) => {
      s.outletSelectDropdownView = () => {
        return (
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              borderRadius: 8,
              width: 200,
              backgroundColor: "white",
            }}
          >
            {currOutletId.length > 0 &&
              allOutlets.find((item) => item.uniqueId === currOutletId) ? (
              <DropDownPicker
                style={{
                  backgroundColor: Colors.fieldtBgColor,
                  width: 200,
                  height: 40,
                  borderRadius: 10,
                  borderWidth: 1,
                  borderColor: "#E5E5E5",
                  flexDirection: "row",
                }}
                dropDownContainerStyle={{
                  width: 200,
                  backgroundColor: Colors.fieldtBgColor,
                  borderColor: "#E5E5E5",
                }}
                labelStyle={{
                  marginLeft: 5,
                  flexDirection: "row",
                }}
                textStyle={{
                  fontSize: 14,
                  fontFamily: 'NunitoSans-Regular',

                  marginLeft: 5,
                  paddingVertical: 10,
                  flexDirection: "row",
                }}
                selectedItemContainerStyle={{
                  flexDirection: "row",
                }}

                showArrowIcon={true}
                ArrowDownIconComponent={({ style }) => (
                  <Ionicon
                    size={25}
                    color={Colors.fieldtTxtColor}
                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                    name="chevron-down-outline"
                  />
                )}
                ArrowUpIconComponent={({ style }) => (
                  <Ionicon
                    size={25}
                    color={Colors.fieldtTxtColor}
                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                    name="chevron-up-outline"
                  />
                )}

                showTickIcon={true}
                TickIconComponent={({ press }) => (
                  <Ionicon
                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                    color={
                      press ? Colors.fieldtBgColor : Colors.primaryColor
                    }
                    name={'md-checkbox'}
                    size={25}
                  />
                )}
                placeholderStyle={{
                  color: Colors.fieldtTxtColor,
                  // marginTop: 15,
                }}
                dropDownDirection="BOTTOM"
                placeholder="Choose Outlet"
                items={targetOutletDropdownListTemp}
                value={currOutletId}
                onSelectItem={(item) => {
                  if (item) { // if choose the same option again, value = ''
                    MerchantStore.update((s) => {
                      s.currOutletId = item.value;
                      s.currOutlet =
                        allOutlets.find(
                          (outlet) => outlet.uniqueId === item.value
                        ) || {};
                    });
                  }

                  CommonStore.update((s) => {
                    s.shiftClosedModal = false;
                  });
                }}
                open={openO}
                setOpen={setOpenO}
              />
            ) : (
              <ActivityIndicator size={"small"} color={Colors.whiteColor} />
            )}

            {/* <Select

              placeholder={"Choose Outlet"}
              onChange={(items) => {
                setSelectedOutletList(items);
              }}
              options={outletDropdownList}
              isMulti
            /> */}
          </View>
        );
      };
    });
  }, [allOutlets, currOutletId, isLoading, currOutletShiftStatus]);

  navigation.setOptions({
    headerLeft: () => (
      <View
        style={[
          styles.headerLeftStyle,
          {
            width: windowWidth * 0.17,
          },
        ]}
      >
        <img src={headerLogo} width={124} height={26} />
        {/* <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        /> */}
      </View>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            justifyContent: "center",
            alignItems: "center",
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            //width:  "55%",
          },
          Dimensions.get("screen").width <= 768
            ? { right: Dimensions.get("screen").width * 0.12 }
            : {},
        ]}
      >
        <Text
          style={{
            fontSize: 24,
            // lineHeight: 25,
            textAlign: "center",
            fontFamily: "NunitoSans-Bold",
            color: Colors.whiteColor,
            opacity: 1,
          }}
        >
          Orders Channel Report
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        {/* {console.log('edward test')} */}
        {/* {console.log(outletSelectDropdownView)} */}
        {outletSelectDropdownView && outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: "white",
            width: 0.5,
            height: Dimensions.get("screen").height * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
            // borderWidth: 1
          }}
        ></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate("General Settings - KooDoo BackOffice")
            }
          }}
          style={{ flexDirection: "row", alignItems: "center" }}
        >
          <Text
            style={{
              fontFamily: "NunitoSans-SemiBold",
              fontSize: 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }}
          >
            {userName}
          </Text>
          <View
            style={{
              //backgroundColor: 'red',
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "white",
            }}
          >
            <img
              src={personicon}
              width={windowHeight * 0.035}
              height={windowHeight * 0.035}
            />
            {/* <Image
              style={{
                width: windowHeight * 0.05,
              height: windowHeight * 0.05,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            /> */}
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  navigation.setOptions({
    headerLeft: () => (
      <View
        style={[
          styles.headerLeftStyle,
          {
            width: windowWidth * 0.17,
          },
        ]}
      >
        <img src={headerLogo} width={124} height={26} />
        {/* <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        /> */}
      </View>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            justifyContent: "center",
            alignItems: "center",
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            //width:  "55%",
          },
          Dimensions.get("screen").width <= 768
            ? { right: Dimensions.get("screen").width * 0.12 }
            : {},
        ]}
      >
        <Text
          style={{
            fontSize: 24,
            // lineHeight: 25,
            textAlign: "center",
            fontFamily: "NunitoSans-Bold",
            color: Colors.whiteColor,
            opacity: 1,
          }}
        >
          Employee
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        {/* {console.log('edward test')} */}
        {/* {console.log(outletSelectDropdownView)} */}
        {outletSelectDropdownView && outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: "white",
            width: 0.5,
            height: Dimensions.get("screen").height * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
            // borderWidth: 1
          }}
        ></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate("General Settings - KooDoo BackOffice")
            }
          }}
          style={{ flexDirection: "row", alignItems: "center" }}
        >
          <Text
            style={{
              fontFamily: "NunitoSans-SemiBold",
              fontSize: 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }}
          >
            {userName}
          </Text>
          <View
            style={{
              //backgroundColor: 'red',
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "white",
            }}
          >
            <img
              src={personicon}
              width={windowHeight * 0.035}
              height={windowHeight * 0.035}
            />
            {/* <Image
              style={{
                width: windowHeight * 0.05,
              height: windowHeight * 0.05,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            /> */}
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  // componentDidMount = () => {
  //   getEmployeeList();
  // }

  const renderItem = ({ item }) => (
    <TouchableOpacity
      onPress={() => {
        //setState({ showDetail: true, showEmployee: item, addEmployeeItem: false }),
        CommonStore.update((s) => {
          s.selectedOutletEmployeeEdit = item;
        });
        setAddEmployee(true);
        setAddEmployeeItem(false);
        // console.log('PRESSWS');
      }}
    >
      <View
        style={{
          backgroundColor: "#ffffff",
          flexDirection: "row",
          paddingVertical: 20,
          paddingHorizontal: 10,
          borderBottomWidth: StyleSheet.hairlineWidth,
          borderBottomColor: "#c4c4c4",
          //justifyContent: 'space-between'
          borderBottomLeftRadius: 5,
          borderBottomRightRadius: 5,
        }}
      >
        <AsyncImage
          source={{ uri: item.avatar }}
          style={{ width: 35, height: 35, borderRadius: 100, marginRight: 15 }}
        />
        <Text
          style={{
            width: switchMerchant ? "10%" : "12%",
            marginRight: 5,
            fontFamily: "NunitoSans-Regular",
            fontSize: switchMerchant ? 10 : 14,
          }}
        >
          {item.name}
        </Text>
        <Text
          style={{
            width: "20%", //15%
            marginRight: 5,
            fontFamily: "NunitoSans-Regular",
            fontSize: switchMerchant ? 10 : 14,
          }}
        >
          {allOutletsDict[item.outletId] ? allOutletsDict[item.outletId].name : 'N/A'}
        </Text>
        <Text
          style={{
            width: switchMerchant ? "10%" : "14%",
            marginRight: 5,
            fontFamily: "NunitoSans-Regular",
            fontSize: switchMerchant ? 10 : 14,
          }}
        >
          {ROLE_TYPE_PARSED[item.role]}
        </Text>
        <Text
          style={{
            width: switchMerchant ? "10%" : "10%",
            marginRight: 5,
            fontFamily: "NunitoSans-Regular",
            fontSize: switchMerchant ? 10 : 14,
          }}
        >
          {"Active"}
        </Text>
        {/* <Text style={{ width: '13%' }}>{item.number}</Text> */}
        {/* <Text style={{ width: '17.5%', marginRight: 0 }}>{item.email}</Text> */}
        {/* <Text style={{ width: '10%' }}>******</Text> */}
        {/* <Text style={{ width: '7%' }}>****</Text> */}
        {/* <Text style={{ width: '9%', marginRight: 0 }}>{item.dayOfWeek == null || item.dayOfWeek == undefined ? "-" : moment(item.dayOfWeek).format('L')}</Text> */}
        {/* <Text style={{ width: '9%', marginRight: 0 }}>{item.regularTime == null || item.regularTime == undefined ? "-" : moment(item.regularTime).format('L')}</Text> */}
        {/* <Text style={{ width: '9%', marginRight: 0 }}>{item.overTime == null || item.overTime == undefined ? "-" : moment(item.overTime).format('L')}</Text> */}
        <View
          style={{ width: switchMerchant ? "13%" : "16%" }} //11%
        >
          {!item.lastLoginAt ? (
            <Text
              style={{
                fontVariant: ["tabular-nums"],
                fontFamily: "NunitoSans-Regular",
                fontSize: switchMerchant ? 10 : 14,
              }}
            >
              {"N/A"}
            </Text>
          ) : (
            <>
              <Text
                style={{
                  fontVariant: ["tabular-nums"],
                  fontFamily: "NunitoSans-Regular",
                  fontSize: switchMerchant ? 10 : 14,
                }}
              >
                {moment(item.lastLoginAt).format("DD MMM YYYY")}
              </Text>
              <Text
                style={{
                  fontVariant: ["tabular-nums"],
                  fontFamily: "NunitoSans-Regular",
                  fontSize: switchMerchant ? 10 : 14,
                }}
              >
                {moment(item.lastLoginAt).format("hh:mm A")}
              </Text>
            </>
          )}
        </View>

        <View
          style={{ width: "16%" }} //11%
        >
          {!item.lastLogoutAt ? (
            <Text
              style={{
                fontVariant: ["tabular-nums"],
                fontFamily: "NunitoSans-Regular",
                fontSize: switchMerchant ? 10 : 14,
              }}
            >
              {"N/A"}
            </Text>
          ) : (
            <>
              <Text
                style={{
                  fontVariant: ["tabular-nums"],
                  fontFamily: "NunitoSans-Regular",
                  fontSize: switchMerchant ? 10 : 14,
                }}
              >
                {moment(item.lastLogoutAt).format("DD MMM YYYY")}
              </Text>
              <Text
                style={{
                  fontVariant: ["tabular-nums"],
                  fontFamily: "NunitoSans-Regular",
                  fontSize: switchMerchant ? 10 : 14,
                }}
              >
                {moment(item.lastLogoutAt).format("hh:mm A")}
              </Text>
            </>
          )}
        </View>
        {/*  <View style={{ width: '11%' }}>
          <TouchableOpacity
            style={{
              justifyContent: 'center',
              flexDirection: 'row',
              borderWidth: 1,
              borderColor: Colors.primaryColor,
              backgroundColor: '#4E9F7D',
              borderRadius: 5,
              //width: 200,
              paddingHorizontal: 5,
              height: switchMerchant ? 35 : 40,
              alignItems: 'center',
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 1,
              zIndex: -1,
              marginRight: switchMerchant ? 10 : 15,
              paddingVertical: '5%',
            }}
            onPress={() => {
              setCurrEditEmployee(item);

              setClockInModal(true);
            }}>
            <View style={{ flexDirection: 'row' }}>
              <Text
                style={
                  switchMerchant
                    ? {
                      color: 'white',
                      fontSize: 8,
                      fontFamily: 'NunitoSans-Bold',
                      textAlign: 'center',
                    }
                    : {
                      color: 'white',
                      fontSize: 14,
                      fontFamily: 'NunitoSans-Bold',
                      textAlign: 'center',
                      //paddingVertical: '5%'
                    }
                }>
                CLOCK IN
              </Text>
            </View>
          </TouchableOpacity>
        </View> */}
        {/* <View style={{ width: '11%' }}>
          <TouchableOpacity
            style={{
              justifyContent: 'center',
              flexDirection: 'row',
              borderWidth: 1,
              borderColor: Colors.primaryColor,
              backgroundColor: '#4E9F7D',
              borderRadius: 5,
              //width: 200,
              paddingHorizontal: 5,
              height: switchMerchant ? 35 : 40,
              alignItems: 'center',
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 1,
              zIndex: -1,
              marginRight: switchMerchant ? 10 : 15,
              paddingVertical: '5%',
            }}
            onPress={() => {
              setCurrEditEmployee(item);

              setClockOutModal(true);
            }}>
            <View style={{ flexDirection: 'row' }}>
              <Text
                style={
                  switchMerchant
                    ? {
                      color: 'white',
                      fontSize: 8,
                      fontFamily: 'NunitoSans-Bold',
                      textAlign: 'center',
                    }
                    : {
                      color: 'white',
                      fontSize: 14,
                      fontFamily: 'NunitoSans-Bold',
                      textAlign: 'center',
                      //paddingVertical: '5%'
                    }
                }>
                CLOCK OUT
              </Text>
            </View>
          </TouchableOpacity>
        </View> */}
      </View>
    </TouchableOpacity>
  );

  // function here

  const getEmployeeList = () => {
    ApiClient.GET(API.getEmployee + User.getOutletId()).then((results) => {
      //// console.log('employee', results)
      setState({ employeeList: results, employeeListLength: results.length });
    });
  };

  const addEmployeeFunc = async () => {
    // if (
    //   !name ||
    //   !email ||
    //   !outletId ||
    //   !role ||
    //   !password ||
    //   !number
    //   // !dayOfWeek ||
    //   // !regularTime ||
    //   // !overTime
    // ) {
    //   window.confirm(
    //     "Error. Please fill in the following information.\n\nName\nEmail\nRole\nPassword\nPhone Number",
    //     [{ text: "OK", onPress: () => { } }],
    //     { cancelable: false }
    //   );
    //   return;
    const missingFields = [];

    if (!name) {
      missingFields.push("Name");
    }

    if (!email) {
      missingFields.push("Email");
    }

    if (!selectedOutletId) {
      missingFields.push("Outlet ID");
    }

    if (!role) {
      missingFields.push("Role");
    }

    if (!password) {
      missingFields.push("Password");
    }

    if (!number) {
      missingFields.push("Phone Number");
    }

    if (missingFields.length > 0) {
      const errorMessage = "Error. Please fill in the following information:\n\n" + missingFields.join("\n");
      window.confirm(errorMessage, [{ text: "OK", onPress: () => { } }], { cancelable: false });
      return;
    } else {
      if (password.length < 6) {
        window.confirm(
          "Error. Password must be equal or more than 6 characters",
          [{ text: "OK", onPress: () => { } }],
          { cancelable: false }
        );
        return;
      }

      if (
        selectedOutletEmployeeEdit &&
        selectedOutletEmployeeEdit.firebaseUid
      ) {
        // pin unique check
        if (pin) {
          var userSnapshot = await firebase
            .firestore()
            .collection(Collections.User)
            .where("pinNo", "==", pin)
            .where("outletId", "==", currOutletId)
            .get();

          if (userSnapshot.size > 0) {
            var foundUser = userSnapshot.docs[0].data();

            if (
              foundUser.firebaseUid !== selectedOutletEmployeeEdit.firebaseUid
            ) {
              // means not same user

              window.confirm(
                "Error. This pin number was already existed.",
                [{ text: "OK", onPress: () => { } }],
                { cancelable: false }
              );
              return;
            }
          }
        } else {
          window.confirm(
            "Info. Please enter the pin number to proceed.",
            [{ text: "OK", onPress: () => { } }],
            { cancelable: false }
          );
          return;
        }

        var employeeImagePath = "";
        var uniqueIdLocal = selectedOutletEmployeeEdit.employeeId;

        if (image && imageType) {
          const rawBase64 = image
            .replace("data:image/jpeg;base64,", "")
            .replace("data:image/jpg;base64,", "")
            .replace("data:image/png;base64,", "");

          const arrayBuffer = _base64ToArrayBuffer(rawBase64);

          // outletItemIdLocal = selectedProductEdit.uniqueId;
          employeeImagePath = await uploadImageToFirebaseStorage64(
            {
              arrayBuffer: arrayBuffer,
              type: imageType,
            },
            `/merchant/${merchantId}/employee/${uniqueIdLocal}/image${imageType}`
          );
        }

        if (!isImageChanged) {
          employeeImagePath = selectedOutletEmployeeEdit.avatar;
        }

        var body = {
          name: name,
          email: email,
          outletId: selectedOutletId,
          role: role,
          // password: password,
          number: number,
          merchantId: merchantId,
          uniqueName: username,
          image: employeeImagePath,
          // dayOfWeek: moment(dayOfWeek).valueOf(),
          // regularTime: moment(regularTime).valueOf(),
          // overTime: moment(overTime).valueOf(),
          pinNo: pin,
          privileges: privileges, //.map((item) => item.value)
          screensToBlock: screensToBlock, //.map((item) => item.value)

          uniqueIdLocal: uniqueIdLocal,

          outletEmployeeId: selectedOutletEmployeeEdit.firebaseUid,
        };
        // console.log('BODY', body);

        ApiClient.POST(API.updateOutletEmployee, body)
          // APILocal.updateOutletEmployee({ body: body, uid: userId })
          .then((result) => {
            // console.log('RESULT', result);

            // if (result.email.toLowerCase() == email.toLowerCase()) {
            if (result && result.status === "success") {
              // emailFunc(email);

              if (
                window.confirm("Success. Employee has been updated.") == true
              ) {
                setEmail("");
                setPassword("");
                setName("");
                setNumber("");
                setUsername("");
                // setRole('');
                setImage("");
                clearImageContainer();
                setImageType("");
                setDayOfWeek("");
                setRegularTime("");
                setOverTime("");
                setPin("");
                setPrivileges([]);
                setScreensToBlock([]);

                setAddEmployee(false);
                setAddEmployeeItem(true);
              } else {
                console.log("You canceled!");
              }
            } else {
              if (window.confirm("Error. Failed to update employee.") == true) {
                setEmail("");
                setPassword("");
                setName("");
                setNumber("");
                setUsername("");
                // setRole('');
                setImage("");
                clearImageContainer();
                setImageType("");
                setDayOfWeek("");
                setRegularTime("");
                setOverTime("");

                setAddEmployee(false);
                setAddEmployeeItem(true);
              } else {
                console.log("You canceled!");
              }
            }
          });
      } else {
        // create new employee

        // email unique check
        if (email) {
          var userEmailSnapshot = await firebase
            .firestore()
            .collection(Collections.User)
            .where("email", "==", email)
            .get();

          if (userEmailSnapshot.size > 0) {
            window.confirm(
              "Error. This email was already used.",
              [{ text: "OK", onPress: () => { } }],
              { cancelable: false }
            );
            return;
          }
        }

        // pin unique check
        if (pin) {
          var UserSnapshot = await firebase
            .firestore()
            .collection(Collections.User)
            .where("pinNo", "==", pin)
            .where("outletId", "==", currOutletId)
            .get();

          if (UserSnapshot.size > 0) {
            window.confirm(
              "Error. This pin number was already existed.",
              [{ text: "OK", onPress: () => { } }],
              { cancelable: false }
            );
            return;
          }
        } else {
          window.confirm(
            "Info. Please enter the pin number to proceed.",
            [{ text: "OK", onPress: () => { } }],
            { cancelable: false }
          );
          return;
        }

        var employeeImagePath = "";
        var uniqueIdLocal = "";

        if (image && imageType) {
          const rawBase64 = image
            .replace("data:image/jpeg;base64,", "")
            .replace("data:image/jpg;base64,", "")
            .replace("data:image/png;base64,", "");

          const arrayBuffer = _base64ToArrayBuffer(rawBase64);

          // outletItemIdLocal = selectedProductEdit.uniqueId;
          employeeImagePath = await uploadImageToFirebaseStorage64(
            {
              arrayBuffer: arrayBuffer,
              type: imageType,
            },
            `/merchant/${merchantId}/employee/${uniqueIdLocal}/image${imageType}`
          );
        }

        var body = {
          name: name,
          email: email,
          outletId: selectedOutletId,
          role: role,
          password: password,
          number: number,
          merchantId: merchantId,
          uniqueName: username,
          image: employeeImagePath,
          // dayOfWeek: moment(dayOfWeek).valueOf(),
          // regularTime: moment(regularTime).valueOf(),
          // overTime: moment(overTime).valueOf(),
          pinNo: pin,
          privileges,
          screensToBlock,

          uniqueIdLocal: uniqueIdLocal,
        };
        // console.log('BODY', body);

        CommonStore.update((s) => {
          s.isLoading = true;
        });

        ApiClient.POST(API.createOutletEmployee, body)
          .then(
            async (result) => {
              // console.log('RESULT', result);

              // if (result.email.toLowerCase() == email.toLowerCase()) {
              if (result && result.status === "success") {
                // emailFunc(email);
                if (
                  window.confirm("Success. Employee has been added.") == true
                ) {
                  setEmail("");
                  setPassword("");
                  setUsername("");
                  setName("");
                  setNumber("");
                  // setRole('');
                  setImage("");
                  clearImageContainer();
                  setImageType("");
                  setDayOfWeek("");
                  setRegularTime("");
                  setOverTime("");
                  setPin("");
                  setPrivileges([]);
                  setScreensToBlock([]);

                  setAddEmployee(false);
                  setAddEmployeeItem(true);
                } else {
                  console.log("You canceled!");
                }
              } else {
                if (window.confirm("Error. Failed to add employee.") == true) {
                  setEmail("");
                  setPassword("");
                  setUsername("");
                  setName("");
                  setNumber("");
                  // setRole('');
                  setImage("");
                  clearImageContainer();
                  setImageType("");
                  setDayOfWeek("");
                  setRegularTime("");
                  setOverTime("");

                  setAddEmployee(false);
                  setAddEmployeeItem(true);
                } else {
                  console.log("You canceled!");
                }
              }

              CommonStore.update((s) => {
                s.isLoading = false;
              });
            },
            (result) => {
              CommonStore.update((s) => {
                s.isLoading = false;
              });

              // console.log(result);
            }
          )
          .catch((result) => {
            CommonStore.update((s) => {
              s.isLoading = false;
            });

            // console.log(result);
          });
      }
    }
  };

  const clockInEmployeeDateTime = async () => {
    var body = {
      clockInDate: moment(shiftDate).valueOf(),
      clockInTime: moment(shiftTime).valueOf(),
      employeeUserId: currEditEmployee.firebaseUid,
    };

    ApiClient.POST(API.clockInEmployeeDateTime, body).then((result) => {
      setClockInModal(false);
    });
  };

  const clockOutEmployeeDateTime = async () => {
    var body = {
      clockOutDate: moment(shiftDate).valueOf(),
      clockOutTime: moment(shiftTime).valueOf(),
      employeeUserId: currEditEmployee.firebaseUid,
    };

    ApiClient.POST(API.clockOutEmployeeDateTime, body).then((result) => {
      setClockOutModal(false);
    });
  };

  const deleteOutletEmployee = async () => {
    var body = {
      employeeUserId: selectedOutletEmployeeEdit.firebaseUid,
    };

    ApiClient.POST(API.deleteOutletEmployee, body).then((result) => {
      if (result && result.status === "success") {
        if (window.confirm("Success. Employee has been removed") == true) {
          setAddEmployee(false);
          setAddEmployeeItem(true);
        }
      } else {
        console.log("You canceled!");
      }
    });
  };

  const emailFunc = (email) => {
    var body = {
      data: `Your employee account has been created.\n The password is ${password}`,
      email: email,
    };
    ApiClient.POST(API.emailEmployee, body, false).then((result) => {
      if (result == true) {
        if (window.confirm("Success! Email has sent") == true) {
          console.log("You have pressed ok!");
        } else {
          console.log("You have cancelled!");
        }
      }
    });
  };

  const removeEmployee = (employeeId) => {
    var body = {
      role: "user",
    };
    ApiClient.POST(API.updateRole + employeeId + "/role", body).then(
      (result) => {
        if (window.confirm("Success! Employee has been removed") == true) {
          console.log("You have pressed ok!");
        } else {
          console.log("You have cancelled!");
        }
        getEmployeeList();
        setState({ showDetail: false, addEmployeeItem: true });
      }
    );
  };

  const searchBarItem = () => {
    ApiClient.GET(
      API.searchBarEmployee + search + "&outletId=" + outletId
    ).then((result) => {
      setState({ lists: result });
    });
  };

  const renderSearchItem = ({ elements }) => {
    return (
      <View
        style={{
          backgroundColor: "#ffffff",
          flexDirection: "row",
          paddingVertical: 20,
          paddingHorizontal: 20,
          borderBottomWidth: StyleSheet.hairlineWidth,
          borderBottomColor: "#c4c4c4",
        }}
      >
        <TouchableOpacity>
          <AsyncImage
            source={{ uri: elements.avatar }}
            item={elements}
            style={{ width: 20, height: 20, borderRadius: 10 }}
          />
          <Text
            style={{
              width: "2%",
              fontFamily: "NunitoSans-Regular",
              fontSize: switchMerchant ? 10 : 14,
            }}
          ></Text>
          <Text
            style={{
              width: "14%",
              fontFamily: "NunitoSans-Regular",
              fontSize: switchMerchant ? 10 : 14,
            }}
          >
            {elements.name}
          </Text>
          <Text
            style={{
              width: "15%",
              fontFamily: "NunitoSans-Regular",
              fontSize: switchMerchant ? 10 : 14,
            }}
          >
            {elements.role}
          </Text>
          <Text
            style={{
              width: "13%",
              fontFamily: "NunitoSans-Regular",
              fontSize: switchMerchant ? 10 : 14,
            }}
          >
            {elements.role}
          </Text>
          {/* <Text style={{ width: '15%' }}>{elements.number}</Text>
          <Text style={{ width: '16%', marginRight: 20 }}>{elements.email}</Text> */}
          <Text
            style={{
              width: "10%",
              fontFamily: "NunitoSans-Regular",
              fontSize: switchMerchant ? 10 : 14,
            }}
          >
            ******
          </Text>
          <Text
            style={{
              width: "7%",
              fontFamily: "NunitoSans-Regular",
              fontSize: switchMerchant ? 10 : 14,
            }}
          >
            ****
          </Text>
          <Text
            style={{
              width: "9%",
              fontFamily: "NunitoSans-Regular",
              fontSize: switchMerchant ? 10 : 14,
            }}
          >
            {elements.dayOfWeek}
          </Text>
          <Text
            style={{
              width: "9%",
              fontFamily: "NunitoSans-Regular",
              fontSize: switchMerchant ? 10 : 14,
            }}
          >
            {elements.regularTime}
          </Text>
          <Text
            style={{
              width: "9%",
              fontFamily: "NunitoSans-Regular",
              fontSize: switchMerchant ? 10 : 14,
            }}
          >
            {elements.overTime}
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  const exportFunc = () => {
    // var body = {
    //   data: orderList
    // }

    if (allOutletsEmployees) {
      const csvData = convertArrayToCSV(allOutletsEmployees);

      // const pathToWrite = `${RNFetchBlob.fs.dirs.DownloadDir
      //   }/koodoo-report-employee-${moment().format('YYYY-MM-DD-HH-mm-ss')}.csv`;
      // // console.log('PATH', pathToWrite);
      // RNFetchBlob.fs
      //   .writeFile(pathToWrite, csvData, 'utf8')
      //   .then(() => {
      //     // console.log(`wrote file ${pathToWrite}`);
      //     // wrote file /storage/emulated/0/Download/data.csv
      //     window.confirm(
      //       'Success',
      //       `Sent to ${pathToWrite}`,
      //       [{ text: 'OK', onPress: () => { } }],
      //       { cancelable: false },
      //     );
      //   })
      //   .catch((error) => console.error(error));
    }
  };

  const handleChoosePhoto = () => {
    openFileSelector();
    // const imagePickerOptions = {
    //   mediaType: 'photo',
    //   quality: 0.5,
    //   includeBase64: false,
    // };

    // launchImageLibrary(imagePickerOptions, (response) => {
    //   if (response.didCancel) {
    //   } else if (response.error) {
    //     window.confirm(response.error.toString());
    //   } else {
    //     // setState({ image: response.uri });
    //     setImage(response.uri);
    //     // console.log('response.uri: ', response.uri);
    //     setImageType(response.uri.slice(response.uri.lastIndexOf('.')));
    //     // console.log(
    //     //   `response.uri.slice(response.uri.lastIndexOf('.')): `,
    //     //   response.uri.slice(response.uri.lastIndexOf('.')),
    //     // );

    //     setIsImageChanged(false);
    //   }
    // });
  };

  const convertDataToExcelFormat = () => {
    var excelData = [];

    for (var i = 0; i < allOutletsEmployees.length; i++) {
      var excelRow = {
        Name: allOutletsEmployees[i].name ? allOutletsEmployees[i].name : "N/A",
        // 'Outlet': outletSupplyItems[i].skuMerchant ? outletSupplyItems[i].skuMerchant : 'N/A',
        // 'Staff': parseFloat(outletSupplyItems[i].quantity).toFixed(2),
        // 'Status': outletSupplyItems[i].unit ? outletSupplyItems[i].unit : 'N/A',
        // 'Clock In': parseFloat(outletSupplyItems[i].stockIdealQuantity).toFixed(2),
      };

      excelData.push(excelRow);
    }

    // console.log('excelData');
    // console.log(excelData);

    return excelData;
  };
  const handleExportExcel = () => {
    var wb = XLSX.utils.book_new(),
      ws = XLSX.utils.json_to_sheet(convertDataToExcelFormat());

    XLSX.utils.book_append_sheet(wb, ws, "Employee");
    XLSX.writeFile(wb, "Employee.xlsx");
  };

  // function end

  return (
    //<UserIdleWrapper disabled={!isMounted}>
    <View
      style={[
        styles.container,
        {
          height: windowHeight,
          width: windowWidth,
          ...getTransformForScreenInsideNavigation(),
        },
      ]}
    >
      {/* Sidebar view */}
      <View style={{ flex: 0.8 }}>
        <SideBar navigation={navigation} selectedTab={0} />
      </View>
      <View style={{ height: windowHeight, flex: 9 }}>
        {/************ Test clocked in time modals************/}
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{ width: windowWidth * 0.9 }}
          contentContainerStyle={{
            paddingBottom: windowHeight * 0.1,
            backgroundColor: Colors.highlightColor,
          }}
        >
          {/* <ScrollView horizontal={true} showsHorizontalScrollIndicator={true}> */}
          <DateTimePickerModal
            date={new Date()}
            minimumDate={new Date()}
            isVisible={showDateTimePicker}
            mode={"date"}
            //display={"default"} //for iOS to use minuteInterval
            onConfirm={(text) => {
              var date_ob = new Date(text);
              let date = ("0" + date_ob.getDate()).slice(-2);
              let month = ("0" + (date_ob.getMonth() + 1)).slice(-2);
              let year = date_ob.getFullYear();
              if (pickDate == "dayOfWeek") {
                // setState({ startDate: year + "-" + month + "-" + date })
                setDayOfWeek(year + "-" + month + "-" + date);
              }
              // setState({ showDateTimePicker: false })
              setShowDateTimePicker(false);
            }}
            onCancel={() => {
              // setState({ showDateTimePicker: false })
              setShowDateTimePicker(false);
            }}
          />

          {/* Time picker modal */}
          <DateTimePickerModal
            isVisible={showDateTimePicker1}
            mode={"time"}
            onConfirm={(text) => {
              if (pickTime == "regularTime") {
                setRegularTime(text);
              } else {
                setOverTime(text);
              }
              setShowDateTimePicker1(false);
            }}
            onCancel={() => {
              setShowDateTimePicker1(false);
            }}
          />
          {/************ Test clocked in time modals************/}

          <View
            style={{
              paddingVertical: 30,
              marginHorizontal: 30,
            }}
          >
            <Modal
              supportedOrientations={["landscape", "portrait"]}
              style={{ flex: 1 }}
              visible={visible}
              transparent={true}
              animationType="slide"
            >
              <View
                style={{
                  backgroundColor: "rgba(0,0,0,0.5)",
                  flex: 1,
                  justifyContent: "center",
                  alignItems: "center",
                  minHeight: windowHeight,
                }}
              >
                <View style={[styles.confirmBoxRemove, { ...getTransformForModalInsideNavigation(), }]}>
                  <Text
                    style={{
                      fontWeight: "700",
                      fontSize: 24,
                      justifyContent: "center",
                      alignSelf: "center",
                      marginTop: 40,
                    }}
                  >
                    Remove employee
                  </Text>
                  <View
                    style={{
                      justifyContent: "center",
                      alignSelf: "center",
                      alignContent: "center",
                      marginTop: 20,
                      flexDirection: "row",
                      width: "80%",
                    }}
                  >
                    <View
                      style={{
                        justifyContent: "center",
                        marginHorizontal: 5,
                      }}
                    >
                      <Text
                        style={{
                          color: Colors.descriptionColor,
                          fontSize: 20,
                          marginBottom: 5,
                        }}
                      >
                        Are you sure you want to remove this employee?
                      </Text>
                      <Text
                        style={{
                          color: Colors.descriptionColor,
                          fontSize: 16,
                        }}
                      >
                        Name: {showEmployee.name}
                      </Text>
                      <Text
                        style={{
                          color: Colors.descriptionColor,
                          fontSize: 16,
                        }}
                      >
                        Position: {showEmployee.role}
                      </Text>
                    </View>
                  </View>

                  <View
                    style={{
                      alignSelf: "center",
                      marginTop: 20,
                      justifyContent: "center",
                      alignItems: "center",
                      width: 260,
                      height: 40,
                      alignContent: "center",
                      flexDirection: "row",
                      marginTop: 40,
                    }}
                  >
                    <TouchableOpacity
                      onPress={() => {
                        removeEmployee(showEmployee.id);
                        setState({ visible: false });
                      }}
                      style={{
                        backgroundColor: Colors.fieldtBgColor,
                        width: "100%",
                        justifyContent: "center",
                        alignItems: "center",
                        alignContent: "center",
                        height: 80,
                        borderBottomLeftRadius: 10,
                        borderRightWidth: StyleSheet.hairlineWidth,
                        borderTopWidth: StyleSheet.hairlineWidth,
                      }}
                    >
                      <Text
                        style={{ fontSize: 22, color: Colors.primaryColor }}
                      >
                        Confirm
                      </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => {
                        setState({ visible: false });
                      }}
                      style={{
                        backgroundColor: Colors.fieldtBgColor,
                        width: "100%",
                        justifyContent: "center",
                        alignItems: "center",
                        alignContent: "center",
                        height: 80,
                        borderBottomRightRadius: 10,
                        borderTopWidth: StyleSheet.hairlineWidth,
                      }}
                    >
                      <Text
                        style={{
                          fontSize: 22,
                          color: Colors.descriptionColor,
                        }}
                      >
                        Cancel
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </Modal>

            {/****************************************** DOWNLOAD *******************************************/}
            <Modal
              style={
                {
                  // flex: 1
                }
              }
              visible={exportModalVisibility}
              transparent={true}
              animationType={"fade"}
              supportedOrientations={["portrait", "landscape"]}
            >
              <View
                style={{
                  flex: 1,
                  backgroundColor: Colors.modalBgColor,
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <View
                  style={{
                    // height: windowWidth * 0.08,
                    // width: windowWidth * 0.18,
                    height: Dimensions.get("screen").width * 0.08,
                    width: Dimensions.get("screen").width * 0.18,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 12,
                    // padding: windowWidth * 0.02,
                    padding: Dimensions.get("screen").width * 0.02,
                    alignItems: "center",
                    justifyContent: "center",
                    ...getTransformForModalInsideNavigation(),
                  }}
                >
                  <TouchableOpacity
                    disabled={isLoading}
                    style={{
                      position: "absolute",
                      // right: windowWidth * 0.015,
                      // top: windowWidth * 0.01,
                      right: Dimensions.get("screen").width * 0.015,
                      top: Dimensions.get("screen").width * 0.01,

                      elevation: 1000,
                      zIndex: 1000,
                    }}
                    onPress={() => {
                      setExportModalVisibility(false);
                    }}
                  >
                    <AntDesign
                      name="closecircle"
                      size={switchMerchant ? 15 : 25}
                      color={Colors.fieldtTxtColor}
                    />
                  </TouchableOpacity>
                  <View
                    style={{
                      alignItems: "center",
                      top: "20%",
                      position: "absolute",
                    }}
                  >
                    <Text
                      style={{
                        fontFamily: "NunitoSans-Bold",
                        textAlign: "center",
                        fontSize: switchMerchant ? 16 : 24,
                      }}
                    >
                      Download Report
                    </Text>
                  </View>
                  <View style={{ top: "10%" }}>
                    {/* <Text
                      style={{
                        fontSize: switchMerchant ? 12 : 20,
                        fontFamily: 'NunitoSans-Bold',
                      }}>
                      Email Address:
                    </Text>
                    <TextInput
                      style={{
                        backgroundColor: Colors.fieldtBgColor,
                        width: switchMerchant ? 260 : 370,
                        height: switchMerchant ? 35 : 50,
                        borderRadius: 5,
                        padding: 5,
                        marginVertical: 5,
                        borderWidth: 1,
                        borderColor: '#E5E5E5',
                        paddingLeft: 10,
                      }}
                      autoCapitalize='none'
                      placeholderStyle={{ padding: 5 }}
                      placeholder="Enter your email"
                      placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                      onChangeText={(text) => {
                        setExportEmail(text);
                      }}
                      value={exportEmail}
                    />
                    <Text
                      style={{
                        fontSize: switchMerchant ? 12 : 20,
                        fontFamily: 'NunitoSans-Bold',
                        marginTop: 15,
                      }}>
                      Send As:
                    </Text> */}

                    <View
                      style={{
                        alignItems: "center",
                        justifyContent: "center",
                        flexDirection: "row",
                        marginTop: 30,
                      }}
                    >
                      <TouchableOpacity
                        disabled={isLoading}
                        style={{
                          justifyContent: "center",
                          flexDirection: "row",
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: "#4E9F7D",
                          borderRadius: 5,
                          width: switchMerchant ? 100 : 100,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 35 : 40,
                          alignItems: "center",
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                          marginRight: 15,
                        }}
                        onPress={() => {
                          // if (exportEmail.length > 0) {
                          //   CommonStore.update((s) => {
                          //     s.isLoading = true;
                          //   });
                          //   setIsLoadingExcel(true);
                          //   const excelData = convertDataToExcelFormat();

                          //   generateEmailReport(
                          //     EMAIL_REPORT_TYPE.EXCEL,
                          //     excelData,
                          //     'KooDoo Employee Report',
                          //     'KooDoo Employee Report.xlsx',
                          //     `/merchant/${merchantId}/reports/${uuidv4()}.xlsx`,
                          //     exportEmail,
                          //     'KooDoo Employee Report',
                          //     'KooDoo Employee Report',
                          //     () => {
                          //       CommonStore.update((s) => {
                          //         s.isLoading = false;
                          //       });
                          //       setIsLoadingExcel(false);

                          //      window.confirm(
                          //         'Success',
                          //         'Report will be sent to the email address shortly',
                          //       );

                          //       setExportModalVisibility(false);
                          //     },
                          //   );
                          // } else {
                          //   window.confirm('Info', 'Invalid email address');
                          // }
                          handleExportExcel();
                        }}
                      >
                        {isLoadingExcel ? (
                          <ActivityIndicator
                            size={"small"}
                            color={Colors.whiteColor}
                          />
                        ) : (
                          <Text
                            style={{
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: "NunitoSans-Bold",
                            }}
                          >
                            EXCEL
                          </Text>
                        )}
                      </TouchableOpacity>

                      {/*<TouchableOpacity
                        disabled={isLoading}
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#4E9F7D',
                          borderRadius: 5,
                          width: switchMerchant ? 80 : 100,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 30 : 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                          marginLeft: 15,
                        }}
                        onPress={() => {
                          if (exportEmail.length > 0) {
                            CommonStore.update((s) => {
                              s.isLoading = true;
                            });
                            setIsLoadingCsv(true);
                            const csvData =
                              convertArrayToCSV(allOutletsEmployees);

                            generateEmailReport(
                              EMAIL_REPORT_TYPE.CSV,
                              csvData,
                              'KooDoo Employee Report',
                              'KooDoo Employee Report.csv',
                              `/merchant/${merchantId}/reports/${uuidv4()}.csv`,
                              exportEmail,
                              'KooDoo Employee Report',
                              'KooDoo Employee Report',
                              () => {
                                CommonStore.update((s) => {
                                  s.isLoading = false;
                                });
                                setIsLoadingCsv(false);
                               window.confirm(
                                  'Success',
                                  'Report will be sent to the email address shortly',
                                );

                                setExportModalVisibility(false);
                              },
                            );
                          } else {
                            window.confirm('Info', 'Invalid email address');
                          }
                        }}>
                        {isLoadingCsv ? (
                          <ActivityIndicator
                            size={'small'}
                            color={Colors.whiteColor}
                          />
                        ) : (
                          <Text
                            style={{
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                            }}>
                            CSV
                          </Text>
                        )}
                      </TouchableOpacity> */}

                      {/* <TouchableOpacity
                            style={[styles.modalSaveButton, {
                                zIndex: -1
                            }]}
                            onPress={() => { downloadPDF() }}>
                            <Text style={[styles.modalDescText, { color: Colors.primaryColor }]}>PDF</Text>
                        </TouchableOpacity> */}
                      <CSVLink
                        style={{
                          justifyContent: "center",
                          alignContent: "center",
                          alignItems: "center",
                          display: "inline-block",
                          flexDirection: "row",
                          textDecoration: "none",
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: "#4E9F7D",
                          borderRadius: 5,
                          width: 100,
                          paddingHorizontal: 10,
                          height: 40,
                          alignItems: "center",
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        }}
                        data={convertDataToExcelFormat()}
                        filename="Employee.csv"
                      >
                        <View
                          style={{
                            width: "100%",
                            height: "100%",
                            alignContent: "center",
                            alignItems: "center",
                            alignSelf: "center",
                            justifyContent: "center",
                          }}
                        >
                          <Text
                            style={{
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: 16,
                              fontFamily: "NunitoSans-Bold",
                            }}
                          >
                            CSV
                          </Text>
                        </View>
                      </CSVLink>
                    </View>
                  </View>
                </View>
              </View>
            </Modal>

            {/****************************************** Clock In *******************************************/}
            <Modal
              style={
                {
                  // flex: 1
                }
              }
              visible={clockInModal}
              transparent={true}
              animationType={"slide"}
              supportedOrientations={["portrait", "landscape"]}
            >
              <DateTimePickerModal
                isVisible={showShiftDatePicker}
                mode={"date"}
                onConfirm={(text) => {
                  setShiftDate(moment(text));

                  setShowShiftDatePicker(false);
                }}
                onCancel={() => {
                  setShowShiftDatePicker(false);
                }}
              />

              <DateTimePickerModal
                isVisible={showShiftTimePicker}
                mode={"time"}
                onConfirm={(text) => {
                  setShiftTime(moment(text));

                  setShowShiftTimePicker(false);
                }}
                onCancel={() => {
                  setShowShiftTimePicker(false);
                }}
              />
              <View
                style={{
                  flex: 1,
                  backgroundColor: Colors.modalBgColor,
                  alignItems: "center",
                  justifyContent: "center",
                  // top: keyboardHeight > 0 ? -keyboardHeight * 0.45 : 0,
                }}
              >
                <View
                  style={{
                    height: windowWidth * 0.2,
                    width: windowWidth * 0.4,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 12,
                    padding: windowWidth * 0.05,
                    alignItems: "center",
                    justifyContent: "center",
                    ...getTransformForModalInsideNavigation(),
                  }}
                >
                  <TouchableOpacity
                    style={{
                      position: "absolute",
                      right: windowWidth * 0.02,
                      top: windowWidth * 0.02,

                      elevation: 1000,
                      zIndex: 1000,
                    }}
                    onPress={() => {
                      setClockInModal(false);
                      setClockTimePicker(false);
                    }}
                  >
                    <AntDesign
                      name="closecircle"
                      size={switchMerchant ? 15 : 25}
                      color={Colors.fieldtTxtColor}
                    />
                  </TouchableOpacity>
                  <View
                    style={{
                      alignItems: "center",
                      top: "20%",
                      position: "absolute",
                    }}
                  >
                    <Text
                      style={{
                        fontFamily: "NunitoSans-Bold",
                        textAlign: "center",
                        fontSize: switchMerchant ? 16 : 24,
                      }}
                    >
                      Clock In
                    </Text>
                  </View>
                  <View
                    style={{
                      flexDirection: "row",
                      flexDirection: "row",
                      marginTop: 15,
                      justifyContent: "center",
                    }}
                  >
                    <View style={{ flexDirection: "column" }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 11 : 14,
                          fontFamily: "NunitoSans-Regular",
                          fontWeight: "500",
                        }}
                      >
                        Date
                      </Text>
                      <View
                        style={{
                          borderWidth: 1,
                          width: 120,
                          borderRadius: 5,
                          justifyContent: "center",
                          alignItems: "center",
                          flexDirection: "row",
                        }}
                      >
                        <GCalendar
                          width={20}
                          height={20}
                          style={{ marginRight: 0 }}
                        />
                        <DatePicker
                          selected={rev_date.toDate()}
                          onChange={(date) => {
                            setRev_date(moment(date));
                          }}
                        />
                      </View>
                    </View>

                    <View style={{ flexDirection: "column", marginLeft: 15 }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 11 : 14,
                          fontFamily: "NunitoSans-Regular",
                          fontWeight: "500",
                        }}
                      >
                        Time
                      </Text>
                      <TouchableOpacity
                        style={{
                          borderWidth: 1,
                          width: 120,
                          borderRadius: 5,
                          justifyContent: "center",
                          alignItems: "center",
                          flexDirection: "row",
                        }}
                        onPress={() => {
                          setClockTimePicker(!clockTimePicker);
                        }}
                      >
                        <Time />
                        <View>
                          <Text>{rev_time}</Text>
                        </View>
                      </TouchableOpacity>
                      {clockTimePicker ? (
                        <View
                          style={{
                            // borderWidth:10
                            zIndex: 100,
                          }}
                        >
                          <View
                            style={{
                              position: "absolute",
                              // borderWidth: 1,
                            }}
                          >
                            <TimeKeeper
                              time={rev_time}
                              onChange={(time) =>
                                setRev_time(time.formatted12)
                              }
                              onDoneClick={() => {
                                setClockTimePicker(false);
                              }}
                            ></TimeKeeper>
                          </View>
                        </View>
                      ) : null}
                    </View>
                  </View>
                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "center",
                      flexDirection: "row",
                      marginTop: "5%",
                    }}
                  >
                    <TouchableOpacity
                      style={{
                        justifyContent: "center",
                        flexDirection: "row",
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        backgroundColor: "#4E9F7D",
                        borderRadius: 5,
                        //width: 200,
                        paddingHorizontal: 10,
                        height: switchMerchant ? 35 : 40,
                        alignItems: "center",
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,
                        marginRight: switchMerchant ? 10 : 15,
                      }}
                      onPress={() => {
                        clockInEmployeeDateTime();
                        setClockTimePicker(false);
                      }}
                    >
                      <Text
                        style={{
                          color: "white",
                          fontSize: switchMerchant ? 10 : 16,
                          fontFamily: "NunitoSans-Bold",
                        }}
                      >
                        UPDATE
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </Modal>

            {/****************************************** Clock Out *******************************************/}
            <Modal
              style={
                {
                  // flex: 1
                }
              }
              visible={clockOutModal}
              transparent={true}
              animationType={"slide"}
              supportedOrientations={["portrait", "landscape"]}
            >
              <DateTimePickerModal
                isVisible={showShiftDatePicker}
                mode={"date"}
                onConfirm={(text) => {
                  setShiftDate(moment(text));

                  setShowShiftDatePicker(false);
                }}
                onCancel={() => {
                  setShowShiftDatePicker(false);
                }}
              />

              <DateTimePickerModal
                isVisible={showShiftTimePicker}
                mode={"time"}
                onConfirm={(text) => {
                  setShiftTime(moment(text));

                  setShowShiftTimePicker(false);
                }}
                onCancel={() => {
                  setShowShiftTimePicker(false);
                }}
              />
              <View
                style={{
                  flex: 1,
                  backgroundColor: Colors.modalBgColor,
                  alignItems: "center",
                  justifyContent: "center",
                  // top: keyboardHeight > 0 ? -keyboardHeight * 0.45 : 0,
                }}
              >
                <View
                  style={{
                    // height: windowWidth * 0.2,
                    height: Dimensions.get("screen").width * 0.2,
                    // width: windowWidth * 0.4,
                    width: Dimensions.get("screen").width * 0.4,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 12,
                    // padding: windowWidth * 0.05,
                    padding: Dimensions.get("screen").width * 0.05,
                    alignItems: "center",
                    justifyContent: "center",
                    ...getTransformForModalInsideNavigation(),
                  }}
                >
                  <TouchableOpacity
                    style={{
                      position: "absolute",
                      // right: windowWidth * 0.02,
                      right: Dimensions.get("screen").width * 0.02,
                      // top: windowWidth * 0.02,
                      top: Dimensions.get("screen").width * 0.02,

                      elevation: 1000,
                      zIndex: 1000,
                    }}
                    onPress={() => {
                      setClockOutModal(false);
                      setClockTimePicker(false);
                    }}
                  >
                    <AntDesign
                      name="closecircle"
                      size={switchMerchant ? 15 : 25}
                      color={Colors.fieldtTxtColor}
                    />
                  </TouchableOpacity>
                  <View
                    style={{
                      alignItems: "center",
                      top: "20%",
                      position: "absolute",
                    }}
                  >
                    <Text
                      style={{
                        fontFamily: "NunitoSans-Bold",
                        textAlign: "center",
                        fontSize: switchMerchant ? 16 : 24,
                      }}
                    >
                      Clock Out
                    </Text>
                  </View>
                  <View
                    style={{
                      flexDirection: "row",
                      flexDirection: "row",
                      marginTop: 15,
                      justifyContent: "center",
                    }}
                  >
                    <View style={{ flexDirection: "column" }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 11 : 14,
                          fontFamily: "NunitoSans-Regular",
                          fontWeight: "500",
                        }}
                      >
                        Date
                      </Text>
                      <View
                        style={{
                          borderWidth: 1,
                          width: 120,
                          borderRadius: 5,
                          justifyContent: "center",
                          alignItems: "center",
                          flexDirection: "row",
                        }}
                      >
                        <GCalendar
                          width={20}
                          height={20}
                        //style={{marginRight: 5}}
                        />

                        <DatePicker
                          selected={rev_date.toDate()}
                          onChange={(date) => {
                            setRev_date(moment(date));
                          }}
                        />
                      </View>
                    </View>

                    <View style={{ flexDirection: "column", marginLeft: 15 }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 11 : 14,
                          fontFamily: "NunitoSans-Regular",
                          fontWeight: "500",
                        }}
                      >
                        Time
                      </Text>
                      <TouchableOpacity
                        style={{
                          borderWidth: 1,
                          width: 120,
                          borderRadius: 5,
                          justifyContent: "center",
                          alignItems: "center",
                          flexDirection: "row",
                        }}
                        onPress={() => {
                          setClockTimePicker(!clockTimePicker);
                        }}
                      >
                        <Time />
                        <View>
                          <Text>{rev_time}</Text>
                        </View>
                      </TouchableOpacity>
                      {clockTimePicker ? (
                        <View
                          style={{
                            // borderWidth:10
                            zIndex: 100,
                          }}
                        >
                          <View
                            style={{
                              position: "absolute",
                              // borderWidth: 1,
                            }}
                          >
                            <TimeKeeper
                              time={rev_time}
                              onChange={(time) => {
                                setRev_time(time.formatted12);
                                // console.log('time.formatted12: ', time.formatted12)
                              }}
                              onDoneClick={() => {
                                setClockTimePicker(false);
                              }}
                            ></TimeKeeper>
                          </View>
                        </View>
                      ) : null}
                    </View>
                  </View>
                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "center",
                      flexDirection: "row",
                      marginTop: 15,
                    }}
                  >
                    <TouchableOpacity
                      style={{
                        justifyContent: "center",
                        flexDirection: "row",
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        backgroundColor: "#4E9F7D",
                        borderRadius: 5,
                        //width: 200,
                        paddingHorizontal: 10,
                        height: switchMerchant ? 35 : 40,
                        alignItems: "center",
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,
                        marginRight: switchMerchant ? 10 : 15,
                      }}
                      onPress={() => {
                        clockOutEmployeeDateTime();
                        setClockTimePicker(false);
                      }}
                    >
                      <Text
                        style={{
                          color: "white",
                          fontSize: switchMerchant ? 10 : 16,
                          fontFamily: "NunitoSans-Bold",
                        }}
                      >
                        UPDATE
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </Modal>

            {addEmployeeItem ? (
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
                  marginHorizontal: 30,
                  marginTop: 20,
                  height: windowHeight * 0.1,
                  width: windowWidth * 0.877,
                  alignSelf: 'center',
                }}
              >
                <View
                  style={{ alignItems: "center", flexDirection: "row" }}
                >
                  <Text
                    style={{
                      fontSize: switchMerchant ? 20 : 26,
                      fontFamily: "NunitoSans-Bold",
                    }}
                  >
                    {allOutletsEmployees.length}
                  </Text>
                  <Text
                    style={{
                      fontSize: switchMerchant ? 20 : 26,
                      fontFamily: "NunitoSans-Bold",
                      marginLeft: 10,
                    }}
                  >
                    {allOutletsEmployees.length > 1
                      ? "Employees"
                      : "Employee"}
                  </Text>
                </View>
                <View style={{ flexDirection: "row" }}>
                  <TouchableOpacity
                    style={{
                      justifyContent: "center",
                      flexDirection: "row",
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: "#4E9F7D",
                      borderRadius: 5,
                      //width: 200,
                      paddingHorizontal: 10,
                      height: switchMerchant ? 35 : 40,
                      alignItems: "center",
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                      marginRight: switchMerchant ? 10 : 15,
                    }}
                    onPress={() => {
                      setExportModalVisibility(true);
                    }}
                  >
                    <View style={{ flexDirection: "row" }}>
                      <View style={{ marginTop: switchMerchant ? 1 : 0 }}>
                        <Icon
                          name="download"
                          size={switchMerchant ? 10 : 20}
                          color={Colors.whiteColor}
                        />
                      </View>
                      <Text
                        style={{
                          color: Colors.whiteColor,
                          marginLeft: 5,
                          fontSize: switchMerchant ? 10 : 16,
                          fontFamily: "NunitoSans-Bold",
                        }}
                      >
                        DOWNLOAD
                      </Text>
                    </View>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={{
                      justifyContent: "center",
                      flexDirection: "row",
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: "#4E9F7D",
                      borderRadius: 5,
                      //width: 200,
                      paddingHorizontal: 10,
                      height: switchMerchant ? 35 : 40,
                      alignItems: "center",
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                      marginRight: 15,
                    }}
                    onPress={() => {
                      // setState({
                      //   addEmployee: true,
                      //   addEmployeeItem: false,
                      // });
                      CommonStore.update((s) => {
                        s.selectedOutletEmployeeEdit = null;
                      });
                      setAddEmployee(true);
                      setAddEmployeeItem(false);
                    }}
                  >
                    <View
                      style={{ flexDirection: "row", alignItems: "center" }}
                    >
                      <AntDesign
                        name="pluscircle"
                        size={switchMerchant ? 10 : 20}
                        color={Colors.whiteColor}
                      />
                      <Text
                        style={{
                          color: Colors.whiteColor,
                          marginLeft: 5,
                          fontSize: switchMerchant ? 10 : 16,
                          fontFamily: "NunitoSans-Bold",
                        }}
                      >
                        EMPLOYEE
                      </Text>
                    </View>
                  </TouchableOpacity>
                  <View
                    style={{
                      width: switchMerchant ? 200 : 250,
                      height: switchMerchant ? 35 : 40,
                      backgroundColor: "white",
                      borderRadius: 5,
                      flexDirection: "row",
                      alignContent: "center",
                      alignItems: "center",
                      shadowColor: "#000",
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 3,
                      borderWidth: 1,
                      borderColor: "#E5E5E5",
                    }}
                  >
                    <Feather
                      name="search"
                      size={switchMerchant ? 13 : 25}
                      color={Colors.primaryColor}
                      style={{ marginLeft: 15 }}
                    />
                    <TextInput
                      editable={!loading}
                      underlineColorAndroid={Colors.whiteColor}
                      style={{
                        width: switchMerchant ? 150 : 220,
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: "NunitoSans-Regular",
                        paddingLeft: 5,
                        height: 45,
                      }}
                      clearButtonMode="while-editing"
                      placeholder=" Search"
                      onChangeText={(text) => {
                        // setSearch(text.trim());
                        // setList1(false);
                        // setSearchList(true);
                        setSearch(text);
                      }}
                      placeholderTextColor={Platform.select({
                        ios: "#a9a9a9",
                      })}
                      value={search}
                    />
                  </View>
                </View>
              </View>
            ) : (
              <></>
            )}

            {addEmployeeItem ? (
              <View
                style={{
                  backgroundColor: Colors.whiteColor,
                  width: windowWidth * 0.877,
                  marginTop: 10,
                  marginBottom: 30,
                  marginHorizontal: 30,
                  alignSelf: 'center',
                  borderRadius: 5,
                  shadowOpacity: 0,
                  shadowColor: "#000",
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 3,
                }}
              >
                <View
                  style={[
                    styles.titleList,
                    { paddingHorizontal: 10, marginTop: 0, borderRadius: 5 },
                  ]}
                >
                  <Text
                    style={{
                      width: 35,
                      marginRight: 15,
                      alignSelf: "center",
                    }}
                  ></Text>
                  <Text
                    style={{
                      width: switchMerchant ? "10%" : "12%",
                      alignSelf: "center",
                      marginRight: 5,
                      fontFamily: "NunitoSans-Regular",
                      fontSize: switchMerchant ? 10 : 14,
                    }}
                  >
                    Name
                  </Text>
                  <Text
                    style={{
                      width: "20%", //15%
                      alignSelf: "center",
                      marginRight: 5,
                      fontFamily: "NunitoSans-Regular",
                      fontSize: switchMerchant ? 10 : 14,
                    }}
                  >
                    Outlet
                  </Text>
                  <Text
                    style={{
                      width: switchMerchant ? "10%" : "14%",
                      alignSelf: "center",
                      marginRight: 5,
                      fontFamily: "NunitoSans-Regular",
                      fontSize: switchMerchant ? 10 : 14,
                    }}
                  >
                    Staff
                  </Text>
                  <Text
                    style={{
                      width: switchMerchant ? "10%" : "10%",
                      alignSelf: "center",
                      marginRight: 5,
                      fontFamily: "NunitoSans-Regular",
                      fontSize: switchMerchant ? 10 : 14,
                    }}
                  >
                    Status
                  </Text>
                  {/* <Text style={{ width: '13%', alignSelf: 'center' }}>
                Contact No
              </Text>
              <Text style={{ width: '17.5%', alignSelf: 'center' }}>
                Email
              </Text> */}
                  {/* <Text style={{ width: '11%', alignSelf: 'center' }}>
                Password
              </Text>
              <Text style={{ width: '7%', alignSelf: 'center' }}>Pin </Text> */}

                  {/* <Text style={{ width: '9%', alignSelf: 'center' }}>
              Day of Week
            </Text>
            <Text style={{ width: '9%', alignSelf: 'center' }}>
              Regular Time
            </Text> */}
                  <Text
                    style={{
                      width: switchMerchant ? "13%" : "16%", //11%
                      alignSelf: "center",
                      fontFamily: "NunitoSans-Regular",
                      fontSize: switchMerchant ? 10 : 14,
                    }}
                  >
                    Clock In
                  </Text>

                  <Text
                    style={{
                      width: "16%", //11%
                      alignSelf: "center",
                      fontFamily: "NunitoSans-Regular",
                      fontSize: switchMerchant ? 10 : 14,
                    }}
                  >
                    Clock Out
                  </Text>
                </View>
                {/* Flat list for employee */}
                <View style={{ flex: 1 }}>
                  {list1 ? (
                    <FlatList
                      nestedScrollEnabled={true}
                      showsVerticalScrollIndicator={false}
                      data={allOutletsEmployees.filter((item) => {
                        if (search !== "") {
                          const searchLowerCase = search.toLowerCase();

                          return (
                            item.email
                              .toLowerCase()
                              .includes(searchLowerCase) ||
                            item.name.toLowerCase().includes(searchLowerCase)
                          );
                        } else {
                          return true;
                        }
                      })}
                      extraData={allOutletsEmployees.filter((item) => {
                        if (search !== "") {
                          const searchLowerCase = search.toLowerCase();

                          return (
                            item.email
                              .toLowerCase()
                              .includes(searchLowerCase) ||
                            item.name.toLowerCase().includes(searchLowerCase)
                          );
                        } else {
                          return true;
                        }
                      })}
                      renderItem={renderItem}
                      keyExtractor={(item, index) => String(index)}
                    />
                  ) : // </ScrollView>
                    null}
                  {searchList ? (
                    <ScrollView>
                      <FlatList
                        nestedScrollEnabled={true}
                        showsVerticalScrollIndicator={false}
                        data={lists}
                        extraData={lists}
                        renderItem={renderSearchItem}
                        keyExtractor={(item, index) => String(index)}
                      />
                    </ScrollView>
                  ) : null}
                </View>

                {/* <View style={styles.footer}>
              
            </View> */}
              </View>
            ) : null}

            {addEmployee == true ? (
              <View
                // style={{
                  // backgroundColor: 'green',
                  //alignItems: 'center',
                  // shadowColor: '#000',
                  // shadowOffset: {
                  //   width: 0,
                  //   height: 1,
                  // },
                  // shadowOpacity: 0.34,
                  // shadowRadius: 3.22,
                  // elevation: 2,
                  // width: switchMerchant
                  //   ? windowWidth * 0.85
                  //   : windowWidth * 0.87,
                  // marginHorizontal: switchMerchant ? -20 : 15,
                  // marginBottom: switchMerchant ? -130 : -130,
                // }}
              >
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    width: windowWidth * 0.877,
                    marginHorizontal: 30,
                    alignSelf: 'center',
                    marginTop: 20,
                  }}>
                  <TouchableOpacity
                    style={{ height: 35, justifyContent: "center" }}
                    onPress={() => {
                      // setState({ addEmployee: false, addEmployeeItem: true })
                      setAddEmployee(false);
                      setAddEmployeeItem(true);
                    }}
                  >
                    <View
                      style={{
                        flexDirection: "row",
                        // paddingHorizontal: "10%",
                        alignContent: "center",
                        alignItems: "center",
                      }}
                    >
                      <View style={{ justifyContent: "center" }}>
                        <Feather
                          name="chevron-left"
                          size={switchMerchant ? 20 : 30}
                          style={{ color: Colors.primaryColor, alignSelf: "center" }}
                        />
                      </View>
                      <Text
                        style={[
                          {
                            fontSize: 17,
                            color: Colors.primaryColor,
                            fontWeight: "600",
                            marginBottom: 1,
                          },
                          switchMerchant
                            ? {
                              fontSize: 14,
                            }
                            : {},
                        ]}
                      >
                        Back
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
                {/* White container */}

                <View
                  style={{
                    backgroundColor: Colors.whiteColor,
                    width: windowWidth * 0.877,
                    marginTop: 10,
                    marginBottom: 30,
                    marginHorizontal: 30,
                    alignSelf: 'center',
                    borderRadius: 5,
                    shadowOpacity: 0,
                    shadowColor: "#000",
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                  }}
                >
                  {/* <ScrollView
                    showsVerticalScrollIndicator={false}
                    style={{
                      marginTop: 10,
                      // justifyContent: "center",
                      // alignItems: "center",
                      //width: switchMerchant ? windowWidth * 0.8 : windowWidth * 0.87,
                      //height: windowHeight,
                      backgroundColor: Colors.whiteColor,
                      borderRadius: 5,
                      shadowColor: "#000",
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 3,
                      height: windowHeight * 0.8,
                      //paddingBottom: 100,
                      //paddingHorizontal : -30
                      paddingBottom: windowHeight * 0.1,
                    }}
                    contentContainerStyle={{
                      paddingBottom: windowHeight * 0.1,
                      alignItems: 'center',
                    }}> */}
                    <View
                      style={{
                        alignItems: "center",
                        marginBottom: 30,
                        marginTop: 20,
                        // marginRight: 80
                        width: "100%",
                      }}
                    >
                      <Text
                        style={{
                          fontFamily: "NunitoSans-Bold",
                          fontSize: switchMerchant ? 20 : 40,
                          fontWeight: "bold",
                        }}
                      >
                        {selectedOutletEmployeeEdit
                          ? "Edit Employee"
                          : "Add Employee"}
                      </Text>
                      <Text
                        style={{
                          color: Colors.descriptionColor,
                          //marginTop: 10,
                          fontSize: switchMerchant ? 15 : 16,
                        }}
                      >
                        Fill In Employees Information
                      </Text>

                      <View
                        style={{
                          alignSelf: "flex-end",
                          position: "absolute",
                          marginTop: 10,
                          zIndex: 10000,
                        }}
                      >
                        {/* { selectedOutletEmployeeEdit ?
                <TouchableOpacity
                  style={{
                    backgroundColor: Colors.primaryColor,
                    width: 100,
                    height: 35,
                    borderRadius: 5,
                    flexDirection: 'row',
                    justifyContent: 'center',
                    marginRight: 15,
                  }}
                  onPress={() => {
                    
                  }}>
                  <Text
                    style={{
                      color: Colors.whiteColor,
                      alignSelf: 'center',
                      textAlign: 'center',
                      //marginVertical: 10,
                      fontSize: 16,
                      fontWeight: '600'
                    }}>
                    Disabled
                  </Text>
                </TouchableOpacity>
                :
                null
              } */}

                        <TouchableOpacity
                          disabled={isLoading}
                          style={{
                            justifyContent: "center",
                            flexDirection: "row",
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: "#4E9F7D",
                            borderRadius: 5,
                            width: switchMerchant ? 100 : 120,
                            paddingHorizontal: 10,
                            height: switchMerchant ? 35 : 40,
                            alignItems: "center",
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                            marginRight: 20,
                          }}
                          onPress={() => {
                            addEmployeeFunc();
                          }}
                        >
                          <Text
                            style={{
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: "NunitoSans-Bold",
                            }}
                          >
                            {selectedOutletEmployeeEdit ? "UPDATE" : "SAVE"}
                          </Text>
                        </TouchableOpacity>

                        {selectedOutletEmployeeEdit ? (
                          <TouchableOpacity
                            style={{
                              justifyContent: "center",
                              flexDirection: "row",
                              borderWidth: 1,
                              borderColor: Colors.tabRed,
                              backgroundColor: Colors.tabRed,
                              borderRadius: 5,
                              width: switchMerchant ? 100 : 120,
                              paddingHorizontal: 10,
                              height: switchMerchant ? 35 : 40,
                              alignItems: "center",
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,
                              marginRight: 20,
                              marginTop: 20,
                            }}
                            onPress={() => {
                              if (
                                window.confirm(
                                  "Info. Are you sure you want to remove this employee?"
                                ) == true
                              ) {
                                deleteOutletEmployee();
                              } else {
                                console.log("You canceled!");
                              }
                            }}
                          >
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                //marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: "NunitoSans-Bold",
                              }}
                            >
                              {"DELETE"}
                            </Text>
                          </TouchableOpacity>
                        ) : (
                          <></>
                        )}
                      </View>
                    </View>

                    <View
                      style={{
                        flexDirection: "row",
                        paddingBottom: 30,
                        width: "90%",
                      }}
                    >
                      <View style={[{ width: "40%" }]}>
                        <View
                          style={{
                            width: switchMerchant ? 75 : 150,
                            height: switchMerchant ? 75 : 150,
                            alignItems: "center",
                            justifyContent: "center",
                            backgroundColor: Colors.fieldtBgColor,
                            zIndex: -1,
                            alignSelf: "center",
                            borderWidth: 1,
                            borderRadius: 100,
                            borderColor: "#E5E5E5",
                          }}
                        >
                          <View>
                            <TouchableOpacity onPress={openFileSelector}>
                              {image ? (
                                <View>
                                  <AsyncImage
                                    style={{
                                      width: 150,
                                      height: 150,
                                      alignSelf: "center",
                                      borderRadius: 100,
                                    }}
                                    source={{
                                      uri: image,
                                    }}
                                    hideLoading={true}
                                    placeholderSource
                                  />
                                  <View
                                    style={{
                                      position: "absolute",
                                      bottom: 5,
                                      right: -10,
                                      //opacity: 0.5,
                                    }}
                                  >
                                    <Icon
                                      name="edit"
                                      size={switchMerchant ? 12 : 23}
                                      color={Colors.primaryColor}
                                    />
                                  </View>
                                </View>
                              ) : (
                                <View>
                                  <Image
                                    style={{
                                      width: switchMerchant ? 75 : 150,
                                      height: switchMerchant ? 75 : 150,
                                      borderRadius: 100,
                                      alignSelf: "center",
                                    }}
                                    source={require("../assets/image/profile-pic.jpg")}
                                    hideLoading={true}
                                  />
                                  <View
                                    style={{
                                      position: "absolute",
                                      bottom: 5,
                                      right: -10,
                                      //opacity: 0.5,
                                    }}
                                  >
                                    <Icon
                                      name="edit"
                                      size={switchMerchant ? 12 : 23}
                                      color={Colors.primaryColor}
                                    />
                                  </View>
                                  <Icon
                                    name="upload"
                                    size={switchMerchant ? 50 : 100}
                                    color="lightgrey"
                                    style={{
                                      top: switchMerchant ? 10 : 23,
                                      position: "absolute",
                                      alignSelf: "center",
                                      zIndex: -1,
                                    }}
                                  />
                                </View>
                              )}
                            </TouchableOpacity>
                          </View>
                        </View>

                        <View
                          style={{
                            flexDirection: "row",
                            width: "50%",
                            marginTop: 30,
                          }}
                        >
                          {/* <View style={{ flexDirection: 'row', width: '100%', alignItems: 'center',  }}>
                      <Text style={{ fontSize: 14, color: '#9FA2B4', fontWeight: '700', fontFamily: 'Nunitosans-Regular', width: '30%' }}>
                        Position:
                      </Text>
                      <View style={{ width: '75%', justifyContent: 'space-between', flexDirection: 'row', alignItems: 'center' }}>
                        <TextInput
                            editable={!loading}
                            // underlineColorAndroid={Colors.whiteColor}
                            clearButtonMode="while-editing"
                            style={{fontSize: 15, color: "black", fontWeight: '600', fontFamily: 'Nunitosans-Regular', marginLeft: 5} }
                            placeholder="Name"
                            multiline={true}
                            onChangeText={(text) => {
                              // setState({ name: text });
                              setName(text);
                            }}
                            value={name}
                          />
                    </View>
                  </View> */}
                        </View>

                        {/* ***************Border Line**************  */}
                        {/* <View style={{ borderWidth: 0.5, borderColor: '#E5E5E5', marginVertical: 25 }} /> */}

                        <View style={{ flexDirection: "column", marginLeft: 100, }}>
                          <View
                            style={{
                              flexDirection: "row",
                              width: "90%",
                              alignItems: "center",
                              marginBottom: 10,
                            }}
                          >
                            <Text
                              style={{
                                fontFamily: "Nunitosans-Bold",
                                fontSize: switchMerchant ? 10 : 14,
                                color: "black",
                                width: "30%",
                                fontWeight: "500",
                                textAlign: "left",
                              }}
                            >
                              Role
                            </Text>
                            <View style={{ width: 200, height: 40 }}>
                              {/* {
                              [
                                {
                                  label: 'Frontliner',
                                  value: 'frontliner',
                                },
                                {
                                  label: 'Store Manager',
                                  value: 'store_manager',
                                },
                                {
                                  label: 'Admin',
                                  value: 'admin',
                                },
                              ].find(option => option.value === role)
                                ? */}
                              <DropDownPicker
                                style={{
                                  backgroundColor: Colors.fieldtBgColor,
                                  width: 210,
                                  height: 40,
                                  borderRadius: 10,
                                  borderWidth: 1,
                                  borderColor: "#E5E5E5",
                                  flexDirection: "row",
                                }}
                                dropDownContainerStyle={{
                                  width: 210,
                                  backgroundColor: Colors.fieldtBgColor,
                                  borderColor: "#E5E5E5",
                                }}
                                labelStyle={{
                                  marginLeft: 5,
                                  flexDirection: "row",
                                }}
                                textStyle={{
                                  fontSize: 14,
                                  fontFamily: 'NunitoSans-Regular',

                                  marginLeft: 5,
                                  paddingVertical: 10,
                                  flexDirection: "row",
                                }}
                                selectedItemContainerStyle={{
                                  flexDirection: "row",
                                }}

                                showArrowIcon={true}
                                ArrowDownIconComponent={({ style }) => (
                                  <Ionicon
                                    size={25}
                                    color={Colors.fieldtTxtColor}
                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                    name="chevron-down-outline"
                                  />
                                )}
                                ArrowUpIconComponent={({ style }) => (
                                  <Ionicon
                                    size={25}
                                    color={Colors.fieldtTxtColor}
                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                    name="chevron-up-outline"
                                  />
                                )}

                                showTickIcon={true}
                                TickIconComponent={({ press }) => (
                                  <Ionicon
                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                    color={
                                      press ? Colors.fieldtBgColor : Colors.primaryColor
                                    }
                                    name={'md-checkbox'}
                                    size={25}
                                  />
                                )}
                                placeholderStyle={{
                                  color: Colors.fieldtTxtColor,
                                  // marginTop: 15,
                                }}
                                dropDownDirection="BOTTOM"

                                items={[
                                  {
                                    label: 'Frontliner',
                                    value: 'frontliner',
                                  },
                                  {
                                    label: 'Store Manager',
                                    value: 'store_manager',
                                  },
                                  {
                                    label: 'Admin',
                                    value: 'admin',
                                  },
                                ]}

                                placeholder={'Position'}
                                onSelectItem={(item) => {
                                  setRole(item.value);
                                }}
                                value={role}

                                open={openRole}
                                setOpen={setOpenRole}
                              />
                              {/* :
                                <></>
                            } */}
                            </View>
                          </View>

                          <View
                            style={{
                              flexDirection: "row",
                              width: "90%",
                              alignItems: "center",
                              zIndex: -1,
                            }}
                          >
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 14,
                                color: "black",
                                fontFamily: "Nunitosans-Bold",
                                width: "30%",
                              }}
                            >
                              Outlet
                            </Text>
                            <View style={{ width: 200, height: 40 }}>
                              <DropDownPicker
                                style={{
                                  backgroundColor: Colors.fieldtBgColor,
                                  width: 210,
                                  height: 40,
                                  borderRadius: 10,
                                  borderWidth: 1,
                                  borderColor: "#E5E5E5",
                                  flexDirection: "row",
                                }}
                                dropDownContainerStyle={{
                                  width: 210,
                                  backgroundColor: Colors.fieldtBgColor,
                                  borderColor: "#E5E5E5",
                                }}
                                labelStyle={{
                                  marginLeft: 5,
                                  flexDirection: "row",
                                }}
                                textStyle={{
                                  fontSize: 14,
                                  fontFamily: 'NunitoSans-Regular',

                                  marginLeft: 5,
                                  paddingVertical: 10,
                                  flexDirection: "row",
                                }}
                                selectedItemContainerStyle={{
                                  flexDirection: "row",
                                }}

                                showArrowIcon={true}
                                ArrowDownIconComponent={({ style }) => (
                                  <Ionicon
                                    size={25}
                                    color={Colors.fieldtTxtColor}
                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                    name="chevron-down-outline"
                                  />
                                )}
                                ArrowUpIconComponent={({ style }) => (
                                  <Ionicon
                                    size={25}
                                    color={Colors.fieldtTxtColor}
                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                    name="chevron-up-outline"
                                  />
                                )}

                                showTickIcon={true}
                                TickIconComponent={({ press }) => (
                                  <Ionicon
                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                    color={
                                      press ? Colors.fieldtBgColor : Colors.primaryColor
                                    }
                                    name={'md-checkbox'}
                                    size={25}
                                  />
                                )}
                                placeholderStyle={{
                                  color: Colors.fieldtTxtColor,
                                  // marginTop: 15,
                                }}
                                dropDownDirection="BOTTOM"

                                items={outletDropdownList}

                                placeholder={'Outlet'}
                                onSelectItem={(item) => {
                                  setSelectedOutletId(item.value);
                                }}
                                value={selectedOutletId}

                                open={openOutlet}
                                setOpen={setOpenOutlet}
                              />
                            </View>
                            {/* <Picker
                              selectedValue={selectedOutletId}
                              style={{
                                height: 40,
                                width: 200,
                                paddingVertical: 0,
                                backgroundColor: Colors.fieldtBgColor,
                                borderRadius: 10,
                                zIndex: -1,
                              }}
                              onValueChange={(item) => {
                                setSelectedOutletId(item);
                              }}
                            >
                              {outletDropdownList.map((value, index) => {
                                return (
                                  <Picker.Item
                                    key={index}
                                    label={value.label}
                                    value={value.value}
                                  />
                                );
                              })}
                            </Picker> */}
                          </View>
                        </View>

                        {/* ***************Border Line**************  */}
                        {/* <View style={{ borderWidth: 0.5, borderColor: '#E5E5E5', marginVertical: 25 }} />

                <View style={{ flexDirection: 'row',}}>

                </View> */}
                        {/* ***************Border Line**************  */}
                        <View
                          style={{
                            borderWidth: 0,
                            borderColor: "#E5E5E5",
                            marginVertical: 25,
                            zIndex: -2,
                          }}
                        />
                      </View>

                      {/* Right Side */}
                      <View
                        style={[
                          { width: "60%", paddingLeft: 10 },
                          switchMerchant
                            ? {
                              width: "60%",
                            }
                            : {},
                        ]}
                      >
                        <View>
                          <View
                            style={{
                              flexDirection: "column",
                              marginLeft: 5,
                              paddingTop: 50,
                            }}
                          >
                            <View style={{ flexDirection: "row" }}>
                              <View
                                style={{
                                  flexDirection: "row",
                                  width: "50%",
                                  alignItems: "center",
                                }}
                              >
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    color: "black",
                                    fontFamily: "Nunitosans-Bold",
                                    width: "30%",
                                  }}
                                >
                                  Name
                                </Text>
                                <TextInput
                                  // editable={!loading}
                                  editable={true}
                                  // underlineColorAndroid={Colors.whiteColor}
                                  clearButtonMode="while-editing"
                                  style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    width: windowWidth * 0.12,
                                    height: switchMerchant ? 35 : 40,
                                    borderRadius: 5,
                                    padding: 5,
                                    marginVertical: 5,
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    paddingLeft: 10,
                                    fontSize: switchMerchant ? 10 : 14,
                                    color: "black",
                                    fontFamily: "Nunitosans-Regular",
                                  }}
                                  placeholder="Name"
                                  placeholderStyle={{
                                    fontFamily: "NunitoSans-Regular",
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  placeholderTextColor={Platform.select({
                                    ios: "#a9a9a9",
                                  })}
                                  multiline={false}
                                  //iOS
                                  clearTextOnFocus={true}
                                  //////////////////////////////////////////////
                                  //Android
                                  onFocus={() => {
                                    setTemp(name);
                                    setName("");
                                  }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  onEndEditing={() => {
                                    if (name == "") {
                                      setName(temp);
                                    }
                                  }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    // setState({ name: text });
                                    setName(text);
                                  }}
                                  value={name}
                                  maxLength={30}
                                />
                              </View>

                              <View
                                style={{
                                  flexDirection: "row",
                                  width: "50%",
                                  alignItems: "center",
                                }}
                              >
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    color: "black",
                                    fontFamily: "Nunitosans-Bold",
                                    width: "37%",
                                  }}
                                >
                                  Username
                                </Text>
                                <View
                                  style={{
                                    width: "75%",
                                    justifyContent: "space-between",
                                    flexDirection: "row",
                                    alignItems: "center",
                                  }}
                                >
                                  <TextInput
                                    editable={!loading}
                                    //underlineColorAndroid={Colors.whiteColor}
                                    clearButtonMode="while-editing"
                                    style={{
                                      backgroundColor: Colors.fieldtBgColor,
                                      width: windowWidth * 0.12,
                                      height: switchMerchant ? 35 : 40,
                                      borderRadius: 5,
                                      padding: 5,
                                      marginVertical: 5,
                                      borderWidth: 1,
                                      borderColor: "#E5E5E5",
                                      paddingLeft: 10,
                                      fontSize: switchMerchant ? 10 : 14,
                                      color: "black",
                                      fontFamily: "Nunitosans-Regular",
                                    }}
                                    placeholder="Username"
                                    placeholderStyle={{
                                      fontFamily: "NunitoSans-Regular",
                                      fontSize: switchMerchant ? 10 : 14,
                                    }}
                                    placeholderTextColor={Platform.select({
                                      ios: "#a9a9a9",
                                    })}
                                    multiline={false}
                                    //iOS
                                    clearTextOnFocus={true}
                                    //////////////////////////////////////////////
                                    //Android
                                    onFocus={() => {
                                      setTemp(username);
                                      setUsername("");
                                    }}
                                    ///////////////////////////////////////////////
                                    //When textinput is not selected
                                    onEndEditing={() => {
                                      if (username == "") {
                                        setUsername(temp);
                                      }
                                    }}
                                    //////////////////////////////////////////////
                                    onChangeText={(text) => {
                                      // setState({ name: text });
                                      setUsername(text);
                                    }}
                                    value={username}
                                  />
                                </View>
                              </View>
                            </View>

                            {/* ***************Border Line**************  */}
                            <View
                              style={{
                                borderWidth: 0.5,
                                borderColor: "#E5E5E5",
                                marginVertical: 21,
                              }}
                            />

                            <View style={{ flexDirection: "row" }}>
                              <View
                                style={{
                                  flexDirection: "row",
                                  width: "50%",
                                  alignItems: "center",
                                }}
                              >
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    color: "black",
                                    fontFamily: "Nunitosans-Bold",
                                    width: "30%",
                                  }}
                                >
                                  Email
                                </Text>
                                <TextInput
                                  editable={
                                    !loading && !selectedOutletEmployeeEdit
                                  }
                                  // underlineColorAndroid={Colors.whiteColor}
                                  clearButtonMode="while-editing"
                                  style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    width: windowWidth * 0.12,
                                    height: switchMerchant ? 35 : 40,
                                    borderRadius: 5,
                                    padding: 5,
                                    marginVertical: 5,
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    paddingLeft: 10,
                                    fontSize: switchMerchant ? 10 : 14,
                                    color: "black",
                                    fontFamily: "Nunitosans-Regular",
                                  }}
                                  placeholder="Email"
                                  placeholderStyle={{
                                    fontFamily: "NunitoSans-Regular",
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  placeholderTextColor={Platform.select({
                                    ios: "#a9a9a9",
                                  })}
                                  autoCapitalize="none"
                                  //iOS
                                  clearTextOnFocus={true}
                                  //////////////////////////////////////////////
                                  //Android
                                  onFocus={() => {
                                    setTemp(email);
                                    setEmail("");
                                  }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  onEndEditing={() => {
                                    if (email == "") {
                                      setEmail(temp);
                                    }
                                  }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    // setState({ email: text });
                                    setEmail(text);
                                  }}
                                  value={email}
                                />
                              </View>

                              <View
                                style={{
                                  flexDirection: "row",
                                  width: "50%",
                                  alignItems: "center",
                                }}
                              ></View>
                            </View>

                            {/* ***************Border Line**************  */}
                            <View
                              style={{
                                borderWidth: 0.5,
                                borderColor: "#E5E5E5",
                                marginVertical: 21,
                              }}
                            />

                            <View style={{ flexDirection: "row" }}>
                              <View
                                style={{
                                  flexDirection: "row",
                                  width: "50%",
                                  alignItems: "center",
                                }}
                              >
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    color: "black",
                                    fontFamily: "Nunitosans-Bold",
                                    width: "30%",
                                  }}
                                >
                                  Number
                                </Text>
                                <TextInput
                                  // editable={!loading}
                                  editable={true}
                                  // underlineColorAndroid={Colors.whiteColor}
                                  clearButtonMode="while-editing"
                                  style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    width: windowWidth * 0.12,
                                    height: switchMerchant ? 35 : 40,
                                    borderRadius: 5,
                                    padding: 5,
                                    marginVertical: 5,
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    paddingLeft: 10,
                                    fontSize: switchMerchant ? 10 : 14,
                                    color: "black",
                                    fontFamily: "Nunitosans-Regular",
                                  }}
                                  placeholder="Contact Number"
                                  placeholderStyle={{
                                    fontFamily: "NunitoSans-Regular",
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  placeholderTextColor={Platform.select({
                                    ios: "#a9a9a9",
                                  })}
                                  multiline={false}
                                  //iOS
                                  clearTextOnFocus={true}
                                  //////////////////////////////////////////////
                                  //Android
                                  onFocus={() => {
                                    setTemp(number);
                                    setNumber("");
                                  }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  onEndEditing={() => {
                                    if (number == "") {
                                      setNumber(temp);
                                    }
                                  }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    // setState({ number: text });
                                    setNumber(text);
                                  }}
                                  value={number}
                                />
                              </View>

                              <View
                                style={{
                                  flexDirection: "row",
                                  width: "50%",
                                  alignItems: "center",
                                }}
                              >
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    color: "black",
                                    fontFamily: "Nunitosans-Bold",
                                    width: "37%",
                                  }}
                                >
                                  Password
                                </Text>
                                <View
                                  style={{
                                    width: "75%",
                                    justifyContent: "space-between",
                                    flexDirection: "row",
                                    alignItems: "center",
                                  }}
                                >
                                  {!selectedOutletEmployeeEdit ? (
                                    <TextInput
                                      editable={!loading}
                                      //underlineColorAndroid={Colors.whiteColor}
                                      clearButtonMode="while-editing"
                                      style={{
                                        backgroundColor: Colors.fieldtBgColor,
                                        width: windowWidth * 0.12,
                                        height: switchMerchant ? 35 : 40,
                                        borderRadius: 5,
                                        padding: 5,
                                        marginVertical: 5,
                                        borderWidth: 1,
                                        borderColor: "#E5E5E5",
                                        paddingLeft: 10,
                                        fontSize: switchMerchant ? 10 : 14,
                                        color: "black",
                                        fontFamily: "Nunitosans-Regular",
                                      }}
                                      placeholder="Password"
                                      placeholderStyle={{
                                        fontFamily: "NunitoSans-Regular",
                                        fontSize: switchMerchant ? 10 : 14,
                                      }}
                                      placeholderTextColor={Platform.select({
                                        ios: "#a9a9a9",
                                      })}
                                      //placeholderStyle={{paddingTop: switchMerchant ? 0 : 5}}
                                      secureTextEntry={true}
                                      autoCapitalize="none"
                                      //iOS
                                      clearTextOnFocus={true}
                                      //////////////////////////////////////////////
                                      //Android
                                      onFocus={() => {
                                        setTemp(password);
                                        setPassword("");
                                      }}
                                      ///////////////////////////////////////////////
                                      //When textinput is not selected
                                      onEndEditing={() => {
                                        if (password == "") {
                                          setPassword(temp);
                                        }
                                      }}
                                      //////////////////////////////////////////////
                                      onChangeText={(text) => {
                                        // setState({ password: text });
                                        setPassword(text);
                                      }}
                                      value={password}
                                    />
                                  ) : (
                                    <Text
                                      style={{
                                        marginLeft: 5,
                                        fontFamily: "NunitoSans-Regular",
                                        fontSize: switchMerchant ? 10 : 14,
                                      }}
                                    >
                                      ********
                                    </Text>
                                  )}
                                </View>
                              </View>
                            </View>

                            {/* ***************Border Line**************  */}
                            <View
                              style={{
                                borderWidth: 0.5,
                                borderColor: "#E5E5E5",
                                marginVertical: 21,
                                zIndex: -1,
                              }}
                            />

                            <View style={{ flexDirection: "row" }}>
                              <View
                                style={{
                                  flexDirection: "row",
                                  width: "50%",
                                  alignItems: "center",
                                }}
                              >
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    color: "black",
                                    fontFamily: "Nunitosans-Bold",
                                    width: "30%",
                                  }}
                                >
                                  PIN
                                </Text>
                                <TextInput
                                  // editable={!loading}

                                  // editable={!(
                                  //   selectedOutletEmployeeEdit &&
                                  //   selectedOutletEmployeeEdit.firebaseUid
                                  // )}
                                  // disabled={userRole != ROLE_TYPE.ADMIN || (
                                  //   selectedOutletEmployeeEdit &&
                                  //   selectedOutletEmployeeEdit.firebaseUid
                                  // )}
                                  disabled={userRole != ROLE_TYPE.ADMIN}
                                  // underlineColorAndroid={Colors.whiteColor}
                                  clearButtonMode="while-editing"
                                  style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    width: windowWidth * 0.12,
                                    height: switchMerchant ? 35 : 40,
                                    borderRadius: 5,
                                    padding: 5,
                                    marginVertical: 5,
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    paddingLeft: 10,
                                    fontSize: switchMerchant ? 10 : 14,
                                    color: "black",
                                    fontFamily: "Nunitosans-Regular",
                                  }}
                                  placeholder="PIN"
                                  placeholderStyle={{
                                    fontFamily: "NunitoSans-Regular",
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  placeholderTextColor={Platform.select({
                                    ios: "#a9a9a9",
                                  })}
                                  keyboardType="numeric"
                                  //iOS
                                  clearTextOnFocus={true}
                                  //////////////////////////////////////////////
                                  //Android
                                  onFocus={() => {
                                    setTemp(pin);
                                    setPin("");
                                  }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  onEndEditing={() => {
                                    if (pin == "") {
                                      setPin(temp);
                                    }
                                  }}
                                  //////////////////////////////////////////////

                                  onChangeText={(text) => {
                                    // setState({ name: text });
                                    if (text.length > 4) {
                                      window.confirm(
                                        "Please enter 4 digit pin"
                                      );
                                    } else {
                                      setPin(text);
                                    }
                                  }}
                                  value={pin}
                                />
                              </View>

                              <View
                                style={{
                                  flexDirection: "row",
                                  width: "50%",
                                  alignItems: "center",
                                }}
                              >
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    color: "black",
                                    fontFamily: "Nunitosans-Bold",
                                    width: "37%",
                                  }}
                                >
                                  Privileges
                                </Text>
                                <View style={{ width: "75%" }}>
                                  <DropDownPicker
                                    disabled={userRole !== ROLE_TYPE.ADMIN}
                                    style={{
                                      backgroundColor: Colors.fieldtBgColor,
                                      width: windowWidth * 0.12,
                                      height: 40,
                                      borderRadius: 10,
                                      borderWidth: 1,
                                      borderColor: "#E5E5E5",
                                      flexDirection: "row",
                                    }}
                                    dropDownContainerStyle={{
                                      width: windowWidth * 0.12,
                                      backgroundColor: Colors.fieldtBgColor,
                                      borderColor: "#E5E5E5",
                                    }}
                                    labelStyle={{
                                      marginLeft: 5,
                                      flexDirection: "row",
                                    }}
                                    textStyle={{
                                      fontSize: 14,
                                      fontFamily: 'NunitoSans-Regular',

                                      marginLeft: 5,
                                      paddingVertical: 10,
                                      flexDirection: "row",
                                    }}
                                    selectedItemContainerStyle={{
                                      flexDirection: "row",
                                    }}

                                    showArrowIcon={true}
                                    ArrowDownIconComponent={({ style }) => (
                                      <Ionicon
                                        size={25}
                                        color={Colors.fieldtTxtColor}
                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                        name="chevron-down-outline"
                                      />
                                    )}
                                    ArrowUpIconComponent={({ style }) => (
                                      <Ionicon
                                        size={25}
                                        color={Colors.fieldtTxtColor}
                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                        name="chevron-up-outline"
                                      />
                                    )}

                                    showTickIcon={true}
                                    TickIconComponent={({ press }) => (
                                      <Ionicon
                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                        color={
                                          press ? Colors.fieldtBgColor : Colors.primaryColor
                                        }
                                        name={'md-checkbox'}
                                        size={25}
                                      />
                                    )}
                                    placeholder={'Select a Type'}
                                    placeholderStyle={{
                                      color: Colors.fieldtTxtColor,
                                      // marginTop: 15,
                                    }}
                                    // searchable
                                    // searchableStyle={{
                                    //   paddingHorizontal: windowWidth * 0.0079,
                                    // }}
                                    value={privileges}
                                    items={Object.values(PRIVILEGES_NAME).map((item) => ({
                                      label: item,
                                      value: item,
                                    }))}
                                    multiple={true}
                                    multipleText={`${privileges.length} privilege(s)`}
                                    onSelectItem={(items) => {
                                      setPrivileges(items.map(item => item.value))
                                    }}
                                    open={openPriv}
                                    setOpen={setOpenPriv}
                                    dropDownDirection="BOTTOM"
                                  />
                                </View>
                              </View>
                            </View>
                          </View>

                          {/* ***************Border Line**************  */}
                          <View
                            style={{
                              borderWidth: 0.5,
                              borderColor: '#E5E5E5',
                              marginVertical: 21,
                              zIndex: -1,
                              marginBottom: 30,
                            }}
                          />

                          <View style={{ flexDirection: "row", zIndex: -1, }}>
                            <View
                              style={{
                                flexDirection: "row",
                                width: "50%",
                                alignItems: "center",
                              }}
                            >
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 10 : 14,
                                  color: "black",
                                  fontFamily: "Nunitosans-Bold",
                                  width: "31%",
                                }}
                              >
                                {'Screens to\nblock'}
                              </Text>
                              <View style={{}}>
                                <DropDownPicker
                                  style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    width: windowWidth * 0.12,
                                    height: 40,
                                    borderRadius: 10,
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    flexDirection: "row",
                                  }}
                                  dropDownContainerStyle={{
                                    width: windowWidth * 0.12,
                                    backgroundColor: Colors.fieldtBgColor,
                                    borderColor: "#E5E5E5",
                                    height: 150,
                                  }}
                                  labelStyle={{
                                    marginLeft: 5,
                                    flexDirection: "row",
                                  }}
                                  textStyle={{
                                    fontSize: 14,
                                    fontFamily: 'NunitoSans-Regular',

                                    marginLeft: 5,
                                    paddingVertical: 10,
                                    flexDirection: "row",
                                  }}
                                  selectedItemContainerStyle={{
                                    flexDirection: "row",
                                  }}

                                  showArrowIcon={true}
                                  ArrowDownIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-down-outline"
                                    />
                                  )}
                                  ArrowUpIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-up-outline"
                                    />
                                  )}

                                  showTickIcon={true}
                                  TickIconComponent={({ press }) => (
                                    <Ionicon
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      color={
                                        press ? Colors.fieldtBgColor : Colors.primaryColor
                                      }
                                      name={'md-checkbox'}
                                      size={25}
                                    />
                                  )}
                                  placeholder={'Select screen'}
                                  placeholderStyle={{
                                    color: Colors.fieldtTxtColor,
                                    // marginTop: 15,
                                  }}
                                  searchable
                                  searchableStyle={{
                                    paddingHorizontal: windowWidth * 0.0079,
                                  }}
                                  value={screensToBlock}
                                  items={Object.values(SCREEN_NAME).map((item) => ({
                                    label: SCREEN_NAME_PARSED[item] ? SCREEN_NAME_PARSED[item] : item,
                                    value: item,
                                  }))}
                                  multiple={true}
                                  multipleText={`${screensToBlock.length} screen(s)`}
                                  onSelectItem={(items) => {
                                    setScreensToBlock(items.map(item => item.value))
                                  }}
                                  open={openSTB}
                                  setOpen={setOpenSTB}
                                  dropDownDirection="BOTTOM"
                                />
                              </View>
                            </View>
                          </View>
                        </View>
                      </View>
                    </View>

                    {/* <View style={{ flexDirection: 'row' }}>
                        <View style={{ justifyContent: 'center', height: 50, width: '35%' }}>
                          <Text style={{ fontFamily: 'NunitoSans-SemiBold', fontSize: 16, }}>Day of Week</Text>
                        </View>
                        <View
                          style={{ height: 50, paddingHorizontal: 20, backgroundColor: Colors.fieldtBgColor, borderRadius: 5, marginBottom: 20, width: "55%", marginHorizontal: 10, flexDirection: 'row', alignItems: 'center', justifyContent: "center", borderColor: '#E5E5E5', borderWidth: 1, alignContent: 'center', fontFamily: 'NunitoSans-Regular', fontSize: 16, borderRadius: 12, paddingLeft: 30, }}>
                          <Text style={{ width: "90%", fontFamily: "NunitoSans-Regular", color: Colors.descriptionColor, }}>
                            {dayOfWeek ? moment(dayOfWeek).format('DD/MM/YYYY') : 'Day of Week'}
                          </Text>
                          <TouchableOpacity
                            style={{
                              alignItems: 'center'
                            }}
                            onPress={() => {
                              setPickDate('dayOfWeek');
                              setShowDateTimePicker(true);
                            }}>
                            <EvilIcons name="calendar" size={40} color={Colors.primaryColor} />
                          </TouchableOpacity>
                        </View>
                      </View> */}
                  {/* </ScrollView> */}
                </View>
                {/****************** Test clock regular and over time *******************/}

                {/* <View style={{ flexDirection: 'row' }}>
                        <View style={{ justifyContent: 'center', height: 50, width: '35%' }}>
                          <Text style={{ fontFamily: 'NunitoSans-SemiBold', fontSize: 16, }}>Regular</Text>
                        </View>
                        <View
                          style={{ height: 50, paddingHorizontal: 20, backgroundColor: Colors.fieldtBgColor, borderRadius: 5, marginBottom: 20, width: "55%", marginHorizontal: 10, flexDirection: 'row', alignItems: 'center', justifyContent: "center", borderColor: '#E5E5E5', borderWidth: 1, alignContent: 'center', fontFamily: 'NunitoSans-Regular', fontSize: 16, borderRadius: 12, paddingLeft: 30, }}>
                          <Text style={{ width: "90%", fontFamily: "NunitoSans-Regular", color: Colors.descriptionColor }}>
                            {regularTime ? moment(regularTime).format('hh:mmA') : 'Regular Time'}
                          </Text> 
                          <TouchableOpacity
                            style={{
                              alignItems: 'center'
                            }}
                            onPress={() => {
                              setPickTime('regularTime');
                              setShowDateTimePicker1(true);
                            }}>
                            <EvilIcons name="calendar" size={40} color={Colors.primaryColor} />
                          </TouchableOpacity>
                        </View>
                      </View> */}

                {/* <View style={{ flexDirection: 'row' }}>
                        <View style={{ justifyContent: 'center', height: 50, width: '35%' }}>
                          <Text style={{ fontFamily: 'NunitoSans-SemiBold', fontSize: 16, }}>Overtime</Text>
                        </View>
                        <View
                          style={{ height: 50, paddingHorizontal: 20, backgroundColor: Colors.fieldtBgColor, borderRadius: 5, marginBottom: 20, width: "55%", marginHorizontal: 10, flexDirection: 'row', alignItems: 'center', justifyContent: "center", borderColor: '#E5E5E5', borderWidth: 1, alignContent: 'center', fontFamily: 'NunitoSans-Regular', fontSize: 16, borderRadius: 12, paddingLeft: 30, }}>
                          <Text style={{ width: "90%", fontFamily: "NunitoSans-Regular", color: Colors.descriptionColor }}>
                            {overTime ? moment(overTime).format('hh:mmA') : 'Over Time'}
                          </Text>
                          <TouchableOpacity
                            style={{
                              alignItems: 'center'
                            }}
                            onPress={() => {
                              setPickTime('overTime');
                              setShowDateTimePicker1(true);
                            }}>
                            <EvilIcons name="calendar" size={40} color={Colors.primaryColor} />
                          </TouchableOpacity>
                        </View>
                      </View> */}
                {/****************** Test clock regular and over time *******************/}

                {/* <View style={{ height: 120 }}></View> */}
                {/* <View
                  style={{
                    backgroundColor: Colors.primaryColor,
                    width: 200,
                    height: 40,
                    marginLeft: 20,
                    marginVertical: 15,
                    borderRadius: 5,
                    marginBottom: 60
                  }}>
                  <TouchableOpacity
                    onPress={() => {
                      addEmployeeFunc();
                    }}>
                    <Text
                      style={{
                        color: Colors.whiteColor,
                        alignSelf: 'center',
                        marginVertical: 10,
                      }}>
                      Save
                  </Text>
                  </TouchableOpacity>

                </View> */}
                {/* </KeyboardAwareScrollView> */}
              </View>
            ) : null}

            {showDetail ? (
              <View
                style={{
                  marginTop: 20,
                  marginLeft: 30,
                  backgroundColor: Colors.whiteColor,
                  width: "87%",
                  height: 480,
                  elevation: 5,
                  shadowColor: Colors.blackColor,
                  shadowOffset: 1,
                  shadowOpacity: 10,
                }}
              >
                <Text
                  style={{
                    paddingVertical: 15,
                    marginLeft: 20,
                    color: "#a3a3a3",
                  }}
                >
                  Employee Details
                </Text>
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                  }}
                >
                  <TouchableOpacity
                    style={{
                      marginRight: 430,
                      width: 120,
                      flexDirection: "row",
                      alignItems: "center",
                      paddingLeft: 15,
                      borderRadius: 10,
                      height: windowHeight * 0.055,
                      backgroundColor: Colors.whiteColor,
                    }}
                    onPress={() => {
                      setState({ showDetail: false, addEmployeeItem: true });
                    }}
                  >
                    <Feather
                      name="chevron-left"
                      size={30}
                      color={Colors.primaryColor}
                    />
                    <Text
                      style={{
                        fontFamily: "NunitoSans-Regular",
                        color: Colors.primaryColor,
                        marginBottom: Platform.OS === "ios" ? 0 : 1,
                      }}
                    >
                      Back
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => {
                      setState({ visible: true });
                    }}
                    style={{
                      marginHorizontal: 30,
                      flexDirection: "row",
                      alignSelf: "center",
                      alignItems: "center",
                    }}
                  >
                    <Feather name="trash-2" size={20} color="#eb3446" />
                    <Text style={{ color: "#eb3446" }}>
                      Remove this employee
                    </Text>
                  </TouchableOpacity>
                </View>
                <AsyncImage
                  source={{ uri: showEmployee.avatar }}
                  item={showEmployee}
                  style={{
                    alignSelf: "center",
                    backgroundColor: Colors.secondaryColor,
                    paddingVertical: 60,
                    paddingHorizontal: 60,
                    borderRadius: 100,
                  }}
                />

                <Text
                  style={{
                    alignSelf: "center",
                    fontWeight: "bold",
                    fontSize: 20,
                    marginTop: 10,
                  }}
                >
                  {/* {Employee} */}
                </Text>
                <View style={{ flexDirection: "row", marginTop: 20 }}>
                  <View
                    style={{ flexDirection: "row", flex: 1, marginLeft: 100 }}
                  >
                    <Text
                      style={{
                        alignSelf: "center",
                        fontWeight: "700",
                        fontSize: 15,
                        marginTop: 10,
                      }}
                    >
                      Name:
                    </Text>
                    <Text
                      style={{
                        alignSelf: "center",
                        fontSize: 15,
                        marginTop: 10,
                        marginLeft: 48,
                        color: "#9c9c9c",
                      }}
                    >
                      {showEmployee.name}
                    </Text>
                  </View>
                  <View
                    style={{ flexDirection: "row", flex: 1, marginLeft: 100 }}
                  >
                    <Text
                      style={{
                        alignSelf: "center",
                        fontWeight: "700",
                        fontSize: 15,
                        marginTop: 10,
                      }}
                    >
                      Contact No.:
                    </Text>
                    <Text
                      style={{
                        alignSelf: "center",
                        fontSize: 15,
                        marginTop: 10,
                        marginLeft: 27,
                        color: "#9c9c9c",
                      }}
                    >
                      {showEmployee.number}
                    </Text>
                  </View>
                </View>
                <View style={{ flexDirection: "row", marginTop: 20 }}>
                  <View
                    style={{ flexDirection: "row", flex: 1, marginLeft: 100 }}
                  >
                    <Text
                      style={{
                        alignSelf: "center",
                        fontWeight: "700",
                        fontSize: 15,
                        marginTop: 10,
                      }}
                    >
                      Status:
                    </Text>
                    <Text
                      style={{
                        alignSelf: "center",
                        fontSize: 15,
                        marginTop: 10,
                        marginLeft: 45,
                        color: "#9c9c9c",
                      }}
                    >
                      {showEmployee.role}
                    </Text>
                  </View>
                  <View
                    style={{ flexDirection: "row", flex: 1, marginLeft: 100 }}
                  >
                    <Text
                      style={{
                        alignSelf: "center",
                        fontWeight: "700",
                        fontSize: 15,
                        marginTop: 10,
                      }}
                    >
                      Email:
                    </Text>
                    <Text
                      style={{
                        alignSelf: "center",
                        fontSize: 15,
                        marginTop: 10,
                        marginLeft: 70,
                        color: "#9c9c9c",
                      }}
                    >
                      {showEmployee.email}
                    </Text>
                  </View>
                </View>
                <View style={{ flexDirection: "row", marginTop: 20 }}>
                  <View
                    style={{ flexDirection: "row", flex: 1, marginLeft: 100 }}
                  >
                    <Text
                      style={{
                        alignSelf: "center",
                        fontWeight: "700",
                        fontSize: 15,
                        marginTop: 10,
                      }}
                    >
                      Position:
                    </Text>
                    <Text
                      style={{
                        alignSelf: "center",
                        fontSize: 15,
                        marginTop: 10,
                        marginLeft: 33,
                        color: "#9c9c9c",
                      }}
                    >
                      {showEmployee.role}
                    </Text>
                  </View>
                </View>
                <View style={{ flexDirection: "row", marginTop: 20 }}>
                  <View
                    style={{ flexDirection: "row", flex: 1, marginLeft: 100 }}
                  >
                    <Text
                      style={{
                        alignSelf: "center",
                        fontWeight: "700",
                        fontSize: 15,
                        marginTop: 10,
                      }}
                    >
                      Day of Week:
                    </Text>
                    <Text
                      style={{
                        alignSelf: "center",
                        fontSize: 15,
                        marginTop: 10,
                        marginLeft: 33,
                        color: "#9c9c9c",
                      }}
                    >
                      {showEmployee.dayOfWeek}
                    </Text>
                  </View>
                </View>
                <View style={{ flexDirection: "row", marginTop: 20 }}>
                  <View
                    style={{ flexDirection: "row", flex: 1, marginLeft: 100 }}
                  >
                    <Text
                      style={{
                        alignSelf: "center",
                        fontWeight: "700",
                        fontSize: 15,
                        marginTop: 10,
                      }}
                    >
                      Regular Time:
                    </Text>
                    <Text
                      style={{
                        alignSelf: "center",
                        fontSize: 15,
                        marginTop: 10,
                        marginLeft: 33,
                        color: "#9c9c9c",
                      }}
                    >
                      {showEmployee.regularTime}
                    </Text>
                  </View>
                </View>
                <View style={{ flexDirection: "row", marginTop: 20 }}>
                  <View
                    style={{ flexDirection: "row", flex: 1, marginLeft: 100 }}
                  >
                    <Text
                      style={{
                        alignSelf: "center",
                        fontWeight: "700",
                        fontSize: 15,
                        marginTop: 10,
                      }}
                    >
                      Over Time:
                    </Text>
                    <Text
                      style={{
                        alignSelf: "center",
                        fontSize: 15,
                        marginTop: 10,
                        marginLeft: 33,
                        color: "#9c9c9c",
                      }}
                    >
                      {showEmployee.overTime}
                    </Text>
                  </View>
                </View>
              </View>
            ) : null}
          </View>
          {/* </ScrollView> */}
        </ScrollView>
      </View>
    </View>
    ///UserIdleWrapper >
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: "row",
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: "row",
    alignItems: "center",
  },
  listItem: {
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    //width: windowWidth * Styles.sideBarWidth,
    // shadowColor: "#000",
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    padding: 20,
    width: Dimensions.get("window").width * (1 - Styles.sideBarWidth),
    backgroundColor: Colors.highlightColor,
  },
  textInput: {
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginBottom: 20,
    width: 300,
  },
  dropDown: {
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: "red",
    borderRadius: 5,
    marginBottom: 20,
    width: 300,
  },
  addEmployee: {
    marginTop: 20,
    justifyContent: "center",
    alignItems: "center",
    width: Dimensions.get("window").width - 170,
    backgroundColor: Colors.whiteColor,
    borderRadius: 5,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  submitText: {
    height:
      Platform.OS == "ios"
        ? Dimensions.get("window").height * 0.06
        : Dimensions.get("window").height * 0.05,
    paddingVertical: 5,
    paddingHorizontal: 20,
    flexDirection: "row",
    color: "#4cd964",
    textAlign: "center",
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    justifyContent: "center",
    alignContent: "center",
    alignItems: "center",
    marginRight: 10,
  },
  footer: {
    flexDirection: "row",
    justifyContent: "center",
    alignContent: "center",
    alignItems: "center",
  },
  titleList: {
    backgroundColor: "#ffffff",
    flexDirection: "row",
    paddingVertical: 20,
    paddingHorizontal: 20,
    marginTop: 20,
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
  textInput1: {
    width: 250,
    height: 40,
    backgroundColor: "white",

    borderRadius: 20,
    marginRight: "25%",
    flexDirection: "row",
    alignContent: "center",
    alignItems: "center",
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: "center",
  },
  circleIcon2: {
    width: 60,
    height: 60,
    marginRight: 10,
    alignSelf: "center",
  },
  confirmBox: {
    width: 350,
    height: 260,
    borderRadius: 10,
    backgroundColor: Colors.whiteColor,
  },
  confirmBoxRemove: {
    width: 520,
    height: 300,
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
    justifyContent: "space-between",
  },
  headerLeftStyle: {
    width: useWindowDimensions.width * 0.17,
    justifyContent: "center",
    alignItems: "center",
  },
});
export default EmployeeScreen;
