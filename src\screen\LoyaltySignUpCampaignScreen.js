import React, {
  Component,
  useReducer,
  useState,
  useEffect,
  useCallback,
} from "react";
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  Alert,
  TouchableOpacity,
  Modal,
  Dimensions,
  TextInput,
  FlatList,
  KeyboardAvoidingView,
  ActivityIndicator,
  useWindowDimensions,
} from "react-native";
//import CheckBox from 'react-native-check-box';
// import CheckBox from '@react-native-community/checkbox';
import Colors from "../constant/Colors";
import Styles from "../constant/Styles";
import SideBar from "./SideBar";
import AsyncStorage from "@react-native-async-storage/async-storage";
import * as User from "../util/User";
import Icon from "react-native-vector-icons/Ionicons";
import Icon1 from "react-native-vector-icons/Feather";
import Ionicon from "react-native-vector-icons/Ionicons";
//import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import ApiClient from "../util/ApiClient";
import API from "../constant/API";
import DropDownPicker from "react-native-dropdown-picker";
//import GCalendar from '../assets/svg/GCalendar';
import Feather from "react-native-vector-icons/Feather";
import EvilIcons from "react-native-vector-icons/EvilIcons";
import moment from "moment";
import DateTimePickerModal from "react-native-modal-datetime-picker";
import TimeKeeper from "react-timekeeper";
//import { isTablet } from 'react-native-device-detection';
import {
  MERCHANT_VOUCHER_CODE_FORMAT,
  MERCHANT_VOUCHER_TYPE,
  SEGMENT_TYPE,
  ORDER_TYPE,
  ORDER_TYPE_PARSED,
  ORDER_TYPE_DROP_DOWN_LIST,
  EXPAND_TAB_TYPE,
} from "../constant/common";
import { CommonStore } from "../store/commonStore";
import { UserStore } from "../store/userStore";
import { MerchantStore } from "../store/merchantStore";
import { OutletStore } from "../store/outletStore";
//import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
//import RNPickerSelect from 'react-native-picker-select';
//import { useKeyboard } from '../hooks';
//import { get } from 'react-native/Libraries/Utilities/PixelRatio';
//import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
//import Switch from 'react-native-switch-pro';
import {
  EFFECTIVE_DAY_DROPDOWN_LIST,
  EFFECTIVE_DAY_DROPDOWN_LIST2,
  TARGET_USER_GROUP_DROPDOWN_LIST,
  PROMOTION_TYPE_VARIATION,
  PROMOTION_TYPE_VARIATION_DROPDOWN_LIST,
  EFFECTIVE_TYPE,
  CRM_SEGMENT_DROPDOWN_LIST,
} from "../constant/promotions";
import {
  areArraysEqual,
  uploadImageToFirebaseStorage,
  isObjectEqual,
  sliceUnicodeStringV2WithDots,
  uploadImageToFirebaseStorage64,
  _base64ToArrayBuffer,
  getTransformForScreenInsideNavigation,
} from "../util/common";
import AsyncImage from "../components/asyncImage";
import "react-native-get-random-values";
import { v4 as uuidv4 } from "uuid";
import AntDesign from "react-native-vector-icons/AntDesign";
//import NumericInput from 'react-native-numeric-input';
import FontAwesome5 from "react-native-vector-icons/FontAwesome5";
//import { getAllExternalFilesDirs } from 'react-native-fs';
import {
  LOYALTY_CAMPAIGN_DROPDOWN_LIST,
  LOYALTY_CAMPAIGN_TYPE,
  LOYALTY_CAMPAIGN_TYPE_PARSED,
  LOYALTY_PROMOTION_TYPE,
  LOYALTY_PROMOTION_TYPE_DROPDOWN_LIST,
} from "../constant/loyalty";
import APILocal from "../util/apiLocalReplacers";
import { useFocusEffect, useLinkTo } from "@react-navigation/native";
import { useFilePicker } from "use-file-picker";
import { prefix } from "../constant/env";
import Select from "react-select";
import personicon from "../assets/image/default-profile.png";
import headerLogo from "../assets/image/logo.png";
import { ReactComponent as Edit } from "../assets/svg/Edit.svg";
import MultiSelect from "react-multiple-select-dropdown-lite";
import "../constant/styles.css";

//import UserIdleWrapper from '../components/userIdleWrapper';

//////////////////////////////////////////////////////////////////////////////////////////////////////////

const LoyaltySignUpCampaignScreen = (props) => {
  const { navigation } = props;

  const linkTo = useLinkTo();

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  //const [keyboardHeight] = useKeyboard();

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [campaignName, setCampaignName] = useState("");
  const [isEnableSellOnline, setIsEnableSellOnline] = useState(false);

  const [selectedTargetUserGroup, setSelectedTargetUserGroup] = useState(
    TARGET_USER_GROUP_DROPDOWN_LIST[0].value
  );
  const [selectedTargetSegmentGroupList, setSelectedTargetSegmentGroupList] =
    useState([CRM_SEGMENT_DROPDOWN_LIST[0].value]);
  const [switchMerchant, setSwitchMerchant] = useState(false);
  const [promoCode, setPromoCode] = useState("");
  const [isPromoCodeUsageLimit, setIsPromoCodeUsageLimit] = useState(false);
  const [promoCodeUsageLimit, setPromoCodeUsageLimit] = useState("");
  const [promoCodeUsageLimitTicked, setPromoCodeUsageLimitTicked] =
    useState(false);

  const [selectedEffectiveType, setSelectedEffectiveType] = useState(
    EFFECTIVE_TYPE.DAY
  );
  // const [selectedEffectiveTypeOptions, setSelectedEffectiveTypeOptions] =
  //   useState([EFFECTIVE_DAY_DROPDOWN_LIST[0].value]);
  const [selectedEffectiveTypeOptions, setSelectedEffectiveTypeOptions] =
    useState({
      label: EFFECTIVE_DAY_DROPDOWN_LIST[0].label,
      value: EFFECTIVE_DAY_DROPDOWN_LIST[0].value,
    });
  const [selectedEffectiveDay, setSelectedEffectiveDay] = useState(
    EFFECTIVE_DAY_DROPDOWN_LIST[0].value
  );
  const [showEffectiveTimeStartPicker, setShowEffectiveTimeStartPicker] =
    useState(false);
  const [showEffectiveTimeEndPicker, setShowEffectiveTimeEndPicker] =
    useState(false);
  const [effectiveTimeStart, setEffectiveTimeStart] = useState(moment());
  const [effectiveTimeEnd, setEffectiveTimeEnd] = useState(moment());

  const [showPromoDateStartPicker, setShowPromoDateStartPicker] =
    useState(false);
  const [showPromoDateEndPicker, setShowPromoDateEndPicker] = useState(false);
  const [showPromoTimeStartPicker, setShowPromoTimeStartPicker] =
    useState(false);
  const [showPromoTimeEndPicker, setShowPromoTimeEndPicker] = useState(false);
  const [promoDateStart, setPromoDateStart] = useState(moment());
  const [promoDateEnd, setPromoDateEnd] = useState(moment());
  const [promoTimeStart, setPromoTimeStart] = useState(moment());
  const [promoTimeEnd, setPromoTimeEnd] = useState(moment());

  const [campaignDescription, setCampaignDescription] = useState("");

  // const [selectedPromotionType, setSelectedPromotionType] = useState(
  //   LOYALTY_PROMOTION_TYPE_DROPDOWN_LIST[0].value
  // );
  const [selectedPromotionType, setSelectedPromotionType] = useState({
    label: LOYALTY_PROMOTION_TYPE_DROPDOWN_LIST[0].name,
    value: LOYALTY_PROMOTION_TYPE_DROPDOWN_LIST[0].value,
  });

  const [
    isRequireSpecificProductPurchase,
    setIsRequireSpecificProductPurchase,
  ] = useState(false);

  const [image, setImage] = useState("");
  const [imageType, setImageType] = useState("");
  const [isImageChanged, setIsImageChanged] = useState(false);

  const [outletDropdownList, setOutletDropdownList] = useState([]);
  const [selectedOutletList, setSelectedOutletList] = useState([]); // multi-outlets

  //const [availabilityDropdownList, setAvailabilityDropdownList] = useState([]); // DropDownList for Dine in, TakeAway, Delivery
  const [selectedAvailability, setSelectedAvailability] = useState({
    label: ORDER_TYPE_DROP_DOWN_LIST[0].value,
    value: ORDER_TYPE_DROP_DOWN_LIST[0].value,
  }); // Enable Promotion for Dine in, TakeAway, Delivery

  ///////////////////////////////////////////////////////////////////////////////////////////////////////////

  const [isChecked, setIsChecked] = useState(false);
  const [showDateTimePicker_Date, setShowDateTimePicker_Date] = useState(false);
  const [showDateTimePicker_Time, setShowDateTimePicker_Time] = useState(false);
  //const [isLoading1, setIsLoading1] = useState(false);

  /////////////////////////////////////////////////////////////////////////////////////////////////////////

  const userId = UserStore.useState((s) => s.firebaseUid);
  const userName = UserStore.useState((s) => s.name);
  const merchantName = MerchantStore.useState((s) => s.name);

  const [temp, setTemp] = useState("");
  ////////////////////////////////Combo Set/Package/Bundle Function//////////////////////////////////////////

  const [packageItemPrice, setPackageItemPrice] = useState("0");
  const [packageMinQuantity, setPackageMinQuantity] = useState("0");
  const [packagePriceFinal, setPackagePriceFinal] = useState(0);

  const [packageTaxDropdownList, setPackageTaxDropdownList] = useState([]);
  const [packageTax, setPackageTax] = useState("");

  const [packageProductDropdownList, setPackageProductDropdownList] = useState(
    []
  );
  const [packageselectedProductList, setPackageSelectedProductList] = useState(
    []
  );

  const [packageCategoryDropdownList, setPackageCategoryDropdownList] =
    useState([]);
  const [packageSelectedCategoryList, setPackageSelectedCategoryList] =
    useState([]);

  ////////////////////////////////Override Function//////////////////////////////////////////

  const [overrideItemPrice, setOverrideItemPrice] = useState("0");
  const [overridePriceFinal, setOverridePriceFinal] = useState(0);
  // const [overrideMinQuantity, setOverrideMinQuantity] = useState('');

  const [overrideTaxDropdownList, setOverrideTaxDropdownList] = useState([]);
  const [overrideTax, setOverrideTax] = useState("");

  const [overrideProductDropdownList, setOverrideProductDropdownList] =
    useState([]);
  const [overrideSelectedProductList, setOverrideSelectedProductList] =
    useState([]);

  const [overrideCategoryDropdownList, setOverrideCategoryDropdownList] =
    useState([]);
  const [overrideSelectedCategoryList, setOverrideSelectedCategoryList] =
    useState([]);

  ///////////////////////////////////////////////////////////////////////////////////////////

  // Override in array form

  const [overrideItems, setOverrideItems] = useState([
    {
      priceBeforeTax: 0,
      variation: PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS,
      variationItems: [],
      variationItemsSku: [],
    },
  ]);

  const [variationItemsProducts, setVariationItemsProducts] = useState([]);
  const [variationItemsCategories, setVariationItemsCategories] = useState([]);

  ///////////////////////////////Cashback Function////////////////////////////////////////////

  const [cashbackPercentage, setCashbackPercentage] = useState("");
  const [cashbackMinQuantity, setCashbackMinQuantity] = useState("");
  const [cashbackExpired, setCashbackExpired] = useState("");

  const [cashbackExpiredDropdownList, setCashbackExpiredDropdownList] =
    useState([]);
  const [cashbackSelectedExpiredList, setCashbackSelectedExpiredList] =
    useState([]);

  const [cashbackProductDropdownList, setCashbackProductDropdownList] =
    useState([]);
  const [cashbackSelectedProductList, setCashbackSelectedProductList] =
    useState([]);

  const [cashbackCategoryDropdownList, setCashbackCategoryDropdownList] =
    useState([]);
  const [cashbackSelectedCategoryList, setCashbackSelectedCategoryList] =
    useState([]);

  ///////////////////////////////Buy 1 Free 1 Function////////////////////////////////////////////

  const [buyInput, setBuyInput] = useState("1");
  const [getInput, setGetInput] = useState("1");
  const [getPriceInput, setGetPriceInput] = useState("0");

  const [buy1Free1ProductDropdownList, setBuy1Free1ProductDropdownList] =
    useState([]);
  const [buy1Free1SelectedProductList, setBuy1Free1SelectedProductList] =
    useState([]);

  const [selectedVariationB1F1, setSelectedVariationB1F1] = useState(
    PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
  );

  const [selectedVariationItemsB1F1, setSelectedVariationItemsB1F1] = useState(
    []
  );

  const [selectedVariationItemsSkuB1F1, setSelectedVariationItemsSkuB1F1] =
    useState([]); // for multi-outlets

  const [variationItemsB1F1DropdownList, setVariationItemsB1F1DropdownList] =
    useState([]);

  ///////////////////////////////Take amount off////////////////////////////////////////////

  const [amountOffPrice, setAmountOffPrice] = useState("0");
  const [amountOffMinQuantity, setAmountOffMinQuantity] = useState("0");
  const [amountOffMaxQuantity, setAmountOffMaxQuantity] = useState("0");

  const [amountOffProductDropdownList, setAmountOffProductDropdownList] =
    useState([]);
  const [amountOffSelectedProductList, setAmountOffSelectedProductList] =
    useState([]);

  const [amountOffCategoryDropdownList, setAmountOffCategoryDropdownList] =
    useState([]);
  const [amountOffSelectedCategoryList, setAmountOffSelectedCategoryList] =
    useState([]);

  ///////////////////////////////Take percentage off////////////////////////////////////////////

  const [percentageOffPrice, setPercentageOffPrice] = useState("0");
  const [percentageOffMinQuantity, setPercentageOffMinQuantity] = useState("0");
  const [percentageOffMaxQuantity, setPercentageOffMaxQuantity] = useState("0");

  const [
    percentageOffProductDropdownList,
    setPercentageOffProductDropdownList,
  ] = useState([]);
  const [
    percentageOffSelectedProductList,
    setPercentageOffSelectedProductList,
  ] = useState([]);

  const [
    percentageOffCategoryDropdownList,
    setPercentageOffCategoryDropdownList,
  ] = useState([]);
  const [
    percentageOffSelectedCategoryList,
    setPercentageOffSelectedCategoryList,
  ] = useState([]);

  ///////////////////////////////Buy A Get B For % Discount////////////////////////////////////////////

  const [percentageDiscount, setPercentageDiscount] = useState("0");

  ///////////////////////////////Buy AB Get Voucher////////////////////////////////////////////

  const [selectedVoucher, setSelectedVoucher] = useState([]);
  const [selectedVoucherItems, setSelectedVoucherItems] = useState([]);
  const merchantVouchers = CommonStore.useState((s) => s.merchantVouchers);

  ///////////////////////////////Delivery////////////////////////////////////////////

  const [myTextInput, setMyTextInput] = useState(React.createRef());
  const [deliveryFreeFlag, setDeliveryFreeFlag] = useState(false);
  const [deliveryFreeAboveAmount, setDeliveryFreeAboveAmount] = useState("0");
  const [deliveryDiscountAmount, setDeliveryDiscountAmount] = useState("0");
  const [deliveryDiscountAboveAmount, setDeliveryDiscountAboveAmount] =
    useState("0");

  ///////////////////////////////////Takeaway////////////////////////////////////////////////////////

  const [takeawayFreeFlag, setTakeawayFreeFlag] = useState(false);
  const [takeawayFreeAboveAmount, setTakeawayFreeAboveAmount] = useState("0");
  const [takeawayDiscountAmount, setTakeawayDiscountAmount] = useState("0");
  const [takeawayDiscountAboveAmount, setTakeawayDiscountAboveAmount] =
    useState("0");

  // common

  const [minSpend, setMinSpend] = useState("0");

  ///////////////////////////////////////////////////////////////////////////////////////////

  // Notification

  const [isEditNotification, setIsEditNotification] = useState(true);
  const [notificationTitle, setNotificationTitle] = useState("");
  const [isPushToSMS, setIsPushToSMS] = useState(false);
  const [isPushToApp, setIsPushToApp] = useState(true);
  const [isPushToEmail, setIsPushToEmail] = useState(false);
  const [notificationDescription, setNotificationDescription] = useState(
    "Hi %userName%, we have sent you a voucher of a free cup of coffee, visit %outletName% to redeem now!"
  );
  const [showNotificationDatePicker, setShowNotificationDatePicker] =
    useState(false);
  const [showNotificationTimePicker, setShowNotificationTimePicker] =
    useState(false);
  const [notificationDate, setNotificationDate] = useState(moment());
  const [notificationTime, setNotificationTime] = useState(moment());
  const [notificationPromoCode, setNotificationPromoCode] = useState("");
  const [wordCount, setWordCount] = useState();

  ///////////////////////////////////////////////////////////////////////////////////////////

  // const [variationDropdownList, setVariationDropdownList] = useState(PROMOTION_TYPE_VARIATION_DROPDOWN_LIST);
  const [selectedVariation, setSelectedVariation] = useState(
    PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
  );

  const [variationItemsDropdownList, setVariationItemsDropdownList] = useState(
    []
  );
  const [selectedVariationItems, setSelectedVariationItems] = useState([]);

  const [selectedVariationItemsSku, setSelectedVariationItemsSku] = useState(
    []
  ); // for multi-outlets

  const [taxDropdownList, setTaxDropdownList] = useState([]);
  const [selectedTax, setSelectedTax] = useState("");
  const [selectedTaxName, setSelectedTaxName] = useState("");
  const [selectedTaxRate, setSelectedTaxRate] = useState(0);

  const [crmUserTagsDropdownList, setCrmUserTagsDropdownList] = useState([]);
  const [crmSegmentsDropdownList, setCrmSegmentsDropdownList] = useState([]);

  const [priceMin, setPriceMin] = useState("");
  const [priceMax, setPriceMax] = useState("");
  const [quantityMin, setQuantityMin] = useState("");
  const [quantityMax, setQuantityMax] = useState("");

  const [smsText, setSmsText] = useState(
    "Hi %userName%, we have sent you a voucher of a free cup of coffee, visit %outletName% to redeem now!"
  );

  ///////////////////////////////////////////////////////////////////////////////////////////

  const [outletItems, setOutletItems] = useState([]);
  const [outletCategories, setOutletCategories] = useState([]);

  const allOutlets = MerchantStore.useState((s) => s.allOutlets);
  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const currOutlet = MerchantStore.useState((s) => s.currOutlet);
  const outletItemsUnsorted = OutletStore.useState((s) => s.outletItems);
  const outletCategoriesUnsorted = OutletStore.useState(
    (s) => s.outletCategories
  );
  const outletCategoriesDict = OutletStore.useState(
    (s) => s.outletCategoriesDict
  );
  const outletsTaxDict = OutletStore.useState((s) => s.outletsTaxDict);
  const currOutletTaxes = CommonStore.useState((s) => s.currOutletTaxes);
  const isLoading = CommonStore.useState((s) => s.isLoading);

  const selectedLoyaltySignUpCampaignEdit = CommonStore.useState(
    (s) => s.selectedLoyaltySignUpCampaignEdit
  );

  const merchantId = UserStore.useState((s) => s.merchantId);

  const allOutletsCategoriesUnique = OutletStore.useState(
    (s) => s.allOutletsCategoriesUnique
  );

  const crmUserTags = OutletStore.useState((s) => s.crmUserTags);
  const crmSegments = OutletStore.useState((s) => s.crmSegments);

  ///////////////////////////////////////////////////////////////////////////////////////////
  //Push Button
  const [promotionsNotifications, setPromotionsNotifications] = useState([]);
  const loyaltyCampaigns = OutletStore.useState((s) =>
    s.loyaltyCampaigns.filter(
      (campaign) =>
        campaign.loyaltyCampaignType === LOYALTY_CAMPAIGN_TYPE.SIGN_UP
    )
  );
  //const [isAutoPush, setIsAutoPush] = useState(false);
  //const [isLoading1, setIsLoading1] = useState(false);
  const [promotionPushed, setPromotionPushed] = useState(false);

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView
  );

  const [importModal, setImportModal] = useState(false);
  const [showImportListModal, setShowImportListModal] = useState(false);
  const [importSmsModal, setImportSmsModal] = useState(false);

  //////////////////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////////////////

  // loyalty campaign type

  // const [selectedLoyaltyCampaignType, setSelectedLoyaltyCampaignType] =
  //   useState(LOYALTY_CAMPAIGN_TYPE.SIGN_UP);
  const [selectedLoyaltyCampaignType, setSelectedLoyaltyCampaignType] =
    useState({
      label: LOYALTY_CAMPAIGN_DROPDOWN_LIST[0].label,
      value: LOYALTY_CAMPAIGN_DROPDOWN_LIST[0].value,
    });

  const [
    showLoyaltyCampaignSendTimePicker,
    setShowLoyaltyCampaignSendTimePicker,
  ] = useState(false);
  const [loyaltyCampaignSendTime, setLoyaltyCampaignSendTime] = useState(
    moment(Date.now()).format("hh:mm A")
  );

  const [rev_LoyaltyCampaignSendTime, setRev_LoyaltyCampaignSendTime] =
    useState(moment(Date.now()).format("hh:mm A"));

  const [loyaltyCampaignExpirationDays, setLoyaltyCampaignExpirationDays] =
    useState("30");

  const [
    loyaltyCampaignGuestNotVisitedDays,
    setLoyaltyCampaignGuestNotVisitedDays,
  ] = useState("");
  const [
    loyaltyCampaignGuestBirthdayBeforeDays,
    setLoyaltyCampaignGuestBirthdayBeforeDays,
  ] = useState("");
  const [
    loyaltyCampaignGuestAfterEveryVisits,
    setLoyaltyCampaignGuestAfterEveryVisits,
  ] = useState("");
  const [
    loyaltyCampaignGuestAfterEverySpends,
    setLoyaltyCampaignGuestAfterEverySpends,
  ] = useState("");

  //////////////////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////////////////

  // const [voucherCodeFormat, setVoucherCodeFormat] = useState(
  //   MERCHANT_VOUCHER_CODE_FORMAT.UNIQUE,
  // );

  // const [voucherCodeValueGeneric, setVoucherCodeValueGeneric] = useState(
  //   '',
  // );

  const [taggableVoucherDropdownList, setTaggableVoucherDropdownList] =
    useState([]);
  const [taggableVoucherId, setTaggableVoucherId] = useState("");

  const taggableVouchers = OutletStore.useState((s) => s.taggableVouchers);

  //////////////////////////////////////////////////////////////////////////////////////////

  const [isActive, setIsActive] = useState(false);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  //////////////////////////////////////////////////////////////////////////////////////////

  const setState = () => { };
  const [openVTT, setOpenVTT] = useState(false);
  const [openTS, setOpenTS] = useState(false);

  // useEffect(() => {
  //   if (outletsTaxDict[currOutletId] && outletsTaxDict[currOutletId].length > 0) {
  //     setTaxDropdownList(outletsTaxDict[currOutletId].map(item => ({ label: item.name, value: item.uniqueId })));

  //     if (selectedTax === '') {
  //       setSelectedTax(outletsTaxDict[currOutletId][0]);
  //     }
  //   }
  // }, [outletsTaxDict, currOutletId]);

  /////////////////////////////////////////////////////////////
  // const confirmDeletePromotion = () => {
  //   return  window.confirm(
  //     'Delete',
  //     'Are you sure you want to remove this promotion?',
  //     [
  //       {
  //         text: 'YES',
  //         onPress: () => {
  //           deletePromotion(item);
  //         },
  //       },
  //       {
  //         text: 'NO',
  //         onPress: () => { },
  //       },
  //     ],
  //   );
  // };

  /////////////////////////////////////////////////////////////

  useEffect(() => {
    var selectedLoyaltySignUpCampaignEditTemp = null;

    if (loyaltyCampaigns.length > 0) {
      for (var i = 0; i < loyaltyCampaigns.length; i++) {
        if (loyaltyCampaigns[i].outletId === currOutletId) {
          selectedLoyaltySignUpCampaignEditTemp = loyaltyCampaigns[i];
        }
      }
    } else {
      selectedLoyaltySignUpCampaignEditTemp = loyaltyCampaigns[i];
    }

    CommonStore.update((s) => {
      s.selectedLoyaltySignUpCampaignEdit =
        selectedLoyaltySignUpCampaignEditTemp;
    });
  }, [loyaltyCampaigns, currOutletId]);

  /////////////////////////////////////////////////////////////

  useEffect(() => {
    // console.log('================================');
    console.log('selectedLoyaltySignUpCampaignEdit');
    console.log(selectedLoyaltySignUpCampaignEdit);

    if (
      selectedLoyaltySignUpCampaignEdit
      // currOutletTaxes.length > 0 &&
      // && variationItemsDropdownList.length > 0
      // taxDropdownList.length > 0
    ) {
      // insert info

      setCampaignName(selectedLoyaltySignUpCampaignEdit.campaignName);
      setCampaignDescription(
        selectedLoyaltySignUpCampaignEdit.campaignDescription
      );
      setIsEnableSellOnline(
        selectedLoyaltySignUpCampaignEdit.isEnableSellOnline
      );
      // setSelectedAvailability(
      //   selectedLoyaltySignUpCampaignEdit.orderTypes || [
      //     ORDER_TYPE_DROP_DOWN_LIST[0].value,
      //   ]
      // );
      setSelectedAvailability(
        selectedLoyaltySignUpCampaignEdit.orderTypes || {
          label: ORDER_TYPE_DROP_DOWN_LIST[0].value,
          value: ORDER_TYPE_DROP_DOWN_LIST[0].value,
        }
      );
      setSelectedTargetUserGroup(
        selectedLoyaltySignUpCampaignEdit.targetUserGroup
      );
      if (selectedLoyaltySignUpCampaignEdit.targetSegmentGroupList) {
        setSelectedTargetSegmentGroupList(
          selectedLoyaltySignUpCampaignEdit.targetSegmentGroupList
        );
      }
      if (selectedLoyaltySignUpCampaignEdit.effectiveType) {
        setSelectedEffectiveType(
          selectedLoyaltySignUpCampaignEdit.effectiveType
        );
      } else {
        setSelectedEffectiveType(EFFECTIVE_TYPE.DAY);
      }
      if (selectedLoyaltySignUpCampaignEdit.effectiveTypeOptions) {
        // setSelectedEffectiveTypeOptions(
        //   selectedLoyaltySignUpCampaignEdit.effectiveTypeOptions,
        // );

        var effectiveTypeOptionsTemp = [];

        for (
          var i = 0;
          i < selectedLoyaltySignUpCampaignEdit.effectiveTypeOptions.length;
          i++
        ) {
          if (
            EFFECTIVE_DAY_DROPDOWN_LIST.find(
              (item) =>
                item.value ===
                selectedLoyaltySignUpCampaignEdit.effectiveTypeOptions[i]
            )
          ) {
            effectiveTypeOptionsTemp.push(
              selectedLoyaltySignUpCampaignEdit.effectiveTypeOptions[i]
            );
          }
        }
        setSelectedEffectiveTypeOptions(effectiveTypeOptionsTemp);
      }
      setSelectedEffectiveDay(selectedLoyaltySignUpCampaignEdit.effectiveDay);
      setEffectiveTimeStart(
        selectedLoyaltySignUpCampaignEdit.effectiveTimeStart
          ? selectedLoyaltySignUpCampaignEdit.effectiveTimeStart
          : moment()
      );
      setEffectiveTimeEnd(
        selectedLoyaltySignUpCampaignEdit.effectiveTimeEnd
          ? selectedLoyaltySignUpCampaignEdit.effectiveTimeEnd
          : moment()
      );

      setMinSpend(
        selectedLoyaltySignUpCampaignEdit.minSpend
          ? selectedLoyaltySignUpCampaignEdit.minSpend.toFixed(2)
          : "0"
      );

      setPromoCode(selectedLoyaltySignUpCampaignEdit.promoCode);
      setIsPromoCodeUsageLimit(
        selectedLoyaltySignUpCampaignEdit.isPromoCodeUsageLimit
      );
      if (selectedLoyaltySignUpCampaignEdit.promoCodeUsageLimit) {
        setPromoCodeUsageLimit(
          selectedLoyaltySignUpCampaignEdit.promoCodeUsageLimit.toFixed(0)
        );
      }
      setPromoDateStart(selectedLoyaltySignUpCampaignEdit.promoDateStart);
      setPromoDateEnd(selectedLoyaltySignUpCampaignEdit.promoDateEnd);
      setPromoTimeStart(selectedLoyaltySignUpCampaignEdit.promoTimeStart);
      setPromoTimeEnd(selectedLoyaltySignUpCampaignEdit.promoTimeEnd);
      setImage(selectedLoyaltySignUpCampaignEdit.image);
      setIsImageChanged(false);
      setSelectedOutletList(selectedLoyaltySignUpCampaignEdit.outletIdList);

      setSelectedPromotionType(selectedLoyaltySignUpCampaignEdit.promotionType);

      // setSmsText(selectedLoyaltySignUpCampaignEdit.notificationText || '');

      setIsEditNotification(true);

      if (selectedLoyaltySignUpCampaignEdit.notification) {
        setNotificationTitle(
          selectedLoyaltySignUpCampaignEdit.notification.title
        );
        setIsPushToSMS(
          selectedLoyaltySignUpCampaignEdit.notification.isPushToSMS
        );
        setIsPushToApp(
          selectedLoyaltySignUpCampaignEdit.notification.isPushToApp
        );
        setIsPushToEmail(
          selectedLoyaltySignUpCampaignEdit.notification.isPushToEmail
        );
        setNotificationDescription(
          selectedLoyaltySignUpCampaignEdit.notification.description
        );
        setNotificationDate(
          selectedLoyaltySignUpCampaignEdit.notification.date
        );
        setNotificationTime(
          selectedLoyaltySignUpCampaignEdit.notification.time
        );
        setNotificationPromoCode(
          selectedLoyaltySignUpCampaignEdit.notification.promoCode
        );

        setSmsText(selectedLoyaltySignUpCampaignEdit.notification.description);
      } else {
        setNotificationTitle("");
        setIsPushToSMS(false);
        setIsPushToApp(true);
        setIsPushToEmail(false);
        setNotificationDescription(
          "Hi %userName%, we have sent you a voucher of a free cup of coffee, visit %outletName% to redeem now!"
        );
        setNotificationDate(moment());
        setNotificationTime(moment());
        setNotificationPromoCode("");

        setSmsText(
          "Hi %userName%, we have sent you a voucher of a free cup of coffee, visit %outletName% to redeem now!"
        );
      }

      //////////////////////////////////////////////////////////////////

      setSelectedLoyaltyCampaignType(
        selectedLoyaltySignUpCampaignEdit.loyaltyCampaignType
      );

      if (
        selectedLoyaltySignUpCampaignEdit.loyaltyCampaignType ===
        LOYALTY_CAMPAIGN_TYPE.FIRST_VISIT
      ) {
        for (
          var i = 0;
          i < selectedLoyaltySignUpCampaignEdit.loyaltyCriteriaList.length;
          i++
        ) {
          const criteria =
            selectedLoyaltySignUpCampaignEdit.loyaltyCriteriaList[i];

          setLoyaltyCampaignSendTime(criteria.loyaltyCampaignSendTime);
          setLoyaltyCampaignExpirationDays(
            criteria.loyaltyCampaignExpirationDays.toFixed(0)
          );
        }
      } else if (
        selectedLoyaltySignUpCampaignEdit.loyaltyCampaignType ===
        LOYALTY_CAMPAIGN_TYPE.AT_RISK
      ) {
        for (
          var i = 0;
          i < selectedLoyaltySignUpCampaignEdit.loyaltyCriteriaList.length;
          i++
        ) {
          const criteria =
            selectedLoyaltySignUpCampaignEdit.loyaltyCriteriaList[i];

          setLoyaltyCampaignSendTime(criteria.loyaltyCampaignSendTime);
          setLoyaltyCampaignExpirationDays(
            criteria.loyaltyCampaignExpirationDays.toFixed(0)
          );
          setLoyaltyCampaignGuestNotVisitedDays(
            criteria.loyaltyCampaignGuestNotVisitedDays.toFixed(0)
          );
        }
      } else if (
        selectedLoyaltySignUpCampaignEdit.loyaltyCampaignType ===
        LOYALTY_CAMPAIGN_TYPE.BIRTHDAY
      ) {
        for (
          var i = 0;
          i < selectedLoyaltySignUpCampaignEdit.loyaltyCriteriaList.length;
          i++
        ) {
          const criteria =
            selectedLoyaltySignUpCampaignEdit.loyaltyCriteriaList[i];

          setLoyaltyCampaignSendTime(criteria.loyaltyCampaignSendTime);
          setLoyaltyCampaignExpirationDays(
            criteria.loyaltyCampaignExpirationDays.toFixed(0)
          );
          setLoyaltyCampaignGuestBirthdayBeforeDays(
            criteria.loyaltyCampaignGuestBirthdayBeforeDays.toFixed(0)
          );
        }
      } else if (
        selectedLoyaltySignUpCampaignEdit.loyaltyCampaignType ===
        LOYALTY_CAMPAIGN_TYPE.GROWTH
      ) {
        for (
          var i = 0;
          i < selectedLoyaltySignUpCampaignEdit.loyaltyCriteriaList.length;
          i++
        ) {
          const criteria =
            selectedLoyaltySignUpCampaignEdit.loyaltyCriteriaList[i];

          setLoyaltyCampaignSendTime(criteria.loyaltyCampaignSendTime);
          setLoyaltyCampaignExpirationDays(
            criteria.loyaltyCampaignExpirationDays.toFixed(0)
          );
          setLoyaltyCampaignGuestAfterEveryVisits(
            criteria.loyaltyCampaignGuestAfterEveryVisits.toFixed(0)
          );
        }
      } else if (
        selectedLoyaltySignUpCampaignEdit.loyaltyCampaignType ===
        LOYALTY_CAMPAIGN_TYPE.BIG_SPENDER
      ) {
        for (
          var i = 0;
          i < selectedLoyaltySignUpCampaignEdit.loyaltyCriteriaList.length;
          i++
        ) {
          const criteria =
            selectedLoyaltySignUpCampaignEdit.loyaltyCriteriaList[i];

          setLoyaltyCampaignSendTime(criteria.loyaltyCampaignSendTime);
          setLoyaltyCampaignExpirationDays(
            criteria.loyaltyCampaignExpirationDays.toFixed(0)
          );
          setLoyaltyCampaignGuestAfterEverySpends(
            criteria.loyaltyCampaignGuestAfterEverySpends.toFixed(2)
          );
        }
      } else if (
        selectedLoyaltySignUpCampaignEdit.loyaltyCampaignType ===
        LOYALTY_CAMPAIGN_TYPE.SIGN_UP
      ) {
        for (
          var i = 0;
          i < selectedLoyaltySignUpCampaignEdit.loyaltyCriteriaList.length;
          i++
        ) {
          const criteria =
            selectedLoyaltySignUpCampaignEdit.loyaltyCriteriaList[i];

          setLoyaltyCampaignSendTime(criteria.loyaltyCampaignSendTime);
          setLoyaltyCampaignExpirationDays(
            criteria.loyaltyCampaignExpirationDays.toFixed(0)
          );
        }
      } else if (
        selectedLoyaltySignUpCampaignEdit.loyaltyCampaignType ===
        LOYALTY_CAMPAIGN_TYPE.VOUCHER
      ) {
        for (
          var i = 0;
          i < selectedLoyaltySignUpCampaignEdit.loyaltyCriteriaList.length;
          i++
        ) {
          const criteria =
            selectedLoyaltySignUpCampaignEdit.loyaltyCriteriaList[i];

          setLoyaltyCampaignSendTime(criteria.loyaltyCampaignSendTime);
          setLoyaltyCampaignExpirationDays(
            criteria.loyaltyCampaignExpirationDays.toFixed(0)
          );
        }
      }

      //////////////////////////////////////////////////////////////////

      setTaggableVoucherId(
        selectedLoyaltySignUpCampaignEdit.taggableVoucherId || ""
      );

      //////////////////////////////////////////////////////////////////

      // var selectedOutletListTemp = [];
      // if (allOutletsItemsSkuDict[selectedLoyaltySignUpCampaignEdit.sku]) {
      //   for (var i = 0; i < allOutletsItemsSkuDict[selectedLoyaltySignUpCampaignEdit.sku].length; i++) {
      //     selectedOutletListTemp.push(allOutletsItemsSkuDict[selectedLoyaltySignUpCampaignEdit.sku][i].outletId);
      //   }
      // }
      // setSelectedOutletList(selectedOutletListTemp);

      // setPlace(selectedLoyaltySignUpCampaignEdit.outletId);
      // setItemName(selectedLoyaltySignUpCampaignEdit.name);
      // setItemDescription(selectedLoyaltySignUpCampaignEdit.description);
      // setImage(selectedLoyaltySignUpCampaignEdit.image);
      // setItemPrice(selectedLoyaltySignUpCampaignEdit.price.toFixed(2));

      // setSelectedOutletCategoryId(selectedLoyaltySignUpCampaignEdit.categoryId);

      // if (selectedLoyaltySignUpCampaignEdit.stockLinkItems) {
      //   setStockLinkItems(selectedLoyaltySignUpCampaignEdit.stockLinkItems);
      // }

      setIsActive(selectedLoyaltySignUpCampaignEdit.isActive);
    } else {
      // designed to always mounted, thus need clear manually...

      setCampaignName("");
      setCampaignDescription("");
      setIsEnableSellOnline(false);
      setSelectedAvailability([ORDER_TYPE_DROP_DOWN_LIST[0].value]);
      setSelectedAvailability({
        label: ORDER_TYPE_DROP_DOWN_LIST[0].value,
        value: ORDER_TYPE_DROP_DOWN_LIST[0].value,
      });
      setSelectedTargetUserGroup(TARGET_USER_GROUP_DROPDOWN_LIST[0].value);
      setSelectedTargetSegmentGroupList([CRM_SEGMENT_DROPDOWN_LIST[0].value]);
      setSelectedEffectiveType(EFFECTIVE_TYPE.DAY);
      setSelectedEffectiveTypeOptions([EFFECTIVE_DAY_DROPDOWN_LIST[0].value]);
      setSelectedEffectiveDay(EFFECTIVE_DAY_DROPDOWN_LIST[0].value);
      setPromoCode("");
      setIsPromoCodeUsageLimit(false);
      setPromoCodeUsageLimit("");
      setPromoDateStart(moment());
      setPromoDateEnd(moment());
      setPromoTimeStart(moment());
      setPromoTimeEnd(moment());
      setImage("");
      setIsImageChanged(false);
      setSelectedOutletList([]);

      setSelectedPromotionType(LOYALTY_PROMOTION_TYPE_DROPDOWN_LIST[0].value);

      // setSmsText('');

      setPackageItemPrice("0");
      setPackageMinQuantity("0");
      setPackagePriceFinal(0);

      setOverrideItemPrice("0");
      setOverridePriceFinal(0);

      // if (currOutletTaxes.length > 0) {
      //   setSelectedTax(currOutletTaxes[0].uniqueId);
      //   setSelectedTaxName(currOutletTaxes[0].name);
      //   setSelectedTaxRate(currOutletTaxes[0].rate);

      //   setPackageTax(currOutletTaxes[0].rate);
      //   setOverrideTax(currOutletTaxes[0].rate);
      // } else {
      //   setSelectedTax('');
      //   setSelectedTaxName('');
      //   setSelectedTaxRate(0);

      //   setPackageTax('');
      //   setOverrideTax('');
      // }

      setPriceMin("");
      setPriceMax("");
      setQuantityMin("");
      setQuantityMax("");

      // setSelectedVariation(PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS);
      // setSelectedVariationItems([]);
      // setSelectedVariationItemsSku([]);

      setOverrideItems([
        {
          priceBeforeTax: 0,
          variation: PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS,
          variationItems: [],
          variationItemsSku: [],
        },
      ]);

      //////////////////////////////////////////////

      setSelectedVariationB1F1(PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS);
      setSelectedVariationItemsB1F1([]);
      setSelectedVariationItemsSkuB1F1([]);

      //////////////////////////////////////////////

      setIsEditNotification(true);
      setNotificationTitle("");
      setIsPushToSMS(false);
      setIsPushToApp(true);
      setIsPushToEmail(false);
      setNotificationDescription(
        "Hi %userName%, we have sent you a voucher of a free cup of coffee, visit %outletName% to redeem now!"
      );
      setNotificationDate(moment());
      setNotificationTime(moment());
      setNotificationPromoCode("");

      setSmsText(
        "Hi %userName%, we have sent you a voucher of a free cup of coffee, visit %outletName% to redeem now!"
      );

      //////////////////////////////////////////////

      setSelectedLoyaltyCampaignType(LOYALTY_CAMPAIGN_TYPE.SIGN_UP);
      setLoyaltyCampaignSendTime(moment().valueOf());
      setLoyaltyCampaignExpirationDays("30");
      setLoyaltyCampaignGuestNotVisitedDays("");
      setLoyaltyCampaignGuestBirthdayBeforeDays("");
      setLoyaltyCampaignGuestAfterEveryVisits("");
      setLoyaltyCampaignGuestAfterEverySpends("");

      //////////////////////////////////////////////

      setTaggableVoucherId("");

      //////////////////////////////////////////////

      // setSelectedOutletList([]);

      // setPlace('');
      // setItemSKU('');
      // setItemName('');
      // setItemDescription('');
      // setImage('');
      // setItemPrice('0.00');

      // if (outletSupplyItems.length > 0) {
      //   setStockLinkItems([
      //     {
      //       outletSupplyItemId: outletSupplyItems[0].uniqueId,
      //       sku: outletSupplyItems[0].sku,
      //       name: outletSupplyItems[0].name,
      //       unit: outletSupplyItems[0].unit,
      //       quantityUsage: 0,
      //     }
      //   ]);

      // }
      // else {
      //   setStockLinkItems([
      //     {
      //       outletSupplyItemId: '',
      //       sku: '',
      //       name: '',
      //       unit: '',
      //       quantityUsage: 0,
      //     }
      //   ]);

      // }

      // setIsImageChanged(false);

      // setVariantGroupList([]);
      // setAddOnGroupList([]);

      setIsActive(false);
    }
  }, [
    selectedLoyaltySignUpCampaignEdit,
    currOutletTaxes,
    // variationItemsDropdownList,
    taxDropdownList,
  ]);

  /////////////////////////////////////////////////////////////

  useEffect(() => {
    setTaggableVoucherDropdownList(
      [
        {
          label: "N/A",
          value: "",
        },
      ].concat(
        taggableVouchers.map((item) => ({
          label: `${item.campaignName} [${item.voucherType.replaceAll(
            "_",
            " "
          )}]`,
          value: item.uniqueId,
        }))
      )
    );
  }, [taggableVouchers]);

  /////////////////////////////////////////////////////////////

  useEffect(() => {
    // console.log('crmUserTagsDropdownList');
    // console.log(
    //   TARGET_USER_GROUP_DROPDOWN_LIST.concat(
    //     crmUserTags.map((item) => ({ label: item.name, value: item.uniqueId })),
    //   ),
    // );

    setCrmUserTagsDropdownList(
      TARGET_USER_GROUP_DROPDOWN_LIST.concat(
        crmUserTags.map((item) => ({ label: item.name, value: item.uniqueId }))
      )
    );
  }, [crmUserTags]);

  useEffect(() => {
    var crmSegmentsDropdownListTemp = crmSegments.map((segment) => ({
      label: segment.name,
      value: segment.uniqueId,
    }));

    setCrmSegmentsDropdownList(crmSegmentsDropdownListTemp);

    // if (selectedTargetSegmentGroupList.length > 0) {
    //   var selectedTargetSegmentGroupListTemp = [];

    //   var combinedCrmSegmentsDropdownListTemp =
    //     CRM_SEGMENT_DROPDOWN_LIST.concat(crmSegmentsDropdownListTemp);

    //   for (var i = 0; i < selectedTargetSegmentGroupList.length; i++) {
    //     if (
    //       combinedCrmSegmentsDropdownListTemp.find(
    //         (item) => item.value === selectedTargetSegmentGroupList[i]
    //       )
    //     ) {
    //       selectedTargetSegmentGroupListTemp.push(
    //         selectedTargetSegmentGroupList[i]
    //       );
    //     }
    //   }

    //   var isChanged = false;
    //   if (
    //     selectedTargetSegmentGroupList.length !==
    //     selectedTargetSegmentGroupListTemp.length
    //   ) {
    //     isChanged = true;
    //   } else {
    //     for (var i = 0; i < selectedTargetSegmentGroupList.length; i++) {
    //       const isEqual =
    //         selectedTargetSegmentGroupList[i] ===
    //         selectedTargetSegmentGroupListTemp[i];

    //       if (!isEqual) {
    //         isChanged = true;
    //         break;
    //       }
    //     }
    //   }

    //   if (isChanged) {
    //     setSelectedTargetSegmentGroupList(selectedTargetSegmentGroupListTemp);
    //   }
    // }
  }, [crmSegments, selectedTargetSegmentGroupList]);

  useEffect(() => {
    if (
      selectedPromotionType ===
      LOYALTY_PROMOTION_TYPE.COMBO_SET_OR_PACKAGE_OR_BUNDLE &&
      packageItemPrice.length > 0 &&
      parseFloat(packageItemPrice) >= 0
    ) {
      const packageItemPriceFloat = parseFloat(packageItemPrice);

      setPackagePriceFinal(
        packageItemPriceFloat + packageItemPriceFloat * selectedTaxRate
      );
    } else if (
      selectedPromotionType ===
      LOYALTY_PROMOTION_TYPE.OVERRIDE_EXISTING_PRICE &&
      overrideItemPrice.length > 0 &&
      parseFloat(overrideItemPrice) >= 0
    ) {
      const overrideItemPriceFloat = parseFloat(overrideItemPrice);

      setOverridePriceFinal(
        overrideItemPriceFloat + overrideItemPriceFloat * selectedTaxRate
      );
    }
  }, [
    selectedPromotionType,

    packageItemPrice,
    overrideItemPrice,

    selectedTaxRate,
  ]);

  /////////////////////////////////////////////////////////////

  // useEffect(() => {
  //   if (currOutletTaxes.length > 0) {
  //     setTaxDropdownList(
  //       currOutletTaxes.map((item) => ({
  //         label: item.name,
  //         value: item.uniqueId,
  //       })),
  //     );

  //     if (selectedTax === '') {
  //       setSelectedTax(currOutletTaxes[0].uniqueId);

  //       for (var i = 0; i < currOutletTaxes.length; i++) {
  //         if (currOutletTaxes[i].uniqueId === currOutletTaxes[0].uniqueId) {
  //           setSelectedTaxName(currOutletTaxes[i].name);
  //           setSelectedTaxRate(currOutletTaxes[i].rate);
  //           break;
  //         }
  //       }
  //     }
  //   }
  // }, [currOutletTaxes, currOutletId]);

  ////////////////////////////////////////////////////////
  // useEffect(() => {
  //   //var selectedVariationItemsSkuTemp = [];
  //   var selectedVoucherTemp = [];

  //   // if (selectedVariation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
  //   //   for (var i = 0; i < outletItems.length; i++) {
  //   //     if (selectedVariationItems.includes(outletItems[i].uniqueId)) {
  //   //       selectedVariationItemsSkuTemp.push(outletItems[i].sku);
  //   //     }
  //   //   }
  //   // }
  //   // else if (selectedVariation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
  //   //   for (var i = 0; i < allOutletsCategoriesUnique.length; i++) {
  //   //     if (selectedVariationItems.includes(allOutletsCategoriesUnique[i].uniqueId)) {
  //   //       selectedVariationItemsSkuTemp.push(allOutletsCategoriesUnique[i].name);
  //   //     }
  //   //   }
  //   // }

  //   for (var i=0; i < merchantVouchers.length; i++){
  //     if (selectedVoucherItems.includes(merchantVouchers[i].uniqueId)){
  //       selectedVoucherTemp.push(merchantVouchers[i].name);
  //     }
  //   }

  //   setSelectedVoucher(selectedVoucherTemp);
  // }, [
  //   selectedVoucher,
  //   merchantVouchers,
  // ]);

  ////////////////////////////////////////

  // change when pick better

  useEffect(() => {
    // var overrideItemsSkuArrayChangesList = [];

    var isDiff = false;

    var overrideItemsTemp = [];

    for (var index = 0; index < overrideItems.length; index++) {
      var skuArray = [];

      if (
        overrideItems[index].variation ===
        PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
      ) {
        for (var i = 0; i < outletItems.length; i++) {
          if (
            overrideItems[index].variationItems.includes(
              outletItems[i].uniqueId
            )
          ) {
            skuArray.push(outletItems[i].sku);
          }
        }
      } else if (
        overrideItems[index].variation ===
        PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
      ) {
        for (var i = 0; i < allOutletsCategoriesUnique.length; i++) {
          if (
            overrideItems[index].variationItems.includes(
              allOutletsCategoriesUnique[i].uniqueId
            )
          ) {
            skuArray.push(allOutletsCategoriesUnique[i].name);
          }
        }
      }

      isDiff = !areArraysEqual(
        skuArray,
        overrideItems[index].variationItemsSku
      );

      var overrideItemTemp = {
        priceBeforeTax: overrideItems[index].priceBeforeTax,
        variation: overrideItems[index].variation,
        variationItems: overrideItems[index].variationItems,
        variationItemsSku: skuArray,
      };

      overrideItemsTemp.push(overrideItemTemp);

      // if (isDiff) {

      //   // setOverrideItems(overrideItems.map((overrideItem, j) => (j === i ? {
      //   //   ...overrideItem,
      //   //   variationItemsSku: skuArray,
      //   // } : overrideItem)));

      //   // break;
      // }
    }

    if (isDiff) {
      setOverrideItems(overrideItemsTemp);
    }

    // setSelectedVariationItemsSku(selectedVariationItemsSkuTemp);
  }, [
    // selectedVariation,

    // selectedVariationItems,

    overrideItems,

    outletItems,

    allOutletsCategoriesUnique,
  ]);

  useEffect(() => {
    var selectedVariationItemsSkuTemp = [];

    if (selectedVariation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
      for (var i = 0; i < outletItems.length; i++) {
        if (selectedVariationItems.includes(outletItems[i].uniqueId)) {
          selectedVariationItemsSkuTemp.push(outletItems[i].sku);
        }
      }
    } else if (
      selectedVariation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
    ) {
      for (var i = 0; i < allOutletsCategoriesUnique.length; i++) {
        if (
          selectedVariationItems.includes(
            allOutletsCategoriesUnique[i].uniqueId
          )
        ) {
          selectedVariationItemsSkuTemp.push(
            allOutletsCategoriesUnique[i].name
          );
        }
      }
    }

    setSelectedVariationItemsSku(selectedVariationItemsSkuTemp);
  }, [
    selectedVariation,

    selectedVariationItems,
    outletItems,

    allOutletsCategoriesUnique,
  ]);

  useEffect(() => {
    var selectedVariationItemsSkuB1F1Temp = [];

    if (selectedVariationB1F1 === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
      for (var i = 0; i < outletItems.length; i++) {
        if (selectedVariationItemsB1F1.includes(outletItems[i].uniqueId)) {
          selectedVariationItemsSkuB1F1Temp.push(outletItems[i].sku);
        }
      }
    } else if (
      selectedVariationB1F1 === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
    ) {
      for (var i = 0; i < allOutletsCategoriesUnique.length; i++) {
        if (
          selectedVariationItemsB1F1.includes(
            allOutletsCategoriesUnique[i].uniqueId
          )
        ) {
          selectedVariationItemsSkuB1F1Temp.push(
            allOutletsCategoriesUnique[i].name
          );
        }
      }
    }

    setSelectedVariationItemsSkuB1F1(selectedVariationItemsSkuB1F1Temp);
  }, [
    selectedVariationB1F1,

    selectedVariationItemsB1F1,
    outletItems,

    allOutletsCategoriesUnique,
  ]);

  ////////////////////////////////////////////////////////

  useEffect(() => {
    if (selectedVariation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
      setVariationItemsDropdownList(
        allOutletsCategoriesUnique.map((item) => ({
          label: item.name,
          value: item.uniqueId,
        }))
      );

      if (
        allOutletsCategoriesUnique.length > 0 &&
        !selectedLoyaltySignUpCampaignEdit
      ) {
        // setSelectedVariationItems([allOutletsCategoriesUnique[0].uniqueId]);
      } else {
        // if (selectedVariationItems.length > 0 && variationItemsDropdownList.find(item => item.value === selectedVariationItems[0]) === null) {
        //   // console.log('clear items');
        //   setSelectedVariationItems([]);
        // }
      }
    } else if (
      selectedVariation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
    ) {
      setVariationItemsDropdownList(
        outletItems.map((item) => ({ label: item.name, value: item.uniqueId }))
      );

      if (outletItems.length > 0 && !selectedLoyaltySignUpCampaignEdit) {
        // setSelectedVariationItems([outletItems[0].uniqueId]);
      } else {
        // if (selectedVariationItems.length > 0 && variationItemsDropdownList.find(item => item.value === selectedVariationItems[0]) === null) {
        //   setSelectedVariationItems([]);
        // }
      }
    }

    if (
      selectedVariationB1F1 === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
    ) {
      setVariationItemsB1F1DropdownList(
        allOutletsCategoriesUnique.map((item) => ({
          label: item.name,
          value: item.uniqueId,
        }))
      );

      if (
        allOutletsCategoriesUnique.length > 0 &&
        !selectedLoyaltySignUpCampaignEdit
      ) {
        // setSelectedVariationItemsB1F1([allOutletsCategoriesUnique[0].uniqueId]);
      } else {
        // if (selectedVariationItemsB1F1.length > 0 && variationItemsB1F1DropdownList.find(item => item.value === selectedVariationItemsB1F1[0]) === null) {
        //   setSelectedVariationItemsB1F1([]);
        // }
      }
    } else if (
      selectedVariationB1F1 === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
    ) {
      setVariationItemsB1F1DropdownList(
        outletItems.map((item) => ({ label: item.name, value: item.uniqueId }))
      );

      if (outletItems.length > 0 && !selectedLoyaltySignUpCampaignEdit) {
        // setSelectedVariationItemsB1F1([outletItems[0].uniqueId]);
      } else {
        // if (selectedVariationItemsB1F1.length > 0 && variationItemsB1F1DropdownList.find(item => item.value === selectedVariationItemsB1F1[0]) === null) {
        //   setSelectedVariationItemsB1F1([]);
        // }
      }
    }

    setVariationItemsProducts(
      outletItems.map((item) => ({ label: item.name, value: item.uniqueId }))
    );
    setVariationItemsCategories(
      allOutletsCategoriesUnique.map((item) => ({
        label: item.name,
        value: item.uniqueId,
      }))
    );
  }, [
    outletItems,
    // outletCategories,
    allOutletsCategoriesUnique,

    selectedVariation,
    selectedVariationB1F1,

    selectedVariationItems,
    selectedVariationItemsB1F1,

    selectedLoyaltySignUpCampaignEdit,
  ]);

  ////////////////////////////////////////////////////////

  // to solve product not show

  useEffect(() => {
    // if (
    //   selectedVariationItemsB1F1.length > 0 &&
    //   variationItemsB1F1DropdownList.find(
    //     (item) => item.value === selectedVariationItemsB1F1[0],
    //   ) === undefined
    // ) {
    //   // setSelectedVariationItemsB1F1(variationItemsB1F1DropdownList.length > 0 ? [variationItemsB1F1DropdownList[0].uniqueId] : []);
    //   setSelectedVariationItemsB1F1([]);
    // }

    // // console.log('condition');
    // // console.log(selectedVariationItems.length > 0);
    // // console.log(
    //   variationItemsDropdownList.find(
    //     (item) => item.value === selectedVariationItems[0],
    //   ),
    // );

    if (
      selectedVariationItemsB1F1.length > 0 &&
      selectedVariationItemsB1F1.filter((itemId) => {
        return variationItemsB1F1DropdownList.find(
          (item2) => item2.value === itemId
        )
          ? true
          : false;
      }).length !== selectedVariationItemsB1F1.length
    ) {
      // setSelectedVariationItemsB1F1(variationItemsB1F1DropdownList.length > 0 ? [variationItemsB1F1DropdownList[0].uniqueId] : []);
      setSelectedVariationItemsB1F1([]);
    }

    // if (
    //   selectedVariationItems.length > 0 &&
    //   variationItemsDropdownList.find(
    //     (item) => item.value === selectedVariationItems[0],
    //   ) === undefined
    // ) {
    //   // setSelectedVariationItems(variationItemsDropdownList.length > 0 ? [variationItemsDropdownList[0].uniqueId] : []);
    //   setSelectedVariationItems([]);
    // }

    if (
      selectedVariationItems.length > 0 &&
      selectedVariationItems.filter((itemId) => {
        return variationItemsDropdownList.find(
          (item2) => item2.value === itemId
        )
          ? true
          : false;
      }).length !== selectedVariationItems.length
    ) {
      // setSelectedVariationItems(variationItemsDropdownList.length > 0 ? [variationItemsDropdownList[0].uniqueId] : []);
      // hide first
      // setSelectedVariationItems([]);

      // 2023-05-16 - Only remove those that not existed

      const existedItems = selectedVariationItems.filter(itemId => {
        return variationItemsDropdownList.find(item2 => item2.value === itemId) ? true : false;
      });

      setSelectedVariationItems(existedItems);
    }

    var overrideItemsTemp = [];

    for (var i = 0; i < overrideItems.length; i++) {
      var overrideItemTemp = {
        priceBeforeTax: overrideItems[i].priceBeforeTax,
        variation: overrideItems[i].variation,
        variationItems: overrideItems[i].variationItems,
        variationItemsSku: overrideItems[i].variationItemsSku,
      };

      if (
        overrideItems[i].variationItems.length > 0 &&
        variationItemsDropdownList.find(
          (item) => item.value === overrideItems[i].variationItems[0]
        ) === undefined
      ) {
        overrideItemTemp.variationItems = [];
      }

      overrideItemsTemp.push(overrideItemTemp);
    }

    setOverrideItems(overrideItemsTemp);
  }, [variationItemsDropdownList, variationItemsB1F1DropdownList]);

  ////////////////////////////////////////////////////////

  useEffect(() => {
    // var outletItemsTemp = [...outletItemsUnsorted];

    var outletItemsTemp = outletItemsUnsorted.filter((item) =>
      outletCategoriesDict[item.categoryId] ? true : false
    );

    outletItemsTemp.sort((a, b) => a.name.localeCompare(b.name));

    setOutletItems(outletItemsTemp);
  }, [outletItemsUnsorted]);

  useEffect(() => {
    var outletCategoriesTemp = [...outletCategoriesUnsorted];

    outletCategoriesTemp.sort((a, b) => a.name.localeCompare(b.name));

    setOutletCategories(outletCategoriesTemp);
  }, [outletCategoriesUnsorted]);

  useEffect(() => {
    setOutletDropdownList(
      allOutlets.map((item) => ({
        label: item.name,
        value: item.uniqueId,
      }))
    );
  }, [allOutlets]);

  // useEffect(() => {
  //   setAvailabilityDropdownList(ORDER_TYPE.map(item => ({
  //     label: item.name, value: item.uniqueId,
  //   })))
  // }, [ORDER_TYPE])

  useEffect(() => {
    setPackageTaxDropdownList();
  }, []);

  useEffect(() => {
    setPackageProductDropdownList();
  }, []);

  useEffect(() => {
    setPackageCategoryDropdownList();
  }, []);

  /////////

  useEffect(() => {
    setOverrideTaxDropdownList();
  }, []);

  useEffect(() => {
    setOverrideProductDropdownList();
  }, []);

  useEffect(() => {
    setOverrideCategoryDropdownList();
  }, []);

  ////////

  useEffect(() => {
    setCashbackExpiredDropdownList();
  }, []);

  useEffect(() => {
    setCashbackProductDropdownList();
  }, []);

  useEffect(() => {
    setCashbackCategoryDropdownList();
  }, []);

  ////////

  useEffect(() => {
    setBuy1Free1ProductDropdownList();
  }, []);

  ///////

  useEffect(() => {
    setAmountOffProductDropdownList();
  }, []);

  useEffect(() => {
    setAmountOffCategoryDropdownList();
  }, []);

  ////////

  useEffect(() => {
    setPercentageOffProductDropdownList();
  }, []);

  useEffect(() => {
    setPercentageOffCategoryDropdownList();
  }, []);

  ///////

  useEffect(() => {
    var promotionsNotificationsTemp = [];

    for (var i = 0; i < loyaltyCampaigns.length; i++) {
      if (loyaltyCampaigns[i].notification) {
        var pushToTargetArr = [];

        if (loyaltyCampaigns[i].notification.isPushToSMS) {
          pushToTargetArr.push("SMS");
        }

        // if (loyaltyCampaigns[i].notification.isPushToEmail) {
        //   pushToTargetArr.push('Email');
        // }

        if (loyaltyCampaigns[i].notification.isPushToApp) {
          pushToTargetArr.push("App");
        }

        promotionsNotificationsTemp.push({
          ...loyaltyCampaigns[i],
          notification: {
            ...loyaltyCampaigns[i].notification,
            pushTargets: pushToTargetArr.join("/"),
          },
        });
      }
    }

    setPromotionsNotifications(promotionsNotificationsTemp);

    // setCurrentPage(1);
    // setPageCount(Math.ceil(promotionsNotificationsTemp.length / perPage));
  }, [loyaltyCampaigns]);

  ///////

  //To remove unwanted sidebar
  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });

  const [
    openFileSelector,
    {
      plainFiles,
      filesContent,
      loading: loadingImageInput,
      clear: clearImageContainer,
      errors,
    },
  ] = useFilePicker({
    readAs: "DataURL",
    accept: "image/*",
    multiple: false,
  });

  // Handle selected image file
  useEffect(() => {
    if (plainFiles.length && filesContent.length && !loadingImageInput) {
      setImage(filesContent[0].content);
      setImageType(filesContent[0].name.slice(filesContent[0].name.lastIndexOf(".")));
      setIsImageChanged(true);
    }

    if (errors.length) console.error(errors);
  }, [plainFiles, filesContent, loadingImageInput, errors]);

  const currOutletShiftStatus = OutletStore.useState(
    (s) => s.currOutletShiftStatus
  );

  // const [outletDropdownList, setOutletDropdownList] = useState([]);
  // const [selectedOutletList, setSelectedOutletList] = useState([]); // multi-outlets

  // var outletNames = [];

  // for (var i = 0; i < allOutlets.length; i++) {
  //   for (var j = 0; j < selectedOutletList.length; j++) {
  //     if (selectedOutletList.includes(allOutlets[i].uniqueId)) {
  //       outletNames.push(allOutlets[i].name);
  //       break;
  //     }
  //   }
  // }

  // useEffect(() => {
  //   setOutletDropdownList(
  //     allOutlets.map((item) => {
  //       return { label: item.name, value: item.uniqueId };
  //     })
  //   );
  // }, [allOutlets]);

  var targetOutletDropdownListTemp = allOutlets.map((outlet) => ({
    label: sliceUnicodeStringV2WithDots(outlet.name, 20),
    value: outlet.uniqueId,
  }));

  const [openO, setOpenO] = useState(false);

  // useEffect(() => {
  //   CommonStore.update((s) => {
  //     s.outletSelectDropdownView = () => {
  //       return (
  //         <View
  //           style={{
  //             flexDirection: "row",
  //             alignItems: "center",
  //             borderRadius: 8,
  //             width: 200,
  //             backgroundColor: "white",
  //           }}
  //         >
  //           {currOutletId.length > 0 &&
  //             allOutlets.find((item) => item.uniqueId === currOutletId) ? (
  //               <DropDownPicker
  //               style={{
  //                 backgroundColor: Colors.fieldtBgColor,
  //                 width: 200,
  //                 height: 40,
  //                 borderRadius: 10,
  //                 borderWidth: 1,
  //                 borderColor: "#E5E5E5",
  //                 flexDirection: "row",
  //               }}
  //               dropDownContainerStyle={{
  //                 width: 200,
  //                 backgroundColor: Colors.fieldtBgColor,
  //                 borderColor: "#E5E5E5",
  //               }}
  //               labelStyle={{
  //                 marginLeft: 5,
  //                 flexDirection: "row",
  //               }}
  //               textStyle={{
  //                 fontSize: 14,
  //                 fontFamily: 'NunitoSans-Regular',

  //                 marginLeft: 5,
  //                 paddingVertical: 10,
  //                 flexDirection: "row",
  //               }}
  //               selectedItemContainerStyle={{
  //                 flexDirection: "row",
  //               }}

  //               showArrowIcon={true}
  //               ArrowDownIconComponent={({ style }) => (
  //                 <Ionicon
  //                   size={25}
  //                   color={Colors.fieldtTxtColor}
  //                   style={{ paddingHorizontal: 5, marginTop: 5 }}
  //                   name="chevron-down-outline"
  //                 />
  //               )}
  //               ArrowUpIconComponent={({ style }) => (
  //                 <Ionicon
  //                   size={25}
  //                   color={Colors.fieldtTxtColor}
  //                   style={{ paddingHorizontal: 5, marginTop: 5 }}
  //                   name="chevron-up-outline"
  //                 />
  //               )}

  //               showTickIcon={true}
  //               TickIconComponent={({ press }) => (
  //                 <Ionicon
  //                   style={{ paddingHorizontal: 5, marginTop: 5 }}
  //                   color={
  //                     press ? Colors.fieldtBgColor : Colors.primaryColor
  //                   }
  //                   name={'md-checkbox'}
  //                   size={25}
  //                 />
  //               )}
  //               placeholderStyle={{
  //                 color: Colors.fieldtTxtColor,
  //                 // marginTop: 15,
  //               }}
  //               dropDownDirection="BOTTOM"
  //               placeholder="Choose Outlet"
  //               items={targetOutletDropdownListTemp}
  //               value={currOutletId}
  //               onSelectItem={(item) => {
  //                 if (item) { // if choose the same option again, value = ''
  //                   MerchantStore.update((s) => {
  //                     s.currOutletId = item.value;
  //                     s.currOutlet =
  //                       allOutlets.find(
  //                         (outlet) => outlet.uniqueId === item.value
  //                       ) || {};
  //                   });
  //                 }

  //                 CommonStore.update((s) => {
  //                   s.shiftClosedModal = false;
  //                 });
  //               }}
  //               open={openO}
  //               setOpen={setOpenO}
  //             />
  //           ) : (
  //             <ActivityIndicator size={"small"} color={Colors.whiteColor} />
  //           )}

  //           {/* <Select

  //             placeholder={"Choose Outlet"}
  //             onChange={(items) => {
  //               setSelectedOutletList(items);
  //             }}
  //             options={outletDropdownList}
  //             isMulti
  //           /> */}
  //         </View>
  //       );
  //     };
  //   });
  // }, [allOutlets, currOutletId, isLoading, currOutletShiftStatus]);

  //Header
  navigation.setOptions({
    headerLeft: () => (
      <View
        style={[
          styles.headerLeftStyle,
          {
            width: windowWidth * 0.17,
          },
        ]}
      >
        <img src={headerLogo} width={124} height={26} />
        {/* <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        /> */}
      </View>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            justifyContent: "center",
            alignItems: "center",
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            //width:  "55%",
          },
          Dimensions.get("screen").width <= 768
            ? { right: Dimensions.get("screen").width * 0.12 }
            : {},
        ]}
      >
        <Text
          style={{
            fontSize: 24,
            // lineHeight: 25,
            textAlign: "center",
            fontFamily: "NunitoSans-Bold",
            color: Colors.whiteColor,
            opacity: 1,
          }}
        >
          Sign Up Reward
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        {/* {console.log('edward test')} */}
        {/* {console.log(outletSelectDropdownView)} */}
        {outletSelectDropdownView && outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: "white",
            width: 0.5,
            height: Dimensions.get("screen").height * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
            // borderWidth: 1
          }}
        ></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate("General Settings - KooDoo BackOffice")
            }
          }}
          style={{ flexDirection: "row", alignItems: "center" }}
        >
          <Text
            style={{
              fontFamily: "NunitoSans-SemiBold",
              fontSize: 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }}
          >
            {userName}
          </Text>
          <View
            style={{
              //backgroundColor: 'red',
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "white",
            }}
          >
            <img
              src={personicon}
              width={windowHeight * 0.035}
              height={windowHeight * 0.035}
            />
            {/* <Image
              style={{
                width: windowHeight * 0.05,
              height: windowHeight * 0.05,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            /> */}
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  /////////////////////////////////////////////////

  // const handleChoosePhoto = () => {
  //   const imagePickerOptions = {
  //     mediaType: 'photo',
  //     quality: 0.5,
  //     includeBase64: false,
  //   };

  //   launchImageLibrary(imagePickerOptions, (response) => {
  //     if (response.didCancel) {
  //     } else if (response.error) {
  //       window.confirm(response.error.toString());
  //     } else {
  //       // setState({ image: response.uri });
  //       setImage(response.uri);
  //       setImageType(response.uri.slice(response.uri.lastIndexOf('.')));

  //       setIsImageChanged(true);
  //     }
  //   });
  // };

  const canOnlyPushOnce = () => {
    window.confirm(
      "Notice",
      "Each promotion can only be pushed once. Are you sure you want to proceed?",
      [
        {
          text: "Push",
          onPress: () => {
            createPromotion(true);
          },
        },
        {
          text: "Cancel",
          onPress: () => { },
        },
      ]
    );
  };

  const createPromotion = async (isAutoPush = false) => {
    var message = "";

    if (!campaignName) {
      message += 'Campaign name must be filled\n';
    }
    // if ( !promoCode ){
    //   message += 'Promo code must be filled\n'
    // }
    // if (selectedAvailability.length <= 0) {
    //   message += 'Availability must be selected\n';
    // }
    // if (!campaignDescription) {
    //   message += 'Campaign description must be filled\n';
    // }
    // if (!image) {
    //   message += 'Campaign image must be selected\n';
    // }
    // if (selectedOutletList.length <= 0) {
    //   message += 'Outlet(s) must be selected\n';
    // }
    if (smsText.length <= 0) {
      message += "Notification/SMS message must be filled\n";
    }

    if (message.length > 0) {
      window.confirm(`Info, ${message}`);

      return;
    } else {
      // if (isPromoCodeUsageLimit) {
      //   if (!promoCodeUsageLimit) {
      //    window.confirm(
      //       'Error',
      //       'Please fill in promo code usage',
      //       [{ text: 'OK', onPress: () => { } }],
      //       { cancelable: false },
      //     );
      //     return;
      //   }
      //   else if (parseInt(promoCodeUsageLimit) <= 0) {
      //     window.confirm(
      //       'Error',
      //       'Promo code usage must be more than 0',
      //       [{ text: 'OK', onPress: () => { } }],
      //       { cancelable: false },
      //     );
      //     return;
      //   }
      // }

      // if (moment(promoDateEnd).isSameOrBefore(moment(promoDateStart))) {
      //  window.confirm(
      //     'Error',
      //     'Promo start date must be before the Promo end date',
      //     [{ text: 'OK', onPress: () => { } }],
      //     { cancelable: false },
      //   );
      //   return;
      // }
      // else if (moment(promoDateEnd).isSame(moment(promoDateStart), 'day')) {
      //  window.confirm(
      //     'Error',
      //     'Promo start date and Promo end date cannot be on the same day',
      //     [{ text: 'OK', onPress: () => { } }],
      //     { cancelable: false },
      //   );
      //   return;
      // }

      var notification = {};

      if (
        // isEditNotification
        true
      ) {
        var atLeastOnePush = false;

        if (isPushToSMS || isPushToApp || isPushToEmail) {
          atLeastOnePush = true;
        }

        if (!atLeastOnePush) {
          window.confirm(
            "Error",
            "Please select at least one push to target",
            [{ text: "OK", onPress: () => { } }],
            { cancelable: false }
          );
          return;
        }

        if (
          // !notificationTitle ||
          // !notificationDescription
          !smsText
          // !notificationPromoCode
        ) {
          window.confirm(
            "Error",
            "Please fill in all required information:\nNotification/SMS Message",
            [{ text: "OK", onPress: () => { } }],
            { cancelable: false }
          );
          return;
        }

        notification = {
          title: notificationTitle || "",
          description: smsText,
          // promoCode: notificationPromoCode,
          isPushToApp: isPushToApp || true,
          isPushToEmail: isPushToEmail || false,
          isPushToSMS: isPushToSMS || false,
          date: moment(notificationDate).valueOf(),
          time: moment(notificationTime).valueOf(),
          isAutoPush: false,
        };
      }

      ////////////////////////////////////////////////////////////////

      var criteriaList = [];

      ////////////////////////////////////////////////////////////////

      var loyaltyCriteriaList = [];

      if (selectedLoyaltyCampaignType === LOYALTY_CAMPAIGN_TYPE.FIRST_VISIT) {
        if (!loyaltyCampaignExpirationDays) {
          window.confirm(
            "Error",
            "Please fill in all required information:\nExpiration (Days)",
            [{ text: "OK", onPress: () => { } }],
            { cancelable: false }
          );
          return;
        }

        if (parseInt(loyaltyCampaignExpirationDays) <= 0) {
          window.confirm(
            "Error",
            "Expiration (Days) must be more than 0",
            [{ text: "OK", onPress: () => { } }],
            { cancelable: false }
          );
          return;
        }

        // maybe can add in item info in future to save bandwidth, for displaying purpose?

        loyaltyCriteriaList.push({
          // priceBeforeTax: +parseFloat(packageItemPrice).toFixed(2),
          loyaltyCampaignExpirationDays: parseInt(
            loyaltyCampaignExpirationDays
          ),
          loyaltyCampaignSendTime: moment(loyaltyCampaignSendTime).valueOf(),
        });
      } else if (
        selectedLoyaltyCampaignType === LOYALTY_CAMPAIGN_TYPE.AT_RISK
      ) {
        if (
          !loyaltyCampaignExpirationDays ||
          !loyaltyCampaignGuestNotVisitedDays
        ) {
          window.confirm(
            "Error",
            "Please fill in all required information:\nExpiration (Days)\nNot Visited (Days)",
            [{ text: "OK", onPress: () => { } }],
            { cancelable: false }
          );
          return;
        }

        if (
          parseInt(loyaltyCampaignExpirationDays) <= 0 ||
          parseInt(loyaltyCampaignGuestNotVisitedDays) <= 0
        ) {
          window.confirm(
            "Error",
            "Expiration (Days) and Not Visited (Days) must be more than 0",
            [{ text: "OK", onPress: () => { } }],
            { cancelable: false }
          );
          return;
        }

        // maybe can add in item info in future to save bandwidth, for displaying purpose?

        loyaltyCriteriaList.push({
          // priceBeforeTax: +parseFloat(packageItemPrice).toFixed(2),
          loyaltyCampaignExpirationDays: parseInt(
            loyaltyCampaignExpirationDays
          ),
          loyaltyCampaignSendTime: moment(loyaltyCampaignSendTime).valueOf(),
          loyaltyCampaignGuestNotVisitedDays: parseInt(
            loyaltyCampaignGuestNotVisitedDays
          ),
        });
      } else if (
        selectedLoyaltyCampaignType === LOYALTY_CAMPAIGN_TYPE.BIRTHDAY
      ) {
        if (
          !loyaltyCampaignExpirationDays ||
          !loyaltyCampaignGuestBirthdayBeforeDays
        ) {
          window.confirm(
            "Error",
            "Please fill in all required information:\nExpiration (Days)\nBefore Birthday (Days)",
            [{ text: "OK", onPress: () => { } }],
            { cancelable: false }
          );
          return;
        }

        if (
          parseInt(loyaltyCampaignExpirationDays) <= 0 ||
          parseInt(loyaltyCampaignGuestBirthdayBeforeDays) <= 0
        ) {
          window.confirm(
            "Error",
            "Expiration (Days) and Before Birthday (Days) must be more than 0",
            [{ text: "OK", onPress: () => { } }],
            { cancelable: false }
          );
          return;
        }

        // maybe can add in item info in future to save bandwidth, for displaying purpose?

        loyaltyCriteriaList.push({
          // priceBeforeTax: +parseFloat(packageItemPrice).toFixed(2),
          loyaltyCampaignExpirationDays: parseInt(
            loyaltyCampaignExpirationDays
          ),
          loyaltyCampaignSendTime: moment(loyaltyCampaignSendTime).valueOf(),
          loyaltyCampaignGuestBirthdayBeforeDays: parseInt(
            loyaltyCampaignGuestBirthdayBeforeDays
          ),
        });
      } else if (selectedLoyaltyCampaignType === LOYALTY_CAMPAIGN_TYPE.GROWTH) {
        if (
          !loyaltyCampaignExpirationDays ||
          !loyaltyCampaignGuestAfterEveryVisits
        ) {
          window.confirm(
            "Error",
            "Please fill in all required information:\nExpiration (Days)\nEvery Visits",
            [{ text: "OK", onPress: () => { } }],
            { cancelable: false }
          );
          return;
        }

        if (
          parseInt(loyaltyCampaignExpirationDays) <= 0 ||
          parseInt(loyaltyCampaignGuestAfterEveryVisits) <= 0
        ) {
          window.confirm(
            "Error",
            "Expiration (Days) and Every Visits must be more than 0",
            [{ text: "OK", onPress: () => { } }],
            { cancelable: false }
          );
          return;
        }

        // maybe can add in item info in future to save bandwidth, for displaying purpose?

        loyaltyCriteriaList.push({
          // priceBeforeTax: +parseFloat(packageItemPrice).toFixed(2),
          loyaltyCampaignExpirationDays: parseInt(
            loyaltyCampaignExpirationDays
          ),
          loyaltyCampaignSendTime: moment(loyaltyCampaignSendTime).valueOf(),
          loyaltyCampaignGuestAfterEveryVisits: parseInt(
            loyaltyCampaignGuestAfterEveryVisits
          ),
        });
      } else if (
        selectedLoyaltyCampaignType === LOYALTY_CAMPAIGN_TYPE.BIG_SPENDER
      ) {
        if (
          !loyaltyCampaignExpirationDays ||
          !loyaltyCampaignGuestAfterEverySpends
        ) {
          window.confirm(
            "Error",
            "Please fill in all required information:\nExpiration (Days)\nEvery Spends",
            [{ text: "OK", onPress: () => { } }],
            { cancelable: false }
          );
          return;
        }

        if (
          parseInt(loyaltyCampaignExpirationDays) <= 0 ||
          parseFloat(loyaltyCampaignGuestAfterEverySpends) <= 0
        ) {
          window.confirm(
            "Error",
            "Expiration (Days) and Every Spends must be more than 0",
            [{ text: "OK", onPress: () => { } }],
            { cancelable: false }
          );
          return;
        }

        // maybe can add in item info in future to save bandwidth, for displaying purpose?

        loyaltyCriteriaList.push({
          // priceBeforeTax: +parseFloat(packageItemPrice).toFixed(2),
          loyaltyCampaignExpirationDays: parseInt(
            loyaltyCampaignExpirationDays
          ),
          loyaltyCampaignSendTime: moment(loyaltyCampaignSendTime).valueOf(),
          loyaltyCampaignGuestAfterEverySpends: +parseFloat(
            loyaltyCampaignGuestAfterEverySpends
          ).toFixed(2),
        });
      } else if (
        selectedLoyaltyCampaignType === LOYALTY_CAMPAIGN_TYPE.SIGN_UP
      ) {
        if (!loyaltyCampaignExpirationDays) {
          window.confirm(
            "Error",
            "Please fill in all required information:\nExpiration (Days)",
            [{ text: "OK", onPress: () => { } }],
            { cancelable: false }
          );
          return;
        }

        if (parseInt(loyaltyCampaignExpirationDays) <= 0) {
          window.confirm(
            "Error",
            "Expiration (Days) must be more than 0",
            [{ text: "OK", onPress: () => { } }],
            { cancelable: false }
          );
          return;
        }

        // maybe can add in item info in future to save bandwidth, for displaying purpose?

        loyaltyCriteriaList.push({
          // priceBeforeTax: +parseFloat(packageItemPrice).toFixed(2),
          loyaltyCampaignExpirationDays: parseInt(
            loyaltyCampaignExpirationDays
          ),
          loyaltyCampaignSendTime: moment(loyaltyCampaignSendTime).valueOf(),
        });
      } else if (
        selectedLoyaltyCampaignType === LOYALTY_CAMPAIGN_TYPE.VOUCHER
      ) {
        if (!loyaltyCampaignExpirationDays) {
          window.confirm(
            "Error",
            "Please fill in all required information:\nExpiration (Days)",
            [{ text: "OK", onPress: () => { } }],
            { cancelable: false }
          );
          return;
        }

        if (parseInt(loyaltyCampaignExpirationDays) <= 0) {
          window.confirm(
            "Error",
            "Expiration (Days) must be more than 0",
            [{ text: "OK", onPress: () => { } }],
            { cancelable: false }
          );
          return;
        }

        // maybe can add in item info in future to save bandwidth, for displaying purpose?

        loyaltyCriteriaList.push({
          // priceBeforeTax: +parseFloat(packageItemPrice).toFixed(2),
          loyaltyCampaignExpirationDays: parseInt(
            loyaltyCampaignExpirationDays
          ),
          loyaltyCampaignSendTime: moment(loyaltyCampaignSendTime).valueOf(),
        });
      }

      ////////////////////////////////////////////////////////////////

      var taxIdList = []; // for each outlet
      var categoryIdList = []; // for each outlet

      var taxName = "";
      var taxRate = 0.05;

      for (var i = 0; i < currOutletTaxes.length; i++) {
        if (currOutletTaxes[i].uniqueId === selectedTax) {
          taxName = currOutletTaxes[i].name;
          taxRate = currOutletTaxes[i].rate;
          break;
        }
      }

      var selectedOutletListTemp = [currOutletId];

      var outletNames = [];

      for (var i = 0; i < allOutlets.length; i++) {
        for (var j = 0; j < selectedOutletListTemp.length; j++) {
          if (selectedOutletListTemp[j].includes(allOutlets[i].uniqueId)) {
            outletNames.push(allOutlets[i].name);
            break;
          }
        }
      }

      // var categoryName = outletCategoriesDict[selectedOutletCategoryId].name;

      // for (var i = 0; i < selectedOutletList.length; i++) {
      //   const outletIdTemp = selectedOutletList[i];

      //   if (outletsTaxDict[outletIdTemp]) {
      //     taxIdList.push(outletsTaxDict[outletIdTemp].uniqueId);

      //     taxName = outletsTaxDict[outletIdTemp].name;
      //     taxRate = outletsTaxDict[outletIdTemp].rate;
      //   }
      //   else {
      //     taxIdList.push('');
      //   }

      //   if (allOutletsCategoriesNameDict[categoryName]) {
      //     var isSameCategoryNameExisted = false;
      //     var categoryIdTemp = '';

      //     for (var j = 0; j < allOutletsCategoriesNameDict[categoryName].length; j++) {
      //       if (allOutletsCategoriesNameDict[categoryName][j].outletId === outletIdTemp) {
      //         isSameCategoryNameExisted = true;
      //         categoryIdTemp = allOutletsCategoriesNameDict[categoryName][j].uniqueId;
      //         break;
      //       }
      //     }

      //     categoryIdList.push(categoryIdTemp);
      //   }
      //   else {
      //     categoryIdList.push('');
      //   }
      // }

      ///////////////////////////////////
      // upload image

      var promotionImagePath = "";
      var promotionCommonIdLocal = selectedLoyaltySignUpCampaignEdit
        ? selectedLoyaltySignUpCampaignEdit.commonId
        : uuidv4();

      if (image && imageType) {
        const rawBase64 = image
          .replace("data:image/jpeg;base64,", "")
          .replace("data:image/jpg;base64,", "")
          .replace("data:image/png;base64,", "");

        const arrayBuffer = _base64ToArrayBuffer(rawBase64);

        // outletItemIdLocal = selectedProductEdit.uniqueId;
        promotionImagePath = await uploadImageToFirebaseStorage64(
          {
            arrayBuffer: arrayBuffer,
            type: imageType,
          },
          `/merchant/${merchantId}/promotion/${promotionCommonIdLocal}/image${imageType}`
        );
      }

      ///////////////////////////////////

      if (selectedLoyaltySignUpCampaignEdit === undefined) {
        // means new item

        var body = {
          merchantId: merchantId,
          merchantName: merchantName,

          campaignName: campaignName,
          isEnableSellOnline: isEnableSellOnline,
          orderTypes: selectedAvailability,
          targetUserGroup: selectedTargetUserGroup,
          targetSegmentGroupList: selectedTargetSegmentGroupList,
          promoCode: promoCode,
          isPromoCodeUsageLimit: isPromoCodeUsageLimit,
          promoCodeUsageLimit: parseInt(promoCodeUsageLimit),
          image: promotionImagePath,
          commonId: promotionCommonIdLocal,
          effectiveType: selectedEffectiveType,
          effectiveTypeOptions: selectedEffectiveTypeOptions,
          effectiveDay: selectedEffectiveDay,
          effectiveTimeStart: moment(effectiveTimeStart).valueOf(),
          effectiveTimeEnd: moment(effectiveTimeEnd).valueOf(),

          minSpend: parseFloat(minSpend),

          promoDateStart: moment(promoDateStart).valueOf(),
          promoDateEnd: moment(promoDateEnd).valueOf(),
          promoTimeStart: moment(promoTimeStart).valueOf(),
          promoTimeEnd: moment(promoTimeEnd).valueOf(),

          campaignDescription: campaignDescription,

          promotionType: selectedPromotionType,

          criteriaList: criteriaList,

          isEditNotification: true,
          notification: notification,

          //////////////
          // multi outlet supports

          outletIdList: selectedOutletListTemp,
          outletNameList: outletNames,

          currOutletTaxName: taxName,
          currOutletTaxRate: taxRate,
          currOutletTaxId: selectedTax,
          currOutletId: currOutletId,

          loyaltyCampaignType: selectedLoyaltyCampaignType,
          loyaltyCriteriaList: loyaltyCriteriaList,

          notificationText: smsText,

          taggableVoucherId: taggableVoucherId || "",
        };

        // console.log(body);

        CommonStore.update((s) => {
          s.isLoading = true;
        });

        // ApiClient.POST(API.createLoyaltyCampaign, body, false).then(
        APILocal.createLoyaltyCampaign({ body: body, uid: userId }).then(
          (result) => {
            if (result && result.status === "success") {
              // if (isAutoPush) {
              //   if (isEditNotification) {
              //     ApiClient.POST(API.switchPromotionNotificationAutoPushStatus, {
              //       promotionId: result.promotion.uniqueId,
              //       notificationAutoPushStatus: true,
              //     }).then((result) => {
              //       if (result && result.status === 'success') {
              //      window.confirm(
              //           'Success',
              //           "Promotion auto-push scheduled.",
              //           [
              //             {
              //               text: 'OK',
              //               onPress: () => {
              //                 navigation.navigate('PromotionList');
              //               },
              //             },
              //           ],
              //           { cancelable: false },
              //         );
              //       }
              //       CommonStore.update(s => {
              //         s.isLoading = false;
              //       });
              //     }).catch(err => { console.log(err) });
              //   }
              //   else {
              //     ApiClient.POST(API.pushPromotionNotificationManual, body).then((result) => {
              //       if (result && result.status === 'success') {
              //       window.confirm(
              //           'Success',
              //           "Promotion pushed.",
              //           [
              //             {
              //               text: 'OK',
              //               onPress: () => {
              //                 navigation.navigate('PromotionList');
              //               },
              //             },
              //           ],
              //           { cancelable: false },
              //         );
              //       }
              //       CommonStore.update(s => {
              //         s.isLoading = false;
              //       });
              //     }).catch(err => { console.log(err) });
              //   }
              // }

              window.confirm(
                "Success",
                "Sign up reward settings has been saved.",
                [
                  {
                    text: "OK",
                    onPress: () => {
                      // navigation.navigate('PromotionList');
                      // props.navigation.navigate('SettingLoyalty');
                      // props.navigation.navigate('LoyaltySettingsScreen');
                      linkTo && linkTo(`${prefix}/loyaltySettingsScreen`);
                    },
                  },
                ],
                { cancelable: false }
              );
            }

            CommonStore.update((s) => {
              s.isLoading = false;
            });
          }
        );
      } else if (selectedLoyaltySignUpCampaignEdit !== null) {
        // means existing item

        var body = {
          promotionId: selectedLoyaltySignUpCampaignEdit.uniqueId || '',

          merchantId: merchantId,
          merchantName: merchantName,

          campaignName: campaignName,
          isEnableSellOnline: isEnableSellOnline,
          orderTypes: selectedAvailability,
          targetUserGroup: selectedTargetUserGroup,
          targetSegmentGroupList: selectedTargetSegmentGroupList,
          promoCode: promoCode,
          isPromoCodeUsageLimit: isPromoCodeUsageLimit,
          promoCodeUsageLimit: parseInt(promoCodeUsageLimit),

          image: promotionImagePath,
          commonId: promotionCommonIdLocal,
          isImageChanged: isImageChanged,

          effectiveType: selectedEffectiveType,
          effectiveTypeOptions: selectedEffectiveTypeOptions,
          effectiveDay: selectedEffectiveDay,
          effectiveTimeStart: moment(effectiveTimeStart).valueOf(),
          effectiveTimeEnd: moment(effectiveTimeEnd).valueOf(),

          minSpend: parseFloat(minSpend),

          promoDateStart: moment(promoDateStart).valueOf(),
          promoDateEnd: moment(promoDateEnd).valueOf(),
          promoTimeStart: moment(promoTimeStart).valueOf(),
          promoTimeEnd: moment(promoTimeEnd).valueOf(),

          campaignDescription: campaignDescription,

          promotionType: selectedPromotionType,

          criteriaList: criteriaList,

          isEditNotification: true,
          notification: notification,

          //////////////
          // multi outlet supports

          outletIdList: selectedOutletListTemp,
          outletNameList: outletNames,

          currOutletTaxName: taxName,
          currOutletTaxRate: taxRate,
          currOutletTaxId: selectedTax,
          currOutletId: currOutletId,

          loyaltyCampaignType: selectedLoyaltyCampaignType,
          loyaltyCriteriaList: loyaltyCriteriaList,

          notificationText: smsText,

          taggableVoucherId: taggableVoucherId || "",
        };

        // console.log(body);

        CommonStore.update((s) => {
          s.isLoading = true;
        });

        // ApiClient.updateLoyaltyCampaign({ body: body, uid: userId }).then(
        APILocal.updateLoyaltyCampaign({ body: body, uid: userId }).then(
          (result) => {
            if (result && result.status === "success") {
              //window.confirm(
              //   'Caution',
              //   'Each Promotion can only be Push ONCE',
              //   [
              //     {
              //       text: 'Push',
              //       onPress: () => { createPromotion(true) },
              //     },
              //     {
              //       text: 'Cancel',
              //       onPress: () => { createPromotion(false) },
              //     }
              //   ]
              // );

              // if (isAutoPush) {
              //   if (selectedLoyaltySignUpCampaignEdit.sendQuota > 0) {
              //     if (isEditNotification) {
              //       ApiClient.POST(API.switchPromotionNotificationAutoPushStatus, {
              //         promotionId: selectedLoyaltySignUpCampaignEdit.uniqueId,
              //         notificationAutoPushStatus: true,
              //       }).then((result) => {
              //         if (result && result.status === 'success') {
              //          window.confirm(
              //             'Success',
              //             "Promotion auto-push scheduled.",
              //             [
              //               {
              //                 text: 'OK',
              //                 onPress: () => {
              //                   navigation.navigate('PromotionList');
              //                 },
              //               },
              //             ],
              //             { cancelable: false },
              //           );
              //         }
              //         CommonStore.update(s => {
              //           s.isLoading = false;
              //         });
              //       }).catch(err => { console.log(err) });
              //     }
              //     else {
              //       ApiClient.POST(API.pushPromotionNotificationManual, body).then((result) => {
              //         if (result && result.status === 'success') {
              //          window.confirm(
              //             'Success',
              //             "Promotion pushed.",
              //             [
              //               {
              //                 text: 'OK',
              //                 onPress: () => {
              //                   navigation.navigate('PromotionList');
              //                 },
              //               },
              //             ],
              //             { cancelable: false },
              //           );
              //         }
              //         CommonStore.update(s => {
              //           s.isLoading = false;
              //         });
              //       }).catch(err => { console.log(err) });
              //     }
              //   }
              //   else {
              //    window.confirm(
              //       'Error',
              //       "This promotion has been pushed before.",
              //       [
              //         {
              //           text: 'OK',
              //           onPress: () => {
              //             // navigation.navigate('PromotionList');
              //           },
              //         },
              //       ],
              //       { cancelable: false },
              //     );
              //   }
              // }

              window.confirm(
                "Success\nSign up reward settings has been updated.",
                [
                  {
                    text: "OK",
                    onPress: () => {
                      // navigation.navigate('PromotionList');
                      // props.navigation.navigate('SettingLoyalty');
                      // props.navigation.navigate('LoyaltySettingsScreen');
                    },
                  },
                ],
                { cancelable: false }
              );
            }

            CommonStore.update((s) => {
              s.isLoading = false;
            });
          }
        );
      }
      // else {
      //   // means existing item

      //   var outletItemImagePath = '';
      //   var outletItemIdLocal = selectedLoyaltySignUpCampaignEdit.uniqueId;

      //   if (image && imageType && isImageChanged) {
      //     // outletItemIdLocal = selectedLoyaltySignUpCampaignEdit.uniqueId;

      //     outletItemImagePath = await uploadImageToFirebaseStorage({
      //       uri: image,
      //       type: imageType,
      //     }, `/merchant/${merchantId}/outletItem/${outletItemIdLocal}/image${imageType}`);
      //   }

      //   ///////////////////////////////////

      //   var body = {
      //     //<- this works, the database is updated
      //     // name: itemName,
      //     // code: '',
      //     // description: description,
      //     // price: itemPrice,
      //     // url: image,
      //     // materialTag: materialTag,
      //     // materialSKU: materialSKU,
      //     // categoryId: categoryId,
      //     // options: [
      //     //     {
      //     //         name: options,
      //     //         least: '1',
      //     //         choice: [{ label: options1, price: '1' }],
      //     //     },
      //     // ],
      //     // material: [{ name: material, qty: material }],
      //     // suppliers: [{ name: supplier }],

      //     merchantId: merchantId,
      //     // outletId: place,
      //     name: itemName,
      //     // categoryId: selectedOutletCategoryId,
      //     code: '',
      //     currency: 'MYR',
      //     description: itemDescription,
      //     image: outletItemImagePath,
      //     price: itemPrice,
      //     // taxId: outletsTaxDict[place].uniqueId,
      //     isActive: true,
      //     isComposite: true,
      //     isDelivery: true,
      //     isSellable: true,
      //     isTakeaway: true,
      //     options: '',
      //     prepareTime: 600,
      //     sku: itemName,

      //     stockLinkItems: stockLinkItems,

      //     outletItemIdLocal: outletItemIdLocal,

      //     variantGroupList: variantGroupList,
      //     addOnGroupList: addOnGroupList,

      //     //////////////
      //     // multi outlet supports

      //     categoryIdList: categoryIdList,
      //     categoryName: outletCategoriesDict[selectedOutletCategoryId].name,

      //     outletIdList: selectedOutletList,

      //     taxIdList: taxIdList,
      //     taxName: taxName,
      //     taxRate: taxRate,

      //     //////////////
      //     // for update purpose

      //     editItemId: selectedLoyaltySignUpCampaignEdit.uniqueId,
      //     editItemSku: selectedLoyaltySignUpCampaignEdit.sku,
      //     editItemImage: selectedLoyaltySignUpCampaignEdit.image,
      //   };

      //   // console.log(body);

      //   CommonStore.update(s => {
      //     s.isLoading = true;
      //   });

      //   ApiClient.POST(API.updateOutletItem, body, false).then((result) => {
      //     if (result && result.status === 'success') {
      //       window.confirm(
      //         'Success',
      //         "Product updated.",
      //         [
      //           {
      //             text: 'OK',
      //             onPress: () => { },
      //           },
      //         ],
      //         { cancelable: false },
      //       );
      //     }

      //     CommonStore.update(s => {
      //       s.isLoading = false;
      //     });
      //   });
      // }
    }
  };

  // const pushSavedPromotion = () => {
  //   if ()

  // }

  // const pushPromotionNotificationManual = (promotion) => {
  //   // self.onButtonClickHandler();

  //   var body = {
  //     // outletId: User.getOutletId()
  //     promotionId: promotion.uniqueId,
  //   };

  //   ApiClient.POST(API.pushPromotionNotificationManual, body).then(result => {
  //     if (result && result.status === 'success') {
  //      window.confirm(
  //         'Success',
  //         "Notification pushed.",
  //         [
  //           {
  //             text: 'OK',
  //             onPress: () => {
  //             },
  //           },
  //         ],
  //         { cancelable: false },
  //       );
  //     }
  //   }).catch(err => { console.log(err) });
  // };

  /////////////////////////////////////////////////

  //Render start here
  return (
    //<UserIdleWrapper disabled={!isMounted}>
    <View
      style={[
        styles.container,
        {
          height: windowHeight,
          width: windowWidth,
          ...getTransformForScreenInsideNavigation(),
        },
      ]}
    >
      <View style={{ flex: 0.8 }}>
        <SideBar
          navigation={props.navigation}
          selectedTab={11}
          expandPromotions={true}
        />
      </View>
      <View style={{ height: windowHeight, flex: 9}}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{ width: windowWidth * 0.9 }}
          contentContainerStyle={{
            paddingBottom: windowHeight * 0.1,
            backgroundColor: Colors.highlightColor,
          }}
        >
          <View
            style={{
              paddingVertical: 30,
              marginHorizontal: 30,
            }}
          >
            {/* Modals */}

            <DateTimePickerModal
              isVisible={showPromoDateStartPicker}
              mode={"date"}
              onConfirm={(text) => {
                setPromoDateStart(moment(text));

                setShowPromoDateStartPicker(false);
              }}
              onCancel={() => {
                setShowPromoDateStartPicker(false);
              }}
            />

            <DateTimePickerModal
              isVisible={showPromoDateEndPicker}
              mode={"date"}
              onConfirm={(text) => {
                setPromoDateEnd(moment(text));

                setShowPromoDateEndPicker(false);
              }}
              onCancel={() => {
                setShowPromoDateEndPicker(false);
              }}
            />

            <DateTimePickerModal
              isVisible={showPromoTimeStartPicker}
              mode={"time"}
              onConfirm={(text) => {
                setPromoTimeStart(moment(text));

                setShowPromoTimeStartPicker(false);
              }}
              onCancel={() => {
                setShowPromoTimeStartPicker(false);
              }}
            />

            <DateTimePickerModal
              isVisible={showPromoTimeEndPicker}
              mode={"time"}
              onConfirm={(text) => {
                setPromoTimeEnd(moment(text));

                setShowPromoTimeEndPicker(false);
              }}
              onCancel={() => {
                setShowPromoTimeEndPicker(false);
              }}
            />

            <DateTimePickerModal
              isVisible={showEffectiveTimeStartPicker}
              mode={"time"}
              onConfirm={(text) => {
                setEffectiveTimeStart(moment(text));

                setShowEffectiveTimeStartPicker(false);
              }}
              onCancel={() => {
                setShowEffectiveTimeStartPicker(false);
              }}
            />

            <DateTimePickerModal
              isVisible={showEffectiveTimeEndPicker}
              mode={"time"}
              onConfirm={(text) => {
                setEffectiveTimeEnd(moment(text));

                setShowEffectiveTimeEndPicker(false);
              }}
              onCancel={() => {
                setShowEffectiveTimeEndPicker(false);
              }}
            />

            {/* <DateTimePickerModal
              isVisible={showLoyaltyCampaignSendTimePicker}
              mode={"time"}
              onConfirm={(text) => {
                setLoyaltyCampaignSendTime(moment(text));

                setShowLoyaltyCampaignSendTimePicker(false);
              }}
              onCancel={() => {
                setShowLoyaltyCampaignSendTimePicker(false);
              }}
              date={moment(loyaltyCampaignSendTime).toDate()}
            /> */}

            {/* <View
              style={{
                flexDirection: "row",
                marginTop: 60,
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <TimeKeeper
                // isVisible={showLoyaltyCampaignSendTimePicker}
                // mode={'time'}
                time={rev_LoyaltyCampaignSendTime}
                onChange={(time) => {
                  setLoyaltyCampaignSendTime(time.formatted12);
                  setRev_LoyaltyCampaignSendTime(time.formatted12)
                }}
                onDoneClick={() => {
                  setShowLoyaltyCampaignSendTimePicker(false);
                }}
              />
            </View> */}

            {/* Modals */}
            {/* ********************** Import Modal Start ************************ */}
            <Modal
              supportedOrientations={["landscape", "portrait"]}
              visible={importModal}
              transparent={true}
              animationType={"slide"}
            >
              <View style={[styles.modalContainer1, {}]}>
                <View
                  style={[
                    styles.modalView,
                    {
                      top: 0,
                      width: 300,
                      height: 240,
                    },
                  ]}
                >
                  <TouchableOpacity
                    style={[styles.closeButton, {
                      right: windowWidth * 0.02,
                      top: windowWidth * 0.02,
                    },]}
                    onPress={() => {
                      // setState({ changeTable: false });
                      setImportModal(false);
                    }}
                  >
                    <AntDesign
                      name="closecircle"
                      size={25}
                      color={Colors.fieldtTxtColor}
                    />
                  </TouchableOpacity>
                  <View style={styles.modalTitle}>
                    <Text style={[styles.modalTitleText, { fontSize: 16 }]}>
                      {/* Import Options */}
                      Import
                    </Text>
                  </View>
                  <View
                    style={{
                      alignItems: "center",
                      top: "10%",
                    }}
                  >
                    <TouchableOpacity
                      style={[
                        styles.modalSaveButton,
                        {
                          zIndex: -1,
                        },
                      ]}
                      onPress={() => {
                        setShowImportListModal(true);
                      }}
                    >
                      <Text
                        style={[
                          styles.modalDescText,
                          { color: Colors.primaryColor },
                        ]}
                      >
                        Import Template (Excel)
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </Modal>

            <Modal
              supportedOrientations={["landscape", "portrait"]}
              visible={showImportListModal}
              transparent={true}
              animationType={"slide"}
            >
              <View style={[styles.modalContainer1, {}]}>
                <View
                  style={[
                    styles.modalView1,
                    {
                      top: 0,
                      //width: 400, height: 300,
                    },
                  ]}
                >
                  <TouchableOpacity
                    style={[styles.closeButton, {
                      right: windowWidth * 0.02,
                      top: windowWidth * 0.02,
                    },]}
                    onPress={() => {
                      setShowImportListModal(false);
                    }}
                  >
                    <AntDesign
                      name="closecircle"
                      size={25}
                      color={Colors.fieldtTxtColor}
                    />
                  </TouchableOpacity>
                  <View style={styles.modalTitle}>
                    <Text
                      style={[
                        styles.modalTitleText,
                        { fontSize: 16, fontWeight: "500" },
                      ]}
                    >
                      Imported List
                    </Text>
                  </View>
                  <View
                    style={{
                      //backgroundColor:'red',
                      height: 70,
                      marginVertical: 10,
                      marginTop: 15,
                      borderWidth: 1,
                      borderColor: "#E5E5E5",
                      height: windowWidth * 0.47,
                      width: windowWidth * 0.56,
                    }}
                  >
                    <View style={{ width: "100%", marginTop: 0 }}>
                      <View
                        style={{
                          backgroundColor: Colors.whiteColor,
                          padding: 7,
                          paddingTop: 0,
                          height: "82%",
                        }}
                      >
                        <View style={{ flexDirection: "row" }}>
                          <View
                            style={{
                              flexDirection: "row",
                              flex: 1.5,
                              borderRightColor: "lightgrey",
                              alignItems: "center",
                              justifyContent: "flex-start",
                            }}
                          >
                            <View style={{ flexDirection: "column" }}>
                              <Text
                                style={{
                                  fontSize: 15,
                                  color: Colors.fieldtTxtColor,
                                  fontFamily: "NunitoSans-Bold",
                                }}
                              >
                                Name
                              </Text>
                            </View>
                          </View>

                          <View
                            style={{
                              flexDirection: "row",
                              flex: 1.8,
                              borderRightColor: "lightgrey",
                              alignItems: "center",
                              justifyContent: "flex-start",
                            }}
                          >
                            <View style={{ flexDirection: "column" }}>
                              <Text
                                style={{
                                  fontSize: 15,
                                  color: Colors.fieldtTxtColor,
                                  fontFamily: "NunitoSans-Bold",
                                }}
                              >
                                Email
                              </Text>
                            </View>
                          </View>

                          <View
                            style={{
                              flexDirection: "row",
                              flex: 1.2,
                              borderRightColor: "lightgrey",
                              alignItems: "center",
                              justifyContent: "flex-start",
                            }}
                          >
                            <View style={{ flexDirection: "column" }}>
                              <Text
                                style={{
                                  fontSize: 15,
                                  color: Colors.fieldtTxtColor,
                                  fontFamily: "NunitoSans-Bold",
                                }}
                              >
                                Phone Number
                              </Text>
                            </View>
                          </View>

                          <View
                            style={{
                              flexDirection: "row",
                              flex: 0.8,
                              borderRightColor: "lightgrey",
                              alignItems: "center",
                              justifyContent: "flex-start",
                            }}
                          >
                            <View style={{ flexDirection: "column" }}>
                              <Text
                                style={{
                                  fontSize: 15,
                                  color: Colors.fieldtTxtColor,
                                  fontFamily: "NunitoSans-Bold",
                                }}
                              ></Text>
                            </View>
                          </View>
                        </View>

                        <FlatList />
                      </View>
                    </View>
                    {/* <Table borderStyle={{ borderWidth: 1}}>
                          <Row data={TableData.tableHead} flexArr={[1, 1, 1, 1, 1, 1, 1]} style={{}}/>
                          <TableWrapper style={{}}>
                            <Col data={TableData.tableTitle} style={{flex:1,}} heightArr={[28,28,28,28]} textStyle={{}}/>
                            <Rows data={TableData.tableData} flexArr={[ 1, 2, 1]} style={{height: 28}} textStyle={{textAlign: 'center'}}/>
                          </TableWrapper>
                        </Table> */}
                  </View>
                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                    }}
                  >
                    <TouchableOpacity
                      style={{
                        borderWidth: 1,
                        borderRadius: 10,
                        padding: 5,
                        alignItems: "center",
                        height: 35,
                        width: 85,
                        borderColor: Colors.primaryColor,
                        backgroundColor: Colors.primaryColor,
                      }}
                      onPress={() => {
                        //importSelectFile()
                        //importTemplate()
                      }}
                    >
                      <Text
                        style={{
                          fontWeight: "600",
                          fontSize: 18,
                          textAlign: "center",
                          color: Colors.whiteColor,
                        }}
                      >
                        Import
                      </Text>
                    </TouchableOpacity>
                    <View
                      style={{ flexDirection: "row", justifyContent: "flex-end" }}
                    >
                      <TouchableOpacity
                        style={{
                          borderWidth: 1,
                          borderRadius: 10,
                          padding: 5,
                          alignItems: "center",
                          height: 35,
                          width: 85,
                          borderColor: Colors.primaryColor,
                          backgroundColor: Colors.whiteColor,
                          marginRight: 10,
                        }}
                        onPress={() => {
                          setImportModal(false);
                        }}
                      >
                        <Text
                          style={{
                            fontWeight: "600",
                            fontSize: 18,
                            textAlign: "center",
                            color: Colors.primaryColor,
                          }}
                        >
                          Cancel
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={{
                          borderWidth: 1,
                          borderRadius: 10,
                          padding: 5,
                          alignItems: "center",
                          height: 35,
                          width: 85,
                          borderColor: Colors.primaryColor,
                          backgroundColor: Colors.primaryColor,
                        }}
                      >
                        <Text
                          style={{
                            fontWeight: "600",
                            fontSize: 18,
                            textAlign: "center",
                            color: "white",
                          }}
                        >
                          Submit
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              </View>
            </Modal>

            <Modal
              supportedOrientations={["landscape", "portrait"]}
              visible={importSmsModal}
              transparent={true}
              animationType={"slide"}
            >
              <View style={[styles.modalContainer1, {}]}>
                <View
                  style={[
                    styles.modalView2,
                    {
                      top: 0,
                      //width: 400, height: 300,
                    },
                  ]}
                >
                  <TouchableOpacity
                    style={[styles.closeButton, {
                      right: windowWidth * 0.02,
                      top: windowWidth * 0.02,
                    },]}
                    onPress={() => {
                      // setState({ changeTable: false });
                      setImportSmsModal(false);
                    }}
                  >
                    <AntDesign
                      name="closecircle"
                      size={25}
                      color={Colors.fieldtTxtColor}
                    />
                  </TouchableOpacity>
                  <View style={{}}>
                    <View style={{ height: windowHeight * 0.1 }}>
                      <Text
                        style={{
                          textAlign: "center",
                          fontWeight: "700",
                          fontSize: 30,
                        }}
                      >
                        Insufficient Credit!
                      </Text>
                    </View>

                    <View
                      style={{
                        justifyContent: "center",
                        alignItems: "center",
                        height: windowHeight * 0.15,
                      }}
                    >
                      <Text
                        style={{
                          textAlign: "center",
                          color: Colors.descriptionColor,
                          fontSize: 25,
                          width: "100%",
                          alignSelf: "center",
                        }}
                      >
                        Please contact your account manager
                      </Text>
                    </View>
                  </View>
                </View>
              </View>
            </Modal>

            <Modal
              visible={showLoyaltyCampaignSendTimePicker}
              transparent={true}
              animationType={"slide"}
              supportedOrientations={["portrait", "landscape"]}
            >
              <View
                style={{
                  flex: 1,
                  backgroundColor: Colors.modalBgColor,
                  alignItems: "center",
                  justifyContent: "center",
                  // top: keyboardHeight > 0 ? -keyboardHeight * 0.45 : 0,
                }}
              >
                <View
                  style={{
                    height: windowWidth * 0.4,
                    width: windowWidth * 0.4,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 12,
                    padding: windowWidth * 0.02,
                    // borderWidth:1
                  }}
                >
                  <View style={{ flexDirection: "row" }}>
                    <View style={{ flex: 0.3 }}></View>
                    <View style={{ flex: 0.4 }}>
                      <Text
                        style={{
                          fontFamily: "NunitoSans-Bold",
                          fontSize: 20,
                          textAlign: "center",
                        }}
                      >
                        Send Time Picker
                      </Text>
                    </View>
                    <View style={{ flex: 0.3 }}>
                      <TouchableOpacity
                        style={{
                          elevation: 1000,
                          zIndex: 1000,
                          alignSelf: "flex-end",
                        }}
                        onPress={() => {
                          setShowLoyaltyCampaignSendTimePicker(false);
                        }}
                      >
                        <AntDesign
                          name="closecircle"
                          size={25}
                          color={Colors.fieldtTxtColor}
                        />
                      </TouchableOpacity>
                    </View>
                  </View>
                  <View
                    style={{
                      flexDirection: "row",
                      marginTop: 60,
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <TimeKeeper
                      isVisible={showLoyaltyCampaignSendTimePicker}
                      mode={"time"}
                      time={moment(loyaltyCampaignSendTime).format("hh:mm A")}
                      onChange={(time) => {
                        setLoyaltyCampaignSendTime(moment(`${moment(loyaltyCampaignSendTime).format("MM/DD/YYYY")} ${time.formatted12}`));
                        setRev_LoyaltyCampaignSendTime(moment(`${moment(rev_LoyaltyCampaignSendTime).format("MM/DD/YYYY")} ${time.formatted12}`));
                        // setLoyaltyCampaignSendTime(time.formatted12);
                        // setRev_LoyaltyCampaignSendTime(time.formatted12);
                      }}
                      onDoneClick={() => {
                        setShowLoyaltyCampaignSendTimePicker(false);
                      }}
                    />
                  </View>
                </View>
              </View>
            </Modal>
            {/* ********************** Import Modal End ************************ */}

            <View>
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  width: windowWidth * 0.877,
                  marginHorizontal: 30,
                  alignSelf: 'center',
                  marginTop: 20,
                }}>
                <TouchableOpacity
                  style={{ height: 35, justifyContent: "center" }}
                  onPress={() => {
                    // props.navigation.navigate('PromotionList');
                    linkTo && linkTo(`${prefix}/loyalty-report`);
                  }}
                >
                  <View
                    style={{
                      flexDirection: "row",
                      // paddingHorizontal: "10%",
                      alignContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <View style={{ justifyContent: "center" }}>
                      <Feather
                        name="chevron-left"
                        size={switchMerchant ? 20 : 30}
                        style={{ color: Colors.primaryColor, alignSelf: "center" }}
                      />
                    </View>
                    <Text
                      style={[
                        {
                          fontSize: 17,
                          color: Colors.primaryColor,
                          fontWeight: "600",
                          marginBottom: 1,
                        },
                        switchMerchant
                          ? {
                            fontSize: 14,
                          }
                          : {},
                      ]}
                    >
                      Back
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>

              {/* <KeyBoardAwareScrollView style={styles.list}> */}
              <View
                showsVerticalScrollIndicator={false}
                nestedScrollEnabled={true}
                style={{
                  backgroundColor: Colors.whiteColor,
                  width: windowWidth * 0.877,
                  marginTop: 10,
                  marginBottom: 30,
                  marginHorizontal: 30,
                  alignSelf: 'center',
                  borderRadius: 5,
                  shadowOpacity: 0,
                  shadowColor: "#000",
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 3,
              }}>
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    width: "100%",
                  }}
                >
                  <View
                    style={{
                      //flexDirection: 'row',
                      margin: 20,
                      marginBottom: 10,
                      width: "70%",
                    }}
                  >
                    <View
                      style={{
                        width: "100%",
                        justifyContent: "space-between",
                        flexDirection: "row",
                      }}
                    >
                      <Text
                        style={[
                          { fontFamily: "NunitoSans-Bold", fontSize: 30 },
                          switchMerchant
                            ? {
                              fontSize: 20,
                            }
                            : {},
                        ]}
                      >
                        {campaignName.length > 0 ? campaignName : "Sign Up Reward"}
                      </Text>
                    </View>

                    {campaignDescription.length > 0 ? (
                      <View
                        style={{
                          width: "100%",
                          justifyContent: "space-between",
                          flexDirection: "row",
                          marginTop: 5,
                        }}
                      >
                        <Text
                          style={{ fontFamily: "NunitoSans-Regular", fontSize: 17 }}
                        >
                          {campaignDescription}
                        </Text>
                      </View>
                    ) : (
                      <></>
                    )}
                  </View>
                  <View
                    style={{
                      margin: 20,
                      marginBottom: 10,
                    }}
                  >
                    <View style={{}}>
                      <TouchableOpacity
                        style={[
                          {
                            justifyContent: "center",
                            flexDirection: "row",
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: "#4E9F7D",
                            borderRadius: 5,
                            width: 130,
                            paddingHorizontal: 10,
                            height: 40,
                            alignItems: "center",
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                            marginBottom: 10,
                          },
                          switchMerchant
                            ? {
                              height: 35,
                              width: 120,
                            }
                            : {},
                        ]}
                        disabled={isLoading}
                        onPress={() => createPromotion()}
                      >
                        <Text
                          style={[
                            {
                              color: Colors.whiteColor,
                              fontSize: 16,
                              fontFamily: "NunitoSans-Bold",
                            },
                            switchMerchant
                              ? {
                                fontSize: 10,
                              }
                              : {},
                          ]}
                        >
                          {isLoading ? "LOADING..." : "SAVE"}
                        </Text>

                        {isLoading ? (
                          <ActivityIndicator
                            // color={Colors.whiteColor}
                            size={"small"}
                          />
                        ) : (
                          <></>
                        )}
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>

                <ScrollView
                  showsVerticalScrollIndicator={false}
                  style={{
                    flexDirection: "column",
                    borderWidth: 1,
                    borderColor: "#c4c4c4",
                    width: "95%",
                    alignSelf: "center",
                    flex: 1,
                    paddingBottom: 30,
                  }}
                >
                  <View style={{ flexDirection: "row", flex: 1 }}>
                    <View
                      style={{
                        flex: 4,
                        flexDirection: "column",
                        marginVertical: 20,
                      }}
                    >
                      <View style={{ flexDirection: "column", marginLeft: 20 }}>
                        <TouchableOpacity
                          onPress={() => {
                            openFileSelector();
                          }}
                        >
                          <View style={{ flexDirection: "row", zIndex: -2 }}>
                            {image ? (
                              <View
                                style={{
                                  backgroundColor: "#F7F7F7",
                                  borderRadius: 5,
                                  zIndex: 1,
                                }}
                              >
                                <AsyncImage
                                  source={{ uri: image }}
                                  style={[
                                    {
                                      width: windowWidth < 1750 ? 200 : 260,
                                      height: 200,
                                      borderRadius: 5,
                                    },
                                    switchMerchant
                                      ? {
                                        width: 200,
                                        height: 160,
                                      }
                                      : {},
                                  ]}
                                  hideLoading={true}
                                />
                                <View
                                  style={{
                                    position: "absolute",
                                    bottom: 5,
                                    right: 5,
                                    //opacity: 0.5,
                                  }}
                                >
                                  <Edit
                                    size={switchMerchant ? 10 : 23}
                                    color={Colors.primaryColor}
                                  />
                                </View>
                              </View>
                            ) : (
                              <View
                                style={[
                                  {
                                    backgroundColor: "#F7F7F7",
                                    borderRadius: 5,
                                    width: windowWidth < 1750 ? 200 : 260,
                                    height: 200,
                                    alignItems: "center",
                                    justifyContent: "center",
                                  },
                                  switchMerchant
                                    ? {
                                      width: 200,
                                      height: 160,
                                    }
                                    : {},
                                ]}
                              >
                                <Icon1
                                  name="upload"
                                  size={switchMerchant ? 100 : 150}
                                  color="lightgrey"
                                  style={{ zIndex: -1 }}
                                />
                                <View
                                  style={{
                                    position: "absolute",
                                    bottom: 5,
                                    right: 5,
                                    //opacity: 0.5,
                                  }}
                                >
                                  <Edit
                                    size={switchMerchant ? 10 : 23}
                                    color={Colors.primaryColor}
                                  />
                                </View>
                              </View>
                            )}
                          </View>
                        </TouchableOpacity>

                        {/* <Text
                          style={[
                            {
                              fontWeight: '500',
                              marginTop: 10,
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: 14,
                            },
                            switchMerchant
                              ? {
                                fontSize: 10,
                              }
                              : {},
                          ]}>
                          Campaign Description
                        </Text>
                        <TextInput
                          style={[
                            {
                              marginTop: '2%',
                              padding: 5,
                              backgroundColor: Colors.fieldtBgColor,
                              width: Platform.OS == 'ios' ? '90%' : '85%',
                              height: Platform.OS == 'ios' ? 100 : 117,
                              borderRadius: 5,
                              borderWidth: 1,
                              borderColor: '#E5E5E5',
                              paddingTop: Platform.OS == 'ios' ? 10 : 10,
                              paddingLeft: 10,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: 14,
                            },
                            switchMerchant
                              ? {
                                fontSize: 10,
                                width: '92%',
                                height: 97,
                              }
                              : {},
                          ]}
                          textAlignVertical={'top'}
                          placeholderStyle={{
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: 14,
                          }}
                          placeholder="Description..."
                          placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                          defaultValue={campaignDescription}
                          multiline={true}
                          onChangeText={(text) => {
                            setCampaignDescription(text);
                          }}
                        /> */}
                      </View>
                    </View>

                    <View
                      style={{
                        flex: 7,
                        marginVertical: 20,
                        marginHorizontal: 20,
                        marginLeft: 10,
                      }}
                    >
                      <View style={{ flexDirection: "row", flex: 1, zIndex: 1 }}>
                        <View
                          style={{
                            flex: 1,
                            marginRight: switchMerchant
                              ? "5%"
                              : windowWidth <= 1024
                                ? "3%"
                                : "2%",
                          }}
                        >
                          <Text
                            style={[
                              {
                                alignSelf: "flex-start",
                                fontFamily: "NunitoSans-Bold",
                                fontSize: 14,
                                fontWeight: "500",
                              },
                              switchMerchant
                                ? {
                                  fontSize: 10,
                                }
                                : {},
                            ]}
                          >
                            Campaign Name
                          </Text>
                          <TextInput
                            placeholder="Campaign Name"
                            placeholderTextColor={"#a9a9a9"}
                            placeholderStyle={{
                              fontFamily: "NunitoSans-Regular",
                              fontSize: 14,
                            }}
                            style={[
                              {
                                backgroundColor: Colors.fieldtBgColor,
                                width: 250,
                                height: 40,
                                borderRadius: 5,
                                padding: 5,
                                marginVertical: 5,
                                borderWidth: 1,
                                borderColor: "#E5E5E5",
                                paddingLeft: 10,
                                fontFamily: "NunitoSans-Regular",
                                fontSize: 14,
                              },
                              switchMerchant
                                ? {
                                  fontSize: 10,
                                  width: 200,
                                  height: 35,
                                }
                                : {},
                            ]}
                            //iOS
                            clearTextOnFocus={true}
                            //////////////////////////////////////////////
                            //Android
                            onFocus={() => {
                              setTemp(campaignName);
                              setCampaignName("");
                            }}
                            ///////////////////////////////////////////////
                            //When textinput is not selected
                            onEndEditing={() => {
                              if (campaignName == "") {
                                setCampaignName(temp);
                              }
                            }}
                            //////////////////////////////////////////////
                            onChangeText={(text) => {
                              setCampaignName(text);
                            }}
                            defaultValue={campaignName}
                          />

                          <Text
                            style={[
                              {
                                fontWeight: "500",
                                zIndex: -6,
                                marginTop: 5,
                                fontFamily: "NunitoSans-Bold",
                                fontSize: 14,
                              },
                              switchMerchant
                                ? {
                                  fontSize: 10,
                                }
                                : {},
                            ]}
                          >
                            Status
                          </Text>
                          <View
                            style={{
                              marginTop: "2%",
                              zIndex: -6,
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "flex-start",
                              flexDirection: "row",
                            }}
                          >
                            <TouchableOpacity
                              onPress={() => {
                                // setImportSmsModal(true);
                                setIsActive(!isActive);
                              }}
                            >
                              <View
                                style={{
                                  borderRadius: 5,
                                  backgroundColor: isActive
                                    ? Colors.primaryColor
                                    : "#ACACAC",
                                  marginRight: 2,
                                  marginLeft: 2,
                                  alignItems: "center",
                                  justifyContent: "center",
                                  width: 34,
                                  height: 34,
                                }}
                              >
                                <Icon
                                  name="checkmark-sharp"
                                  size={20}
                                  color={"#FFFFFF"}
                                  style={{ margin: 2 }}
                                />
                              </View>
                            </TouchableOpacity>

                            <Text
                              style={{
                                fontSize: 14,
                                fontFamily: "NunitoSans-Regular",
                                marginLeft: 10,
                                fontWeight: "500",
                              }}
                            >
                              {isActive ? "Active" : "Disabled"}
                            </Text>
                          </View>
                        </View>
                        <View style={{ flex: 1, zIndex: -1, marginTop: 1 }}>
                          <View style={{ zIndex: 2, zIndex: -4 }}>
                            <Text
                              style={[
                                {
                                  fontWeight: "500",
                                  fontFamily: "NunitoSans-Bold",
                                  fontSize: 14,
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}
                            >
                              Target Segment(s)
                            </Text>

                            {CRM_SEGMENT_DROPDOWN_LIST.concat(
                              crmSegmentsDropdownList
                            ).find((item) =>
                              selectedTargetSegmentGroupList.includes(item.value)
                            ) || selectedTargetSegmentGroupList.length === 0 ? (
                              <View style={{ marginTop: "2%" }}>
                                {/* <MultiSelect
                                  //singleSelect={true}
                                  defaultValue={selectedTargetSegmentGroupList}
                                  onChange={(value) => {
                                    if (value) {
                                      const itemParsed = value.split(',');

                                      setSelectedTargetSegmentGroupList(itemParsed);
                                    }
                                  }}
                                  options={CRM_SEGMENT_DROPDOWN_LIST.concat(
                                    crmSegmentsDropdownList
                                  )}
                                  className={
                                    windowWidth < 1750 ? "msl-vars00" : "msl-vars2"
                                  }
                                /> */}
                                <DropDownPicker
                                  style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    width: 250,
                                    // height: 40, 
                                    borderRadius: 10,
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    flexDirection: "row",
                                  }}
                                  dropDownContainerStyle={{
                                    width: 250,
                                    backgroundColor: Colors.fieldtBgColor,
                                    borderColor: "#E5E5E5",
                                  }}
                                  labelStyle={{
                                    marginLeft: 5,
                                    flexDirection: "row",
                                  }}
                                  textStyle={{
                                    fontSize: 14,
                                    fontFamily: 'NunitoSans-Regular',

                                    marginLeft: 5,
                                    paddingVertical: 10,
                                    flexDirection: "row",
                                  }}
                                  selectedItemContainerStyle={{
                                    flexDirection: "row",
                                  }}

                                  showArrowIcon={true}
                                  ArrowDownIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-down-outline"
                                    />
                                  )}
                                  ArrowUpIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-up-outline"
                                    />
                                  )}

                                  showTickIcon={true}
                                  TickIconComponent={({ press }) => (
                                    <Ionicon
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      color={
                                        press ? Colors.fieldtBgColor : Colors.primaryColor
                                      }
                                      name={'md-checkbox'}
                                      size={25}
                                    />
                                  )}
                                  placeholder={'Select'}
                                  placeholderStyle={{
                                    color: Colors.fieldtTxtColor,
                                    // marginTop: 15,
                                  }}
                                  searchable
                                  searchableStyle={{
                                    paddingHorizontal: windowWidth * 0.0079,
                                  }}
                                  value={selectedTargetSegmentGroupList}
                                  items={CRM_SEGMENT_DROPDOWN_LIST.concat(
                                    crmSegmentsDropdownList,
                                  )}
                                  multiple={true}
                                  multipleText={`${selectedTargetSegmentGroupList.length} tag(s) selected`}
                                  onSelectItem={(items) => {
                                    setSelectedTargetSegmentGroupList(items.map(item => item.value))
                                  }}
                                  open={openTS}
                                  setOpen={setOpenTS}
                                  dropDownDirection="BOTTOM"
                                />
                              </View>
                            ) : (
                              <></>
                            )}
                          </View>

                          <Text
                            style={[
                              {
                                fontWeight: "500",
                                zIndex: -10,
                                marginTop: 5,
                                fontFamily: "NunitoSans-Bold",
                                fontSize: 14,
                              },
                              switchMerchant
                                ? {
                                  fontSize: 10,
                                }
                                : {},
                            ]}
                          >
                            Voucher To Tag
                          </Text>
                          <View style={{ marginTop: "2%", zIndex: -10 }}>
                            {/* <MultiSelect
                              singleSelect={true}
                              defaultValue={taggableVoucherId}
                              onChange={(value) => {
                                if (value) {
                                  setTaggableVoucherId(value);
                                }
                              }}
                              options={taggableVoucherDropdownList}
                              className={
                                windowWidth < 1750 ? "msl-vars00" : "msl-vars2"
                              }
                            /> */}
                            {
                              taggableVoucherDropdownList.find(option => option.value === taggableVoucherId)
                                ?
                                <DropDownPicker
                                  style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    width: 250,
                                    // height: 40,
                                    borderRadius: 10,
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    flexDirection: "row",
                                  }}
                                  dropDownContainerStyle={{
                                    width: 250,
                                    backgroundColor: Colors.fieldtBgColor,
                                    borderColor: "#E5E5E5",
                                  }}
                                  labelStyle={{
                                    marginLeft: 5,
                                    flexDirection: "row",
                                  }}
                                  textStyle={{
                                    fontSize: 14,
                                    fontFamily: 'NunitoSans-Regular',

                                    marginLeft: 5,
                                    paddingVertical: 10,
                                    flexDirection: "row",
                                  }}
                                  selectedItemContainerStyle={{
                                    flexDirection: "row",
                                  }}

                                  showArrowIcon={true}
                                  ArrowDownIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-down-outline"
                                    />
                                  )}
                                  ArrowUpIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-up-outline"
                                    />
                                  )}

                                  showTickIcon={true}
                                  TickIconComponent={({ press }) => (
                                    <Ionicon
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      color={
                                        press ? Colors.fieldtBgColor : Colors.primaryColor
                                      }
                                      name={'md-checkbox'}
                                      size={25}
                                    />
                                  )}
                                  items={taggableVoucherDropdownList}
                                  value={taggableVoucherId}
                                  placeholder={"Select voucher"}
                                  placeholderStyle={{
                                    color: Colors.fieldtTxtColor,
                                    // marginTop: 15,
                                  }}
                                  onSelectItem={(item) => {
                                    setTaggableVoucherId(item.value);
                                  }}
                                  // multiple={true}
                                  open={openVTT}
                                  setOpen={setOpenVTT}
                                  dropDownDirection="BOTTOM"
                                />
                                :
                                <></>
                            }
                          </View>
                        </View>
                      </View>
                      <View
                        style={{ flexDirection: "column", marginTop: "5%" }}
                      ></View>
                    </View>
                  </View>

                  {/*///////////////////////////////////1ST VISIT/////////////////////*/}
                  {selectedLoyaltyCampaignType ===
                    LOYALTY_CAMPAIGN_TYPE.FIRST_VISIT ? (
                    <>
                      <View
                        style={{
                          marginHorizontal: 20,
                          marginTop: 10,
                          marginBottom: 20,
                          zIndex: -1,
                        }}
                      >
                        <View style={{ width: "90%" }}>
                          <View style={{ marginBottom: 5 }}>
                            <Text
                              style={[
                                {
                                  fontWeight: "500",
                                  fontFamily: "NunitoSans-Bold",
                                  fontSize: 14,
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}
                            >
                              {/* Criteria */}
                              {
                                LOYALTY_CAMPAIGN_TYPE_PARSED[
                                selectedLoyaltyCampaignType
                                ]
                              }
                            </Text>
                          </View>

                          <View
                            style={{
                              borderWidth: 1,
                              borderColor: "#E5E5E5",
                              borderRadius: 3,
                              padding: 13,
                              paddingTop: 10,
                              zIndex: -1,
                            }}
                          >
                            <View
                              style={{
                                flexDirection: "column",
                                alignItems: "flex-start",
                              }}
                            >
                              <Text
                                style={[
                                  {
                                    fontWeight: "500",
                                    marginRight: 15,
                                    fontFamily: "NunitoSans-Bold",
                                    fontSize: 14,
                                  },
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                    }
                                    : {},
                                ]}
                              >
                                What time of the day should it be sent
                              </Text>
                              <TouchableOpacity
                                onPress={() => {
                                  // setRev_LoyaltyCampaignSendTime(true);
                                  setShowLoyaltyCampaignSendTimePicker(true);
                                }}
                                style={[
                                  {
                                    backgroundColor: Colors.fieldtBgColor,
                                    // width: 115,
                                    // width: '45%',
                                    width: "20%",
                                    height: 40,
                                    borderRadius: 5,
                                    padding: 1,
                                    marginVertical: 5,
                                    alignItems: "center",
                                    justifyContent: "center",
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    fontFamily: "NunitoSans-Regular",
                                    fontSize: 14,
                                  },
                                  switchMerchant
                                    ? {
                                      height: 35,
                                    }
                                    : {},
                                ]}
                              >
                                <Text
                                  style={[
                                    {
                                      fontVariant: ["tabular-nums"],
                                      fontFamily: "NunitoSans-Regular",
                                      fontSize: 14,
                                    },
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                      }
                                      : {},
                                  ]}
                                >
                                  {moment(loyaltyCampaignSendTime).format(
                                    "hh:mm A"
                                  )}
                                </Text>
                              </TouchableOpacity>
                            </View>

                            <View
                              style={{
                                flexDirection: "column",
                                alignItems: "flex-start",
                                marginTop: 10,
                              }}
                            >
                              <Text
                                style={[
                                  {
                                    fontWeight: "500",
                                    marginRight: 15,
                                    fontFamily: "NunitoSans-Bold",
                                    fontSize: 14,
                                  },
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                    }
                                    : {},
                                ]}
                              >
                                Expiration (days)
                              </Text>
                              <TextInput
                                style={{
                                  borderWidth: 1,
                                  borderColor: "#E5E5E5",
                                  borderRadius: 5,
                                  height: switchMerchant ? 35 : 40,
                                  // width: 80,
                                  justifyContent: "center",
                                  textAlign: "center",
                                  marginTop: 5,
                                  width: "20%",
                                  fontFamily: "NunitoSans-Regular",
                                  fontSize: switchMerchant ? 10 : 14,
                                  //left: 8
                                }}
                                placeholder={"0"}
                                placeholderTextColor={"#a9a9a9"}
                                placeholderStyle={{
                                  justifyContent: "center",
                                  fontFamily: "NunitoSans-Regular",
                                  fontSize: switchMerchant ? 10 : 14,
                                }}
                                onChangeText={(text) => {
                                  var value =
                                    text.length >= 0
                                      ? !isNaN(text)
                                        ? text
                                        : "0"
                                      : "0";

                                  setLoyaltyCampaignExpirationDays(value);
                                }}
                                defaultValue={
                                  loyaltyCampaignExpirationDays != 0
                                    ? loyaltyCampaignExpirationDays
                                    : ""
                                }
                                keyboardType={"decimal-pad"}
                              />
                            </View>
                          </View>
                        </View>
                      </View>
                    </>
                  ) : (
                    <></>
                  )}

                  {/*///////////////////////////////////AT_RISK/////////////////////*/}
                  {selectedLoyaltyCampaignType === LOYALTY_CAMPAIGN_TYPE.AT_RISK ? (
                    <>
                      <View
                        style={{
                          marginHorizontal: 20,
                          marginTop: 10,
                          marginBottom: 20,
                          zIndex: -1,
                        }}
                      >
                        <View style={{ width: "90%" }}>
                          <View style={{ marginBottom: 5 }}>
                            <Text
                              style={[
                                {
                                  fontWeight: "500",
                                  fontFamily: "NunitoSans-Bold",
                                  fontSize: 14,
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}
                            >
                              {/* Criteria */}
                              {
                                LOYALTY_CAMPAIGN_TYPE_PARSED[
                                selectedLoyaltyCampaignType
                                ]
                              }
                            </Text>
                          </View>

                          <View
                            style={{
                              borderWidth: 1,
                              borderColor: "#E5E5E5",
                              borderRadius: 3,
                              padding: 13,
                              paddingTop: 10,
                              zIndex: -1,
                            }}
                          >
                            <View
                              style={{
                                flexDirection: "column",
                                alignItems: "flex-start",
                              }}
                            >
                              <Text
                                style={[
                                  {
                                    fontWeight: "500",
                                    marginRight: 15,
                                    fontFamily: "NunitoSans-Bold",
                                    fontSize: 14,
                                  },
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                    }
                                    : {},
                                ]}
                              >
                                What time of the day should it be sent
                              </Text>
                              <TouchableOpacity
                                onPress={() => {
                                  setShowLoyaltyCampaignSendTimePicker(true);
                                }}
                                style={[
                                  {
                                    backgroundColor: Colors.fieldtBgColor,
                                    // width: 115,
                                    // width: '45%',
                                    width: "20%",
                                    height: 40,
                                    borderRadius: 5,
                                    padding: 1,
                                    marginVertical: 5,
                                    alignItems: "center",
                                    justifyContent: "center",
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    fontFamily: "NunitoSans-Regular",
                                    fontSize: 14,
                                  },
                                  switchMerchant
                                    ? {
                                      height: 35,
                                    }
                                    : {},
                                ]}
                              >
                                <Text
                                  style={[
                                    {
                                      fontVariant: ["tabular-nums"],
                                      fontFamily: "NunitoSans-Regular",
                                      fontSize: 14,
                                    },
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                      }
                                      : {},
                                  ]}
                                >
                                  {moment(loyaltyCampaignSendTime).format(
                                    "hh:mm A"
                                  )}
                                </Text>
                              </TouchableOpacity>
                            </View>

                            <View
                              style={{
                                flexDirection: "column",
                                alignItems: "flex-start",
                                marginTop: 10,
                              }}
                            >
                              <Text
                                style={[
                                  {
                                    fontWeight: "500",
                                    marginRight: 15,
                                    fontFamily: "NunitoSans-Bold",
                                    fontSize: 14,
                                  },
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                    }
                                    : {},
                                ]}
                              >
                                Expiration (days)
                              </Text>
                              <TextInput
                                style={{
                                  borderWidth: 1,
                                  borderColor: "#E5E5E5",
                                  borderRadius: 5,
                                  height: switchMerchant ? 35 : 40,
                                  // width: 80,
                                  justifyContent: "center",
                                  textAlign: "center",
                                  marginTop: 5,
                                  width: "20%",
                                  fontFamily: "NunitoSans-Regular",
                                  fontSize: switchMerchant ? 10 : 14,
                                  //left: 8
                                }}
                                placeholder={"0"}
                                placeholderTextColor={"#a9a9a9"}
                                placeholderStyle={{
                                  justifyContent: "center",
                                  fontFamily: "NunitoSans-Regular",
                                  fontSize: switchMerchant ? 10 : 14,
                                }}
                                onChangeText={(text) => {
                                  var value =
                                    text.length >= 0
                                      ? !isNaN(text)
                                        ? text
                                        : "0"
                                      : "0";

                                  setLoyaltyCampaignExpirationDays(value);
                                }}
                                defaultValue={
                                  loyaltyCampaignExpirationDays != 0
                                    ? loyaltyCampaignExpirationDays
                                    : ""
                                }
                                keyboardType={"decimal-pad"}
                              />
                            </View>

                            <View
                              style={{
                                flexDirection: "column",
                                alignItems: "flex-start",
                                marginTop: 10,
                              }}
                            >
                              <Text
                                style={[
                                  {
                                    fontWeight: "500",
                                    marginRight: 15,
                                    fontFamily: "NunitoSans-Bold",
                                    fontSize: 14,
                                  },
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                    }
                                    : {},
                                ]}
                              >
                                Send when guest hasn't visisted for (days)
                              </Text>
                              <TextInput
                                style={{
                                  borderWidth: 1,
                                  borderColor: "#E5E5E5",
                                  borderRadius: 5,
                                  height: switchMerchant ? 35 : 40,
                                  // width: 80,
                                  justifyContent: "center",
                                  textAlign: "center",
                                  marginTop: 5,
                                  width: "20%",
                                  fontFamily: "NunitoSans-Regular",
                                  fontSize: switchMerchant ? 10 : 14,
                                  //left: 8
                                }}
                                placeholder={"0"}
                                placeholderTextColor={"#a9a9a9"}
                                placeholderStyle={{
                                  justifyContent: "center",
                                  fontFamily: "NunitoSans-Regular",
                                  fontSize: switchMerchant ? 10 : 14,
                                }}
                                onChangeText={(text) => {
                                  var value =
                                    text.length >= 0
                                      ? !isNaN(text)
                                        ? text
                                        : "0"
                                      : "0";

                                  setLoyaltyCampaignGuestNotVisitedDays(value);
                                }}
                                defaultValue={
                                  loyaltyCampaignGuestNotVisitedDays != 0
                                    ? loyaltyCampaignGuestNotVisitedDays
                                    : ""
                                }
                                keyboardType={"decimal-pad"}
                              />
                            </View>
                          </View>
                        </View>
                      </View>
                    </>
                  ) : (
                    <></>
                  )}

                  {/*///////////////////////////////////BIRTHDAY/////////////////////*/}
                  {selectedLoyaltyCampaignType ===
                    LOYALTY_CAMPAIGN_TYPE.BIRTHDAY ? (
                    <>
                      <View
                        style={{
                          marginHorizontal: 20,
                          marginTop: 10,
                          marginBottom: 20,
                          zIndex: -1,
                        }}
                      >
                        <View style={{ width: "90%" }}>
                          <View style={{ marginBottom: 5 }}>
                            <Text
                              style={[
                                {
                                  fontWeight: "500",
                                  fontFamily: "NunitoSans-Bold",
                                  fontSize: 14,
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}
                            >
                              {/* Criteria */}
                              {
                                LOYALTY_CAMPAIGN_TYPE_PARSED[
                                selectedLoyaltyCampaignType
                                ]
                              }
                            </Text>
                          </View>

                          <View
                            style={{
                              borderWidth: 1,
                              borderColor: "#E5E5E5",
                              borderRadius: 3,
                              padding: 13,
                              paddingTop: 10,
                              zIndex: -1,
                            }}
                          >
                            <View
                              style={{
                                flexDirection: "column",
                                alignItems: "flex-start",
                              }}
                            >
                              <Text
                                style={[
                                  {
                                    fontWeight: "500",
                                    marginRight: 15,
                                    fontFamily: "NunitoSans-Bold",
                                    fontSize: 14,
                                  },
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                    }
                                    : {},
                                ]}
                              >
                                What time of the day should it be sent
                              </Text>
                              <TouchableOpacity
                                onPress={() => {
                                  setShowLoyaltyCampaignSendTimePicker(true);
                                }}
                                style={[
                                  {
                                    backgroundColor: Colors.fieldtBgColor,
                                    // width: 115,
                                    // width: '45%',
                                    width: "20%",
                                    height: 40,
                                    borderRadius: 5,
                                    padding: 1,
                                    marginVertical: 5,
                                    alignItems: "center",
                                    justifyContent: "center",
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    fontFamily: "NunitoSans-Regular",
                                    fontSize: 14,
                                  },
                                  switchMerchant
                                    ? {
                                      height: 35,
                                    }
                                    : {},
                                ]}
                              >
                                <Text
                                  style={[
                                    {
                                      fontVariant: ["tabular-nums"],
                                      fontFamily: "NunitoSans-Regular",
                                      fontSize: 14,
                                    },
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                      }
                                      : {},
                                  ]}
                                >
                                  {moment(loyaltyCampaignSendTime).format(
                                    "hh:mm A"
                                  )}
                                </Text>
                              </TouchableOpacity>
                            </View>

                            <View
                              style={{
                                flexDirection: "column",
                                alignItems: "flex-start",
                                marginTop: 10,
                              }}
                            >
                              <Text
                                style={[
                                  {
                                    fontWeight: "500",
                                    marginRight: 15,
                                    fontFamily: "NunitoSans-Bold",
                                    fontSize: 14,
                                  },
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                    }
                                    : {},
                                ]}
                              >
                                Expiration (days)
                              </Text>
                              <TextInput
                                style={{
                                  borderWidth: 1,
                                  borderColor: "#E5E5E5",
                                  borderRadius: 5,
                                  height: switchMerchant ? 35 : 40,
                                  // width: 80,
                                  justifyContent: "center",
                                  textAlign: "center",
                                  marginTop: 5,
                                  width: "20%",
                                  fontFamily: "NunitoSans-Regular",
                                  fontSize: switchMerchant ? 10 : 14,
                                  //left: 8
                                }}
                                placeholder={"0"}
                                placeholderTextColor={"#a9a9a9"}
                                placeholderStyle={{
                                  justifyContent: "center",
                                  fontFamily: "NunitoSans-Regular",
                                  fontSize: switchMerchant ? 10 : 14,
                                }}
                                onChangeText={(text) => {
                                  var value =
                                    text.length >= 0
                                      ? !isNaN(text)
                                        ? text
                                        : "0"
                                      : "0";

                                  setLoyaltyCampaignExpirationDays(value);
                                }}
                                defaultValue={
                                  loyaltyCampaignExpirationDays != 0
                                    ? loyaltyCampaignExpirationDays
                                    : ""
                                }
                                keyboardType={"decimal-pad"}
                              />
                            </View>

                            <View
                              style={{
                                flexDirection: "column",
                                alignItems: "flex-start",
                                marginTop: 10,
                              }}
                            >
                              <Text
                                style={[
                                  {
                                    fontWeight: "500",
                                    marginRight: 15,
                                    fontFamily: "NunitoSans-Bold",
                                    fontSize: 14,
                                  },
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                    }
                                    : {},
                                ]}
                              >
                                Send when guest birthday is in (days)
                              </Text>
                              <TextInput
                                style={{
                                  borderWidth: 1,
                                  borderColor: "#E5E5E5",
                                  borderRadius: 5,
                                  height: switchMerchant ? 35 : 40,
                                  // width: 80,
                                  justifyContent: "center",
                                  textAlign: "center",
                                  marginTop: 5,
                                  width: "20%",
                                  fontFamily: "NunitoSans-Regular",
                                  fontSize: switchMerchant ? 10 : 14,
                                  //left: 8
                                }}
                                placeholder={"0"}
                                placeholderTextColor={"#a9a9a9"}
                                placeholderStyle={{
                                  justifyContent: "center",
                                  fontFamily: "NunitoSans-Regular",
                                  fontSize: switchMerchant ? 10 : 14,
                                }}
                                onChangeText={(text) => {
                                  var value =
                                    text.length >= 0
                                      ? !isNaN(text)
                                        ? text
                                        : "0"
                                      : "0";

                                  setLoyaltyCampaignGuestBirthdayBeforeDays(value);
                                }}
                                defaultValue={
                                  loyaltyCampaignGuestBirthdayBeforeDays != 0
                                    ? loyaltyCampaignGuestBirthdayBeforeDays
                                    : ""
                                }
                                keyboardType={"decimal-pad"}
                              />
                            </View>
                          </View>
                        </View>
                      </View>
                    </>
                  ) : (
                    <></>
                  )}

                  {/*///////////////////////////////////GROWTH/////////////////////*/}
                  {selectedLoyaltyCampaignType === LOYALTY_CAMPAIGN_TYPE.GROWTH ? (
                    <>
                      <View
                        style={{
                          marginHorizontal: 20,
                          marginTop: 10,
                          marginBottom: 20,
                          zIndex: -1,
                        }}
                      >
                        <View style={{ width: "90%" }}>
                          <View style={{ marginBottom: 5 }}>
                            <Text
                              style={[
                                {
                                  fontWeight: "500",
                                  fontFamily: "NunitoSans-Bold",
                                  fontSize: 14,
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}
                            >
                              {/* Criteria */}
                              {
                                LOYALTY_CAMPAIGN_TYPE_PARSED[
                                selectedLoyaltyCampaignType
                                ]
                              }
                            </Text>
                          </View>

                          <View
                            style={{
                              borderWidth: 1,
                              borderColor: "#E5E5E5",
                              borderRadius: 3,
                              padding: 13,
                              paddingTop: 10,
                              zIndex: -1,
                            }}
                          >
                            <View
                              style={{
                                flexDirection: "column",
                                alignItems: "flex-start",
                              }}
                            >
                              <Text
                                style={[
                                  {
                                    fontWeight: "500",
                                    marginRight: 15,
                                    fontFamily: "NunitoSans-Bold",
                                    fontSize: 14,
                                  },
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                    }
                                    : {},
                                ]}
                              >
                                What time of the day should it be sent
                              </Text>
                              <TouchableOpacity
                                onPress={() => {
                                  setShowLoyaltyCampaignSendTimePicker(true);
                                }}
                                style={[
                                  {
                                    backgroundColor: Colors.fieldtBgColor,
                                    // width: 115,
                                    // width: '45%',
                                    width: "20%",
                                    height: 40,
                                    borderRadius: 5,
                                    padding: 1,
                                    marginVertical: 5,
                                    alignItems: "center",
                                    justifyContent: "center",
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    fontFamily: "NunitoSans-Regular",
                                    fontSize: 14,
                                  },
                                  switchMerchant
                                    ? {
                                      height: 35,
                                    }
                                    : {},
                                ]}
                              >
                                <Text
                                  style={[
                                    {
                                      fontVariant: ["tabular-nums"],
                                      fontFamily: "NunitoSans-Regular",
                                      fontSize: 14,
                                    },
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                      }
                                      : {},
                                  ]}
                                >
                                  {moment(loyaltyCampaignSendTime).format(
                                    "hh:mm A"
                                  )}
                                </Text>
                              </TouchableOpacity>
                            </View>

                            <View
                              style={{
                                flexDirection: "column",
                                alignItems: "flex-start",
                                marginTop: 10,
                              }}
                            >
                              <Text
                                style={[
                                  {
                                    fontWeight: "500",
                                    marginRight: 15,
                                    fontFamily: "NunitoSans-Bold",
                                    fontSize: 14,
                                  },
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                    }
                                    : {},
                                ]}
                              >
                                Expiration (days)
                              </Text>
                              <TextInput
                                style={{
                                  borderWidth: 1,
                                  borderColor: "#E5E5E5",
                                  borderRadius: 5,
                                  height: switchMerchant ? 35 : 40,
                                  // width: 80,
                                  justifyContent: "center",
                                  textAlign: "center",
                                  marginTop: 5,
                                  width: "20%",
                                  fontFamily: "NunitoSans-Regular",
                                  fontSize: switchMerchant ? 10 : 14,
                                  //left: 8
                                }}
                                placeholder={"0"}
                                placeholderTextColor={"#a9a9a9"}
                                placeholderStyle={{
                                  justifyContent: "center",
                                  fontFamily: "NunitoSans-Regular",
                                  fontSize: switchMerchant ? 10 : 14,
                                }}
                                onChangeText={(text) => {
                                  var value =
                                    text.length >= 0
                                      ? !isNaN(text)
                                        ? text
                                        : "0"
                                      : "0";

                                  setLoyaltyCampaignExpirationDays(value);
                                }}
                                defaultValue={
                                  loyaltyCampaignExpirationDays != 0
                                    ? loyaltyCampaignExpirationDays
                                    : ""
                                }
                                keyboardType={"decimal-pad"}
                              />
                            </View>

                            <View
                              style={{
                                flexDirection: "column",
                                alignItems: "flex-start",
                                marginTop: 10,
                              }}
                            >
                              <Text
                                style={[
                                  {
                                    fontWeight: "500",
                                    marginRight: 15,
                                    fontFamily: "NunitoSans-Bold",
                                    fontSize: 14,
                                  },
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                    }
                                    : {},
                                ]}
                              >
                                Send after every (visits)
                              </Text>
                              <TextInput
                                style={{
                                  borderWidth: 1,
                                  borderColor: "#E5E5E5",
                                  borderRadius: 5,
                                  height: switchMerchant ? 35 : 40,
                                  // width: 80,
                                  justifyContent: "center",
                                  textAlign: "center",
                                  marginTop: 5,
                                  width: "20%",
                                  fontFamily: "NunitoSans-Regular",
                                  fontSize: switchMerchant ? 10 : 14,
                                  //left: 8
                                }}
                                placeholder={"0"}
                                placeholderTextColor={"#a9a9a9"}
                                placeholderStyle={{
                                  justifyContent: "center",
                                  fontFamily: "NunitoSans-Regular",
                                  fontSize: switchMerchant ? 10 : 14,
                                }}
                                onChangeText={(text) => {
                                  var value =
                                    text.length >= 0
                                      ? !isNaN(text)
                                        ? text
                                        : "0"
                                      : "0";

                                  setLoyaltyCampaignGuestAfterEveryVisits(value);
                                }}
                                defaultValue={
                                  loyaltyCampaignGuestAfterEveryVisits != 0
                                    ? loyaltyCampaignGuestAfterEveryVisits
                                    : ""
                                }
                                keyboardType={"decimal-pad"}
                              />
                            </View>
                          </View>
                        </View>
                      </View>
                    </>
                  ) : (
                    <></>
                  )}

                  {/*///////////////////////////////////BIG_SPENDER/////////////////////*/}
                  {selectedLoyaltyCampaignType ===
                    LOYALTY_CAMPAIGN_TYPE.BIG_SPENDER ? (
                    <>
                      <View
                        style={{
                          marginHorizontal: 20,
                          marginTop: 10,
                          marginBottom: 20,
                          zIndex: -1,
                        }}
                      >
                        <View style={{ width: "90%" }}>
                          <View style={{ marginBottom: 5 }}>
                            <Text
                              style={[
                                {
                                  fontWeight: "500",
                                  fontFamily: "NunitoSans-Bold",
                                  fontSize: 14,
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}
                            >
                              {/* Criteria */}
                              {
                                LOYALTY_CAMPAIGN_TYPE_PARSED[
                                selectedLoyaltyCampaignType
                                ]
                              }
                            </Text>
                          </View>

                          <View
                            style={{
                              borderWidth: 1,
                              borderColor: "#E5E5E5",
                              borderRadius: 3,
                              padding: 13,
                              paddingTop: 10,
                              zIndex: -1,
                            }}
                          >
                            <View
                              style={{
                                flexDirection: "column",
                                alignItems: "flex-start",
                              }}
                            >
                              <Text
                                style={[
                                  {
                                    fontWeight: "500",
                                    marginRight: 15,
                                    fontFamily: "NunitoSans-Bold",
                                    fontSize: 14,
                                  },
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                    }
                                    : {},
                                ]}
                              >
                                What time of the day should it be sent
                              </Text>
                              <TouchableOpacity
                                onPress={() => {
                                  setShowLoyaltyCampaignSendTimePicker(true);
                                }}
                                style={[
                                  {
                                    backgroundColor: Colors.fieldtBgColor,
                                    // width: 115,
                                    // width: '45%',
                                    width: "20%",
                                    height: 40,
                                    borderRadius: 5,
                                    padding: 1,
                                    marginVertical: 5,
                                    alignItems: "center",
                                    justifyContent: "center",
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    fontFamily: "NunitoSans-Regular",
                                    fontSize: 14,
                                  },
                                  switchMerchant
                                    ? {
                                      height: 35,
                                    }
                                    : {},
                                ]}
                              >
                                <Text
                                  style={[
                                    {
                                      fontVariant: ["tabular-nums"],
                                      fontFamily: "NunitoSans-Regular",
                                      fontSize: 14,
                                    },
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                      }
                                      : {},
                                  ]}
                                >
                                  {moment(loyaltyCampaignSendTime).format(
                                    "hh:mm A"
                                  )}
                                </Text>
                              </TouchableOpacity>
                            </View>

                            <View
                              style={{
                                flexDirection: "column",
                                alignItems: "flex-start",
                                marginTop: 10,
                              }}
                            >
                              <Text
                                style={[
                                  {
                                    fontWeight: "500",
                                    marginRight: 15,
                                    fontFamily: "NunitoSans-Bold",
                                    fontSize: 14,
                                  },
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                    }
                                    : {},
                                ]}
                              >
                                Expiration (days)
                              </Text>
                              <TextInput
                                style={{
                                  borderWidth: 1,
                                  borderColor: "#E5E5E5",
                                  borderRadius: 5,
                                  height: switchMerchant ? 35 : 40,
                                  // width: 80,
                                  justifyContent: "center",
                                  textAlign: "center",
                                  marginTop: 5,
                                  width: "20%",
                                  fontFamily: "NunitoSans-Regular",
                                  fontSize: switchMerchant ? 10 : 14,
                                  //left: 8
                                }}
                                placeholder={"0"}
                                placeholderTextColor={"#a9a9a9"}
                                placeholderStyle={{
                                  justifyContent: "center",
                                  fontFamily: "NunitoSans-Regular",
                                  fontSize: switchMerchant ? 10 : 14,
                                }}
                                onChangeText={(text) => {
                                  var value =
                                    text.length >= 0
                                      ? !isNaN(text)
                                        ? text
                                        : "0"
                                      : "0";

                                  setLoyaltyCampaignExpirationDays(value);
                                }}
                                defaultValue={
                                  loyaltyCampaignExpirationDays != 0
                                    ? loyaltyCampaignExpirationDays
                                    : ""
                                }
                                keyboardType={"decimal-pad"}
                              />
                            </View>

                            <View
                              style={{
                                flexDirection: "column",
                                alignItems: "flex-start",
                                marginTop: 10,
                              }}
                            >
                              <Text
                                style={[
                                  {
                                    fontWeight: "500",
                                    marginRight: 15,
                                    fontFamily: "NunitoSans-Bold",
                                    fontSize: 14,
                                  },
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                    }
                                    : {},
                                ]}
                              >
                                Send every time guest spends (RM)
                              </Text>
                              <TextInput
                                style={{
                                  borderWidth: 1,
                                  borderColor: "#E5E5E5",
                                  borderRadius: 5,
                                  height: switchMerchant ? 35 : 40,
                                  // width: 80,
                                  justifyContent: "center",
                                  textAlign: "center",
                                  marginTop: 5,
                                  width: "20%",
                                  fontFamily: "NunitoSans-Regular",
                                  fontSize: switchMerchant ? 10 : 14,
                                  //left: 8
                                }}
                                placeholder={"0"}
                                placeholderTextColor={"#a9a9a9"}
                                placeholderStyle={{
                                  justifyContent: "center",
                                  fontFamily: "NunitoSans-Regular",
                                  fontSize: switchMerchant ? 10 : 14,
                                }}
                                onChangeText={(text) => {
                                  var value =
                                    text.length >= 0
                                      ? !isNaN(text)
                                        ? text
                                        : "0"
                                      : "0";

                                  setLoyaltyCampaignGuestAfterEverySpends(value);
                                }}
                                defaultValue={
                                  loyaltyCampaignGuestAfterEverySpends != 0
                                    ? loyaltyCampaignGuestAfterEverySpends
                                    : ""
                                }
                                keyboardType={"decimal-pad"}
                              />
                            </View>
                          </View>
                        </View>
                      </View>
                    </>
                  ) : (
                    <></>
                  )}

                  {/*///////////////////////////////////SIGN_UP/////////////////////*/}
                  {selectedLoyaltyCampaignType === LOYALTY_CAMPAIGN_TYPE.SIGN_UP ? (
                    <View
                      style={{
                        marginHorizontal: 20,
                        marginTop: 10,
                        marginBottom: 20,
                        zIndex: -1,
                      }}
                    >
                      <View style={{ width: "90%" }}>
                        <View style={{ marginBottom: 5 }}>
                          <Text
                            style={[
                              {
                                fontWeight: "500",
                                fontFamily: "NunitoSans-Bold",
                                fontSize: 14,
                              },
                              switchMerchant
                                ? {
                                  fontSize: 10,
                                }
                                : {},
                            ]}
                          >
                            {/* Criteria */}
                            {
                              LOYALTY_CAMPAIGN_TYPE_PARSED[
                              selectedLoyaltyCampaignType
                              ]
                            }
                          </Text>
                        </View>

                        <View
                          style={{
                            borderWidth: 1,
                            borderColor: "#E5E5E5",
                            borderRadius: 3,
                            padding: 13,
                            paddingTop: 10,
                            zIndex: -1,
                          }}
                        >
                          <View
                            style={{
                              flexDirection: "column",
                              alignItems: "flex-start",
                            }}
                          >
                            <Text
                              style={[
                                {
                                  fontWeight: "500",
                                  marginRight: 15,
                                  fontFamily: "NunitoSans-Bold",
                                  fontSize: 14,
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}
                            >
                              What time of the day should it be sent
                            </Text>
                            <TouchableOpacity
                              onPress={() => {
                                setShowLoyaltyCampaignSendTimePicker(true);
                              }}
                              style={[
                                {
                                  backgroundColor: Colors.fieldtBgColor,
                                  // width: 115,
                                  // width: '45%',
                                  width: "20%",
                                  height: 40,
                                  borderRadius: 5,
                                  padding: 1,
                                  marginVertical: 5,
                                  alignItems: "center",
                                  justifyContent: "center",
                                  borderWidth: 1,
                                  borderColor: "#E5E5E5",
                                  fontFamily: "NunitoSans-Regular",
                                  fontSize: 14,
                                },
                                switchMerchant
                                  ? {
                                    height: 35,
                                  }
                                  : {},
                              ]}
                            >
                              <Text
                                style={[
                                  {
                                    fontVariant: ["tabular-nums"],
                                    fontFamily: "NunitoSans-Regular",
                                    fontSize: 14,
                                  },
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                    }
                                    : {},
                                ]}
                              >
                                {moment(loyaltyCampaignSendTime).format("hh:mm A")}
                              </Text>
                            </TouchableOpacity>
                          </View>

                          <View
                            style={{
                              flexDirection: "column",
                              alignItems: "flex-start",
                              marginTop: 10,
                            }}
                          >
                            <Text
                              style={[
                                {
                                  fontWeight: "500",
                                  marginRight: 15,
                                  fontFamily: "NunitoSans-Bold",
                                  fontSize: 14,
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}
                            >
                              Expiration (days)
                            </Text>
                            <TextInput
                              style={{
                                borderWidth: 1,
                                borderColor: "#E5E5E5",
                                borderRadius: 5,
                                height: switchMerchant ? 35 : 40,
                                // width: 80,
                                justifyContent: "center",
                                textAlign: "center",
                                marginTop: 5,
                                width: "20%",
                                fontFamily: "NunitoSans-Regular",
                                fontSize: switchMerchant ? 10 : 14,
                                //left: 8
                              }}
                              placeholder={"0"}
                              placeholderTextColor={"#a9a9a9"}
                              placeholderStyle={{
                                justifyContent: "center",
                                fontFamily: "NunitoSans-Regular",
                                fontSize: switchMerchant ? 10 : 14,
                              }}
                              //iOS
                              clearTextOnFocus={true}
                              //////////////////////////////////////////////
                              //Android
                              onFocus={() => {
                                setTemp(loyaltyCampaignExpirationDays);
                                setLoyaltyCampaignExpirationDays("");
                              }}
                              ///////////////////////////////////////////////
                              //When textinput is not selected
                              onEndEditing={() => {
                                if (loyaltyCampaignExpirationDays == "") {
                                  setLoyaltyCampaignExpirationDays(temp);
                                }
                              }}
                              //////////////////////////////////////////////
                              onChangeText={(text) => {
                                var value =
                                  text.length >= 0
                                    ? !isNaN(text)
                                      ? text
                                      : "0"
                                    : "0";

                                setLoyaltyCampaignExpirationDays(value);
                              }}
                              defaultValue={
                                loyaltyCampaignExpirationDays != ""
                                  ? loyaltyCampaignExpirationDays
                                  : ""
                              }
                              keyboardType={"decimal-pad"}
                            />
                          </View>
                        </View>
                      </View>
                    </View>
                  ) : (
                    <></>
                  )}

                  {/*///////////////////////////////////VOUCHER/////////////////////*/}
                  {selectedLoyaltyCampaignType === LOYALTY_CAMPAIGN_TYPE.VOUCHER ? (
                    <>
                      <View
                        style={{
                          marginHorizontal: 20,
                          marginTop: 10,
                          marginBottom: 20,
                          zIndex: -1,
                        }}
                      >
                        <View style={{ width: "90%" }}>
                          <View style={{ marginBottom: 5 }}>
                            <Text
                              style={[
                                {
                                  fontWeight: "500",
                                  fontFamily: "NunitoSans-Bold",
                                  fontSize: 14,
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}
                            >
                              {/* Criteria */}
                              {
                                LOYALTY_CAMPAIGN_TYPE_PARSED[
                                selectedLoyaltyCampaignType
                                ]
                              }
                            </Text>
                          </View>

                          <View
                            style={{
                              borderWidth: 1,
                              borderColor: "#E5E5E5",
                              borderRadius: 3,
                              padding: 13,
                              paddingTop: 10,
                              zIndex: -1,
                            }}
                          >
                            <View
                              style={{
                                flexDirection: "column",
                                alignItems: "flex-start",
                              }}
                            >
                              <Text
                                style={[
                                  {
                                    fontWeight: "500",
                                    marginRight: 15,
                                    fontFamily: "NunitoSans-Bold",
                                    fontSize: 14,
                                  },
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                    }
                                    : {},
                                ]}
                              >
                                What time of the day should it be sent
                              </Text>
                              <TouchableOpacity
                                onPress={() => {
                                  setShowLoyaltyCampaignSendTimePicker(true);
                                }}
                                style={[
                                  {
                                    backgroundColor: Colors.fieldtBgColor,
                                    // width: 115,
                                    // width: '45%',
                                    width: "20%",
                                    height: 40,
                                    borderRadius: 5,
                                    padding: 1,
                                    marginVertical: 5,
                                    alignItems: "center",
                                    justifyContent: "center",
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    fontFamily: "NunitoSans-Regular",
                                    fontSize: 14,
                                  },
                                  switchMerchant
                                    ? {
                                      height: 35,
                                    }
                                    : {},
                                ]}
                              >
                                <Text
                                  style={[
                                    {
                                      fontVariant: ["tabular-nums"],
                                      fontFamily: "NunitoSans-Regular",
                                      fontSize: 14,
                                    },
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                      }
                                      : {},
                                  ]}
                                >
                                  {moment(loyaltyCampaignSendTime).format(
                                    "hh:mm A"
                                  )}
                                </Text>
                              </TouchableOpacity>
                            </View>

                            <View
                              style={{
                                flexDirection: "column",
                                alignItems: "flex-start",
                                marginTop: 10,
                              }}
                            >
                              <Text
                                style={[
                                  {
                                    fontWeight: "500",
                                    marginRight: 15,
                                    fontFamily: "NunitoSans-Bold",
                                    fontSize: 14,
                                  },
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                    }
                                    : {},
                                ]}
                              >
                                Expiration (days)
                              </Text>
                              <TextInput
                                style={{
                                  borderWidth: 1,
                                  borderColor: "#E5E5E5",
                                  borderRadius: 5,
                                  height: switchMerchant ? 35 : 40,
                                  // width: 80,
                                  justifyContent: "center",
                                  textAlign: "center",
                                  marginTop: 5,
                                  width: "20%",
                                  fontFamily: "NunitoSans-Regular",
                                  fontSize: switchMerchant ? 10 : 14,
                                  //left: 8
                                }}
                                placeholder={"0"}
                                placeholderTextColor={"#a9a9a9"}
                                placeholderStyle={{
                                  justifyContent: "center",
                                  fontFamily: "NunitoSans-Regular",
                                  fontSize: switchMerchant ? 10 : 14,
                                }}
                                onChangeText={(text) => {
                                  var value =
                                    text.length >= 0
                                      ? !isNaN(text)
                                        ? text
                                        : "0"
                                      : "0";

                                  setLoyaltyCampaignExpirationDays(value);
                                }}
                                defaultValue={
                                  loyaltyCampaignExpirationDays != 0
                                    ? loyaltyCampaignExpirationDays
                                    : ""
                                }
                                keyboardType={"decimal-pad"}
                              />
                            </View>
                          </View>
                        </View>
                      </View>
                    </>
                  ) : (
                    <></>
                  )}

                  <View style={{ paddingTop: 30, zIndex: -3 }}>
                    <Text
                      style={[
                        {
                          fontWeight: "500",
                          fontFamily: "NunitoSans-Bold",
                          fontSize: 14,
                          marginLeft: "2%",
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                          }
                          : {},
                      ]}
                    >
                      Blasting Notification
                    </Text>
                    <View
                      style={{
                        marginTop: "1%",
                        borderWidth: 1,
                        borderColor: "#E5E5E5",
                        width: "80%",
                        // width: 690,
                        marginHorizontal: 20,
                      }}
                    >
                      {/* <View style={{margin: '2%'}}>
                              <View style={{flexDirection: 'row'}}>
                                <DateTimePickerModal
                                  isVisible={showNotificationDatePicker}
                                  mode={'date'}
                                  onConfirm={(text) => {
                                    setNotificationDate(moment(text));

                                    setShowNotificationDatePicker(false);
                                  }}
                                  onCancel={() => {
                                    setShowNotificationDatePicker(false);
                                  }}
                                />

                                <DateTimePickerModal
                                  isVisible={showNotificationTimePicker}
                                  mode={'time'}
                                  onConfirm={(text) => {
                                    setNotificationTime(moment(text));

                                    setShowNotificationTimePicker(false);
                                  }}
                                  onCancel={() => {
                                    setShowNotificationTimePicker(false);
                                  }}
                                />

                                <View style={{flexDirection: 'column'}}>
                                  <Text
                                    style={[
                                      {
                                        fontSize: 14,
                                        fontFamily: 'NunitoSans-Bold',
                                        fontWeight: '500',
                                      },
                                      switchMerchant
                                        ? {
                                            fontSize: 10,
                                          }
                                        : {},
                                    ]}>
                                    Send Date
                                  </Text>
                                  <TouchableOpacity
                                    onPress={() => {
                                      setShowNotificationDatePicker(true);
                                    }}
                                    style={[
                                      {
                                        // height: 50,
                                        height: switchMerchant ? 35 : 40,
                                        paddingHorizontal: 20,
                                        backgroundColor: Colors.fieldtBgColor,
                                        //marginBottom: 20,
                                        width: 160,
                                        //marginHorizontal: 10,
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        justifyContent: 'space-between',
                                        alignContent: 'center',
                                        borderColor: '#E5E5E5',
                                        borderWidth: 1,
                                        fontFamily: 'NunitoSans-Regular',
                                        fontSize: switchMerchant ? 10 : 14,
                                        borderRadius: 12,
                                        marginTop: 10,
                                      },
                                      switchMerchant
                                        ? {
                                            width: 130,
                                            height: 35,
                                          }
                                        : {},
                                    ]}>
                                    <View
                                      style={{alignSelf: 'center'}}
                                      onPress={() => {
                                        setState({
                                          pickerMode: 'date',
                                          showDateTimePicker: true,
                                        });
                                      }}>
                                      <GCalendar
                                        width={switchMerchant ? 15 : 20}
                                        height={switchMerchant ? 15 : 20}
                                      />
                                    </View>
                                    <Text
                                      style={[
                                        {
                                          marginRight: 0,
                                          //color: '#B6B6B6',
                                          fontSize: 14,
                                          fontFamily: 'NunitoSans-Regular',
                                          fontVariant: ['tabular-nums'],
                                        },
                                        switchMerchant
                                          ? {
                                              fontSize: 10,
                                            }
                                          : {},
                                      ]}>
                                      {moment(notificationDate).format(
                                        'DD MMM YYYY',
                                      )}
                                    </Text>
                                  </TouchableOpacity>
                                </View>

                                <View
                                  style={{flexDirection: 'column', marginLeft: 15}}>
                                  <Text
                                    style={[
                                      {
                                        fontSize: 14,
                                        fontFamily: 'NunitoSans-Bold',
                                        fontWeight: '500',
                                      },
                                      switchMerchant
                                        ? {
                                            fontSize: 10,
                                          }
                                        : {},
                                    ]}>
                                    Send Time
                                  </Text>
                                  <TouchableOpacity
                                    onPress={() => {
                                      setShowNotificationTimePicker(true);
                                    }}
                                    style={[
                                      {
                                        // height: 50,
                                        height: switchMerchant ? 35 : 40,
                                        paddingHorizontal: 20,
                                        backgroundColor: Colors.fieldtBgColor,
                                        //marginBottom: 20,
                                        width: 160,
                                        //marginHorizontal: 10,
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        justifyContent: 'space-between',
                                        alignContent: 'center',
                                        borderColor: '#E5E5E5',
                                        borderWidth: 1,
                                        fontFamily: 'NunitoSans-Regular',
                                        fontSize: switchMerchant ? 10 : 14,
                                        borderRadius: 12,
                                        marginTop: 10,
                                      },
                                      switchMerchant
                                        ? {
                                            width: 130,
                                            height: 35,
                                          }
                                        : {},
                                    ]}>
                                    <EvilIcons
                                      name="clock"
                                      size={switchMerchant ? 20 : 25}
                                      color={Colors.primaryColor}
                                      style={{marginLeft: 0}}
                                    />

                                    <Text
                                      style={[
                                        {
                                          marginRight: 10,
                                          //color: '#B6B6B6',
                                          fontSize: 14,
                                          fontFamily: 'NunitoSans-Regular',
                                          fontVariant: ['tabular-nums'],
                                        },
                                        switchMerchant
                                          ? {
                                              fontSize: 10,
                                            }
                                          : {},
                                      ]}>
                                      {moment(notificationTime).format('hh:mm A')}
                                    </Text>
                                  </TouchableOpacity>
                                </View>
                              </View>
                            </View> */}
                      <View
                        style={{
                          margin: "2%",
                        }}
                      >
                        <View
                          style={{
                            marginBottom: "3%",
                          }}
                        >
                          <Text
                            style={{
                              fontSize: 14,
                              fontFamily: "NunitoSans-Regular",
                              // marginTop: 40,
                              //marginLeft: 15,
                              fontWeight: "500",
                            }}
                          >
                            Push To
                          </Text>
                          <View
                            style={{
                              flexDirection: "row",
                              alignItems: "center",
                              marginTop: 10,
                            }}
                          >
                            <TouchableOpacity
                              onPress={() => {
                                // setImportSmsModal(true);
                                setIsPushToSMS(!isPushToSMS);
                              }}
                            >
                              <View
                                style={{
                                  borderRadius: 5,
                                  backgroundColor: isPushToSMS
                                    ? Colors.primaryColor
                                    : "#ACACAC",
                                  marginRight: 2,
                                  marginLeft: 2,
                                  alignItems: "center",
                                  justifyContent: "center",
                                  width: 34,
                                  height: 34,
                                }}
                              >
                                <Icon
                                  name="checkmark-sharp"
                                  size={20}
                                  color={"#FFFFFF"}
                                  style={{ margin: 2 }}
                                />
                              </View>
                            </TouchableOpacity>

                            <Text
                              style={{
                                fontSize: 14,
                                fontFamily: "NunitoSans-Regular",
                                marginLeft: 10,
                                fontWeight: "500",
                              }}
                            >
                              SMS
                            </Text>

                            <TouchableOpacity
                              onPress={() => {
                                setIsPushToApp(!isPushToApp);
                              }}
                            >
                              <View
                                style={{
                                  borderRadius: 5,
                                  backgroundColor: isPushToApp
                                    ? Colors.primaryColor
                                    : "#ACACAC",
                                  marginRight: 2,
                                  marginLeft: 30,
                                  alignItems: "center",
                                  justifyContent: "center",
                                  width: 34,
                                  height: 34,
                                }}
                              >
                                <Icon
                                  name="checkmark-sharp"
                                  size={20}
                                  color={"#FFFFFF"}
                                  style={{ margin: 2 }}
                                />
                              </View>
                            </TouchableOpacity>

                            <Text
                              style={{
                                fontSize: 14,
                                fontFamily: "NunitoSans-Regular",
                                marginLeft: 10,
                                fontWeight: "500",
                              }}
                            >
                              App
                            </Text>

                            {/* <TouchableOpacity onPress={() => {
                              setIsPushToEmail(!isPushToEmail);
                            }}>
                              <View style={{
                                borderRadius: 5,
                                backgroundColor: isPushToEmail ? Colors.primaryColor : '#ACACAC',
                                marginRight: 2,
                                marginLeft: 30,
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: 34,
                                height: 34
                              }}>
                                <Icon name="checkmark-sharp" size={20} color={'#FFFFFF'} style={{ margin: 2 }} />
                              </View>
                            </TouchableOpacity>

                            <Text style={{
                              fontSize: 14,
                              fontFamily: 'NunitoSans-Regular',
                              marginLeft: 10,
                              fontWeight: '500',
                            }}>
                              Email
                            </Text> */}
                          </View>
                        </View>

                        <Text
                          style={{
                            fontFamily: "NunitoSans-Bold",
                            fontSize: switchMerchant ? 10 : 14,
                          }}
                        >
                          Notification/SMS Message
                        </Text>
                        {/* <View
                                style={{
                                  flexDirection: 'row',
                                  width: '100%',
                                  justifyContent: 'space-between',
                                  paddingTop: 10,
                                  alignItems: 'center',
                                }}>
                                <Text
                                  style={{
                                    borderRadius: 999,
                                    padding: 1,
                                    borderWidth: 1,
                                    height: 35,
                                    textAlignVertical: 'center',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}>
                                  {`  en  `}
                                </Text>
                              </View> */}
                      </View>
                      <View style={{ marginTop: 0 }}>
                        <TextInput
                          //underlineColorAndroid={Colors.fieldtBgColor}
                          // style={[styles.textFieldInput, {
                          //   justifyContent: 'flex-start',
                          //   alignContent: 'flex-start',
                          //   backgroundColor: Colors.whiteColor,
                          //   borderRadius: 10,
                          //   padding: 10,
                          //   shadowColor: '#000',
                          //   shadowOffset: {
                          //     width: 0,
                          //     height: 2,
                          //   },
                          //   shadowOpacity: 0.22,
                          //   shadowRadius: 3.22,
                          //   elevation: 1,
                          // }]}
                          style={[
                            {
                              // marginTop: '2%',

                              marginLeft: "2%",

                              padding: 5,
                              backgroundColor: Colors.fieldtBgColor,
                              // width: Platform.OS == 'ios' ? '90%' : '85%',
                              // height: Platform.OS == 'ios' ? 100 : 117,
                              width: 650,
                              height: 140,
                              borderRadius: 5,
                              borderWidth: 1,
                              borderColor: "#E5E5E5",
                              paddingTop: 10,
                              paddingLeft: 10,
                              fontFamily: "NunitoSans-Regular",
                              fontSize: 14,
                            },
                            switchMerchant
                              ? {
                                fontSize: 10,
                                width: "92%",
                                height: 97,
                              }
                              : {},
                          ]}
                          textAlignVertical={"top"}
                          placeholderStyle={{
                            fontFamily: "NunitoSans-Regular",
                            fontSize: 14,
                          }}
                          placeholder="Hi %userName%, we have sent you a voucher of a free cup of coffee, visit %outletName% to redeem now!"
                          placeholderTextColor={"#a9a9a9"}
                          //placeholderStyle={{alignItems: 'flex-start', alignContent: 'flex-start'}}
                          onChangeText={(text) => {
                            // setState({ remark: text });
                            //setRemark(text);
                            text.length <= 150
                              ? setSmsText(text)
                              : window.confirm("Error", "Maximum character 150");
                            // setWordCount(text.length)
                            //setNotificationDescription(text);
                          }}
                          //value={remark}
                          defaultValue={smsText}
                          multiline={true}
                        //maxLength={100}
                        />

                        <View
                          style={{
                            width: 650,
                            marginLeft: "2%",
                            marginBottom: "2%",
                          }}
                        >
                          <Text
                            style={{
                              color: Colors.fieldtTxtColor,
                              fontSize: 13,
                              alignSelf: "flex-end",
                              marginTop: 2,
                            }}
                          >
                            {/* Character: {notificationDescription ? wordCount : '0'}/150 */}
                            Characters: {`${smsText.length}`}/150
                          </Text>
                        </View>
                      </View>
                      {/* <View style={{ margin: '2%' }}>
                              <TextInput
                                style={[
                                  {
                                    backgroundColor: Colors.whiteColor,
                                    width: '100%',
                                    height: windowHeight * 0.15,
                                    borderRadius: 5,
                                    borderWidth: 1,
                                    borderColor: '#E5E5E5',
                                    paddingVertical:
                                      windowHeight * 0.02,
                                    paddingHorizontal:
                                      windowWidth * 0.01,
                                    textAlignVertical: 'top',
                                    fontSize: switchMerchant ? 10 : 16,
                                    fontFamily: 'NunitoSans-Regular',
                                  },
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                    }
                                    : {},
                                ]}
                                placeholder="Notification text here..."
                                placeholderTextColor={Colors.descriptionColor}
                                onChangeText={(text) => {
                                  setSmsText(text);
                                }}
                                defaultValue={smsText}
                                multiline={true}
                              />
                            </View> */}
                    </View>
                  </View>
                </ScrollView>

                <View style={{ height: 60 }}></View>
              </View>
            </View>
          </View>
        </ScrollView>
      </View>
    </View>
    //</UserIdleWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: "row",
  },
  list: {
    backgroundColor: Colors.whiteColor,
    width: Dimensions.get("window").width * 0.87,
    height: Dimensions.get("window").height * 0.825,
    marginTop: 0,
    marginHorizontal: 20,
    alignSelf: "center",
    borderRadius: 5,
    shadowOpacity: 0,
    shadowColor: "#000",
    shadowOffset: {
      width: 1,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
  },
  listItem: {
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    //width: windowWidth * Styles.sideBarWidth,
    // shadowColor: "#000",
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    padding: 16,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: "center",
  },
  titleList: {
    backgroundColor: "#ffffff",
    flexDirection: "row",
    paddingVertical: 20,
    paddingHorizontal: 20,
    //marginTop: 20,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,

    alignItems: "center",

    // shadowOpacity: 0,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 2,
    // },
    // shadowOpacity: 0.22,
    // shadowRadius: 3.22,
    // elevation: 3,
  },
  textTitle: {
    fontSize: 16,
    fontFamily: "NunitoSans-Bold",
  },
  textItem: {
    fontSize: 14,
    fontFamily: "NunitoSans-Regular",
  },
  footer: {
    flexDirection: "row",
    justifyContent: "center",
    // alignContent: 'center',
    alignItems: "center",
    backgroundColor: "white",
    padding: 20,
    paddingTop: 15,
    marginTop: 0,
    width: "100%",

    shadowOpacity: 0,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 7,
  },
  addVoucher: {
    marginTop: 0,
    //justifyContent: 'center',
    alignItems: "center",
    //alignContent: 'center',
    width: Dimensions.get("window").width * 0.78,
    backgroundColor: Colors.whiteColor,
    // marginRight: 100,

    borderRadius: 4,

    shadowOpacity: 0,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
  },
  textInput: {
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginBottom: 20,
    width: 300,
  },
  textInput1: {
    width: 250,
    height: 40,
    backgroundColor: "white",
    borderRadius: 10,
    // marginLeft: '53%',
    flexDirection: "row",
    alignContent: "center",
    alignItems: "center",

    marginRight: Dimensions.get("window").width * Styles.sideBarWidth,

    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  headerLeftStyle: {
    width: Dimensions.get("window").width * 0.17,
    justifyContent: "center",
    alignItems: "center",
  },
  textFieldInput: {
    height: 140,
    width: 650,
    //paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
  },
  modalView: {
    height: 500,
    width: 415,
    backgroundColor: Colors.whiteColor,
    borderRadius: Dimensions.get("window").width * 0.03,
    padding: 12,
    paddingTop: 25,
    //paddingBottom: 30,
  },
  modalView1: {
    height: Dimensions.get("window").width * 0.6,
    width: Dimensions.get("window").width * 0.6,
    backgroundColor: Colors.whiteColor,
    borderRadius: Dimensions.get("window").width * 0.03,
    padding: 20,
    paddingTop: 25,
    //paddingBottom: 30,
  },
  modalView2: {
    height: Dimensions.get("window").width * 0.25,
    width: Dimensions.get("window").width * 0.4,
    backgroundColor: Colors.whiteColor,
    borderRadius: Dimensions.get("window").width * 0.03,
    padding: 20,
    paddingTop: 25,
    //paddingBottom: 30,
  },
  modalContainer1: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: "center",
    justifyContent: "center",
  },
  closeButton: {
    position: "relative",
    alignSelf: "flex-end",
    marginRight: -10,
    marginTop: -15,

    elevation: 1000,
    zIndex: 1000,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
  },
  modalSaveButton: {
    width: Dimensions.get("window").width * 0.15,
    backgroundColor: Colors.fieldtBgColor,
    height: 40,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 8,

    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,

    marginVertical: 10,
  },
  textInput8: {
    fontFamily: "NunitoSans-Regular",
    width: 60,
    height: 40,
    flex: 1,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    paddingHorizontal: 5,
    borderColor: "#E5E5E5",
    borderWidth: 1,
  },
});

export default LoyaltySignUpCampaignScreen;
