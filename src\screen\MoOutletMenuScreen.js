// import { Text } from "react-native-fast-text";
import React, { Component, useState, useEffect, useCallback, useRef } from 'react';
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  TouchableOpacity,
  Alert,
  Dimensions,
  Modal,
  Platform,
  useWindowDimensions,
  InteractionManager,
  TextInput,
  Animated,
  Text,
} from 'react-native';
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import SideBar from './SideBar';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import * as User from '../util/User';
import * as Cart from '../util/Cart';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Entypo from 'react-native-vector-icons/Entypo';
import Back from 'react-native-vector-icons/EvilIcons';
import { FlatList } from 'react-native-gesture-handler';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Close from 'react-native-vector-icons/AntDesign';
import Draggable from 'react-native-draggable';
import {
  compareTwoValues,
  getTransformForScreenInsideNavigation,
  // isTablet
} from '../util/common';
import AntDesign from 'react-native-vector-icons/AntDesign';
import { CommonStore } from '../store/commonStore';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { UserStore } from '../store/userStore';
import AsyncImage from '../components/asyncImage';
import Feather from 'react-native-vector-icons/Feather';
import { CHARGES_TYPE, MODE_ADD_CART, ORDER_TYPE, ORDER_TYPE_SUB, OUTLET_SHIFT_STATUS, PRODUCT_PRICE_TYPE, UNIT_TYPE, UNIT_TYPE_SHORT } from '../constant/common';
import { naturalCompare } from '../util/common';
import moment from 'moment';
import { useFocusEffect } from '@react-navigation/native';
// import UserIdleWrapper from '../components/userIdleWrapper';
import APILocal from '../util/apiLocalReplacers';
import { PROMOTION_TYPE_VARIATION, PROMOTION_TYPE_VARIATION_DROPDOWN_LIST } from '../constant/promotions';
import DropDownPicker from 'react-native-dropdown-picker';

// const View = require(
//   'react-native/Libraries/Components/View/ViewNativeComponent'
// ).default;

const MoOutletMenuScreen = props => {
  const { numColumns, listKey, navigation, route, } = props;
  const [numberColumns, setNumColumns] = useState(7); // Default value
  const [listedKey, setListKey] = useState('default');

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  // const outletData = route.params.outletData;
  const outletDataParam = route.params.outletData;
  const testParam = route.params.test;
  const navFromParam = route.params.navFrom;

  // const [outletData, setOutletData] = useState(outletData);
  const [outletMenu, setOutletMenu] = useState([]);
  const [category, setCategory] = useState('');
  const [menu, setMenu] = useState([]);
  const [reverse, setReverse] = useState(false);
  const [categoryOutlet, setCategoryOutlet] = useState([]);
  const [test, setTest] = useState(testParam);
  const [currentMenu, setCurrentMenu] = useState([]);
  const [productList2, setProductList2] = useState([]);
  const [productList, setProductList] = useState([]);
  const [choice, setChoice] = useState(null);
  const [categoryIndex, setCategoryIndex] = useState(0);
  const [navFrom, setNavFrom] = useState(navFromParam);
  const [isInfoTabHitTop, setIsInfoTabHitTop] = useState(false);
  const [onStartVisible, setOnStartVisible] = useState(false);
  const [cartIcon, setCartIcon] = useState(false);
  const [cartItem, setCartItem] = useState([]);
  const [cartWarning, setCartWarning] = useState(false);
  const [cartProceed, setCartProceed] = useState([]);
  const [switchMerchant, setSwitchMerchant] = useState(false);

  const [outletItemsDisplay, setOutletItemsDisplay] = useState([]);

  const [effectiveDays, setEffectiveDays] = useState(moment().day());

  // 2022-06-22 Sortable outlet categories

  const [filteredOutletCategories, setFilteredOutletCategories] = useState([]);

  //////////////////////////////////////////////

  const outletData = MerchantStore.useState((s) => s.currOutlet);

  // const selectedOutletItems = CommonStore.useState(s => s.selectedOutletItems);
  // const selectedOutletItemCategories = CommonStore.useState(s => s.selectedOutletItemCategories);
  const selectedOutletItemCategory = CommonStore.useState(
    (s) => s.selectedOutletItemCategory,
  );

  const outletItems = OutletStore.useState((s) => s.outletItems);
  const outletCategories = OutletStore.useState((s) => s.outletCategories);

  const cartItems = CommonStore.useState((s) => s.cartItems);
  const orderTypeMo = CommonStore.useState((s) => s.orderTypeMo);
  const orderTypeSubMo = CommonStore.useState((s) => s.orderTypeSubMo);

  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const sidebarClose = () => {
    setIsSidebarOpen(false);
    Animated.timing(sidebarXValue, {
      toValue: -200,
      duration: 200,
      useNativeDriver: true,
    }).start();

    Animated.timing(contentXValue, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };
  const sidebarOpen = () => {
    setIsSidebarOpen(true);
    Animated.timing(sidebarXValue, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
    Animated.timing(contentXValue, {
      toValue: 120,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  const sidebarXValue = useRef(new Animated.Value(-200)).current;
  const contentXValue = useRef(new Animated.Value(0)).current;

  const cartItemChoices = CommonStore.useState((s) => s.cartItemChoices);

  const userName = UserStore.useState((s) => s.name);
  const merchantName = MerchantStore.useState((s) => s.name);
  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );

  const overrideItemPriceSkuDict = CommonStore.useState(
    (s) => s.overrideItemPriceSkuDict,
  );
  const amountOffItemSkuDict = CommonStore.useState(
    (s) => s.amountOffItemSkuDict,
  );
  const percentageOffItemSkuDict = CommonStore.useState(
    (s) => s.percentageOffItemSkuDict,
  );
  const buy1Free1ItemSkuDict = CommonStore.useState(
    (s) => s.buy1Free1ItemSkuDict,
  );

  const overrideCategoryPriceNameDict = CommonStore.useState(
    (s) => s.overrideCategoryPriceNameDict,
  );
  const amountOffCategoryNameDict = CommonStore.useState(
    (s) => s.amountOffCategoryNameDict,
  );
  const percentageOffCategoryNameDict = CommonStore.useState(
    (s) => s.percentageOffCategoryNameDict,
  );
  const buy1Free1CategoryNameDict = CommonStore.useState(
    (s) => s.buy1Free1CategoryNameDict,
  );

  const selectedOutletItemCategoriesDict = OutletStore.useState(
    (s) => s.outletCategoriesDict,
  );

  const [routeParamsMenuItemDetails, setRouteParamsMenuItemDetails] = useState({
    refresh: () => { },
    menuItem: {},
    outletData: {},
    orderType: '',
  });

  const isOnMenu = CommonStore.useState((s) => s.isOnMenu);
  const isOnCategory = CommonStore.useState((s) => s.isOnCategory == true);

  /////////////////////////////////////////////////////////////////////////

  const currOutletShiftStatus = OutletStore.useState(
    (s) => s.currOutletShiftStatus,
  );

  const isOrdering = CommonStore.useState((s) => s.isOrdering);
  const isLoading = CommonStore.useState((s) => s.isLoading);

  const moPax = CommonStore.useState((s) => s.moPax);
  const moMethod = CommonStore.useState((s) => s.moMethod);

  const selectedOutletTableMo = CommonStore.useState(
    (s) => s.selectedOutletTableMo,
  );

  const currOutletId = MerchantStore.useState((s) => s.currOutletId);

  const outletsItemAddOnDict = CommonStore.useState(
    (s) => s.allOutletsItemAddOnDict,
  );
  const outletsItemAddOnChoiceDict = CommonStore.useState(
    (s) => s.allOutletsItemAddOnChoiceDict,
  );

  const selectedOutletItemAddOn = CommonStore.useState(
    (s) => s.selectedOutletItemAddOn,
  );
  const selectedOutletItemAddOnChoice = CommonStore.useState(
    (s) => s.selectedOutletItemAddOnChoice,
  );

  const [
    newSelectedOutletItemAddOnDetails,
    setNewSelectedOutletItemAddOnDetails,
  ] = useState({});

  const allOutletsItemAddOnChoiceIdDict = CommonStore.useState(
    (s) => s.allOutletsItemAddOnChoiceIdDict,
  );

  const onUpdatingCartItem = CommonStore.useState((s) => s.onUpdatingCartItem);
  const modeAddCart = CommonStore.useState((s) => s.modeAddCart);
  const onUpdatingCurrPendingOrder = CommonStore.useState(
    (s) => s.onUpdatingCurrPendingOrder,
  );

  // const menuItem = CommonStore.useState((s) => s.selectedOutletItem);

  /////////////////////////////////////////////////////////////////////////
  // 4/1/2023 For Search Bar - Greg

  // const [search, setSearch] = useState('');
  const [loading, setLoading] = useState(false);
  const [isOnSearch, setIsOnSearch] = useState(false);

  const search = CommonStore.useState((s) => s.moSearch);
  // console.log(search, "SEARCCCCHDFJSHI")

  /////////////////////////////////////////////////////////////////////////

  // 11/10/2022 For Available On (Time) - Greg

  const timeCheckItem = CommonStore.useState(s => s.timeCheckItem);

  // 2024-04-01 - commented, can be triggered by dashboard
  // useEffect(() => {
  //   setInterval(() => {
  //     CommonStore.update(s => {
  //       s.timeCheckItem = Date.now();
  //     });
  //   }, 60000);
  // }, []);

  useEffect(() => {
    if (isMounted) {
      console.log('useEffect - MoOutletMenu - 1');

      InteractionManager.runAfterInteractions(() => {
        var filteredOutletCategoriesTemp = outletCategories
          .filter((category) => {
            // return category.hideInOrderTypes &&
            //   category.hideInOrderTypes.length > 0
            //   ? category.hideInOrderTypes.includes(orderType)
            //     ? false
            //     : true
            //   : true;

            if ((category.isActive || category.isActive === undefined) &&
              (category.isAvailableDayActive
                ? (category.effectiveTypeOptions.includes(effectiveDays) &&
                  category.effectiveStartTime && category.effectiveEndTime &&
                  moment().isSameOrAfter(
                    moment(category.effectiveStartTime)
                      .year(moment().year())
                      .month(moment().month())
                      .date(moment().date())
                  )
                  &&
                  moment().isBefore
                    (moment(category.effectiveEndTime)
                      .year(moment().year())
                      .month(moment().month())
                      .date(moment().date())
                    )
                )
                : true)) {
              return true;
            }
            else {
              return false;
            }
          });

        filteredOutletCategoriesTemp = filteredOutletCategoriesTemp.sort((a, b) => {
          return (
            (a.orderIndex
              ? a.orderIndex
              : filteredOutletCategoriesTemp.length) -
            (b.orderIndex
              ? b.orderIndex
              : filteredOutletCategoriesTemp.length)
          );
        });

        const isEqual = compareTwoValues(filteredOutletCategories, filteredOutletCategoriesTemp);
        if (!isEqual) {
          setFilteredOutletCategories(filteredOutletCategoriesTemp);
        }
      });
    }
  }, [outletCategories, timeCheckItem, isMounted]);

  useEffect(() => {
    if (isMounted) {
      console.log('useEffect - MoOutletMenu - 2');

      if (filteredOutletCategories.length > 0 && ((Object.keys(selectedOutletItemCategory).length === 0 || !selectedOutletItemCategory) || !filteredOutletCategories.find(category => category.uniqueId === selectedOutletItemCategory.uniqueId))) {
        const isEqual = compareTwoValues(selectedOutletItemCategory, filteredOutletCategories[0]);
        if (!isEqual) {
          CommonStore.update(s => {
            s.selectedOutletItemCategory = filteredOutletCategories[0];
          });
        }
      }
    }
  }, [filteredOutletCategories, isMounted]);



  useEffect(() => {
    console.log('useEffect - MoOutletMenu - 3');

    if (cartItems.length > 0) {
      setCartIcon(true);
    }
  }, [cartItems.length]);

  useEffect(() => {
    console.log('useEffect - MoOutletMenu - 4');

    var outletItemsDisplayTemp = [];

    // InteractionManager.runAfterInteractions(() => {
    if (search && search.trim() !== '') {
      // setOutletItemsDisplay(outletItems.filter((item) => {
      //   if (search !== '') {
      //     const searchLowerCase = search.toString().toLowerCase();
      //     if (item.name.toLowerCase().includes(searchLowerCase) || (item.barcode && item.barcode.serialNumber.includes(searchLowerCase))) {
      //       return true;
      //     } else {
      //       return false;
      //     }
      //   } else {
      //     return true;
      //   }
      // }));

      outletItemsDisplayTemp = outletItems;
    }
    else {
      outletItemsDisplayTemp = outletItems.filter((item) => {
        if (search && search.trim() !== '') {
          const searchLowerCase = search.toString().toLowerCase();
          if (item.name.toLowerCase().includes(searchLowerCase) || (item.barcode && item.barcode.serialNumber.includes(searchLowerCase))) {
            return true;
          } else {
            return false;
          }
        } else {
          return (
            selectedOutletItemCategory &&
            item.categoryId === selectedOutletItemCategory.uniqueId
            // &&
            // (item.hideInOrderTypes && item.hideInOrderTypes.length > 0
            //   ? item.hideInOrderTypes.includes(orderTypeMo)
            //     ? false
            //     : true
            //   : true)
          );
        }
      });
    }

    setOutletItemsDisplay(outletItemsDisplayTemp.slice().sort((a, b) => {
      return (a.orderIndex ? a.orderIndex : outletItemsDisplayTemp.length) -
        (b.orderIndex ? b.orderIndex : outletItemsDisplayTemp.length)
    }));

    // });
  }, [outletItems, selectedOutletItemCategory, orderTypeMo,
    // timeCheckItem,
    search]);

  /////////////////////////////////////////////////////////////////////////

  // 20/04/2023 For Add Item Straight to Cart (If No Addons/Variants) - Greg

  //const [quantity, setQuantity] = useState('1');

  /////////////////////////////////////////////////////////////////////////

  const setState = () => { };

  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });

  // function here
  const getCartItem = () => {
    setState({ cartItem: Cart.getCartItem() });
    // // console.log(cartItem)
  };

  const cartCount = () => {
    if (Cart.getCartItem() !== null) {
      if (Cart.getCartItem().length > 0) {
        setState({ cartIcon: true });
      } else {
        setState({ cartIcon: false });
      }
    } else {
      setState({ cartIcon: false });
    }
  };

  const goToCart = () => {
    if (Cart.getCartItem().length > 0) {
      if (navFrom == 'TAKEAWAY') {
        props.navigation.navigate('Cart', {
          screen: 'Cart',
          params: { test, outletData, navFrom },
        });
      } else {
        props.navigation.navigate('Cart', { test, outletData });
      }
    } else {
      alert(
        'Info\n\nNo items in your cart at the moment'
      );
    }
  };

  const onCartClicked = () => {
    if (cartItems.length > 0) {
      // if (navFrom == ORDER_TYPE.TAKEAWAY) {
      if (orderTypeMo !== ORDER_TYPE.DINEIN) {
        props.navigation.navigate('Cart', {
          test,
          outletData,
          navFrom,
        });
      } else {
        props.navigation.navigate('Cart', { test, outletData });
      }
    } else {
      alert(
        'Info\n\nNo items in your cart at the moment',
      );
    }
  };

  const categoryFunc = () => {
    // ApiClient.GET(API.activeMenu + outletData.id)
    //   .then((result) => {
    //     const tmpCategories = {};
    //     for (const category of result) {
    //       const categoryName = category.name;
    //       const categoryId = category.id;
    //       if (!tmpCategories[categoryName]) {
    //         tmpCategories[categoryName] = {
    //           label: categoryName,
    //           value: categoryId,
    //         };
    //       }
    //     }
    //     const categories = Object.values(tmpCategories);
    //     setState({categoryOutlet: categories, category: categories[0].label});
    //   })
    //   .catch((err) => {
    //     // console.log('Error');
    //     // console.log(err);
    //   });
  };

  const refresh = () => {
    ApiClient.GET(API.merchantMenu + outletData.id).then((result) => {
      if (result != undefined && result.length > 0) {
        var productListRaw = [];

        result.forEach((element) => {
          // console.log(element.items);
          productListRaw = productListRaw.concat(element.category);
          const activeItem = productListRaw.filter((item) => item.active == 1);
          setState(
            { productList: productListRaw, productList2: activeItem },
            () => { },
          );
        });
      }
    });
  };

  const refreshMenu = () => {
    ApiClient.GET(API.activeMenu + outletData.id).then((result) => {
      const category = result.filter((i) => i.name == category);
      category.map((i) => {
        // const newList = []
        // for (const item of i.items){
        //   if(item.name !== ""){
        //     newList.push(item)
        //   }
        // }
        // setState({ currentMenu: newList })
        setState({ currentMenu: i.items });
      });
      // }

      // }else{
      //   setState({ currentMenu: result });
      // }
    });
  };

  // const refresh = () => {
  //   setState({ refresh: true });
  // }

  const renderOutletCategory = ({ item }) => {
    return (
      <TouchableOpacity
        testID={`MoOutletMenuScreen.${item.name}`}
        onPress={() => {
          requestAnimationFrame(() => {
            CommonStore.update((s) => {
              s.selectedOutletItemCategory = item;
              s.isOnCategory = false;
            });
          });
        }}
        style={[{
          //backgroundColor: '#717378',
          backgroundColor: '#fafafa',

          width: switchMerchant
            ? windowWidth * 0.07
            : windowWidth <= 1823 && windowWidth >= 1820
              ? windowWidth * 0.08
              : windowWidth * 0.10,

          height: windowHeight * 0.23,

          //height: switchMerchant ? windowWidth * 0.19 : '50%',

          borderRadius: 10,
          //marginLeft: 30,
          //marginTop: 10,
          margin: 2,
          zIndex: switchMerchant ? -1000 : -1,
          //alignSelf: 'center',
          justifyContent: switchMerchant ? 'flex-start' : windowWidth < 1000 ? 'center' : 'flex-start',
          alignContent: 'center',
          alignItems: 'center',
          //flexDirection: 'column',
          //flex: 1,
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.22,
          shadowRadius: 3.22,
          elevation: 1,
        },
        !switchMerchant && windowWidth < 1000 ? {
          height: windowHeight * 0.3,
        } : {},
        ]}>
        <View style={[styles.category, switchMerchant ? {


        } : {}]}>
          {item.image ? (
            <>
              <AsyncImage
                style={{
                  width: switchMerchant
                    ? windowWidth <= 900
                      ? windowWidth * 0.048
                      : windowWidth * 0.056
                    : windowWidth * 0.09,
                  height: switchMerchant
                    ? windowWidth <= 900
                      ? windowWidth * 0.048
                      : windowWidth * 0.056
                    : windowWidth * 0.074,
                  borderRadius: 10,
                  //position: 'absolute',
                  top: 5,
                }}
                resizeMode="cover"
                source={{ uri: item.image }}
                item={item}
              />
            </>
          ) : (
            <View
              style={[{
                // backgroundColor: 'red',
                marginBottom: '0%',
                marginTop: '8%',
              }, switchMerchant ? {
                // marginTop: '5%',
                marginBottom: '0%',
              } : {}]}>
              <Ionicons name="fast-food-outline" size={switchMerchant ? 40 : 80} />
            </View>
          )}
          <View
            style={[
              {
                borderBottomColor:
                  (selectedOutletItemCategory && selectedOutletItemCategory.name == item.name)
                    ? Colors.primaryColor
                    : null,
                borderBottomWidth:
                  (selectedOutletItemCategory && selectedOutletItemCategory.name == item.name) ? switchMerchant ? 0 : 3 : 0,
              },
              !switchMerchant ? 10 : windowWidth < 1000 ?
                {
                  alignItems: 'center',
                  justifyContent: 'center',
                  //alignSelf: 'flex-start',
                  //flex: 0.24,
                  //padding: switchMerchant ? 5 : 5,
                  paddingHorizontal: 8,
                  marginTop: 5,
                  //alignItems: 'center'
                } : {},
            ]}>
            <Text
              numberOfLines={2}
              style={[{
                textTransform: 'capitalize',
                paddingVertical: 12,
                fontFamily:
                  (selectedOutletItemCategory && selectedOutletItemCategory.name == item.name)
                    ? 'NunitoSans-Bold'
                    : 'NunitoSans-Bold',
                color:
                  (selectedOutletItemCategory && selectedOutletItemCategory.name == item.name)
                    ? Colors.primaryColor
                    : Colors.mainTxtColor,
                fontSize: switchMerchant ? windowWidth / 80 : 16,
                textAlign: 'center',
                width: 100
              },
              switchMerchant ? {
                paddingBottom: 4,
              } : {},
              ]}>
              {item.name}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderMenu = ({ item }) => {
    var quantity = 0;
    //const cartItem = cartItem.find(obj => obj.itemId === item.id);

    // const itemsInCart = cartItems.filter(
    //   (obj) => item && obj.itemId === item.uniqueId,
    // );
    // if (itemsInCart) {
    //   for (const obj of itemsInCart) {
    //     quantity += parseInt(obj.quantity);
    //   }
    // }

    // quantity = cartItems.reduce((accum, cartItem) => {
    //   accum + (
    //     cartItem.itemId === item.uniqueId
    //       ?
    //       parseInt(cartItem.quantity)
    //       :
    //       0
    //   )
    // }, 0);

    var overrideCategoryPrice = undefined;

    if (
      selectedOutletItemCategoriesDict[item.categoryId] &&
      overrideCategoryPriceNameDict[
      selectedOutletItemCategoriesDict[item.categoryId].name
      ] !== undefined
    ) {
      overrideCategoryPrice =
        overrideCategoryPriceNameDict[
          selectedOutletItemCategoriesDict[item.categoryId].name
        ].overridePrice;
    }

    var amountOffCategory = undefined;
    if (
      selectedOutletItemCategoriesDict[item.categoryId] &&
      amountOffCategoryNameDict[
      selectedOutletItemCategoriesDict[item.categoryId].name
      ] !== undefined
    ) {
      amountOffCategory =
        amountOffCategoryNameDict[
        selectedOutletItemCategoriesDict[item.categoryId].name
        ];
    }

    var percentageOffCategory = undefined;
    if (
      selectedOutletItemCategoriesDict[item.categoryId] &&
      percentageOffCategoryNameDict[
      selectedOutletItemCategoriesDict[item.categoryId].name
      ] !== undefined
    ) {
      percentageOffCategory =
        percentageOffCategoryNameDict[
        selectedOutletItemCategoriesDict[item.categoryId].name
        ];
    }

    // var pointsRedeemCategory = undefined;
    // if (selectedOutletItemCategoriesDict[item.categoryId] && pointsRedeemCategoryNameDict[selectedOutletItemCategoriesDict[item.categoryId].name] !== undefined) {
    //   pointsRedeemCategory = pointsRedeemCategoryNameDict[selectedOutletItemCategoriesDict[item.categoryId].name];
    // }

    var buy1Free1Category = undefined;
    if (
      selectedOutletItemCategoriesDict[item.categoryId] &&
      buy1Free1CategoryNameDict[
      selectedOutletItemCategoriesDict[item.categoryId].name
      ] !== undefined
    ) {
      buy1Free1Category =
        buy1Free1CategoryNameDict[
        selectedOutletItemCategoriesDict[item.categoryId].name
        ];
    }

    var extraPrice = 0;
    if (
      orderTypeMo === ORDER_TYPE.DELIVERY &&
      outletData &&
      outletData.deliveryPrice
    ) {
      extraPrice = outletData.deliveryPrice;
    } else if (
      orderTypeMo === ORDER_TYPE.PICKUP &&
      orderTypeSubMo === ORDER_TYPE_SUB.NORMAL &&
      outletData &&
      outletData.pickUpPrice
    ) {
      // extraPrice = outletData.pickUpPrice;
    }

    if (orderTypeMo === ORDER_TYPE.DELIVERY && item.deliveryChargesActive) {
      extraPrice = item.deliveryCharges || 0;

      if (
        extraPrice &&
        item.deliveryChargesType === CHARGES_TYPE.PERCENTAGE_BASED
      ) {
        extraPrice = (item.price * extraPrice) / 100;
      }
    }
    // else {
    //   extraPrice = 0;
    // }

    if (orderTypeMo === ORDER_TYPE.PICKUP && orderTypeSubMo === ORDER_TYPE_SUB.NORMAL && item.pickUpChargesActive) {
      extraPrice = item.pickUpCharges || 0;

      if (
        extraPrice &&
        item.pickUpChargesType === CHARGES_TYPE.PERCENTAGE_BASED
      ) {
        extraPrice = (item.price * extraPrice) / 100;
      }
    }
    // else {
    //   extraPrice = 0;
    // }

    if (orderTypeMo === ORDER_TYPE.PICKUP && orderTypeSubMo === ORDER_TYPE_SUB.OTHER_DELIVERY && item.otherDChargesActive) {
      extraPrice = item.otherDCharges || 0;

      if (
        extraPrice &&
        item.otherDChargesType === CHARGES_TYPE.PERCENTAGE_BASED
      ) {
        extraPrice = (item.price * extraPrice) / 100;
      }
    }

    // console.log('ITEM', item);

    // if (item.name === 'Burger') {
    //   console.log('break');

    //   console.log(item.isActive)
    //   console.log(item.isAvailableDayActive)
    //   console.log(item.effectiveTypeOptions.includes(effectiveDays))

    //   console.log('===========');

    //   console.log(moment(item.effectiveStartTime)
    //   .year(moment().year())
    //   .month(moment().month())
    //   .date(moment().date()).format('YYYY MM DD hh:mm A'))

    //   console.log(moment().isSameOrAfter(
    //     moment(item.effectiveStartTime)
    //     .year(moment().year())
    //     .month(moment().month())
    //     .date(moment().date())
    //     ) );

    //     console.log(        (moment(item.effectiveEndTime)
    //     .year(moment().year())
    //     .month(moment().month())
    //     .date(moment().date())
    //     ).format('YYYY MM DD hh:mm A'))

    //     console.log(moment().isBefore
    //     (moment(item.effectiveEndTime)
    //     .year(moment().year())
    //     .month(moment().month())
    //     .date(moment().date())
    //     ))
    // }


    return (
      <TouchableOpacity

        onPress={async () => {
          // for short press

          console.log('pressed item!');

          if (
            item.isActive &&
            (item.isAvailableDayActive
              ? (item.effectiveTypeOptions.includes(effectiveDays) &&
                item.effectiveStartTime && item.effectiveEndTime &&
                moment().isSameOrAfter(
                  moment(item.effectiveStartTime)
                    .year(moment().year())
                    .month(moment().month())
                    .date(moment().date())
                )
                &&
                moment().isBefore
                  (moment(item.effectiveEndTime)
                    .year(moment().year())
                    .month(moment().month())
                    .date(moment().date())
                  ))
              : true)
          ) {
            CommonStore.update((s) => {
              s.selectedOutletItem = item;
              s.selectedOutletItemAddOn = {};
              s.selectedOutletItemAddOnChoice = {};

              s.isOnMenu = false;
            });

            setRouteParamsMenuItemDetails({
              refresh: refresh.bind(this),
              menuItem: item,
              outletData,
              orderTypeMo,
            });
          }
        }}
      >
        <View
          style={[{
            //backgroundColor: '#717378',
            backgroundColor: '#fafafa',

            width: switchMerchant
              ? windowWidth * 0.14
              : windowWidth <= 1823 && windowWidth >= 1820
                ? windowWidth * 0.1
                : windowWidth * 0.1,

            height: windowHeight * 0.247,

            //height: switchMerchant ? windowWidth * 0.19 : '50%',

            borderRadius: 3,
            //marginLeft: 30,
            //marginTop: 10,
            margin: 2,
            zIndex: switchMerchant ? -1000 : -1,
            alignSelf: 'center',
            justifyContent: 'flex-start',
            alignContent: 'center',
            alignItems: 'center',
            flexDirection: 'column',
            flex: 1,
            shadowColor: '#000',
            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowOpacity: 0.22,
            shadowRadius: 3.22,
            elevation: 1,
          },
          !switchMerchant && windowWidth < 1000 ? {
            height: windowHeight * 0.3,
          } : {},
          ]}>
          {/* <View
            style={{
              flexDirection: 'row',
              alignContent: 'center',
              alignItems: 'center',
              //width: '25%',
              display: 'flex',
              //justifyContent: 'flex-start',
              // backgroundColor: 'blue',
              // ...(buy1Free1ItemSkuDict[item.sku] !== undefined ||
              //   buy1Free1Category !== undefined) && {
              //     marginTop: 5,
              //     marginBottom: 20,
              // },
            }}> */}
          {/* <View
              style={[
                {
                  backgroundColor: Colors.secondaryColor,
                  // width: 60,
                  // height: 60,
                  width: switchMerchant
                    ? windowWidth * 0.08
                    : windowWidth * 0.11,
                  height: switchMerchant
                    ? windowWidth * 0.08
                    : windowWidth * 0.11,
                  borderRadius: 10,
                },
                item.image
                  ? {}
                  : {
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  },
              ]}> */}

          {item.image ? (
            //with image
            <>
              <View style={{
                marginBottom: 5
              }}>
                <AsyncImage
                  style={{
                    width: switchMerchant
                      ? windowWidth <= 900
                        ? windowWidth * 0.048
                        : windowWidth * 0.056
                      : windowWidth * 0.09,
                    height: switchMerchant
                      ? windowWidth <= 900
                        ? windowWidth * 0.048
                        : windowWidth * 0.056
                      : windowWidth * 0.074,
                    borderRadius: 10,
                    //position: 'absolute',
                    top: 5,
                  }}
                  resizeMode="cover"
                  source={{ uri: item.image }}
                  item={item}
                />
              </View>
              {!item.isActive ||
                !(item.isAvailableDayActive
                  ? (item.effectiveTypeOptions.includes(effectiveDays) &&
                    item.effectiveStartTime && item.effectiveEndTime &&
                    moment().isSameOrAfter(
                      moment(item.effectiveStartTime)
                        .year(moment().year())
                        .month(moment().month())
                        .date(moment().date())
                    )
                    &&
                    moment().isBefore
                      (moment(item.effectiveEndTime)
                        .year(moment().year())
                        .month(moment().month())
                        .date(moment().date())
                      )
                  )
                  : true) ? (
                <View
                  style={{
                    position: 'absolute',
                    zIndex: 3,
                  }}>
                  <View
                    style={[{
                      // width: 120,
                      width: switchMerchant
                        ? windowWidth * 0.14
                        : windowWidth <= 1823 && windowWidth >= 1820
                          ? windowWidth * 0.1
                          : windowWidth * 0.1,
                      //left: -windowWidth * 0.015,
                      padding: 0,
                      //paddingLeft: windowWidth * 0.01,
                      justifyContent: 'center',
                      alignItems: 'center',
                      backgroundColor: Colors.tabRed,
                      height: 25,
                      borderTopRightRadius: 3,
                      borderTopLeftRadius: 3,
                      // borderBottomRightRadius: 3,

                      ...(!item.image && {
                        left: -windowWidth * -0.0001,
                        top: -windowWidth * -0.001,
                        bottom: '112%',
                      }),
                    }, switchMerchant ? {
                      height: 20,
                    } : {}]}>
                    <Text
                      style={{
                        color: '#FFF',
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? windowWidth / 60 : 12,
                        bottom: 1,
                      }}>
                      Not available
                    </Text>
                  </View>

                </View>
              ) : (
                <></>
              )}

              <View
                style={{
                  // width: '100%',
                  // height: switchMerchant ? windowWidth * 0.07 : windowWidth * 0.065,
                  height: windowHeight * 0.06,
                  alignItems: 'center',
                  justifyContent: 'flex-start',
                  //alignSelf: 'flex-start',
                  //flex: 0.24,
                  padding: 2,
                  paddingHorizontal: 8,
                  marginTop: 5,
                  //alignItems: 'center'
                }}>

                <Text
                  style={[{
                    textTransform: 'capitalize',
                    paddingVertical: 5,
                    paddingBottom: 0,

                    fontFamily: 'NunitoSans-Bold',
                    color: Colors.mainTxtColor,
                    fontSize: switchMerchant ? windowWidth / 70 : 13,
                    textAlign: 'center',
                    // verticalAlign: 'top',
                    // textAlignVertical: 'top',
                    // width: 100,
                    width: windowWidth * 0.08,
                    // backgroundColor: 'red',
                  },
                  switchMerchant ? {

                  } : {},
                  ]}

                  numberOfLines={2}
                >
                  {item.name}
                  {/* {item.name} */}
                </Text>
              </View>

              <View
                style={[
                  styles.content4,
                  {
                    //flex: 0.1,
                    width: switchMerchant ? 80 : 120,
                    //backgroundColor: Colors.highlightColor,
                    justifyContent: 'center',

                  },
                ]}>
                <Text
                  style={{
                    color:
                      overrideItemPriceSkuDict[item.sku] !== undefined ||
                        overrideCategoryPrice !== undefined
                        ? Colors.secondaryColor
                        : Colors.primaryColor,

                    fontFamily: 'NunitoSans-Bold',
                    //paddingTop: 5,
                    // fontSize: 14,
                    fontSize: switchMerchant ? windowWidth / 60 : 16,
                    textDecorationLine:
                      overrideItemPriceSkuDict[item.sku] !== undefined ||
                        overrideCategoryPrice !== undefined
                        ? 'line-through'
                        : 'none',
                  }}>
                  {/*edit here for menu items this is the main one*/}
                  RM{' '}
                  {parseFloat(extraPrice + item.price)
                    .toFixed(2)
                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[item.unitType]}` : ''}
                </Text>

                {overrideItemPriceSkuDict[item.sku] !== undefined ||
                  overrideCategoryPrice !== undefined ? (
                  overrideItemPriceSkuDict[item.sku] !== undefined ? (
                    <Text
                      style={{
                        //color: Colors.secondaryColor,
                        color: Colors.primaryColor,
                        fontFamily: 'NunitoSans-Bold',
                        paddingTop: 0,
                        marginTop: -5,
                        fontSize: switchMerchant ? 10 : 16,
                        marginLeft: 5,
                      }}>
                      RM{' '}
                      {parseFloat(
                        overrideItemPriceSkuDict[item.sku].overridePrice,
                      ).toFixed(2)}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[item.unitType]}` : ''}
                    </Text>
                  ) : (
                    <Text
                      style={{
                        //color: Colors.secondaryColor,
                        color: Colors.primaryColor,
                        fontFamily: 'NunitoSans-Bold',
                        paddingTop: 0,
                        marginTop: -5,
                        fontSize: switchMerchant ? 10 : 16,
                        marginLeft: 5,
                      }}>
                      RM{' '}{parseFloat(overrideCategoryPrice).toFixed(2)}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[item.unitType]}` : ''}
                    </Text>
                  )
                ) : (
                  <></>
                )}
              </View>
            </>
          ) : (

            //split point 
            //without image
            <>
              <View style={{
                marginBottom: 5
              }}>
                <View
                  style={{
                    //backgroundColor: 'red',
                    justifyContent: 'center',
                    width: switchMerchant
                      ? windowWidth <= 900
                        ? windowWidth * 0.048
                        : windowWidth * 0.056
                      : windowWidth * 0.09,
                    height: switchMerchant
                      ? windowWidth <= 900
                        ? windowWidth * 0.048
                        : windowWidth * 0.056
                      : windowWidth * 0.074,
                    alignItem: 'center',
                    alignSelf: 'center',
                  }}>
                  <Ionicons
                    name="fast-food-outline"
                    size={switchMerchant ? 40 : 80}
                    style={[{ alignSelf: 'center', }, switchMerchant ? {
                      marginBottom: '-46%',
                    } : {}]}
                  />
                </View>
              </View>

              {!item.isActive ||
                !(item.isAvailableDayActive
                  ? (item.effectiveTypeOptions.includes(effectiveDays) &&
                    item.effectiveStartTime && item.effectiveEndTime &&
                    moment().isSameOrAfter(
                      moment(item.effectiveStartTime)
                        .year(moment().year())
                        .month(moment().month())
                        .date(moment().date())
                    )
                    &&
                    moment().isBefore
                      (moment(item.effectiveEndTime)
                        .year(moment().year())
                        .month(moment().month())
                        .date(moment().date())
                      )
                  )
                  : true) ? (
                <View
                  style={{
                    position: 'absolute',
                    zIndex: 3,
                  }}>
                  <View
                    style={[{
                      // width: 120,
                      width: switchMerchant
                        ? windowWidth * 0.14
                        : windowWidth <= 1823 && windowWidth >= 1820
                          ? windowWidth * 0.1
                          : windowWidth * 0.1,
                      //left: -windowWidth * 0.015,
                      padding: 0,
                      //paddingLeft: windowWidth * 0.01,
                      justifyContent: 'center',
                      alignItems: 'center',
                      backgroundColor: Colors.tabRed,
                      height: 25,
                      borderTopRightRadius: 3,
                      borderTopLeftRadius: 3,
                      // borderBottomRightRadius: 3,

                      ...(!item.image && {
                        left: -windowWidth * -0.0001,
                        top: -windowWidth * -0.001,
                        bottom: '112%',
                      }),
                    }, switchMerchant ? {
                      height: 20,
                    } : {}]}>
                    <Text
                      style={{
                        color: '#FFF',
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? windowWidth / 60 : 12,
                        bottom: 1,
                      }}>
                      Not available
                    </Text>
                  </View>

                </View>
              ) : (
                <></>
              )}

              <View
                style={{
                  // width: '100%',
                  // height: switchMerchant ? windowWidth * 0.07 : windowWidth * 0.065,
                  height: windowHeight * 0.06,
                  alignItems: 'center',
                  justifyContent: 'flex-start',
                  //alignSelf: 'flex-start',
                  //flex: 0.24,
                  padding: 2,
                  paddingHorizontal: 8,
                  marginTop: 5,
                  //alignItems: 'center'
                }}>

                <Text
                  style={[{
                    textTransform: 'capitalize',
                    paddingVertical: 5,
                    paddingBottom: 0,

                    fontFamily: 'NunitoSans-Bold',
                    color: Colors.mainTxtColor,
                    fontSize: switchMerchant ? windowWidth / 70 : 13,
                    textAlign: 'center',
                    // verticalAlign: 'top',
                    // textAlignVertical: 'top',
                    // width: 100,
                    width: windowWidth * 0.08,
                    // backgroundColor: 'red',
                  },
                  switchMerchant ? {

                  } : {},
                  ]}

                  numberOfLines={2}
                >
                  {item.name}
                  {/* {item.name} */}
                </Text>
              </View>

              <View
                style={[
                  styles.content4,
                  {
                    //flex: 0.1,
                    width: switchMerchant ? 80 : 120,
                    //backgroundColor: Colors.highlightColor,
                    justifyContent: 'center',

                  },
                ]}>
                <Text
                  style={{
                    color:
                      overrideItemPriceSkuDict[item.sku] !== undefined ||
                        overrideCategoryPrice !== undefined
                        ? Colors.secondaryColor
                        : Colors.primaryColor,

                    fontFamily: 'NunitoSans-Bold',
                    //paddingTop: 5,
                    // fontSize: 14,
                    fontSize: switchMerchant ? windowWidth / 60 : 16,
                    textDecorationLine:
                      overrideItemPriceSkuDict[item.sku] !== undefined ||
                        overrideCategoryPrice !== undefined
                        ? 'line-through'
                        : 'none',
                  }}>
                  {/*edit here for menu items this is the main one*/}
                  RM{' '}
                  {parseFloat(extraPrice + item.price)
                    .toFixed(2)
                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[item.unitType]}` : ''}
                </Text>

                {overrideItemPriceSkuDict[item.sku] !== undefined ||
                  overrideCategoryPrice !== undefined ? (
                  overrideItemPriceSkuDict[item.sku] !== undefined ? (
                    <Text
                      style={{
                        //color: Colors.secondaryColor,
                        color: Colors.primaryColor,
                        fontFamily: 'NunitoSans-Bold',
                        paddingTop: 0,
                        marginTop: -5,
                        fontSize: switchMerchant ? 10 : 16,
                        marginLeft: 5,
                      }}>
                      RM{' '}
                      {parseFloat(
                        overrideItemPriceSkuDict[item.sku].overridePrice,
                      ).toFixed(2)}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[item.unitType]}` : ''}
                    </Text>
                  ) : (
                    <Text
                      style={{
                        //color: Colors.secondaryColor,
                        color: Colors.primaryColor,
                        fontFamily: 'NunitoSans-Bold',
                        paddingTop: 0,
                        marginTop: -5,
                        fontSize: switchMerchant ? 10 : 16,
                        marginLeft: 5,
                      }}>
                      RM{' '}{parseFloat(overrideCategoryPrice).toFixed(2)}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? `/${UNIT_TYPE_SHORT[item.unitType]}` : ''}
                    </Text>
                  )
                ) : (
                  <></>
                )}
              </View>
            </>
          )}



        </View>
        {/* </View> */}
      </TouchableOpacity>
    );
  };

  const occupyingSeats = async (seatedPax) => {
    await new Promise((resolve, reject) => {
      var moPaxParsed = !isNaN(parseInt(moPax)) ? parseInt(moPax) : 0;

      if (!selectedOutletTableMo || !moPaxParsed) {
        alert('Error\n\nMinimum pax must be 1');
        resolve();
      } else {
        var body = {
          tableId: selectedOutletTableMo.uniqueId,
          pax: moPaxParsed,
          outletId: currOutletId,
        };

        // ApiClient.POST(API.addCustomer, body, false)
        APILocal.addCustomer({ body }).then((result) => {
          // if (result.outletId == User.getOutletId()) {
          //     getTableBySection(currentSectionArea)
          //     setState({ seatingModal: false })
          //     Alert.alert("Table seated!")
          // }

          if (result && result.status === 'success') {
            //setSeatingModal(false);
            // Alert.alert('Success', 'Table has been seated');

            CommonStore.update((s) => {
              s.selectedOutletTableMo = {
                ...selectedOutletTableMo,
                seated: moPaxParsed,
              };
            });
          }

          resolve();
        });
      }
    });
  };

  // onContainerScroll = e => {
  //   // console.log(windowWidth);
  //   // console.log(e.nativeEvent.contentOffset.y);
  //   // console.log('---------------------------')

  //   if (e.nativeEvent.contentOffset.y * 2 >= windowWidth) {
  //     // console.log('hit top');

  //     // this.setState({
  //     //   isInfoTabHitTop: true,
  //     // });
  //   }
  //   else {
  //     // console.log('not hit top');

  //     // this.setState({
  //     //   isInfoTabHitTop: false,
  //     // });
  //   }
  // }

  const isCloseToBottom = ({ layoutMeasurement, contentOffset, contentSize }) => {
    const paddingToBottom = 20;
    return (
      layoutMeasurement.height + contentOffset.y >=
      contentSize.height - paddingToBottom
    );
  };

  const nextCategory = () => {
    // const catLength = categoryOutlet.length;
    // if (categoryIndex == catLength - 1) {
    //   setState({
    //     category: categoryOutlet[0],
    //     categoryIndex: 0,
    //     // menu: choice[index].category,
    //   });
    // } else {
    //   setState({
    //     category: categoryOutlet[categoryIndex + 1],
    //     categoryIndex: categoryIndex + 1,
    //     // menu: choice[index].category,
    //   });
    // }
    // refreshMenu();
  };

  const checkCartOutlet = () => {
    const outletId = outletData ? outletData.uniqueId : '';
    // console.log(Cart.getOutletId() != null);
    if (outletId != Cart.getOutletId() && Cart.getOutletId() != null) {
      return true;
    }
    return false;
  };
  // function end

  return (
    // <UserIdleWrapper disabled={!isMounted} screenName={'MoOutletMenuScreen'}>
    <View
      style={[
        styles.container,
        {
          // width: windowWidth,
          // height: windowHeight,
        }
        // !isTablet()
        //   ? {
        //     transform: [{ scaleX: 1 }, { scaleY: 1 }],
        //   }
        //   : {},
        // {
        //   ...getTransformForScreenInsideNavigation(),
        // }
      ]}>
      {/* <View
        style={[
          styles.sidebar,
          !isTablet()
            ? {
              width: windowWidth * 0.08,
            }
            : {},
          switchMerchant
            ? {
              // width: '10%'
            }
            : {},
        ]}>
        <SideBar
          navigation={props.navigation}
          selectedTab={1}
          expandOperation={true}
        />
      </View> */}

      <Modal
        supportedOrientations={['landscape', 'portrait']}
        style={{ flex: 1 }}
        visible={cartWarning}
        transparent
        animationType="slide">
        <View
          style={{
            backgroundColor: 'rgba(0,0,0,0.5)',
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: windowHeight,
          }}>
          <View style={styles.confirmBox}>
            <TouchableOpacity
              onPress={() => {
                setState({ cartWarning: false });
              }}>
              <View
                style={{
                  alignSelf: 'flex-start',
                  padding: 14,
                }}>
                {/* <Close name="close" size={25} color={'#b0b0b0'} /> */}
                <AntDesign
                  name="closecircle"
                  size={25}
                  color={Colors.fieldtTxtColor}
                />
              </View>
            </TouchableOpacity>
            <View style={{ marginBottom: 10 }}>
              <Text
                style={{
                  textAlign: 'center',
                  fontWeight: '700',
                  fontSize: switchMerchant ? 10 : 18,
                }}>
                You are entering a different outlet
              </Text>
              <Text
                style={{
                  textAlign: 'center',
                  fontWeight: '700',
                  fontSize: switchMerchant ? 10 : 14,
                }}>
                Your existing cart items will be cleared if you proceed. Are you
                sure?
              </Text>
            </View>
            <View
              style={{
                alignSelf: 'center',
                marginTop: 30,
                justifyContent: 'center',
                alignItems: 'center',
                width: 250,
                height: 40,
                alignContent: 'center',
                marginTop: 40,
              }}>
              <TouchableOpacity
                style={{
                  backgroundColor: Colors.primaryColor,
                  width: '100%',
                  justifyContent: 'center',
                  alignItems: 'center',
                  alignContent: 'center',
                  borderRadius: 10,
                  height: 60,
                  marginTop: 30,
                  alignSelf: 'center',
                }}
                onPress={() => {
                  setState({ cartWarning: false });
                  Cart.clearCart();

                  // CommonStore.update(s => {
                  //   s.selectedOutletItem = item;
                  // });

                  setRouteParamsMenuItemDetails({
                    refresh: refresh.bind(this),
                    menuItem: cartProceed,
                    outletData,
                    orderTypeMo,
                  });
                  CommonStore.update((s) => {
                    s.isOnMenu = false;
                  });
                }}>
                <Text
                  style={{
                    fontSize: switchMerchant ? 10 : 28,
                    color: Colors.whiteColor,
                  }}>
                  Proceed
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  setState({ visible: false });
                  setState({ cartWarning: false });
                }}
                style={{
                  backgroundColor: Colors.secondaryColor,
                  width: '100%',
                  justifyContent: 'center',
                  alignItems: 'center',
                  alignContent: 'center',
                  borderRadius: 10,
                  height: 60,
                  marginTop: 20,
                }}>
                <Text
                  style={{
                    fontSize: switchMerchant ? 10 : 28,
                    color: Colors.whiteColor,
                  }}>
                  Take me back
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* <ScrollView showsHorizontalScrollIndicator={false}
         //onScroll={onContainerScroll}
        onScroll={({ nativeEvent }) => {
          if (isCloseToBottom(nativeEvent)) {
            //// console.log("HIT BOTTOM")
            //nextCategory()
          }
        }}
        stickyHeaderIndices={[1]}
      > */}

      <View
        style={[
          switchMerchant
            ? {
              width: '100%',
            }
            : {
              width: '100%',
            },
        ]}>
        {/* <Image
          source={{ uri: outletData.cover }}
          style={styles.outletCover}
          resizeMode={'cover'}
        /> */}
        {/* <View
            style={[
              {
                width: '100%',
                // height: 60,
                paddingVertical: 13,
                backgroundColor: '#ddebe5',
                justifyContent: 'center',
                paddingHorizontal: 25,
                marginTop: 0,

                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 1,
              },
              switchMerchant ? {
                paddingVertical: 6,
              } : {},
            ]}>
            <Text
              style={{
                color: Colors.primaryColor,
                //marginLeft: 4,
                fontSize: switchMerchant ? 20 : 19,
                fontFamily: 'NunitoSans-SemiBold',
                alignSelf: 'center',
              }}>
              Menus
            </Text>
          </View> */}

        {/* <View style={{
            marginTop: 10,
            marginBottom: -10,
          }}>
            <TouchableOpacity 
            style={{
              alignItems: 'center',
              alignSelf: 'flex-end',
              marginRight: 15,
              width: 30,
              backgroundColor: 'white',
              borderRadius: 5,
              padding: 5,
  
              shadowOpacity: 0,
              shadowColor: '#000',
              shadowOffset: {
                width: 1,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 3,
            }}
            onPress={() => { setSearchModal(true)}}>
              <Feather
                name="search"
                size={switchMerchant ? 13 : 18}
                color={Colors.primaryColor}
                style={{  }}
              />
            </TouchableOpacity>
          </View> */}

        <View style={{ flexDirection: 'row' }}>
          {!isOnCategory ? (
            <TouchableOpacity
              // style={{
              //   flexDirection: 'row',
              //   marginLeft: Platform.OS == 'android' ? 6 : 10,
              //   //paddingBottom: '5%',
              //   //backgroundColor: 'blue',
              //   width: '20%',
              //   marginRight: -100,
              // }}
              onPress={() => {
                requestAnimationFrame(() => {
                  CommonStore.update((s) => {
                    s.isOnMenu = true;
                    s.isOnCategory = true;
                    s.moSearch = '';
                    // setSearch('');
                  });
                });
              }}>
              <View
              // style={{
              //   flexDirection: 'row',
              //   alignItems: 'center',
              //   justifyContent: 'center',
              //   alignContent: 'center',
              //   marginBottom: 25,
              // }}
              >

                {/* <Text
                    style={{
                      color: Colors.primaryColor,
                      fontSize: switchMerchant ? 14 : 17,
                      textAlign: 'center',
                      fontFamily: 'NunitoSans-Bold',
                      marginBottom: Platform.OS === 'ios' ? 0 : 2,
                      //top: -2,
                      //marginLeft: -3,
                    }}>
                    Menu
                  </Text> */}
              </View>
            </TouchableOpacity>
          ) : (
            <View
            // style={{
            //   flexDirection: 'row',
            //   marginLeft: Platform.OS == 'android' ? 6 : 10,
            //   //paddingBottom: '5%',
            //   //backgroundColor: 'blue',
            //   width: '20%',
            //   marginRight: -100,
            // }}
            />

          )}



        </View>

        {/* Checkpoint (JJ's comment) */}
        {isOnCategory ? (
          <View
            style={{
              justifyContent: 'center',

              // height: switchMerchant ? (windowHeight * 0.6) : windowHeight < 670 ? (windowHeight * 0.9) : (windowHeight * 0.85),
              height: windowHeight * 0.9,

              ...(switchMerchant && windowWidth < 775 && windowHeight < 412 && {
                height: windowHeight * 0.9
              })


            }}>
            {/* Data search here */}
            <FlatList
              showsVerticalScrollIndicator={false}
              data={
                !search || search === '' ? (
                  filteredOutletCategories
                ) : (
                  outletItemsDisplay.filter((item) => {
                    return item.name
                    // .toLowerCase()
                    // .includes(search.toLowerCase())
                  })
                )
              }

              key={props.listKey}
              renderItem={renderOutletCategory}
              numColumns={props.numColumns}
              contentContainerStyle={{
                paddingLeft: 5,
                paddingRight: 10,
                //width: windowWidth * 0.9,
                justifyContent: 'center',

                paddingBottom: 20,
                // backgroundColor: 'red',

                // height: windowHeight * 1,
                // overflow: 'hidden',
              }}
              style={{
                // height: windowHeight * 0.1,
                // overflow: 'hidden',
              }}
            />
          </View>
        ) : (
          <>
            <View style={{
              justifyContent: 'center',
              // alignContent: 'center',
              // height: switchMerchant ? (windowHeight * 0.6) : windowHeight < 670 ? (windowHeight * 0.5) : (windowHeight * 0.72),
              height: windowHeight * 0.9,

              ...(switchMerchant && windowWidth < 775 && windowHeight < 412 && {
                height: windowHeight * 0.44
              })
            }}>
              <FlatList
                showsVerticalScrollIndicator={false}
                data={outletItemsDisplay.filter((item) => {
                  if (search && search.trim() !== '') {
                    return item.name
                      .toLowerCase()
                      .includes(search.toString().toLowerCase())
                  } else {
                    return true;
                  }
                })}
                key={props.listKey}
                renderItem={renderMenu}
                numColumns={props.numColumns}
                keyExtractor={(item, index) => index}
                contentContainerStyle={{
                  paddingLeft: 5,
                  paddingRight: 10,
                  // paddingTop: 5,
                  //width: windowWidth * 0.9,
                  justifyContent: 'center',
                  paddingBottom: 20,

                  // height: switchMerchant ? (windowHeight * 0.48) : (windowHeight * 0.7),
                }}
              />
            </View>
            {/* </View> */}
          </>
        )}
      </View>
      {/* <View style={{ minHeight: 100 }} /> */}
      {/* </ScrollView>  */}

      {/* {cartIcon ? (
        <Draggable
          shouldReverse={reverse}
          renderSize={100}
          renderColor={Colors.secondaryColor}
          isCircle
          x={switchMerchant ? 100 : windowWidth * 0.8}
          y={switchMerchant ? 80 : windowHeight * 0.6}
          // onShortPressRelease={() => { goToCart(), cartCount() }}
          onShortPressRelease={onCartClicked}
          // onPressOut={onCartClicked}
          onRelease={onCartClicked}>
          <View
            style={{
              width: switchMerchant ? 30 : 60,
              height: switchMerchant ? 30 : 60,
              justifyContent: 'center',
            }}>
            <View style={{ alignSelf: 'center' }}>
              {switchMerchant ? (
                <Ionicons
                  name="cart-outline"
                  size={18}
                  color={Colors.mainTxtColor}
                />
              ) : (
                <Ionicons
                  name="cart-outline"
                  size={42}
                  color={Colors.mainTxtColor}
                />
              )}
            </View>
            <View
              style={[
                styles.cartCount,
                switchMerchant ? { height: 10, width: 10 } : {},
              ]}>
              {/* <Text style={{ color: Colors.whiteColor, fontSize: 10, fontFamily: "NunitoSans-Regular" }}>{Cart.getCartItem().length}</Text> */}
      {/* <Text
                style={{
                  color: Colors.whiteColor,
                  fontSize: switchMerchant ? 7 : 14,
                  fontFamily: 'NunitoSans-Bold',
                  bottom: Platform.OS === 'android' ? 2 : 0,
                }}>
                {cartItems.length}
              </Text>
            </View>
          </View>
        </Draggable>
      ) : null} */}
    </View>
    // </UserIdleWrapper >
  );
}

const styles = StyleSheet.create({
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  confirmBox: {
    width: 350,
    height: 350,
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
  },
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    position: 'relative',
    flexDirection: 'row',
  },
  outletCover: {
    // width: '50%',
    // alignSelf: 'center',
    // height: undefined,
    // aspectRatio: 2,
    // borderRadius: 5,
    width: '100%',
    alignSelf: 'center',
    height: '30%',
    // aspectRatio: 2,
    borderRadius: 5,
  },
  infoTab: {
    backgroundColor: Colors.fieldtBgColor,
  },
  workingHourTab: {
    padding: 16,
    flexDirection: 'row',
  },
  outletAddress: {
    textAlign: 'center',
    color: Colors.mainTxtColor,
  },
  outletName: {
    fontWeight: 'bold',
    fontSize: 20,
    marginBottom: 10,
  },
  logo: {
    width: 100,
    height: 100,
  },
  actionTab: {
    flexDirection: 'row',
    marginTop: 20,
  },
  actionView: {
    width: Dimensions.get('window').width / 4,
    height: Dimensions.get('window').width / 4,
    justifyContent: 'center',
    alignItems: 'center',
    alignContent: 'center',
  },
  actionBtn: {
    borderRadius: 50,
    width: 70,
    height: 70,
    borderColor: Colors.secondaryColor,
    borderWidth: StyleSheet.hairlineWidth,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionText: {
    fontSize: 12,
    marginTop: 10,
  },
  category: {
    width: 190,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  floatCartBtn: {
    zIndex: 2,
    position: 'absolute',
    bottom: 30,
    right: 30,
    width: 60,
    height: 60,
    borderRadius: 60 / 2,
    backgroundColor: Colors.secondaryColor,
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  cartCount: {
    position: 'absolute',
    top: -1,
    right: -2,
    backgroundColor: Colors.primaryColor,
    width: 20,
    height: 20,
    borderRadius: 20 / 2,
    alignContent: 'center',
    alignItems: 'center',
    justifyContent: 'center',
  },
  content4: {
    //backgroundColor: Colors.whiteColor,

    height: 25,
    //borderRadius: 20,
    alignSelf: 'center',
    alignItems: 'center',

  },
  modalView: {
    height: 200,
    width: 300,
    backgroundColor: Colors.whiteColor,
    borderRadius: Dimensions.get('window').width * 0.03,
    padding: 12,
    paddingTop: 25,
    //paddingBottom: 30,
  },
  closeButton: {
    position: 'relative',
    alignSelf: 'flex-end',
    marginRight: 10,
    marginTop: 10,

    elevation: 1000,
    zIndex: 1000,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
export default MoOutletMenuScreen;
