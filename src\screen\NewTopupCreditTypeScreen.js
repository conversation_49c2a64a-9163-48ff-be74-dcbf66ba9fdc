import React, { useState, useEffect, useCallback } from "react";
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  Alert,
  TouchableOpacity,
  Modal,
  Dimensions,
  TextInput,
  FlatList,
  KeyboardAvoidingView,
  ActivityIndicator,
  useWindowDimensions,
} from "react-native";
//import CheckBox from 'react-native-check-box';
import Colors from "../constant/Colors";
import Styles from "../constant/Styles";
import SideBar from "./SideBar";
import Icon from "react-native-vector-icons/Ionicons";
import Icon1 from "react-native-vector-icons/Feather";
import Ionicon from "react-native-vector-icons/Ionicons";
//import DropDownPicker from 'react-native-dropdown-picker';
import Feather from "react-native-vector-icons/Feather";
import moment from "moment";
import DateTimePickerModal from "react-native-modal-datetime-picker";
//import { isTablet } from 'react-native-device-detection';
import {
  MERCHANT_VOUCHER_CODE_FORMAT,
  ORDER_TYPE_DROP_DOWN_LIST,
  EXPAND_TAB_TYPE,
} from "../constant/common";
import { CommonStore } from "../store/commonStore";
import { UserStore } from "../store/userStore";
import { MerchantStore } from "../store/merchantStore";
import { OutletStore } from "../store/outletStore";
//import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
//import { useKeyboard } from '../hooks';
//import { launchImageLibrary } from 'react-native-image-picker';
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import {
  areArraysEqual,
  uploadImageToFirebaseStorage,
  parseValidPriceText,
  sliceUnicodeStringV2WithDots,
  uploadImageToFirebaseStorage64,
  _base64ToArrayBuffer,
  getTransformForScreenInsideNavigation,
} from "../util/common";
import AsyncImage from "../components/asyncImage";
import "react-native-get-random-values";
import { v4 as uuidv4 } from "uuid";
import AntDesign from "react-native-vector-icons/AntDesign";
import FontAwesome5 from "react-native-vector-icons/FontAwesome5";
import {
  LOYALTY_CAMPAIGN_DROPDOWN_LIST,
  LOYALTY_PROMOTION_TYPE,
  LOYALTY_PROMOTION_TYPE_DROPDOWN_LIST,
} from "../constant/loyalty";
import APILocal from "../util/apiLocalReplacers";
import { useFocusEffect, useLinkTo } from "@react-navigation/native";
import { useFilePicker } from "use-file-picker";
import { prefix } from "../constant/env";
import { ReactComponent as Edit } from "../assets/svg/Edit.svg";
import personicon from "../assets/image/default-profile.png";
import headerLogo from "../assets/image/logo.png";
import MultiSelect from "react-multiple-select-dropdown-lite";
import "../constant/styles.css";
import Select from "react-select";
//import UserIdleWrapper from '../components/userIdleWrapper';

//////////////////////////////////////////////////////////////////////////////////////////////////////////

const NewTopupCreditTypeScreen = (props) => {
  const { navigation } = props;

  const linkTo = useLinkTo();
  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  //const [keyboardHeight] = useKeyboard();

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [creditTypeName, setCreditTypeName] = useState("");
  const [creditTypePrice, setCreditTypePrice] = useState("");
  const [creditTypeValue, setCreditTypeValue] = useState("");

  const [switchMerchant, setSwitchMerchant] = useState(false);

  const [image, setImage] = useState("");
  const [imageType, setImageType] = useState("");
  const [isImageChanged, setIsImageChanged] = useState(false);

  ///////////////////////////////////////////////////////////////////////////////////////////////////////////

  const userId = UserStore.useState((s) => s.firebaseUid);
  const userName = UserStore.useState((s) => s.name);
  const merchantName = MerchantStore.useState((s) => s.name);

  ///////////////////////////////////////////////////////////////////////////////////////////

  const [outletItems, setOutletItems] = useState([]);
  const [outletCategories, setOutletCategories] = useState([]);

  const allOutlets = MerchantStore.useState((s) => s.allOutlets);
  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const currOutlet = MerchantStore.useState((s) => s.currOutlet);
  const outletItemsUnsorted = OutletStore.useState((s) => s.outletItems);
  const outletCategoriesUnsorted = OutletStore.useState(
    (s) => s.outletCategories
  );
  const outletCategoriesDict = OutletStore.useState(
    (s) => s.outletCategoriesDict
  );
  const outletsTaxDict = OutletStore.useState((s) => s.outletsTaxDict);
  const currOutletTaxes = CommonStore.useState((s) => s.currOutletTaxes);
  const isLoading = CommonStore.useState((s) => s.isLoading);

  const [loadingModal, setLoadingModal] = useState(false);

  const selectedTopupCreditTypeEdit = CommonStore.useState(
    (s) => s.selectedTopupCreditTypeEdit
  );

  const merchantId = UserStore.useState((s) => s.merchantId);

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView
  );

  const [temp, setTemp] = useState("");

  //////////////////////////////////////////////////////////////////////////////////////////

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  //////////////////////////////////////////////////////////////////////////////////////////

  const setState = () => { };

  /////////////////////////////////////////////////////////////
  // const confirmDeletePromotion = () => {
  //   return window.confirm(
  //     'Delete',
  //     'Are you sure you want to remove this promotion?',
  //     [
  //       {
  //         text: 'YES',
  //         onPress: () => {
  //           deletePromotion(item);
  //         },
  //       },
  //       {
  //         text: 'NO',
  //         onPress: () => { },
  //       },
  //     ],
  //   );
  // };

  useEffect(() => {
    if (selectedTopupCreditTypeEdit) {
      // insert info

      setCreditTypeName(selectedTopupCreditTypeEdit.name);
      setCreditTypePrice(selectedTopupCreditTypeEdit.price.toFixed(2));
      setCreditTypeValue(selectedTopupCreditTypeEdit.value.toFixed(2));
      setImage(selectedTopupCreditTypeEdit.image);
      setIsImageChanged(false);
    } else {
      // designed to always mounted, thus need clear manually...

      setCreditTypeName("");
      setCreditTypePrice("10.00");
      setCreditTypeValue("10.00");
      setImage("");
      setIsImageChanged(false);
    }
  }, [selectedTopupCreditTypeEdit]);

  /////////////////////////////////////////////////////////////

  //To remove unwanted sidebar
  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });

  const currOutletShiftStatus = OutletStore.useState(
    (s) => s.currOutletShiftStatus
  );

  // const [outletDropdownList, setOutletDropdownList] = useState([]);
  // const [selectedOutletList, setSelectedOutletList] = useState([]); // multi-outlets

  // var outletNames = [];

  // for (var i = 0; i < allOutlets.length; i++) {
  //   for (var j = 0; j < selectedOutletList.length; j++) {
  //     if (selectedOutletList.includes(allOutlets[i].uniqueId)) {
  //       outletNames.push(allOutlets[i].name);
  //       break;
  //     }
  //   }
  // }

  // useEffect(() => {
  //   setOutletDropdownList(
  //     allOutlets.map((item) => {
  //       return { label: item.name, value: item.uniqueId };
  //     })
  //   );
  // }, [allOutlets]);

  var targetOutletDropdownListTemp = allOutlets.map((outlet) => ({
    label: sliceUnicodeStringV2WithDots(outlet.name, 20),
    value: outlet.uniqueId,
  }));

  // useEffect(() => {
  //   CommonStore.update((s) => {
  //     s.outletSelectDropdownView = () => {
  //       return (
  //         <View
  //           style={{
  //             flexDirection: "row",
  //             alignItems: "center",
  //             borderRadius: 8,
  //             width: 200,
  //             backgroundColor: "white",
  //           }}
  //         >
  //           {currOutletId.length > 0 &&
  //             allOutlets.find((item) => item.uniqueId === currOutletId) ? (
  //             <MultiSelect
  //               clearable={false}
  //               singleSelect={true}
  //               defaultValue={currOutletId}
  //               placeholder={"Choose Outlet"}
  //               onChange={(value) => {
  //                 if (value) { // if choose the same option again, value = ''
  //                   MerchantStore.update((s) => {
  //                     s.currOutletId = value;
  //                     s.currOutlet =
  //                       allOutlets.find(
  //                         (outlet) => outlet.uniqueId === value
  //                       ) || {};
  //                   });
  //                 }

  //                 CommonStore.update((s) => {
  //                   s.shiftClosedModal = false;
  //                 });
  //               }}
  //               options={targetOutletDropdownListTemp}
  //               className="msl-varsHEADER"
  //             />
  //           ) : (
  //             <ActivityIndicator size={"small"} color={Colors.whiteColor} />
  //           )}
  //           {/* <Select

  //             placeholder={"Choose Outlet"}
  //             onChange={(items) => {
  //               setSelectedOutletList(items);
  //             }}
  //             options={outletDropdownList}
  //             isMulti
  //           /> */}
  //         </View>
  //       );
  //     };
  //   });
  // }, [allOutlets, currOutletId, isLoading, currOutletShiftStatus]);

  //Header
  navigation.setOptions({
    headerLeft: () => (
      <View
        style={[
          styles.headerLeftStyle,
          {
            width: windowWidth * 0.17,
          },
        ]}
      >
        <img src={headerLogo} width={124} height={26} />
        {/* <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        /> */}
      </View>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            justifyContent: "center",
            alignItems: "center",
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            //width:  "55%",
          },
          Dimensions.get("screen").width <= 768
            ? { right: Dimensions.get("screen").width * 0.12 }
            : {},
        ]}
      >
        <Text
          style={{
            fontSize: 24,
            // lineHeight: 25,
            textAlign: "center",
            fontFamily: "NunitoSans-Bold",
            color: Colors.whiteColor,
            opacity: 1,
          }}
        >
          Credit Type
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        {/* {console.log('edward test')} */}
        {/* {console.log(outletSelectDropdownView)} */}
        {outletSelectDropdownView && outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: "white",
            width: 0.5,
            height: Dimensions.get("screen").height * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
            // borderWidth: 1
          }}
        ></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate("General Settings - KooDoo BackOffice")
            }
          }}
          style={{ flexDirection: "row", alignItems: "center" }}
        >
          <Text
            style={{
              fontFamily: "NunitoSans-SemiBold",
              fontSize: 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }}
          >
            {userName}
          </Text>
          <View
            style={{
              //backgroundColor: 'red',
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "white",
            }}
          >
            <img
              src={personicon}
              width={windowHeight * 0.035}
              height={windowHeight * 0.035}
            />
            {/* <Image
              style={{
                width: windowHeight * 0.05,
              height: windowHeight * 0.05,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            /> */}
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  /////////////////////////////////////////////////

  // const handleChoosePhoto = () => {
  //   const imagePickerOptions = {
  //     mediaType: 'photo',
  //     quality: 0.5,
  //     includeBase64: false,
  //   };

  //   launchImageLibrary(imagePickerOptions, (response) => {
  //     if (response.didCancel) {
  //     } else if (response.error) {
  //      window.confirm(response.error.toString());
  //     } else {
  //       // setState({ image: response.uri });
  //       setImage(response.uri);
  //       setImageType(response.uri.slice(response.uri.lastIndexOf('.')));

  //       setIsImageChanged(true);
  //     }
  //   });
  // };

  const [
    openFileSelector,
    {
      plainFiles,
      filesContent,
      loading: loadingImageInput,
      clear: clearImageContainer,
      errors,
    },
  ] = useFilePicker({
    readAs: "DataURL",
    accept: "image/*",
    multiple: false,
  });

  useEffect(() => {
    console.log(plainFiles, filesContent, loadingImageInput);
    if (loadingImageInput) setLoadingModal(true);

    // display the image when it is finish loaded
    if (plainFiles.length && filesContent.length && !loadingImageInput) {
      // image in base64 filecontent
      setImage(filesContent[0].content);
      setImageType(
        filesContent[0].name.slice(filesContent[0].name.lastIndexOf("."))
      );
      setIsImageChanged(true);

      setLoadingModal(false);
    }

    if (errors.length) console.error(errors);
  }, [plainFiles, filesContent, loadingImageInput, errors]);

  const createCreditType = async (isAutoPush = false) => {
    var message = "";

    if (!creditTypeName) {
      message += "Name must be filled in.\n";
    }

    if (!creditTypePrice || isNaN(creditTypePrice)) {
      message += "Invalid or empty price.\n";
    }

    if (!creditTypeValue || isNaN(!creditTypeValue)) {
      message += "Invalid or empty point.\n";
    }

    if (message.length > 0) {
      window.confirm(`Info, ${message}`);

      return;
    } else {
      ////////////////////////////////////////////////////////////////

      ///////////////////////////////////
      // upload image

      var creditTypeImagePath = "";
      var creditTypeCommonIdLocal = selectedTopupCreditTypeEdit
        ? selectedTopupCreditTypeEdit.commonId
        : uuidv4();

      if (image && imageType) {
        const rawBase64 = image
          .replace("data:image/jpeg;base64,", "")
          .replace("data:image/jpg;base64,", "")
          .replace("data:image/png;base64,", "");

        const arrayBuffer = _base64ToArrayBuffer(rawBase64);

        // outletItemIdLocal = selectedProductEdit.uniqueId;
        creditTypeImagePath = await uploadImageToFirebaseStorage64(
          {
            arrayBuffer: arrayBuffer,
            type: imageType,
          },
          `/merchant/${merchantId}/topup-credit-type/${creditTypeCommonIdLocal}/image${imageType}`
        );
      }

      ///////////////////////////////////

      if (selectedTopupCreditTypeEdit === null) {
        // means new item

        var body = {
          merchantId: merchantId,
          merchantName: merchantName,
          outletId: currOutlet.uniqueId,
          outletName: currOutlet.name,

          name: creditTypeName,
          price: parseFloat(creditTypePrice),
          value: parseFloat(creditTypeValue),
          image: creditTypeImagePath,
          isImageChanged: isImageChanged,

          /////////////////////////////////////////////////////////////
        };

        CommonStore.update((s) => {
          s.isLoading = true;
        });

        //   ApiClient.POST(API.createTaggableVoucher, body, false).then(
        APILocal.createTopupCreditType({ body: body, uid: userId }).then(
          (result) => {
            if (result && result.status === "success") {
              if (window.confirm("Success! Credit type has been created.")) {
                linkTo && linkTo(`${prefix}/credit-type-list`);
              }
            }

            CommonStore.update((s) => {
              s.isLoading = false;
            });
          }
        );
      } else if (selectedTopupCreditTypeEdit !== null) {
        //   // means existing item

        var body = {
          creditTypeId: selectedTopupCreditTypeEdit.uniqueId,

          merchantId: merchantId,
          merchantName: merchantName,
          outletId: currOutlet.uniqueId,
          outletName: currOutlet.name,

          name: creditTypeName,
          price: parseFloat(creditTypePrice),
          value: parseFloat(creditTypeValue),
          image: creditTypeImagePath,
          isImageChanged: isImageChanged,

          /////////////////////////////////////////////////////////////
        };

        CommonStore.update((s) => {
          s.isLoading = true;
        });

        // ApiClient.POST(API.updateTaggableVoucher, body, false).then(
        APILocal.updateTopupCreditType({ body: body, uid: userId }).then(
          (result) => {
            if (result && result.status === "success") {
              if (window.confirm("Success! Credit type has been updated.")) {
                linkTo && linkTo(`${prefix}/credit-type-list`);
              }
            }

            CommonStore.update((s) => {
              s.isLoading = false;
            });
          }
        );
      }
    }
  };
  /////////////////////////////////////////////////

  //Render start here
  return (
    //<UserIdleWrapper disabled={!isMounted}>
    <View
      style={[
        styles.container,
        {
          height: windowHeight,
          width: windowWidth,
          ...getTransformForScreenInsideNavigation(),
        },
      ]}
    >
      <View style={{ flex: 0.8 }}>
        <SideBar
          navigation={props.navigation}
          selectedTab={11}
          expandPromotions={true}
        />
      </View>

      <Modal
        style={{ flex: 1 }}
        visible={loadingModal}
        supportedOrientations={["portrait", "landscape"]}
        transparent={true}
        animationType={"slide"}
      >
        <View style={styles.modalContainer}>
          <ActivityIndicator color={Colors.whiteColor} size={"large"} />
        </View>
      </Modal>

      <View style={{ height: windowHeight, flex: 9 }}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{ width: windowWidth * 0.9 }}
          contentContainerStyle={{
            paddingBottom: windowHeight * 0.1,
            backgroundColor: Colors.highlightColor,
          }}
        >
          <View
            style={{
              paddingVertical: 30,
              marginHorizontal: 30,
            }}
          >
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: windowWidth * 0.877,
                marginHorizontal: 30,
                alignSelf: 'center',
                marginTop: 20,
              }}>
              <TouchableOpacity
                style={{ height: 35, justifyContent: "center" }}
                onPress={() => {
                  // props.navigation.navigate('PromotionList');
    
                  linkTo && linkTo(`${prefix}/credit-type-list`);
                }}
              >
                <View
                  style={{
                    flexDirection: "row",
                    // paddingHorizontal: "10%",
                    alignContent: "center",
                    alignItems: "center",
                  }}
                >
                  <View style={{ justifyContent: "center" }}>
                    <Feather
                      name="chevron-left"
                      size={switchMerchant ? 20 : 30}
                      style={{ color: Colors.primaryColor, alignSelf: "center" }}
                    />
                  </View>
                  <Text
                    style={[
                      {
                        fontSize: 17,
                        color: Colors.primaryColor,
                        fontWeight: "600",
                        marginBottom: 1,
                      },
                      switchMerchant
                        ? {
                          fontSize: 14,
                        }
                        : {},
                    ]}
                  >
                    Back
                  </Text>
                </View>
              </TouchableOpacity>
            </View>

            {/* <View style={styles.list}> */}
            <View
              showsVerticalScrollIndicator={false}
              nestedScrollEnabled={true}
              style={{
                backgroundColor: Colors.whiteColor,
                width: windowWidth * 0.877,
                height: windowHeight * 0.68,
                marginTop: 10,
                marginBottom: 30,
                marginHorizontal: 30,
                alignSelf: 'center',
                borderRadius: 5,
                shadowOpacity: 0,
                shadowColor: "#000",
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 3,
              }}
            >
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  width: "100%",
                }}
              >
                <View
                  style={{
                    //flexDirection: 'row',
                    margin: 20,
                    marginBottom: 10,
                    width: "70%",
                  }}
                >
                  <View
                    style={{
                      width: "100%",
                      justifyContent: "space-between",
                      flexDirection: "row",
                    }}
                  >
                    <Text
                      style={[
                        { fontFamily: "NunitoSans-Bold", fontSize: 30 },
                        switchMerchant
                          ? {
                            fontSize: 20,
                          }
                          : {},
                      ]}
                    >
                      {creditTypeName.length > 0
                        ? creditTypeName
                        : "New Credit Type"}
                    </Text>
                  </View>
                </View>
                <View
                  style={{
                    margin: 20,
                    marginBottom: 10,
                  }}
                >
                  <View style={{}}>
                    <TouchableOpacity
                      style={[
                        {
                          justifyContent: "center",
                          flexDirection: "row",
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: "#4E9F7D",
                          borderRadius: 5,
                          width: 130,
                          paddingHorizontal: 10,
                          height: 40,
                          alignItems: "center",
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                          marginBottom: 10,
                        },
                        switchMerchant
                          ? {
                            height: 35,
                            width: 120,
                          }
                          : {},
                      ]}
                      disabled={isLoading}
                      onPress={() => {
                        createCreditType();
                      }}
                    >
                      <Text
                        style={[
                          {
                            color: Colors.whiteColor,
                            fontSize: 16,
                            fontFamily: "NunitoSans-Bold",
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}
                      >
                        {isLoading ? "LOADING..." : "SAVE"}
                      </Text>

                      {isLoading ? (
                        <ActivityIndicator
                          color={Colors.whiteColor}
                          size={"small"}
                        />
                      ) : (
                        <></>
                      )}
                    </TouchableOpacity>
                  </View>
                </View>
              </View>

              <View
                style={{
                  flexDirection: "column",
                  borderWidth: 1,
                  borderColor: "#c4c4c4",
                  width: "95%",
                  height: 120,
                  alignSelf: "center",
                  flex: 1,
                  marginBottom: 50,
                }}
              >
                <View style={{ flexDirection: "row", flex: 1 }}>
                  <View
                    style={{
                      flex: 4,
                      flexDirection: "column",
                      marginVertical: 20,
                    }}
                  >
                    <View style={{ flexDirection: "column", marginLeft: 20 }}>
                      <TouchableOpacity
                        onPress={() => {
                          openFileSelector();
                        }}
                      >
                        <View style={{ flexDirection: "row", zIndex: -2 }}>
                          {image ? (
                            <View
                              style={{
                                backgroundColor: "#F7F7F7",
                                borderRadius: 5,
                                zIndex: 1,
                              }}
                            >
                              <AsyncImage
                                source={{ uri: image }}
                                style={[
                                  { width: 260, height: 200, borderRadius: 5 },
                                  switchMerchant
                                    ? {
                                      width: 200,
                                      height: 160,
                                    }
                                    : {},
                                ]}
                                hideLoading={true}
                              />
                              <View
                                style={{
                                  position: "absolute",
                                  bottom: 5,
                                  right: 5,
                                  //opacity: 0.5,
                                }}
                              >
                                <Edit
                                  style={{ width: 30, height: 30 }}
                                  color={Colors.primaryColor}
                                />
                              </View>
                            </View>
                          ) : (
                            <View
                              style={[
                                {
                                  backgroundColor: "#F7F7F7",
                                  borderRadius: 5,
                                  width: 260,
                                  height: 200,
                                  alignItems: "center",
                                  justifyContent: "center",
                                },
                                switchMerchant
                                  ? {
                                    width: 200,
                                    height: 160,
                                  }
                                  : {},
                              ]}
                            >
                              <Icon1
                                name="upload"
                                size={switchMerchant ? 100 : 150}
                                color="lightgrey"
                                style={{ zIndex: -1 }}
                              />
                              <View
                                style={{
                                  position: "absolute",
                                  bottom: 5,
                                  right: 5,
                                  //opacity: 0.5,
                                }}
                              >
                                <Edit
                                  style={{ width: 30, height: 30 }}
                                  color={Colors.primaryColor}
                                />
                              </View>
                            </View>
                          )}
                        </View>
                      </TouchableOpacity>
                    </View>
                  </View>

                  <View
                    style={{
                      flex: 7,
                      marginVertical: 20,
                      marginHorizontal: 20,
                      marginLeft: 10,
                    }}
                  >
                    <View style={{ flexDirection: "row", flex: 1, zIndex: 1 }}>
                      {/* 1st column */}

                      <View
                        style={{
                          flex: 1,
                          marginRight: switchMerchant
                            ? "5%"
                            : windowWidth <= 1024
                              ? "3%"
                              : "2%",
                        }}
                      >
                        <Text
                          style={[
                            {
                              alignSelf: "flex-start",
                              fontFamily: "NunitoSans-Bold",
                              fontSize: 14,
                              fontWeight: "500",
                            },
                            switchMerchant
                              ? {
                                fontSize: 10,
                              }
                              : {},
                          ]}
                        >
                          Name
                        </Text>
                        <TextInput
                          placeholder="Name (Ex: RM 10)"
                          placeholderTextColor={"#a9a9a9"}
                          placeholderStyle={{
                            fontFamily: "NunitoSans-Regular",
                            fontSize: 14,
                          }}
                          style={[
                            {
                              backgroundColor: Colors.fieldtBgColor,
                              width: 250,
                              height: 40,
                              borderRadius: 5,
                              padding: 5,
                              // marginVertical: 5,
                              marginTop: 5,
                              borderWidth: 1,
                              borderColor: "#E5E5E5",
                              paddingLeft: 10,
                              fontFamily: "NunitoSans-Regular",
                              fontSize: 14,
                            },
                            switchMerchant
                              ? {
                                fontSize: 10,
                                width: 200,
                                height: 35,
                              }
                              : {},
                          ]}
                          //iOS
                          clearTextOnFocus={true}
                          //////////////////////////////////////////////
                          //Android
                          onFocus={() => {
                            setTemp(creditTypeName);
                            setCreditTypeName("");
                          }}
                          ///////////////////////////////////////////////
                          //When textinput is not selected
                          onEndEditing={() => {
                            if (creditTypeName == "") {
                              setCreditTypeName(temp);
                            }
                          }}
                          onChangeText={(text) => {
                            setCreditTypeName(text);
                          }}
                          defaultValue={creditTypeName}
                        />
                      </View>

                      {/* 2nd column */}

                      <View style={{ flex: 1, zIndex: -1, marginTop: 1 }}>
                        <View style={{ marginTop: "0%", zIndex: -2 }}>
                          <Text
                            style={[
                              {
                                alignSelf: "flex-start",
                                fontFamily: "NunitoSans-Bold",
                                fontSize: 14,
                                fontWeight: "500",
                              },
                              switchMerchant
                                ? {
                                  fontSize: 10,
                                }
                                : {},
                            ]}
                          >
                            Price To Buy (RM)
                          </Text>
                          <TextInput
                            placeholder="Price (Ex: 10)"
                            placeholderTextColor={"#a9a9a9"}
                            placeholderStyle={{
                              fontFamily: "NunitoSans-Regular",
                              fontSize: 14,
                            }}
                            style={[
                              {
                                backgroundColor: Colors.fieldtBgColor,
                                width: 250,
                                height: 40,
                                borderRadius: 5,
                                padding: 5,
                                // marginVertical: 5,
                                marginTop: 5,
                                borderWidth: 1,
                                borderColor: "#E5E5E5",
                                paddingLeft: 10,
                                fontFamily: "NunitoSans-Regular",
                                fontSize: 14,
                              },
                              switchMerchant
                                ? {
                                  fontSize: 10,
                                  // width: 200,
                                  height: 35,
                                }
                                : {},
                            ]}
                            //iOS
                            clearTextOnFocus={true}
                            //////////////////////////////////////////////
                            //Android
                            onFocus={() => {
                              setTemp(creditTypePrice);
                              setCreditTypePrice("");
                            }}
                            ///////////////////////////////////////////////
                            //When textinput is not selected
                            onEndEditing={() => {
                              if (creditTypePrice == "") {
                                setCreditTypePrice(temp);
                              }
                            }}
                            onChangeText={(text) => {
                              setCreditTypePrice(parseValidPriceText(text));
                            }}
                            defaultValue={creditTypePrice}
                            keyboardType={"decimal-pad"}
                          />
                        </View>

                        <View style={{ marginTop: "2%", zIndex: -2 }}>
                          <Text
                            style={[
                              {
                                alignSelf: "flex-start",
                                fontFamily: "NunitoSans-Bold",
                                fontSize: 14,
                                fontWeight: "500",
                              },
                              switchMerchant
                                ? {
                                  fontSize: 10,
                                }
                                : {},
                            ]}
                          >
                            Credit Point
                          </Text>
                          <TextInput
                            placeholder="Point (Ex: 10)"
                            placeholderTextColor={"#a9a9a9"}
                            placeholderStyle={{
                              fontFamily: "NunitoSans-Regular",
                              fontSize: 14,
                            }}
                            style={[
                              {
                                backgroundColor: Colors.fieldtBgColor,
                                width: 250,
                                height: 40,
                                borderRadius: 5,
                                padding: 5,
                                // marginVertical: 5,
                                marginTop: 5,
                                borderWidth: 1,
                                borderColor: "#E5E5E5",
                                paddingLeft: 10,
                                fontFamily: "NunitoSans-Regular",
                                fontSize: 14,
                              },
                              switchMerchant
                                ? {
                                  fontSize: 10,
                                  // width: 200,
                                  height: 35,
                                }
                                : {},
                            ]}
                            //iOS
                            clearTextOnFocus={true}
                            //////////////////////////////////////////////
                            //Android
                            onFocus={() => {
                              setTemp(creditTypeValue);
                              setCreditTypeValue("");
                            }}
                            ///////////////////////////////////////////////
                            //When textinput is not selected
                            onEndEditing={() => {
                              if (creditTypeValue == "") {
                                setCreditTypeValue(temp);
                              }
                            }}
                            onChangeText={(text) => {
                              setCreditTypeValue(parseValidPriceText(text));
                            }}
                            defaultValue={creditTypeValue}
                            keyboardType={"decimal-pad"}
                          />
                        </View>
                      </View>
                    </View>
                  </View>
                </View>
              </View>
            </View>
            {/* </View> */}
          </View>
        </ScrollView>
      </View>
    </View>
    //</UserIdleWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: "row",
  },
  list: {
    backgroundColor: Colors.whiteColor,
    width: Dimensions.get("window").width * 0.87,
    height: Dimensions.get("window").height * 0.825,
    marginTop: 0,
    marginHorizontal: 20,
    alignSelf: "center",
    borderRadius: 5,
    shadowOpacity: 0,
    shadowColor: "#000",
    shadowOffset: {
      width: 1,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
  },
  listItem: {
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    //width: windowWidth * Styles.sideBarWidth,
    // shadowColor: "#000",
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  textInput: {
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginBottom: 20,
    width: 300,
  },
  headerLeftStyle: {
    width: Dimensions.get("window").width * 0.17,
    justifyContent: "center",
    alignItems: "center",
  },
});

export default NewTopupCreditTypeScreen;
