import React, {
    Component,
    useReducer,
    useState,
    useEffect,
    useRef,
    useCallback,
} from "react";
import {
    StyleSheet,
    ScrollView,
    Image,
    View,
    Text,
    Alert,
    TouchableOpacity,
    Dimensions,
    Platform,
    Switch,
    Modal,
    KeyboardAvoidingView,
    TextInput,
    ActivityIndicator,
    Picker,
    useWindowDimensions,
} from "react-native";
import Colors from "../constant/Colors";
import SideBar from "./SideBar";
import Icon from "react-native-vector-icons/Feather";
import Ionicon from "react-native-vector-icons/Ionicons";
import AntDesign from "react-native-vector-icons/AntDesign";
import Entypo from "react-native-vector-icons/Entypo";
import { ReactComponent as GCalendar } from "../assets/svg/GCalendar.svg";
import MaterialIcons from "react-native-vector-icons/MaterialIcons";
import EvilIcons from "react-native-vector-icons/EvilIcons";
import { FlatList } from "react-native-gesture-handler";
import API from "../constant/API";
import ApiClient from "../util/ApiClient";
import Styles from "../constant/Styles";
import * as User from "../util/User";
import AsyncStorage from "@react-native-async-storage/async-storage";
// import CheckBox from 'react-native-check-box';
import moment from "moment";
import DateTimePickerModal from "react-native-modal-datetime-picker";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
// import { isTablet } from 'react-native-device-detection';
import { CommonStore } from "../store/commonStore";
import { OutletStore } from "../store/outletStore";
import { MerchantStore } from "../store/merchantStore";
import { UserStore } from "../store/userStore";
import { ReactComponent as Upload } from "../assets/svg/Upload.svg";
import { ReactComponent as Download } from "../assets/svg/Download.svg";
import { ReactComponent as ArrowLeft } from "../assets/svg/ArrowLeft.svg";
import { ReactComponent as ArrowRight } from "../assets/svg/ArrowRight.svg";
import {
    convertArrayToCSV,
    generateEmailReport,
    sortReportDataList,
    sliceUnicodeStringV2WithDots,
    getTransformForModalInsideNavigation,
    getTransformForScreenInsideNavigation,
} from "../util/common";
import {
    EMAIL_REPORT_TYPE,
    REPORT_SORT_FIELD_TYPE,
    TABLE_PAGE_SIZE_DROPDOWN_LIST,
    ORDER_TYPE,
    USER_ORDER_ACTION_PARSED,
    EXPAND_TAB_TYPE,
} from "../constant/common";
// import RNFetchBlob from 'rn-fetch-blob';
// import { useKeyboard } from '../hooks';
import XLSX from "xlsx";
import { CSVLink } from "react-csv";
import "react-native-get-random-values";
import { v4 as uuidv4 } from "uuid";
// import RNPickerSelect from 'react-native-picker-select';
import AsyncImage from "../components/asyncImage";
import Feather from "react-native-vector-icons/Feather";
// import Tooltip from 'react-native-walkthrough-tooltip';

// import firestore from '@react-native-firebase/firestore';
import firebase from "firebase";
import { Collections } from "../constant/firebase";
import { useFocusEffect } from "@react-navigation/native";
import Select from "react-select";
import personicon from "../assets/image/default-profile.png";
import headerLogo from "../assets/image/logo.png";
import MultiSelect from "react-multiple-select-dropdown-lite";
import "../constant/styles.css";
import Multiselect from "multiselect-react-dropdown";
// import MultiSelect from  'react-multiple-select-dropdown-lite'
import { EFFECTIVE_DAY_DROPDOWN_LIST } from "../constant/promotions";
// import UserIdleWrapper from '../components/userIdleWrapper';
import DropDownPicker from 'react-native-dropdown-picker';

const { nanoid } = require("nanoid");
// const RNFS = require('react-native-fs');

const ReportActivityLog = (props) => {
    //port til aug 11 changes
    const { navigation } = props;

    ///////////////////////////////////////////////////////////

    // const [isMounted, setIsMounted] = useState(true);

    // useFocusEffect(
    //     useCallback(() => {
    //         setIsMounted(true);
    //         return () => {
    //             setIsMounted(false);
    //         };
    //     }, [])
    // );

    ///////////////////////////////////////////////////////////

    const { width: windowWidth, height: windowHeight } = useWindowDimensions();

    // const [keyboardHeight] = useKeyboard();
    const [visible, setVisible] = useState(false);
    const [perPage, setPerPage] = useState(10);
    const [pageCount, setPageCount] = useState(0);
    const [currentPage, setCurrentPage] = useState(1);
    const [currentDetailsPage, setCurrentDetailsPage] = useState(1);
    const [switchMerchant, setSwitchMerchant] = useState(false);
    const [pageReturn, setPageReturn] = useState(1);
    const [search, setSearch] = useState("");

    const [loading, setLoading] = useState(false);

    const [pushPagingToTop, setPushPagingToTop] = useState(false);

    const [showDateTimePicker, setShowDateTimePicker] = useState(false);
    const [showDateTimePicker1, setShowDateTimePicker1] = useState(false);
    const [rev_date, setRev_date] = useState(
        moment().subtract(6, "days").startOf("day")
    );
    const [rev_date1, setRev_date1] = useState(
        moment().endOf(Date.now()).endOf("day")
    );

    const userName = UserStore.useState((s) => s.name);

    const [exportEmail, setExportEmail] = useState("");

    const [showDetails, setShowDetails] = useState(false);

    const [exportModalVisibility, setExportModalVisibility] = useState(false);

    const merchantId = UserStore.useState((s) => s.merchantId);
    const isLoading = CommonStore.useState((s) => s.isLoading);

    const [isCsv, setIsCsv] = useState(false);
    const [isExcel, setIsExcel] = useState(false);

    const outletSelectDropdownView = CommonStore.useState(
        (s) => s.outletSelectDropdownView
    );

    const allOutletsEmployees = OutletStore.useState(
        (s) => s.allOutletsEmployees
    );
    const allOutletsEmployeesUserActionsDict = OutletStore.useState(
        (s) => s.allOutletsEmployeesUserActionsDict
    );

    const [allOutletsEmployeesAction, setAllOutletsEmployeesAction] = useState(
        []
    );
    const [allOutletsEmployeesDetails, setAllOutletsEmployeesDetails] = useState(
        []
    );

    const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
    const expandTab = CommonStore.useState((s) => s.expandTab);
    const currPageStack = CommonStore.useState((s) => s.currPageStack);
    const [openPage, setOpenPage] = useState(false);

    const [userActionSliceEmployeeUserIdDict, setUserActionSliceEmployeeUserIdDict] = useState({});

    const currOutletId = MerchantStore.useState((s) => s.currOutletId);
    const allOutlets = MerchantStore.useState((s) => s.allOutlets);

    useEffect(async () => {
        var userActionSliceList = [];

        const userActionSliceStartSnapshot = await firebase.firestore()
            .collection(Collections.UserActionSlice)
            .where('outletId', '==', currOutletId)
            .where('startDate', '>=', moment(rev_date).valueOf())
            .where('startDate', '<=', moment(rev_date1).valueOf())
            .orderBy('startDate', 'desc') // get the latest data first
            .limit(500)
            .get();

        if (userActionSliceStartSnapshot) {
            for (var i = 0; i < userActionSliceStartSnapshot.size; i++) {
                userActionSliceList.push(userActionSliceStartSnapshot.docs[i].data());
            }
        }

        const userActionSliceEndSnapshot = await firebase.firestore()
            .collection(Collections.UserActionSlice)
            .where('outletId', '==', currOutletId)
            .where('endDate', '>=', moment(rev_date).valueOf())
            .where('endDate', '<=', moment(rev_date1).valueOf())
            .orderBy('endDate', 'desc') // get the latest data first
            .limit(500)
            .get();

        if (userActionSliceEndSnapshot) {
            for (var i = 0; i < userActionSliceEndSnapshot.size; i++) {
                const record = userActionSliceEndSnapshot.docs[i].data();

                if (userActionSliceList.find(findItem => findItem.uniqueId === record.uniqueId)) {
                    // existed, no need add
                }
                else {
                    userActionSliceList.push(record);
                }
            }
        }

        var userActionSliceEmployeeUserIdDictTemp = {};

        for (var i = 0; i < userActionSliceList.length; i++) {
            const userActionSlice = userActionSliceList[i];

            if (userActionSliceEmployeeUserIdDictTemp[userActionSlice.userId]) {
                userActionSliceEmployeeUserIdDictTemp[userActionSlice.userId] = [
                    ...userActionSliceEmployeeUserIdDictTemp[userActionSlice.userId],

                    userActionSlice.actions,
                ];
            }
            else {
                userActionSliceEmployeeUserIdDictTemp[userActionSlice.userId] = userActionSlice.actions;
            }
        }

        setUserActionSliceEmployeeUserIdDict(userActionSliceEmployeeUserIdDictTemp);
    }, [rev_date, rev_date1, currOutletId]);

    useEffect(() => {
        if (showDetails && allOutletsEmployeesDetails) {
            setPageReturn(currentPage);
            // console.log('currentPage value is');
            // console.log(currentPage);
            setCurrentDetailsPage(1);
            setPageCount(Math.ceil(allOutletsEmployeesDetails.length / perPage));
        }
    }, [showDetails, allOutletsEmployeesDetails, perPage]);

    useEffect(async () => {
        // const userActionSnapshot = await firestore()
        //   .collection(Collections.UserAction)
        //   .get();

        // const userAction = userActionSnapshot.docs.map(doc => doc.data());

        const tempAllOutletsEmployeesAction = allOutletsEmployees.map(
            (employee) => {
                // const employeeAction = allOutletsEmployeesUserActionsDict.reduce(
                //   (prev, curr) => {
                //     if (curr.userId == employee.firebaseUid) {
                //       return curr.actions;
                //     }
                //     return [];
                //   },
                //   []
                // );

                var employeeAction = [];

                if (
                    allOutletsEmployeesUserActionsDict[employee.firebaseUid] &&
                    allOutletsEmployeesUserActionsDict[employee.firebaseUid].actions &&
                    allOutletsEmployeesUserActionsDict[employee.firebaseUid].actions
                        .length > 0
                ) {
                    var filteredActions = allOutletsEmployeesUserActionsDict[
                        employee.firebaseUid
                    ].actions.filter((action) =>
                        moment(action.actionDate, "x").isBetween(rev_date, rev_date1)
                    );

                    var additionalActions = [];
                    if (userActionSliceEmployeeUserIdDict[employee.firebaseUid] &&
                        userActionSliceEmployeeUserIdDict[employee.firebaseUid].length > 0) {
                        additionalActions = userActionSliceEmployeeUserIdDict[employee.firebaseUid].filter(action => moment(action.actionDate, 'x').isBetween(rev_date, rev_date1));
                    }

                    employeeAction = [
                        ...filteredActions,
                        ...additionalActions,
                    ];
                }

                return {
                    ...employee,
                    detailsList: employeeAction.sort((a, b) => b.actionDate - a.actionDate),
                };
            }
        );

        // console.log('allOutletsEmployeesAction', tempAllOutletsEmployeesAction);
        setAllOutletsEmployeesAction(tempAllOutletsEmployeesAction);

        setPageCount(Math.ceil(tempAllOutletsEmployeesAction.length / perPage));
    }, [allOutletsEmployees,
        allOutletsEmployeesUserActionsDict,
        rev_date, rev_date1,

        userActionSliceEmployeeUserIdDict,
    ]);

    const setState = () => { };

    const currOutletShiftStatus = OutletStore.useState(
        (s) => s.currOutletShiftStatus
    );
    // const [outletDropdownList, setOutletDropdownList] = useState([]);
    // const [selectedOutletList, setSelectedOutletList] = useState([]); // multi-outlets

    // var outletNames = [];

    // for (var i = 0; i < allOutlets.length; i++) {
    //     for (var j = 0; j < selectedOutletList.length; j++) {
    //         if (selectedOutletList.includes(allOutlets[i].uniqueId)) {
    //             outletNames.push(allOutlets[i].name);
    //             break;
    //         }
    //     }
    // }

    // useEffect(() => {
    //     setOutletDropdownList(
    //         allOutlets.map((item) => {
    //             return { label: item.name, value: item.uniqueId };
    //         })
    //     );
    // }, [allOutlets]);

    var targetOutletDropdownListTemp = allOutlets.map((outlet) => ({
        label: sliceUnicodeStringV2WithDots(outlet.name, 20),
        value: outlet.uniqueId,
    }));

    // useEffect(() => {
    //     CommonStore.update((s) => {
    //         s.outletSelectDropdownView = () => {
    //             return (
    //                 <View
    //                     style={{
    //                         flexDirection: "row",
    //                         alignItems: "center",
    //                         borderRadius: 8,
    //                         width: 200,
    //                         backgroundColor: "white",
    //                     }}
    //                 >
    //                     {currOutletId.length > 0 &&
    //                         allOutlets.find((item) => item.uniqueId === currOutletId) ? (
    //                         <MultiSelect
    //                             clearable={false}
    //                             singleSelect={true}
    //                             defaultValue={currOutletId}
    //                             placeholder={"Choose Outlet"}
    //                             onChange={(value) => {
    //                                 if (value) { // if choose the same option again, value = ''
    //                                     MerchantStore.update((s) => {
    //                                         s.currOutletId = value;
    //                                         s.currOutlet =
    //                                             allOutlets.find(
    //                                                 (outlet) => outlet.uniqueId === value
    //                                             ) || {};
    //                                     });
    //                                 }

    //                                 CommonStore.update((s) => {
    //                                     s.shiftClosedModal = false;
    //                                 });
    //                             }}
    //                             options={targetOutletDropdownListTemp}
    //                             className="msl-varsHEADER"
    //                         />
    //                     ) : (
    //                         <ActivityIndicator size={"small"} color={Colors.whiteColor} />
    //                     )}
    //                     {/* <Select

    //                         placeholder={"Choose Outlet"}
    //                         onChange={(items) => {
    //                             setSelectedOutletList(items);
    //                         }}
    //                         options={outletDropdownList}
    //                         isMulti
    //                     /> */}
    //                 </View>
    //             );
    //         };
    //     });
    // }, [allOutlets, currOutletId, isLoading, currOutletShiftStatus]);

    navigation.setOptions({
        headerLeft: () => (
            <View
                style={[
                    styles.headerLeftStyle,
                    {
                        width: windowWidth * 0.17,
                    },
                ]}
            >
                <img src={headerLogo} width={124} height={26} />
                {/* <Image
              style={{
                width: 124,
                height: 26,
              }}
              resizeMode="contain"
              source={require('../assets/image/logo.png')}
            /> */}
            </View>
        ),
        headerTitle: () => (
            <View
                style={[
                    {
                        justifyContent: "center",
                        alignItems: "center",
                        // marginRight: Platform.OS === 'ios' ? "27%" : 0,
                        // bottom: switchMerchant ? '2%' : 0,
                        //width:  "55%",
                    },
                    Dimensions.get("screen").width <= 768
                        ? { right: Dimensions.get("screen").width * 0.12 }
                        : {},
                ]}
            >
                <Text
                    style={{
                        fontSize: 24,
                        // lineHeight: 25,
                        textAlign: "center",
                        fontFamily: "NunitoSans-Bold",
                        color: Colors.whiteColor,
                        opacity: 1,
                    }}
                >
                    Orders Channel Report
                </Text>
            </View>
        ),
        headerRight: () => (
            <View
                style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between",
                }}
            >
                {/* {console.log('edward test')} */}
                {/* {console.log(outletSelectDropdownView)} */}
                {outletSelectDropdownView && outletSelectDropdownView()}
                <View
                    style={{
                        backgroundColor: "white",
                        width: 0.5,
                        height: Dimensions.get("screen").height * 0.025,
                        opacity: 0.8,
                        marginHorizontal: 15,
                        bottom: -1,
                        // borderWidth: 1
                    }} />
                <TouchableOpacity
                    onPress={() => {
                        if (global.currUserRole === 'admin') {
                            navigation.navigate("General Settings - KooDoo BackOffice");
                        }
                    }}
                    style={{ flexDirection: "row", alignItems: "center" }}
                >
                    <Text
                        style={{
                            fontFamily: "NunitoSans-SemiBold",
                            fontSize: 16,
                            color: Colors.secondaryColor,
                            marginRight: 15,
                        }}
                    >
                        {userName}
                    </Text>
                    <View
                        style={{
                            //backgroundColor: 'red',
                            marginRight: 30,
                            width: windowHeight * 0.05,
                            height: windowHeight * 0.05,
                            borderRadius: windowHeight * 0.05 * 0.5,
                            alignItems: "center",
                            justifyContent: "center",
                            backgroundColor: "white",
                        }}
                    >
                        <img
                            src={personicon}
                            width={windowHeight * 0.035}
                            height={windowHeight * 0.035}
                        />
                        {/* <Image
                  style={{
                    width: windowHeight * 0.05,
                  height: windowHeight * 0.05,
                    alignSelf: 'center',
                  }}
                  source={require('../assets/image/profile-pic.jpg')}
                /> */}
                    </View>
                </TouchableOpacity>
            </View>
        ),
    });

    navigation.setOptions({
        headerLeft: () => (
            <View
                style={[
                    styles.headerLeftStyle,
                    {
                        width: windowWidth * 0.17,
                    },
                ]}
            >
                <img src={headerLogo} width={124} height={26} />
                {/* <Image
              style={{
                width: 124,
                height: 26,
              }}
              resizeMode="contain"
              source={require('../assets/image/logo.png')}
            /> */}
            </View>
        ),
        headerTitle: () => (
            <View
                style={[
                    {
                        justifyContent: "center",
                        alignItems: "center",
                        // marginRight: Platform.OS === 'ios' ? "27%" : 0,
                        // bottom: switchMerchant ? '2%' : 0,
                        //width:  "55%",
                    },
                    Dimensions.get("screen").width <= 768
                        ? { right: Dimensions.get("screen").width * 0.12 }
                        : {},
                ]}
            >
                <Text
                    style={{
                        fontSize: 24,
                        // lineHeight: 25,
                        textAlign: "center",
                        fontFamily: "NunitoSans-Bold",
                        color: Colors.whiteColor,
                        opacity: 1,
                    }}
                >
                    Activity Log Report
                </Text>
            </View>
        ),
        headerRight: () => (
            <View
                style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between",
                }}
            >
                {/* {console.log('edward test')} */}
                {/* {console.log(outletSelectDropdownView)} */}
                {outletSelectDropdownView && outletSelectDropdownView()}
                <View
                    style={{
                        backgroundColor: "white",
                        width: 0.5,
                        height: Dimensions.get("screen").height * 0.025,
                        opacity: 0.8,
                        marginHorizontal: 15,
                        bottom: -1,
                        // borderWidth: 1
                    }}
                ></View>
                <TouchableOpacity
                    onPress={() => {
                        navigation.navigate("General Settings - KooDoo BackOffice");
                    }}
                    style={{ flexDirection: "row", alignItems: "center" }}
                >
                    <Text
                        style={{
                            fontFamily: "NunitoSans-SemiBold",
                            fontSize: 16,
                            color: Colors.secondaryColor,
                            marginRight: 15,
                        }}
                    >
                        {userName}
                    </Text>
                    <View
                        style={{
                            //backgroundColor: 'red',
                            marginRight: 30,
                            width: windowHeight * 0.05,
                            height: windowHeight * 0.05,
                            borderRadius: windowHeight * 0.05 * 0.5,
                            alignItems: "center",
                            justifyContent: "center",
                            backgroundColor: "white",
                        }}
                    >
                        <img
                            src={personicon}
                            width={windowHeight * 0.035}
                            height={windowHeight * 0.035}
                        />
                        {/* <Image
                  style={{
                    width: windowHeight * 0.05,
                  height: windowHeight * 0.05,
                    alignSelf: 'center',
                  }}
                  source={require('../assets/image/profile-pic.jpg')}
                /> */}
                    </View>
                </TouchableOpacity>
            </View>
        ),
    });

    const nextPage = () => {
        setCurrentPage(currentPage + 1 > pageCount ? currentPage : currentPage + 1);
    };

    const prevPage = () => {
        setCurrentPage(currentPage - 1 < 1 ? currentPage : currentPage - 1);
    };

    const nextDetailsPage = () => {
        setCurrentDetailsPage(
            currentDetailsPage + 1 > pageCount
                ? currentDetailsPage
                : currentDetailsPage + 1
        );
    };

    const prevDetailsPage = () => {
        setCurrentDetailsPage(
            currentDetailsPage - 1 < 1 ? currentDetailsPage : currentDetailsPage - 1
        );
    };

    const renderItem = ({ item, index }) => {
        return (
            <TouchableOpacity
                onPress={() => {
                    setShowDetails(true);
                    setAllOutletsEmployeesDetails(item.detailsList);
                }}
                style={{
                    backgroundColor:
                        (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
                    paddingVertical: 10,
                    paddingHorizontal: 3,
                    paddingLeft: 1,
                    borderColor: "#BDBDBD",
                    borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
                    borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
                }}
            >
                <View style={{ flexDirection: "row" }}>
                    <Text
                        style={{
                            width: "5%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Regular",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {index + 1}
                    </Text>
                    <Text
                        style={{
                            width: "25%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Regular",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {item.name}
                    </Text>
                    <Text
                        style={{
                            width: "25%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Regular",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {item.detailsList.length}
                    </Text>
                </View>
            </TouchableOpacity>
        );
    };

    const renderItemDetails = ({ item, index }) => {
        return (
            <View
                style={{
                    backgroundColor:
                        (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
                    paddingVertical: 10,
                    borderColor: "#BDBDBD",
                    borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
                    borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
                }}
            >
                <View style={{ flexDirection: "row" }}>
                    <Text
                        style={{
                            width: "6%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Regular",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {index + 1}
                    </Text>
                    <Text
                        style={{
                            width: "25%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontWeight: "500",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {USER_ORDER_ACTION_PARSED[item.actionType]
                            ? USER_ORDER_ACTION_PARSED[item.actionType]
                            : item.actionType}
                    </Text>
                    <View style={{ width: "25%", paddingLeft: 10 }}>
                        <Text
                            style={{
                                fontSize: switchMerchant ? 10 : 13,
                                fontFamily: "NunitoSans-Regular",
                                textAlign: "left",
                                paddingLeft: 0,
                            }}
                        >
                            {moment(item.actionDate, "x").format("DD MMM YY hh:mm A")}
                        </Text>
                    </View>
                    <View style={{ width: '25%', paddingLeft: 10 }}>
                        <Text
                            style={{
                                fontSize: switchMerchant ? 10 : 13,
                                fontFamily: 'NunitoSans-Regular',
                                textAlign: 'left',
                                paddingLeft: 0,
                            }}>
                            {item.r ? item.r : 'N/A'}
                        </Text>
                    </View>
                </View>
            </View>
        );
    };

    const convertDataToExcelFormat = () => {
        var excelData = [];

        if (!showDetails) {
            for (var i = 0; i < allOutletsEmployeesAction.length; i++) {
                var excelRow = {
                    "Employee Name": allOutletsEmployeesAction[i].name,
                    "Num of Actions": allOutletsEmployeesAction[i].detailsList.length,
                };

                excelData.push(excelRow);
            }
        } else {
            for (var i = 0; i < allOutletsEmployeesDetails.length; i++) {
                var excelRow = {
                    'Action Type': USER_ORDER_ACTION_PARSED[allOutletsEmployeesDetails[i].actionType] ? USER_ORDER_ACTION_PARSED[allOutletsEmployeesDetails[i].actionType] : allOutletsEmployeesDetails[i].actionType,
                    'Date': moment(allOutletsEmployeesDetails[i].actionDate).format(
                        'DD MMM hh:mm A',
                    ),
                    "Action Details": allOutletsEmployeesDetails[i].r ? allOutletsEmployeesDetails[i].r : 'N/A',
                };

                excelData.push(excelRow);
            }
        }

        // console.log('excelData');
        // console.log(excelData);

        return excelData;
    };
    const handleExportExcel = () => {
        var wb = XLSX.utils.book_new(),
            ws = XLSX.utils.json_to_sheet(convertDataToExcelFormat());

        XLSX.utils.book_append_sheet(wb, ws, "ReportActivityLog");
        XLSX.writeFile(wb, "ReportActivityLog.xlsx");
    };

    const emailVariant = () => {
        const excelData = convertDataToExcelFormat();

        var body = {
            // data: CsvData,
            //data: convertArrayToCSV(todaySalesChart.dataSource.data),
            data: JSON.stringify(excelData),
            //data: convertDataToExcelFormat(),
            email: exportEmail,
        };

        ApiClient.POST(API.emailDashboard, body, false).then((result) => {
            if (result !== null) {
                window.confirm(
                    "Success",
                    "Email has been sent",
                    [{ text: "OK", onPress: () => { } }],
                    { cancelable: false }
                );
            }
        });

        setVisible(false);
    };

    var leftSpacing = "0%";

    if (windowWidth >= 1280) {
        leftSpacing = "10%";
    }

    const flatListRef = useRef();

    const filterItem = (item) => {
        if (search !== "") {
            if (item.name.toLowerCase().includes(search.toLowerCase())) {
                return true;
            } else {
                return false;
            }
        } else {
            return true;
        }
    };

    const filterItemDetails = (item) => {
        if (search !== "") {
            if (item.actionType.toLowerCase().includes(search.toLowerCase())) {
                return true;
            } else if (
                moment(item.actionDate, "x")
                    .format("DD MMM YYY hh:mma")
                    .toLowerCase()
                    .includes(search.toLowerCase())
            ) {
                return true;
            } else {
                return false;
            }
        } else {
            // check if there is data between the dates
            // return moment(item.actionDate, 'x').isBetween(rev_date, rev_date1);

            return true;
        }
    };

    return (
        //<UserIdleWrapper disabled={!isMounted}>
        <View
            style={[
                styles.container,
                {
                    height: windowHeight,
                    width: windowWidth,
                    ...getTransformForScreenInsideNavigation(),
                },
            ]}
        >
            <View style={{ flex: 0.8 }}>
                <SideBar
                    navigation={navigation}
                    selectedTab={8}
                    expandReport
                />
            </View>
            <View style={{ height: windowHeight, flex: 9 }}>
                <ScrollView
                    showsVerticalScrollIndicator={false}
                    style={{ width: windowWidth * 0.9 }}
                    contentContainerStyle={{
                        paddingBottom: windowHeight * 0.1,
                        backgroundColor: Colors.highlightColor,
                    }}
                >
                    <Modal
                        style={{}}
                        visible={exportModalVisibility}
                        supportedOrientations={["portrait", "landscape"]}
                        transparent
                        animationType={"fade"}
                    >
                        <View
                            style={{
                                flex: 1,
                                backgroundColor: Colors.modalBgColor,
                                alignItems: "center",
                                justifyContent: "center",
                            }}
                        >
                            <View
                                style={{
                                    // height: windowWidth * 0.08,
                                    height: Dimensions.get("screen").width * 0.08,
                                    // width: windowWidth * 0.18,
                                    width: Dimensions.get("screen").width * 0.18,
                                    backgroundColor: Colors.whiteColor,
                                    borderRadius: 12,
                                    // padding: windowWidth * 0.03,
                                    padding: Dimensions.get("screen").width * 0.03,
                                    alignItems: "center",
                                    justifyContent: "center",
                                    ...getTransformForModalInsideNavigation(),
                                }}
                            >
                                <TouchableOpacity
                                    disabled={isLoading}
                                    style={{
                                        position: "absolute",
                                        // right: windowWidth * 0.015,
                                        right: Dimensions.get("screen").width * 0.015,
                                        // top: windowWidth * 0.01,
                                        top: Dimensions.get("screen").width * 0.01,

                                        elevation: 1000,
                                        zIndex: 1000,
                                    }}
                                    onPress={() => {
                                        setExportModalVisibility(false);
                                    }}
                                >
                                    <AntDesign
                                        name="closecircle"
                                        size={switchMerchant ? 15 : 25}
                                        color={Colors.fieldtTxtColor}
                                    />
                                </TouchableOpacity>
                                <View
                                    style={{
                                        alignItems: "center",
                                        top: "20%",
                                        position: "absolute",
                                    }}
                                >
                                    <Text
                                        style={{
                                            fontFamily: "NunitoSans-Bold",
                                            textAlign: "center",
                                            fontSize: switchMerchant ? 16 : 24,
                                        }}
                                    >
                                        Download Report
                                    </Text>
                                </View>
                                <View style={{ top: switchMerchant ? "14%" : "10%" }}>
                                    {/* <Text
                                    style={{
                                        fontSize: switchMerchant ? 10 : 20,
                                        fontFamily: 'NunitoSans-Bold',
                                    }}>
                                    Email Address:
                                </Text>
                                <TextInput
                                    underlineColorAndroid={Colors.fieldtBgColor}
                                    style={{
                                        backgroundColor: Colors.fieldtBgColor,
                                        width: switchMerchant ? 240 : 370,
                                        height: switchMerchant ? 35 : 50,
                                        borderRadius: 5,
                                        padding: 5,
                                        marginVertical: 5,
                                        borderWidth: 1,
                                        borderColor: '#E5E5E5',
                                        paddingLeft: 10,
                                        fontSize: switchMerchant ? 10 : 14,
                                    }}
                                    autoCapitalize='none'
                                    placeholderStyle={{ padding: 5 }}
                                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                    placeholder="Enter your email"
                                    onChangeText={(text) => {
                                        setExportEmail(text);
                                    }}
                                    value={exportEmail}
                                />
                                <Text
                                    style={{
                                        fontSize: switchMerchant ? 10 : 20,
                                        fontFamily: 'NunitoSans-Bold',
                                        marginTop: 15,
                                    }}>
                                    Send As:
                                </Text> */}

                                    <View
                                        style={{
                                            alignItems: "center",
                                            justifyContent: "center",
                                            flexDirection: "row",
                                            marginTop: 30,
                                        }}
                                    >
                                        <TouchableOpacity
                                            disabled={isLoading}
                                            style={{
                                                justifyContent: "center",
                                                flexDirection: "row",
                                                borderWidth: 1,
                                                borderColor: Colors.primaryColor,
                                                backgroundColor: "#4E9F7D",
                                                borderRadius: 5,
                                                width: switchMerchant ? 100 : 100,
                                                paddingHorizontal: 10,
                                                height: switchMerchant ? 35 : 40,
                                                alignItems: "center",
                                                shadowOffset: {
                                                    width: 0,
                                                    height: 2,
                                                },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 3.22,
                                                elevation: 1,
                                                zIndex: -1,
                                                marginRight: 15,
                                            }}
                                            onPress={() => {
                                                // if (exportEmail.length > 0) {
                                                //     CommonStore.update((s) => {
                                                //         s.isLoading = true;
                                                //     });

                                                //     setIsExcel(true);

                                                //     const excelData = convertDataToExcelFormat();

                                                //     generateEmailReport(
                                                //         EMAIL_REPORT_TYPE.EXCEL,
                                                //         excelData,
                                                //         'KooDoo Add-Ons Sales Report',
                                                //         'KooDoo Add-Ons Sales Report.xlsx',
                                                //         `/merchant/${merchantId}/reports/${uuidv4()}.xlsx`,
                                                //         exportEmail,
                                                //         'KooDoo Add-Ons Sales Report',
                                                //         'KooDoo Add-Ons Sales Report',
                                                //         () => {
                                                //             CommonStore.update((s) => {
                                                //                 s.isLoading = false;
                                                //             });

                                                //             setIsExcel(false);

                                                //             window.confirm(
                                                //                 'Success',
                                                //                 'Report will be sent to the email address shortly',
                                                //             );

                                                //             setExportModalVisibility(false);
                                                //         },
                                                //     );
                                                // } else {
                                                //    window.confirm('Info', 'Invalid email address');
                                                // }
                                                handleExportExcel();
                                            }}
                                        >
                                            {isLoading && isExcel ? (
                                                <ActivityIndicator
                                                    size={"small"}
                                                    color={Colors.whiteColor}
                                                />
                                            ) : (
                                                <Text
                                                    style={{
                                                        color: Colors.whiteColor,
                                                        //marginLeft: 5,
                                                        fontSize: switchMerchant ? 10 : 16,
                                                        fontFamily: "NunitoSans-Bold",
                                                    }}
                                                >
                                                    EXCEL
                                                </Text>
                                            )}
                                        </TouchableOpacity>

                                        {/* <TouchableOpacity
                                        disabled={isLoading}
                                        style={{
                                            justifyContent: 'center',
                                            flexDirection: 'row',
                                            borderWidth: 1,
                                            borderColor: Colors.primaryColor,
                                            backgroundColor: '#4E9F7D',
                                            borderRadius: 5,
                                            width: switchMerchant ? 100 : 100,
                                            paddingHorizontal: 10,
                                            height: switchMerchant ? 35 : 40,
                                            alignItems: 'center',
                                            shadowOffset: {
                                                width: 0,
                                                height: 2,
                                            },
                                            shadowOpacity: 0.22,
                                            shadowRadius: 3.22,
                                            elevation: 1,
                                            zIndex: -1,
                                        }}
                                        onPress={() => {
                                            if (exportEmail.length > 0) {
                                                CommonStore.update((s) => {
                                                    s.isLoading = true;
                                                });

                                                setIsCsv(true);

                                                const csvData = convertArrayToCSV(allOutletsEmployeesAction);

                                                generateEmailReport(
                                                    EMAIL_REPORT_TYPE.CSV,
                                                    csvData,
                                                    'KooDoo Add-Ons Sales Report',
                                                    'KooDoo Add-Ons Sales Report.csv',
                                                    `/merchant/${merchantId}/reports/${uuidv4()}.csv`,
                                                    exportEmail,
                                                    'KooDoo Add-Ons Sales Report',
                                                    'KooDoo Add-Ons Sales Report',
                                                    () => {
                                                        CommonStore.update((s) => {
                                                            s.isLoading = false;
                                                        });

                                                        setIsCsv(false);

                                                        window.confirm(
                                                            'Success',
                                                            'Report will be sent to the email address shortly',
                                                        );

                                                        setExportModalVisibility(false);
                                                    },
                                                );
                                            } else {
                                                window.confirm('Info', 'Invalid email address');
                                            }
                                        }}>
                                        {isLoading && isCsv ? (
                                            <ActivityIndicator
                                                size={'small'}
                                                color={Colors.whiteColor}
                                            />
                                        ) : (
                                            <Text
                                                style={{
                                                    color: Colors.whiteColor,
                                                    //marginLeft: 5,
                                                    fontSize: switchMerchant ? 10 : 16,
                                                    fontFamily: 'NunitoSans-Bold',
                                                }}>
                                                CSV
                                            </Text>
                                        )}
                                    </TouchableOpacity> */}
                                        <CSVLink
                                            style={{
                                                justifyContent: "center",
                                                flexDirection: "row",
                                                borderWidth: 1,
                                                textDecoration: "none",
                                                borderColor: Colors.primaryColor,
                                                backgroundColor: "#4E9F7D",
                                                borderRadius: 5,
                                                width: switchMerchant ? 100 : 100,
                                                paddingHorizontal: 10,
                                                height: switchMerchant ? 35 : 40,
                                                alignItems: "center",
                                                shadowOffset: {
                                                    width: 0,
                                                    height: 2,
                                                },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 3.22,
                                                elevation: 1,
                                            }}
                                            data={convertDataToExcelFormat()}
                                            filename="ReportActivityLog.csv"
                                        >
                                            <View
                                                style={{
                                                    width: "100%",
                                                    height: "100%",
                                                    alignContent: "center",
                                                    alignItems: "center",
                                                    alignSelf: "center",
                                                    justifyContent: "center",
                                                }}
                                            >
                                                <Text
                                                    style={{
                                                        color: Colors.whiteColor,
                                                        //marginLeft: 5,
                                                        fontSize: switchMerchant ? 10 : 16,
                                                        fontFamily: "NunitoSans-Bold",
                                                    }}
                                                >
                                                    CSV
                                                </Text>
                                            </View>
                                        </CSVLink>
                                    </View>
                                </View>
                            </View>
                        </View>
                    </Modal>

                    <DateTimePickerModal
                        isVisible={showDateTimePicker}
                        mode={"date"}
                        onConfirm={(text) => {
                            setRev_date(moment(text).startOf("day"));
                            setShowDateTimePicker(false);
                        }}
                        onCancel={() => {
                            setShowDateTimePicker(false);
                        }}
                        maximumDate={moment(rev_date1).toDate()}
                        date={moment(rev_date).toDate()}
                    />

                    <DateTimePickerModal
                        isVisible={showDateTimePicker1}
                        mode={"date"}
                        onConfirm={(text) => {
                            setRev_date1(moment(text).endOf("day"));
                            setShowDateTimePicker1(false);
                        }}
                        onCancel={() => {
                            setShowDateTimePicker1(false);
                        }}
                        minimumDate={moment(rev_date).toDate()}
                        date={moment(rev_date1).toDate()}
                    />

                    <Modal
                        supportedOrientations={["landscape", "portrait"]}
                        style={{ flex: 1 }}
                        visible={visible}
                        transparent
                        animationType="slide"
                    >
                        <KeyboardAvoidingView
                            style={{
                                backgroundColor: "rgba(0,0,0,0.5)",
                                flex: 1,
                                justifyContent: "center",
                                alignItems: "center",
                                minHeight: windowHeight,
                            }}
                        >
                            <View style={[styles.confirmBox1, { ...getTransformForModalInsideNavigation(), }]}>
                                <Text
                                    style={{
                                        fontSize: 24,
                                        justifyContent: "center",
                                        alignSelf: "center",
                                        marginTop: 40,
                                        fontFamily: "NunitoSans-Bold",
                                    }}
                                >
                                    Enter your email
                                </Text>
                                <View
                                    style={{
                                        justifyContent: "center",
                                        alignSelf: "center",
                                        alignContent: "center",
                                        marginTop: 20,
                                        flexDirection: "row",
                                        width: "80%",
                                    }}
                                >
                                    <View
                                        style={{ justifyContent: "center", marginHorizontal: 5 }}
                                    >
                                        <Text
                                            style={{ color: Colors.descriptionColor, fontSize: 20 }}
                                        >
                                            email:
                                        </Text>
                                    </View>
                                    <TextInput
                                        underlineColorAndroid={Colors.fieldtBgColor}
                                        style={[styles.textInput8, { paddingLeft: 5 }]}
                                        placeholder="Enter your email"
                                        // style={{
                                        //     // paddingLeft: 1,
                                        // }}
                                        //defaultValue={extentionCharges}
                                        onChangeText={(text) => {
                                            // setState({ exportEmail: text });
                                            setExportEmail(text);
                                        }}
                                        placeholderTextColor={Platform.select({ ios: "#a9a9a9" })}
                                        value={exportEmail}
                                    />
                                </View>
                                <Text
                                    style={{
                                        fontSize: 20,
                                        fontFamily: "NunitoSans-Bold",
                                        marginTop: 25,
                                        justifyContent: "center",
                                        alignSelf: "center",
                                        alignContent: "center",
                                    }}
                                >
                                    Share As:
                                </Text>

                                {/* Share file using email */}
                                <View
                                    style={{
                                        justifyContent: "space-between",
                                        alignSelf: "center",
                                        marginTop: 10,
                                        flexDirection: "row",
                                        width: "80%",
                                    }}
                                >
                                    <TouchableOpacity
                                        style={[
                                            styles.modalSaveButton1,
                                            {
                                                zIndex: -1,
                                            },
                                        ]}
                                        onPress={() => { }}
                                    >
                                        <Text
                                            style={[
                                                styles.modalDescText,
                                                { color: Colors.primaryColor },
                                            ]}
                                        >
                                            Excel
                                        </Text>
                                    </TouchableOpacity>

                                    <TouchableOpacity
                                        style={[
                                            styles.modalSaveButton1,
                                            {
                                                zIndex: -1,
                                            },
                                        ]}
                                        onPress={() => { }}
                                    >
                                        <Text
                                            style={[
                                                styles.modalDescText,
                                                { color: Colors.primaryColor },
                                            ]}
                                        >
                                            CSV
                                        </Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        style={[
                                            styles.modalSaveButton1,
                                            {
                                                zIndex: -1,
                                            },
                                        ]}
                                        onPress={() => { }}
                                    >
                                        <Text
                                            style={[
                                                styles.modalDescText,
                                                { color: Colors.primaryColor },
                                            ]}
                                        >
                                            PDF
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                                <View
                                    style={{
                                        alignSelf: "center",
                                        marginTop: 20,
                                        justifyContent: "center",
                                        alignItems: "center",
                                        // width: 260,
                                        width: windowWidth * 0.2,
                                        height: 60,
                                        alignContent: "center",
                                        flexDirection: "row",
                                        marginTop: 40,
                                    }}
                                >
                                    <TouchableOpacity
                                        onPress={emailVariant}
                                        style={{
                                            backgroundColor: Colors.fieldtBgColor,
                                            width: "100%",
                                            justifyContent: "center",
                                            alignItems: "center",
                                            alignContent: "center",
                                            height: 60,
                                            borderBottomLeftRadius: 10,
                                            borderRightWidth: StyleSheet.hairlineWidth,
                                            borderTopWidth: StyleSheet.hairlineWidth,
                                        }}
                                    >
                                        <Text
                                            style={{
                                                fontSize: 22,
                                                color: Colors.primaryColor,
                                                fontFamily: "NunitoSans-SemiBold",
                                            }}
                                        >
                                            Email
                                        </Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        onPress={() => {
                                            // setState({ visible: false });
                                            setVisible(false);
                                        }}
                                        style={{
                                            backgroundColor: Colors.fieldtBgColor,
                                            width: "100%",
                                            justifyContent: "center",
                                            alignItems: "center",
                                            alignContent: "center",
                                            height: 60,
                                            borderBottomRightRadius: 10,
                                            borderTopWidth: StyleSheet.hairlineWidth,
                                        }}
                                    >
                                        <Text
                                            style={{
                                                fontSize: 22,
                                                color: Colors.descriptionColor,
                                                fontFamily: "NunitoSans-SemiBold",
                                            }}
                                        >
                                            Cancel
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </KeyboardAvoidingView>
                    </Modal>

                    <View
                        style={{
                            paddingVertical: 30,
                            marginHorizontal: 30,
                        }}
                    >
                        {/* <View style={{ flex: 1 }}> */}
                        <View
                            style={{
                                flexDirection: "row",
                                justifyContent: "space-between",
                                alignItems: "center",
                                marginHorizontal: 30,
                                marginTop: 20,
                                height: windowHeight * 0.1,
                                width: windowWidth * 0.877,
                                alignSelf: 'center',
                            }}
                        >
                            <Text
                                style={{
                                    fontSize: switchMerchant ? 20 : 26,
                                    fontFamily: "NunitoSans-Bold",
                                }}
                            >
                                Activity Log
                            </Text>
                            <View
                                style={{
                                    flexDirection: "row",
                                }}
                            >
                                <TouchableOpacity
                                    style={{
                                        justifyContent: "center",
                                        flexDirection: "row",
                                        borderWidth: 1,
                                        borderColor: Colors.primaryColor,
                                        backgroundColor: "#4E9F7D",
                                        borderRadius: 5,
                                        //width: 140,
                                        paddingHorizontal: 10,
                                        height: switchMerchant ? 35 : 40,
                                        alignItems: "center",
                                        shadowOffset: {
                                            width: 0,
                                            height: 2,
                                        },
                                        shadowOpacity: 0.22,
                                        shadowRadius: 3.22,
                                        elevation: 1,
                                        zIndex: -1,
                                        marginRight: 10,
                                    }}
                                    onPress={() => {
                                        setExportModalVisibility(true);
                                    }}
                                >
                                    <View
                                        style={{ flexDirection: "row", alignItems: "center" }}
                                    >
                                        <Icon
                                            name="download"
                                            size={switchMerchant ? 10 : 20}
                                            color={Colors.whiteColor}
                                        />
                                        <Text
                                            style={{
                                                color: Colors.whiteColor,
                                                marginLeft: 5,
                                                fontSize: switchMerchant ? 10 : 16,
                                                fontFamily: "NunitoSans-Bold",
                                            }}
                                        >
                                            DOWNLOAD
                                        </Text>
                                    </View>
                                </TouchableOpacity>

                                <View
                                    style={[
                                        {
                                            height: switchMerchant ? 35 : 40,
                                        },
                                    ]}
                                >
                                    <View
                                        style={{
                                            width: switchMerchant ? 200 : 250,
                                            height: switchMerchant ? 35 : 40,
                                            backgroundColor: "white",
                                            borderRadius: 5,
                                            flexDirection: "row",
                                            alignContent: "center",
                                            alignItems: "center",

                                            shadowColor: "#000",
                                            shadowOffset: {
                                                width: 0,
                                                height: 2,
                                            },
                                            shadowOpacity: 0.22,
                                            shadowRadius: 3.22,
                                            elevation: 3,
                                            borderWidth: 1,
                                            borderColor: "#E5E5E5",
                                        }}
                                    >
                                        <Icon
                                            name="search"
                                            size={switchMerchant ? 13 : 18}
                                            color={Colors.primaryColor}
                                            style={{ marginLeft: 15 }}
                                        />
                                        <TextInput
                                            editable={!loading}
                                            underlineColorAndroid={Colors.whiteColor}
                                            style={{
                                                width: switchMerchant ? 180 : 220,
                                                fontSize: switchMerchant ? 10 : 15,
                                                fontFamily: "NunitoSans-Regular",
                                                paddingLeft: 5,
                                                height: 45,
                                            }}
                                            placeholderTextColor={Platform.select({
                                                ios: "#a9a9a9",
                                            })}
                                            clearButtonMode="while-editing"
                                            placeholder=" Search"
                                            onChangeText={(text) => {
                                                setSearch(text);
                                            }}
                                            value={search}
                                        />
                                    </View>
                                </View>
                            </View>
                        </View>
                        <View
                            style={{
                                flexDirection: "row",
                                justifyContent: "space-between",
                                alignItems: "center",
                                marginHorizontal: 30,
                                marginTop: -5,
                                width: windowWidth * 0.877,
                                alignSelf: 'center',
                                zIndex: 9999,
                            }}
                        >
                            <TouchableOpacity
                                style={[
                                    {
                                        justifyContent: "center",
                                        flexDirection: "row",
                                        borderWidth: 1,
                                        borderColor: Colors.primaryColor,
                                        backgroundColor: "#4E9F7D",
                                        borderRadius: 5,
                                        //width: 160,
                                        paddingHorizontal: 10,
                                        height: switchMerchant ? 35 : 40,
                                        alignItems: "center",
                                        shadowOffset: {
                                            width: 0,
                                            height: 2,
                                        },
                                        shadowOpacity: 0.22,
                                        shadowRadius: 3.22,
                                        elevation: 1,
                                        zIndex: -1,

                                        opacity: !showDetails ? 0 : 100,
                                    },
                                ]}
                                onPress={() => {
                                    setShowDetails(false);
                                    setPageCount(
                                        Math.ceil(allOutletsEmployeesAction.length / perPage)
                                    );
                                    setCurrentPage(pageReturn);
                                    // console.log('Returning to page');
                                    // console.log(pageReturn);
                                }}
                                disabled={!showDetails}
                            >
                                <AntDesign
                                    name="arrowleft"
                                    size={switchMerchant ? 10 : 20}
                                    color={Colors.whiteColor}
                                    style={{}}
                                />
                                <Text
                                    style={{
                                        color: Colors.whiteColor,
                                        marginLeft: 5,
                                        fontSize: switchMerchant ? 10 : 16,
                                        fontFamily: "NunitoSans-Bold",
                                    }}
                                >
                                    Summary
                                </Text>
                            </TouchableOpacity>

                            <View style={{ flexDirection: "row" }}>
                                <View
                                    style={[
                                        {
                                            paddingHorizontal: 15,
                                            flexDirection: "row",
                                            alignItems: "center",
                                            borderRadius: 10,
                                            paddingVertical: 10,
                                            justifyContent: "center",
                                            backgroundColor: Colors.whiteColor,
                                            shadowOpacity: 0,
                                            shadowColor: "#000",
                                            shadowOffset: {
                                                width: 0,
                                                height: 2,
                                            },
                                            shadowOpacity: 0.22,
                                            shadowRadius: 3.22,
                                            elevation: 1,
                                        },
                                    ]}
                                >
                                    <View
                                        style={{ alignSelf: "center", marginRight: 5 }}
                                        onPress={() => {
                                            setState({
                                                pickerMode: "date",
                                                showDateTimePicker: true,
                                            });
                                        }}
                                    >
                                        <GCalendar
                                            width={switchMerchant ? 15 : 20}
                                            height={switchMerchant ? 15 : 20}
                                        />
                                    </View>

                                    <DatePicker
                                        selected={rev_date.toDate()}
                                        onChange={(date) => {
                                            setRev_date(moment(date));
                                        }}
                                        maxDate={moment(rev_date1).toDate()}
                                        date={moment(rev_date).toDate()}
                                    />

                                    <Text
                                        style={
                                            switchMerchant
                                                ? { fontSize: 10, fontFamily: "NunitoSans-Regular" }
                                                : { fontFamily: "NunitoSans-Regular" }
                                        }
                                    >
                                        -
                                    </Text>

                                    <DatePicker
                                        selected={rev_date1.toDate()}
                                        onChange={(date) => {
                                            setRev_date1(moment(date));
                                        }}
                                        minDate={moment(rev_date).toDate()}
                                        date={moment(rev_date1).toDate()}
                                    />
                                </View>
                            </View>
                        </View>

                        <View>
                            <View
                                style={{
                                    backgroundColor: Colors.whiteColor,
                                    width: windowWidth * 0.877,
                                    height: 450,
                                    marginTop: 10,
                                    marginBottom: 30,
                                    marginHorizontal: 30,
                                    alignSelf: 'center',
                                    borderRadius: 5,
                                    shadowOpacity: 0,
                                    shadowColor: "#000",
                                    shadowOffset: {
                                        width: 0,
                                        height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 3,
                                }}
                            >
                                {!showDetails ? (
                                    <View style={{ marginTop: 10, flexDirection: "row" }}>
                                        <View
                                            style={{
                                                flexDirection: "row",
                                                width: "5%",
                                                borderRightWidth: 1,
                                                borderRightColor: "lightgrey",
                                                alignItems: "center",
                                                justifyContent: "flex-start",
                                                paddingLeft: 10,
                                            }}
                                        >
                                            <View style={{ flexDirection: "row" }}>
                                                <View style={{ flexDirection: "column" }}>
                                                    <Text
                                                        numberOfLines={2}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: "NunitoSans-Bold",
                                                            textAlign: "left",
                                                        }}
                                                    >
                                                        {"No.\n"}
                                                    </Text>
                                                </View>
                                            </View>
                                        </View>
                                        <View
                                            style={{
                                                flexDirection: "row",
                                                width: "25%",
                                                borderRightWidth: 1,
                                                borderRightColor: "lightgrey",
                                                alignItems: "center",
                                                justifyContent: "flex-start",
                                                padding: 10,
                                            }}
                                        >
                                            <TouchableOpacity
                                                onPress={() => {
                                                    // if (
                                                    //   currReportSummarySort ===
                                                    //   REPORT_SORT_FIELD_TYPE.ADD_ON_NAME_ASC
                                                    // ) {
                                                    //   setCurrReportSummarySort(
                                                    //     REPORT_SORT_FIELD_TYPE.ADD_ON_NAME_DESC,
                                                    //   );
                                                    // } else {
                                                    //   setCurrReportSummarySort(
                                                    //     REPORT_SORT_FIELD_TYPE.ADD_ON_NAME_ASC,
                                                    //   );
                                                    // }
                                                }}
                                            >
                                                <View style={{ flexDirection: "row" }}>
                                                    <View style={{ flexDirection: "column" }}>
                                                        <Text
                                                            numberOfLines={2}
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 13,
                                                                fontFamily: "NunitoSans-Bold",
                                                                textAlign: "left",
                                                            }}
                                                        >
                                                            {"Employee Name\n"}
                                                        </Text>
                                                    </View>
                                                </View>
                                            </TouchableOpacity>
                                        </View>
                                        <View
                                            style={{
                                                flexDirection: "row",
                                                width: "25%",
                                                borderRightWidth: 1,
                                                borderRightColor: "lightgrey",
                                                alignItems: "center",
                                                justifyContent: "flex-start",
                                                padding: 10,
                                            }}
                                        >
                                            <TouchableOpacity onPress={() => { }}>
                                                <View style={{ flexDirection: "row" }}>
                                                    <View style={{ flexDirection: "column" }}>
                                                        <Text
                                                            numberOfLines={2}
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 13,
                                                                fontFamily: "NunitoSans-Bold",
                                                            }}
                                                        >
                                                            {"Number of Actions\n"}
                                                        </Text>
                                                    </View>
                                                </View>
                                            </TouchableOpacity>
                                        </View>
                                    </View>
                                ) : (
                                    <View style={{ marginTop: 10, flexDirection: "row" }}>
                                        <View
                                            style={{
                                                flexDirection: "row",
                                                width: "6%",
                                                borderRightWidth: 1,
                                                borderRightColor: "lightgrey",
                                                alignItems: "center",
                                                justifyContent: "flex-start",
                                                paddingLeft: 10,
                                            }}
                                        >
                                            <View style={{ flexDirection: "row" }}>
                                                <View style={{ flexDirection: "column" }}>
                                                    <Text
                                                        numberOfLines={2}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: "NunitoSans-Bold",
                                                            textAlign: "left",
                                                        }}
                                                    >
                                                        {"No.\n"}
                                                    </Text>
                                                </View>
                                            </View>
                                        </View>
                                        <View
                                            style={{
                                                flexDirection: "row",
                                                width: "25%",
                                                borderRightWidth: 1,
                                                borderRightColor: "lightgrey",
                                                alignItems: "center",
                                                justifyContent: "flex-start",
                                                paddingLeft: 10,
                                            }}
                                        >
                                            <TouchableOpacity onPress={() => { }}>
                                                <View style={{ flexDirection: "row" }}>
                                                    <View style={{ flexDirection: "column" }}>
                                                        <Text
                                                            numberOfLines={2}
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 13,
                                                                fontFamily: "NunitoSans-Bold",
                                                            }}
                                                        >
                                                            {"Action Type\n"}
                                                        </Text>
                                                    </View>
                                                </View>
                                            </TouchableOpacity>
                                        </View>
                                        <View
                                            style={{
                                                flexDirection: "row",
                                                width: "25%",
                                                borderRightWidth: 1,
                                                borderRightColor: "lightgrey",
                                                alignItems: "center",
                                                justifyContent: "flex-start",
                                                paddingLeft: 10,
                                            }}
                                        >
                                            <TouchableOpacity onPress={() => { }}>
                                                <View style={{ flexDirection: "row" }}>
                                                    <View style={{ flexDirection: "column" }}>
                                                        <Text
                                                            numberOfLines={2}
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 13,
                                                                fontFamily: "NunitoSans-Bold",
                                                            }}
                                                        >
                                                            {"Action Date\n"}
                                                        </Text>
                                                    </View>
                                                </View>
                                            </TouchableOpacity>
                                        </View>

                                        <View
                                            style={{
                                                flexDirection: 'row',
                                                width: '25%',
                                                borderRightWidth: 1,
                                                borderRightColor: 'lightgrey',
                                                alignItems: 'center',
                                                justifyContent: 'flex-start',
                                                padding: 10,
                                            }}>
                                            <TouchableOpacity
                                                onPress={() => {

                                                }}>
                                                <View style={{ flexDirection: 'row' }}>
                                                    <View style={{ flexDirection: 'column' }}>
                                                        <Text
                                                            numberOfLines={2}
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 13,
                                                                fontFamily: 'NunitoSans-Bold',
                                                            }}>
                                                            {'Action Details\n'}
                                                        </Text>
                                                    </View>
                                                </View>
                                            </TouchableOpacity>
                                        </View>
                                    </View>
                                )}

                                {!showDetails ? (
                                    <>
                                        {allOutletsEmployeesAction.filter((item) => {
                                            return filterItem(item);
                                        }).length > 0 ? (
                                            <FlatList
                                                showsVerticalScrollIndicator={false}
                                                ref={flatListRef}
                                                data={allOutletsEmployeesAction
                                                    .filter((item) => {
                                                        return filterItem(item);
                                                    })
                                                    .slice(
                                                        (currentPage - 1) * perPage,
                                                        currentPage * perPage
                                                    )}
                                                renderItem={renderItem}
                                                keyExtractor={(item, index) => String(index)}
                                                style={{ marginTop: 10 }}
                                            />
                                        ) : (
                                            <View
                                                style={{
                                                    height: windowHeight * 0.4,
                                                }}
                                            >
                                                <View
                                                    style={{
                                                        alignItems: "center",
                                                        justifyContent: "center",
                                                        height: "100%",
                                                    }}
                                                >
                                                    <Text style={{ color: Colors.descriptionColor }}>
                                                        - No Data Available -
                                                    </Text>
                                                </View>
                                            </View>
                                        )}
                                    </>
                                ) : (
                                    <>
                                        {allOutletsEmployeesDetails.filter((item) => {
                                            return filterItemDetails(item);
                                        }).length > 0 ? (
                                            <FlatList
                                                showsVerticalScrollIndicator={false}
                                                ref={flatListRef}
                                                data={allOutletsEmployeesDetails
                                                    .filter((item) => {
                                                        return filterItemDetails(item);
                                                    })
                                                    .slice(
                                                        (currentDetailsPage - 1) * perPage,
                                                        currentDetailsPage * perPage
                                                    )}
                                                renderItem={renderItemDetails}
                                                keyExtractor={(item, index) => String(index)}
                                                style={{ marginTop: 10 }}
                                            />
                                        ) : (
                                            <View
                                                style={{
                                                    height: windowHeight * 0.4,
                                                }}
                                            >
                                                <View
                                                    style={{
                                                        alignItems: "center",
                                                        justifyContent: "center",
                                                        height: "100%",
                                                    }}
                                                >
                                                    <Text style={{ color: Colors.descriptionColor }}>
                                                        - No Data Available -
                                                    </Text>
                                                </View>
                                            </View>
                                        )}
                                    </>
                                )}
                            </View>

                            {!showDetails ? (
                                <View
                                    style={{
                                        flexDirection: "row",
                                        marginTop: 10,
                                        width: windowWidth * 0.87,
                                        alignItems: "center",
                                        alignSelf: "center",
                                        justifyContent: "flex-end",
                                    }}
                                >
                                    <Text
                                        style={{
                                            fontSize: switchMerchant ? 10 : 14,
                                            fontFamily: "NunitoSans-Bold",
                                            marginRight: "1%",
                                        }}
                                    >
                                        Items Showed
                                    </Text>
                                    <View
                                        style={{
                                            width: Platform.OS === "ios" ? 65 : "13%", //65,
                                            height: switchMerchant ? 20 : 35,
                                            backgroundColor: Colors.whiteColor,
                                            borderRadius: 10,
                                            justifyContent: "center",
                                            paddingHorizontal: Platform.OS === "ios" ? 0 : 0,
                                            //paddingLeft:switchMerchant ? '4.5%': Platform.OS === 'ios' ? '2%' : '4%',
                                            // paddingTop: '-60%',
                                            borderWidth: 1,
                                            borderColor: "#E5E5E5",
                                            marginRight: "1%",
                                        }}
                                    >
                                        <DropDownPicker
                                            style={{
                                                backgroundColor: Colors.fieldtBgColor,
                                                width: '100%',
                                                height: 40,
                                                borderRadius: 10,
                                                borderWidth: 1,
                                                borderColor: "#E5E5E5",
                                                flexDirection: "row",
                                            }}
                                            dropDownContainerStyle={{
                                                width: '100%',
                                                backgroundColor: Colors.fieldtBgColor,
                                                borderColor: "#E5E5E5",
                                            }}
                                            labelStyle={{
                                                marginLeft: 5,
                                                flexDirection: "row",
                                            }}
                                            textStyle={{
                                                fontSize: 14,
                                                fontFamily: 'NunitoSans-Regular',

                                                marginLeft: 5,
                                                paddingVertical: 10,
                                                flexDirection: "row",
                                            }}
                                            selectedItemContainerStyle={{
                                                flexDirection: "row",
                                            }}

                                            showArrowIcon={true}
                                            ArrowDownIconComponent={({ style }) => (
                                                <Ionicon
                                                    size={25}
                                                    color={Colors.fieldtTxtColor}
                                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                                    name="chevron-down-outline"
                                                />
                                            )}
                                            ArrowUpIconComponent={({ style }) => (
                                                <Ionicon
                                                    size={25}
                                                    color={Colors.fieldtTxtColor}
                                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                                    name="chevron-up-outline"
                                                />
                                            )}

                                            showTickIcon={true}
                                            TickIconComponent={({ press }) => (
                                                <Ionicon
                                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                                    color={
                                                        press ? Colors.fieldtBgColor : Colors.primaryColor
                                                    }
                                                    name={'md-checkbox'}
                                                    size={25}
                                                />
                                            )}
                                            placeholder={'Select a Type'}
                                            placeholderStyle={{
                                                color: Colors.fieldtTxtColor,
                                                // marginTop: 15,
                                            }}
                                            // searchable
                                            // searchableStyle={{
                                            //   paddingHorizontal: windowWidth * 0.0079,
                                            // }}
                                            value={perPage}
                                            items={TABLE_PAGE_SIZE_DROPDOWN_LIST.concat({
                                                label: 'All',
                                                value: !showDetails
                                                    ? allOutletsEmployeesAction.length
                                                    : allOutletsEmployeesDetails.length,
                                            })}
                                            // multiple={true}
                                            // multipleText={`${item.tagIdList.length} Tag(s)`}
                                            onSelectItem={(item) => {
                                                setPerPage(item.value);
                                                // var currentPageTemp =
                                                //   text.length > 0 ? parseInt(text) : 1;

                                                // setCurrentPage(
                                                //   currentPageTemp > pageCount
                                                //     ? pageCount
                                                //     : currentPageTemp < 1
                                                //       ? 1
                                                //       : currentPageTemp,
                                                // );
                                            }}
                                            open={openPage}
                                            setOpen={setOpenPage}
                                            dropDownDirection="TOP"
                                        />
                                    </View>

                                    <Text
                                        style={{
                                            fontSize: switchMerchant ? 10 : 14,
                                            fontFamily: "NunitoSans-Bold",
                                            marginRight: "1%",
                                        }}
                                    >
                                        Page
                                    </Text>
                                    <View
                                        style={{
                                            width: switchMerchant ? 65 : 70,
                                            height: switchMerchant ? 20 : 35,
                                            backgroundColor: Colors.whiteColor,
                                            borderRadius: 10,
                                            justifyContent: "center",
                                            paddingHorizontal: 22,
                                            borderWidth: 1,
                                            borderColor: "#E5E5E5",
                                        }}
                                    >
                                        {console.log("currentPage")}
                                        {console.log(currentPage)}

                                        <TextInput
                                            onChangeText={(text) => {
                                                var currentPageTemp =
                                                    text.length > 0 ? parseInt(text) : 1;

                                                setCurrentPage(
                                                    currentPageTemp > pageCount
                                                        ? pageCount
                                                        : currentPageTemp < 1
                                                            ? 1
                                                            : currentPageTemp
                                                );
                                            }}
                                            placeholder={
                                                pageCount !== 0 ? currentPage.toString() : "0"
                                            }
                                            placeholderTextColor={Platform.select({
                                                ios: "#a9a9a9",
                                            })}
                                            style={{
                                                color: "black",
                                                fontSize: switchMerchant ? 10 : 14,
                                                fontFamily: "NunitoSans-Regular",
                                                marginTop: Platform.OS === "ios" ? 0 : -15,
                                                marginBottom: Platform.OS === "ios" ? 0 : -15,
                                                textAlign: "center",
                                                width: "100%",
                                            }}
                                            value={pageCount !== 0 ? currentPage.toString() : "0"}
                                            defaultValue={
                                                pageCount !== 0 ? currentPage.toString() : "0"
                                            }
                                            keyboardType={"numeric"}
                                            onFocus={() => {
                                                setPushPagingToTop(true);
                                            }}
                                        />
                                    </View>
                                    <Text
                                        style={{
                                            fontSize: switchMerchant ? 10 : 14,
                                            fontFamily: "NunitoSans-Bold",
                                            marginLeft: "1%",
                                            marginRight: "1%",
                                        }}
                                    >
                                        of {pageCount}
                                    </Text>
                                    <TouchableOpacity
                                        style={{
                                            width: switchMerchant ? 30 : 45,
                                            height: switchMerchant ? 20 : 28,
                                            backgroundColor: Colors.primaryColor,
                                            alignItems: "center",
                                            justifyContent: "center",
                                        }}
                                        onPress={() => {
                                            prevPage();
                                        }}
                                    >
                                        <ArrowLeft color={Colors.whiteColor} />
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        style={{
                                            width: switchMerchant ? 30 : 45,
                                            height: switchMerchant ? 20 : 28,
                                            backgroundColor: Colors.primaryColor,
                                            alignItems: "center",
                                            justifyContent: "center",
                                        }}
                                        onPress={() => {
                                            nextPage();
                                        }}
                                    >
                                        <ArrowRight color={Colors.whiteColor} />
                                    </TouchableOpacity>
                                </View>
                            ) : (
                                <View
                                    style={{
                                        flexDirection: "row",
                                        marginTop: 10,
                                        width: windowWidth * 0.87,
                                        alignItems: "center",
                                        alignSelf: "center",
                                        justifyContent: "flex-end",
                                    }}
                                >
                                    <Text
                                        style={{
                                            fontSize: switchMerchant ? 10 : 14,
                                            fontFamily: "NunitoSans-Bold",
                                            marginRight: "1%",
                                        }}
                                    >
                                        Items Showed
                                    </Text>
                                    <View
                                        style={{
                                            width: Platform.OS === "ios" ? 65 : "13%", //65,
                                            height: switchMerchant ? 20 : 35,
                                            backgroundColor: Colors.whiteColor,
                                            borderRadius: 10,
                                            justifyContent: "center",
                                            paddingHorizontal: Platform.OS === "ios" ? 0 : 0,
                                            //paddingLeft:switchMerchant ? '4.5%': Platform.OS === 'ios' ? '2%' : '4%',
                                            // paddingTop: '-60%',
                                            borderWidth: 1,
                                            borderColor: "#E5E5E5",
                                            marginRight: "1%",
                                        }}
                                    >
                                        <DropDownPicker
                                            style={{
                                                backgroundColor: Colors.fieldtBgColor,
                                                width: '100%',
                                                height: 40,
                                                borderRadius: 10,
                                                borderWidth: 1,
                                                borderColor: "#E5E5E5",
                                                flexDirection: "row",
                                            }}
                                            dropDownContainerStyle={{
                                                width: '100%',
                                                backgroundColor: Colors.fieldtBgColor,
                                                borderColor: "#E5E5E5",
                                            }}
                                            labelStyle={{
                                                marginLeft: 5,
                                                flexDirection: "row",
                                            }}
                                            textStyle={{
                                                fontSize: 14,
                                                fontFamily: 'NunitoSans-Regular',

                                                marginLeft: 5,
                                                paddingVertical: 10,
                                                flexDirection: "row",
                                            }}
                                            selectedItemContainerStyle={{
                                                flexDirection: "row",
                                            }}

                                            showArrowIcon={true}
                                            ArrowDownIconComponent={({ style }) => (
                                                <Ionicon
                                                    size={25}
                                                    color={Colors.fieldtTxtColor}
                                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                                    name="chevron-down-outline"
                                                />
                                            )}
                                            ArrowUpIconComponent={({ style }) => (
                                                <Ionicon
                                                    size={25}
                                                    color={Colors.fieldtTxtColor}
                                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                                    name="chevron-up-outline"
                                                />
                                            )}

                                            showTickIcon={true}
                                            TickIconComponent={({ press }) => (
                                                <Ionicon
                                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                                    color={
                                                        press ? Colors.fieldtBgColor : Colors.primaryColor
                                                    }
                                                    name={'md-checkbox'}
                                                    size={25}
                                                />
                                            )}
                                            placeholder={'Select a Type'}
                                            placeholderStyle={{
                                                color: Colors.fieldtTxtColor,
                                                // marginTop: 15,
                                            }}
                                            // searchable
                                            // searchableStyle={{
                                            //   paddingHorizontal: windowWidth * 0.0079,
                                            // }}
                                            value={perPage}
                                            items={TABLE_PAGE_SIZE_DROPDOWN_LIST.concat({
                                                label: 'All',
                                                value: !showDetails
                                                    ? allOutletsEmployeesAction.length
                                                    : allOutletsEmployeesDetails.length,
                                            })}
                                            // multiple={true}
                                            // multipleText={`${item.tagIdList.length} Tag(s)`}
                                            onSelectItem={(item) => {
                                                setPerPage(item.value);
                                                // var currentPageTemp =
                                                //   text.length > 0 ? parseInt(text) : 1;

                                                // setCurrentPage(
                                                //   currentPageTemp > pageCount
                                                //     ? pageCount
                                                //     : currentPageTemp < 1
                                                //       ? 1
                                                //       : currentPageTemp,
                                                // );
                                            }}
                                            open={openPage}
                                            setOpen={setOpenPage}
                                            dropDownDirection="TOP"
                                        />
                                    </View>

                                    <Text
                                        style={{
                                            fontSize: switchMerchant ? 10 : 14,
                                            fontFamily: "NunitoSans-Bold",
                                            marginRight: "1%",
                                        }}
                                    >
                                        Page
                                    </Text>
                                    <View
                                        style={{
                                            width: switchMerchant ? 65 : 70,
                                            height: switchMerchant ? 20 : 35,
                                            backgroundColor: Colors.whiteColor,
                                            borderRadius: 10,
                                            justifyContent: "center",
                                            paddingHorizontal: 22,
                                            borderWidth: 1,
                                            borderColor: "#E5E5E5",
                                        }}
                                    >
                                        {console.log("currentDetailsPage")}
                                        {console.log(currentDetailsPage)}

                                        <TextInput
                                            onChangeText={(text) => {
                                                var currentPageTemp =
                                                    text.length > 0 ? parseInt(text) : 1;
                                                // console.log('currentDetailsPage pending');
                                                // console.log(
                                                //   currentPageTemp > pageCount
                                                //     ? pageCount
                                                //     : currentPageTemp < 1
                                                //       ? 1
                                                //       : currentPageTemp,
                                                // );
                                                setCurrentDetailsPage(
                                                    currentPageTemp > pageCount
                                                        ? pageCount
                                                        : currentPageTemp < 1
                                                            ? 1
                                                            : currentPageTemp
                                                );
                                            }}
                                            placeholder={
                                                pageCount !== 0
                                                    ? currentDetailsPage.toString()
                                                    : "0"
                                            }
                                            placeholderTextColor={Platform.select({
                                                ios: "#a9a9a9",
                                            })}
                                            style={{
                                                color: "black",
                                                fontSize: switchMerchant ? 10 : 14,
                                                fontFamily: "NunitoSans-Regular",
                                                marginTop: Platform.OS === "ios" ? 0 : -15,
                                                marginBottom: Platform.OS === "ios" ? 0 : -15,
                                                textAlign: "center",
                                                width: "100%",
                                            }}
                                            value={
                                                pageCount !== 0
                                                    ? currentDetailsPage.toString()
                                                    : "0"
                                            }
                                            defaultValue={
                                                pageCount !== 0
                                                    ? currentDetailsPage.toString()
                                                    : "0"
                                            }
                                            keyboardType={"numeric"}
                                            onFocus={() => {
                                                setPushPagingToTop(true);
                                            }}
                                        />
                                    </View>
                                    <Text
                                        style={{
                                            fontSize: switchMerchant ? 10 : 14,
                                            fontFamily: "NunitoSans-Bold",
                                            marginLeft: "1%",
                                            marginRight: "1%",
                                        }}
                                    >
                                        of {pageCount}
                                    </Text>
                                    <TouchableOpacity
                                        style={{
                                            width: switchMerchant ? 30 : 45,
                                            height: switchMerchant ? 20 : 28,
                                            backgroundColor: Colors.primaryColor,
                                            alignItems: "center",
                                            justifyContent: "center",
                                        }}
                                        onPress={() => {
                                            prevDetailsPage();
                                        }}
                                    >
                                        <ArrowLeft color={Colors.whiteColor} />
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        style={{
                                            width: switchMerchant ? 30 : 45,
                                            height: switchMerchant ? 20 : 28,
                                            backgroundColor: Colors.primaryColor,
                                            alignItems: "center",
                                            justifyContent: "center",
                                        }}
                                        onPress={() => {
                                            nextDetailsPage();
                                        }}
                                    >
                                        <ArrowRight color={Colors.whiteColor} />
                                    </TouchableOpacity>
                                </View>
                            )}
                        </View>
                        {/* </View> */}
                    </View>
                </ScrollView>
            </View>
        </View>
        ///</UserIdleWrapper >
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.highlightColor,
        flexDirection: "row",
    },
    sidebar: {
        // width: Dimensions.get("window").width * Styles.sideBarWidth,
        // shadowColor: "#000",
        // shadowOffset: {
        //     width: 0,
        //     height: 8,
        // },
        // shadowOpacity: 0.44,
        // shadowRadius: 10.32,

        // elevation: 16,
    },
    content: {
        padding: 20,
        width: Dimensions.get("window").width * (1 - Styles.sideBarWidth),
        backgroundColor: Colors.highlightColor,
    },
    headerLogo: {
        width: 112,
        height: 25,
        marginLeft: 10,
    },
    confirmBox: {
        // width: '30%',
        // height: '30%',
        // borderRadius: 30,
        // backgroundColor: Colors.whiteColor,
        width: Dimensions.get("window").width * 0.4,
        height: Dimensions.get("window").height * 0.3,
        borderRadius: 12,
        backgroundColor: Colors.whiteColor,
        justifyContent: "space-between",
    },
    modalContainer: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: "center",
        justifyContent: "center",
    },
    modalView: {
        height: Dimensions.get("window").width * 0.2,
        width: Dimensions.get("window").width * 0.3,
        backgroundColor: Colors.whiteColor,
        borderRadius: 12,
        padding: Dimensions.get("window").width * 0.03,
        alignItems: "center",
        justifyContent: "center",
    },
    closeButton: {
        position: "absolute",
        right: Dimensions.get("window").width * 0.02,
        top: Dimensions.get("window").width * 0.02,

        elevation: 1000,
        zIndex: 1000,
    },
    modalTitle: {
        alignItems: "center",
        top: "20%",
        position: "absolute",
    },
    modalBody: {
        flex: 1,
        alignItems: "center",
        justifyContent: "center",
    },
    modalTitleText: {
        fontFamily: "NunitoSans-Bold",
        textAlign: "center",
        fontSize: 20,
    },
    modalDescText: {
        fontFamily: "NunitoSans-SemiBold",
        fontSize: 18,
        color: Colors.fieldtTxtColor,
    },
    modalBodyText: {
        flex: 1,
        fontFamily: "NunitoSans-SemiBold",
        fontSize: 25,
        width: "20%",
    },
    modalSaveButton: {
        width: Dimensions.get("window").width * 0.15,
        backgroundColor: Colors.fieldtBgColor,
        height: 40,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 8,

        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 1,

        marginVertical: 10,
    },
    modalSaveButton1: {
        width: Dimensions.get("window").width * 0.1,
        backgroundColor: Colors.fieldtBgColor,
        height: 40,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 8,

        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 1,

        marginVertical: 10,
    },
    confirmBox1: {
        width: Dimensions.get("window").width * 0.4,
        height: Dimensions.get("window").height * 0.4,
        borderRadius: 12,
        backgroundColor: Colors.whiteColor,
        justifyContent: "space-between",
    },
    submitText: {
        height:
            Platform.OS == "ios"
                ? Dimensions.get("window").height * 0.06
                : Dimensions.get("window").height * 0.07,
        paddingVertical: 5,
        paddingHorizontal: 20,
        flexDirection: "row",
        color: "#4cd964",
        textAlign: "center",
        borderRadius: 10,
        borderWidth: 1,
        borderColor: Colors.primaryColor,
        justifyContent: "center",
        alignContent: "center",
        alignItems: "center",
        marginRight: 10,
    },
    headerLeftStyle: {
        width: useWindowDimensions.width * 0.17,
        justifyContent: "center",
        alignItems: "center",
    },
});
export default ReportActivityLog;
