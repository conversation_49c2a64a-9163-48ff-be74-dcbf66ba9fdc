import React, {
    Component,
    useReducer,
    useState,
    useEffect,
    useRef,
} from "react";
import {
    StyleSheet,
    ScrollView,
    Image,
    View,
    Text,
    Alert,
    TouchableOpacity,
    Dimensions,
    Platform,
    Switch,
    Modal,
    KeyboardAvoidingView,
    TextInput,
    ActivityIndicator,
    Picker,
    useWindowDimensions,
    TouchableWithoutFeedback,
} from "react-native";
import Colors from "../constant/Colors";
import SideBar from "./SideBar";
import Icon from "react-native-vector-icons/Feather";
// import Ionicons from 'react-native-vector-icons/Ionicons';
import AntDesign from "react-native-vector-icons/AntDesign";
import Entypo from "react-native-vector-icons/Entypo";
import { ReactComponent as GCalendar } from "../assets/svg/GCalendar.svg";
import MaterialIcons from "react-native-vector-icons/MaterialIcons";
// import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { FlatList } from "react-native-gesture-handler";
import API from "../constant/API";
import ApiClient from "../util/ApiClient";
import Styles from "../constant/Styles";
// import * as User from '../util/User';
import AsyncStorage from "@react-native-async-storage/async-storage";
// import CheckBox from 'react-native-check-box';
import moment from "moment";
import DateTimePickerModal from "react-native-modal-datetime-picker";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import "../constant/datePicker.css";
// import {isTablet} from 'react-native-device-detection';
import { CommonStore } from "../store/commonStore";
import { OutletStore } from "../store/outletStore";
import { MerchantStore } from '../store/merchantStore';
import { UserStore } from "../store/userStore";
// import Upload from '../assets/svg/Upload';
// import Download from '../assets/svg/Download';
import {
    convertArrayToCSV,
    countWeekdayOccurrencesInMonth,
    generateEmailReport,
    sortReportDataList,
    isMobile,
} from "../util/common";
import {
    EMAIL_REPORT_TYPE,
    REPORT_SORT_FIELD_TYPE,
    TABLE_PAGE_SIZE_DROPDOWN_LIST,
    ORDER_TYPE,
    MERCHANT_DATA_FILTER,
    APP_TYPE,
    ORDER_TYPE_DETAILS,
    PAYMENT_CHANNEL_NAME_PARSED,
    OFFLINE_PAYMENT_METHOD_TYPE
} from "../constant/common";
// import RNFetchBlob from 'rn-fetch-blob';
// import {useKeyboard} from '../hooks';
// import XLSX from 'xlsx';
import "react-native-get-random-values";
import { v4 as uuidv4 } from "uuid";
// import RNPickerSelect from 'react-native-picker-select';
// import AsyncImage from '../components/asyncImage';
// import Feather from 'react-native-vector-icons/Feather';
// import Tooltip from 'react-native-walkthrough-tooltip';

import firebase from "firebase/app";
// import firestore from '@react-native-firebase/firestore';
import { Collections } from "../constant/firebase";

import { ReactComponent as ArrowLeft } from "../assets/svg/ArrowLeft.svg";
import { ReactComponent as ArrowRight } from "../assets/svg/ArrowRight.svg";
import { useLinkTo } from "@react-navigation/native";

import BigNumber from "bignumber.js";

import FusionCharts from "react-fusioncharts";
import FC from "fusioncharts";
import Column2D from "fusioncharts/fusioncharts.charts";
import FusionTheme from "fusioncharts/themes/fusioncharts.theme.fusion";
import {
    CHART_DATA,
    CHART_TYPE,
    //FS_LIBRARY_PATH,
    CHART_Y_AXIS_DROPDOWN_LIST,
    CHART_FIELD_COMPARE_DROPDOWN_LIST,
    CHART_FIELD_NAME_DROPDOWN_LIST,
    CHART_FIELD_TYPE,
    CHART_FIELD_COMPARE_DICT,
    CHART_PERIOD,
    CHART_X_AXIS_DROPDOWN_LIST,
    CHART_X_AXIS_TYPE,
} from "../constant/chart";
import {
    filterChartItems,
    getDataForChartDashboardTodaySales,
    getDataForChartReportOnlineQrSales,
    getDataForChartReportProductSales,
    getDataForSalesLineChart,
} from "../util/chart";
import XLSX from "xlsx";
import headerLogo from "../assets/image/logo.png";
import personicon from "../assets/image/default-profile.png";
import ApiClientReporting from '../util/ApiClientReporting';
import DropDownPicker from 'react-native-dropdown-picker';
import Ionicon from 'react-native-vector-icons/Ionicons';
import { TempStore } from '../store/tempStore';

FusionCharts.fcRoot(FC, Column2D, FusionTheme);

const { nanoid } = require("nanoid");
// const RNFS = require('react-native-fs');

window.pOutletLastOrderDict = {};
window.pOutletFilterDict = {};

window.pOutletPaletteColorDict = {};

window.boldOutletQrSalesLineDict = {};

window.currToDateTime = Date.now();

const KooDooPayoutScreen = (props) => {
    const { navigation } = props;

    // const linkTo = useLinkTo();

    // const rootFocusRef = useRef(null);

    const { height: windowHeight, width: windowWidth } = useWindowDimensions();

    const keyboardHeight = 0;
    const [visible, setVisible] = useState(false);
    const [perPage, setPerPage] = useState(100);
    const [pageCount, setPageCount] = useState(0);
    const [currentPage, setCurrentPage] = useState(1);
    const [currentDetailsPage, setCurrentDetailsPage] = useState(1);
    const [switchMerchant, setSwitchMerchant] = useState(false);
    const [pageReturn, setPageReturn] = useState(1);
    const [search, setSearch] = useState("");

    const [loading, setLoading] = useState(false);

    const [pushPagingToTop, setPushPagingToTop] = useState(false);

    const [showDateTimePicker, setShowDateTimePicker] = useState(false);
    const [showDateTimePicker1, setShowDateTimePicker1] = useState(false);

    // const [ptStartDate, setRev_date] = useState(
    //   moment().subtract(6, "month").startOf("day")
    // );
    // const [ptEndDate, setRev_date1] = useState(
    //   moment().endOf(Date.now()).endOf("day")
    // );

    const userName = UserStore.useState((s) => s.name);

    const [exportEmail, setExportEmail] = useState("");

    const [showDetails, setShowDetails] = useState(false);

    const [exportModalVisibility, setExportModalVisibility] = useState(false);

    const merchantId = UserStore.useState((s) => s.merchantId);
    const isLoading = CommonStore.useState((s) => s.isLoading);
    const [isCsv, setIsCsv] = useState(false);
    const [isExcel, setIsExcel] = useState(false);

    const outletSelectDropdownView = CommonStore.useState(
        (s) => s.outletSelectDropdownView
    );

    const allOutletsEmployees = OutletStore.useState(
        (s) => s.allOutletsEmployees
    );

    const [allOutletsEmployeesAction, setAllOutletsEmployeesAction] = useState(
        []
    );

    const [allOutletsEmployeesDetails, setAllOutletsEmployeesDetails] = useState(
        []
    );

    const [tableDataSummary, setTableDataSummary] = useState([]);
    const [tableDataDetails, setTableDataDetails] = useState([]);

    const [toggleCompare, setToggleCompare] = useState(true);

    // const [boldOutletQrSalesLineDict, setBoldOutletQrSalesLineDict] = useState({});
    const [changedBoldTimestamp, setChangedBoldTimestamp] = useState('');

    /////////////////////////////////////////////////////////////////

    // const [merchantDataFilterType, setMerchantDataFilterType] = useState(MERCHANT_DATA_FILTER.ALL);
    const [merchantDataFilterType, setMerchantDataFilterType] = useState('')
    const [merchantDataAllLength, setMerchantDataAllLength] = useState(0);
    const [merchantData24HLength, setMerchantData24HLength] = useState(0);
    const [merchantData7DLength, setMerchantData7DLength] = useState(0);
    const [merchantData1MLength, setMerchantData1MLength] = useState(0);
    const [merchantData3MLength, setMerchantData3MLength] = useState(0);

    const [outletUserActionDict, setOutletUserActionDict] = useState({});

    /////////////////////////////////////////////////////////////////

    // 2022-10-24 - Add the support of changing date range also will update details list

    const [showDetailsOutletId, setShowDetailsOutletId] = useState('');

    /////////////////////////////////////////////////////////////////

    const allOutlets = MerchantStore.useState(s => s.allOutlets);

    const merchantsOnboarded = CommonStore.useState(s => s.merchantsOnboarded);
    const outletsOnboarded = CommonStore.useState(s => s.outletsOnboarded);

    const employeeClocks = CommonStore.useState(s => s.employeeClocks);
    const userActions = CommonStore.useState(s => s.userActions);

    const userEmail = UserStore.useState((s) => s.email);

    const ptStartDate = CommonStore.useState(s => s.historyStartDate);
    const ptEndDate = CommonStore.useState(s => s.historyEndDate);
    // const payoutTransactions = OutletStore.useState(s => s.payoutTransactions);
    // const payoutTransactionsExtend = OutletStore.useState(s => s.payoutTransactionsExtend);
    const reportOutletIdList = CommonStore.useState((s) => s.reportOutletIdList);

    const ptTimestamp = OutletStore.useState(s => s.ptTimestamp);
    const pteTimestamp = OutletStore.useState(s => s.pteTimestamp);

    const gmvOrdersRecent = CommonStore.useState(s => s.gmvOrdersRecent); // for those orders that haven't included in razer payout transactions

    const [payoutTransactions, setPayoutTransactions] = useState([]);
    const [payoutTransactionsExtend, setPayoutTransactionsExtend] = useState([]);

    //////////////////////////////////////////////

    // 2022-12-10 - Chart related

    const [
        showDateTimePickerFilterLineChart,
        setShowDateTimePickerFilterLineChart,
    ] = useState(false);
    const [
        showDateTimePickerFilterBarChart,
        setShowDateTimePickerFilterBarChart,
    ] = useState(false);

    const [todaySalesChart, setTodaySalesChart] = useState({});

    // const [todaySalesChartPeriod, setTodaySalesChartPeriod] = useState(CHART_PERIOD.TODAY);

    const [salesLineChart, setSalesLineChart] = useState({});
    const [productSalesChart, setProductSalesChart] = useState({});

    const [onlineQrSalesChart, setOnlineQrSalesChart] = useState({});

    const [salesLineChartPeriod, setSalesLineChartPeriod] = useState(
        CHART_PERIOD.NONE
    );
    const [salesBarChartPeriod, setSalesBarChartPeriod] = useState(
        CHART_PERIOD.NONE
    );

    const [dailySalesDetailsList, setDailySalesDetailsList] = useState([]);

    const [selectedItemSummary, setSelectedItemSummary] = useState({});

    const [selectedChartDropdownValueX, setSelectedChartDropdownValueX] =
        useState(
            CHART_X_AXIS_DROPDOWN_LIST[CHART_TYPE.DASHBOARD_LINE_CHART_SALES][0].value
        );

    const [chartDropdownValueXList, setChartDropdownValueXList] = useState(
        CHART_X_AXIS_DROPDOWN_LIST[CHART_TYPE.DASHBOARD_LINE_CHART_SALES]
    );

    const [expandBarSelection, setExpandBarSelection] = useState(
        props.expandBarSelection === undefined ? false : props.expandBarSelection
    );
    // const [barFilterTapped, setBarFilterTapped] = useState(props.barFilterTapped === undefined ? 0 : props.barFilterTapped);
    const [expandLineSelection, setExpandLineSelection] = useState(false);
    // const [lineFilterTapped, setLineFilterTapped] = useState(props.lineFilterTapped === undefined ? 0 : props.lineFilterTapped);

    const [
        selectedChartDropdownValueLineChart,
        setSelectedChartDropdownValueLineChart,
    ] = useState(
        CHART_Y_AXIS_DROPDOWN_LIST[CHART_TYPE.DASHBOARD_LINE_CHART_SALES][0].value
    );
    const [
        selectedChartFilterQueriesLineChart,
        setSelectedChartFilterQueriesLineChart,
    ] = useState([
        {
            fieldNameKey:
                CHART_FIELD_NAME_DROPDOWN_LIST[CHART_TYPE.DASHBOARD_LINE_CHART_SALES][0]
                    .value,
            fieldNameType:
                CHART_FIELD_NAME_DROPDOWN_LIST[CHART_TYPE.DASHBOARD_LINE_CHART_SALES][0]
                    .fieldType,
            fieldCompare:
                CHART_FIELD_COMPARE_DROPDOWN_LIST[
                    CHART_TYPE.DASHBOARD_LINE_CHART_SALES
                ][0].value,
            fieldDataValue: null,
        },
    ]);
    const [
        appliedChartFilterQueriesLineChart,
        setAppliedChartFilterQueriesLineChart,
    ] = useState([]);

    const [
        selectedChartDropdownValueBarChart,
        setSelectedChartDropdownValueBarChart,
    ] = useState(
        CHART_Y_AXIS_DROPDOWN_LIST[CHART_TYPE.DASHBOARD_TODAY_SALES][0].value
    );
    const [
        selectedChartFilterQueriesBarChart,
        setSelectedChartFilterQueriesBarChart,
    ] = useState([
        {
            fieldNameKey:
                CHART_FIELD_NAME_DROPDOWN_LIST[CHART_TYPE.DASHBOARD_TODAY_SALES][0]
                    .value,
            fieldNameType:
                CHART_FIELD_NAME_DROPDOWN_LIST[CHART_TYPE.DASHBOARD_TODAY_SALES][0]
                    .fieldType,
            fieldCompare:
                CHART_FIELD_COMPARE_DROPDOWN_LIST[CHART_TYPE.DASHBOARD_TODAY_SALES][0]
                    .value,
            fieldDataValue: null,
        },
    ]);
    const [
        appliedChartFilterQueriesBarChart,
        setAppliedChartFilterQueriesBarChart,
    ] = useState([]);

    const [currReportSummarySort, setCurrReportSummarySort] = useState("");
    const [currReportDetailsSort, setCurrReportDetailsSort] = useState("");

    ////////////////////////////////////////////////////////

    // 2022-12-11 - Click event

    const [clickedChartDate, setClickedChartDate] = useState(null);

    ////////////////////////////////////////////////////////

    // 2022-12-11 - Expand event

    const [expandedSummaryRow, setExpandedSummaryRow] = useState({});

    ////////////////////////////////////////////////////////

    // 2023-01-04 - For clicking stacked bar chart (pos/qr offline/qr online profit)

    const [clickedBarChartDate, setClickedBarChartDate] = useState(null);
    const [clickedBarChartDateUnit, setClickedBarChartDateUnit] = useState(null);

    ////////////////////////////////////////////////////////

    const isMasterAccount = UserStore.useState(s => s.isMasterAccount);
    const currOutletId = MerchantStore.useState(s => s.currOutletId);
    // const [openOS, setOpenOS] = useState(false);
    // const [outletDropdownList, setOutletDropdownList] = useState([]);
    // const [selectedOutletId, setSelectedOutletId] = useState("");
    // const [allOutlets, setAllOutlets] = useState([]);
    // const selectedOutletList = CommonStore.useState((s) => s.reportOutletIdList);

    ////////////////////////////////////////////////////////

    // setInterval(() => {
    //     var elementList = document.querySelectorAll('[fill="#b1b2b7"]');

    //     for (var i = 0; i < elementList.length; i++) {
    //         elementList[i].style.display = 'none';
    //     }

    //     var elementLegendList = document.querySelectorAll('[opacity="0.7"]');

    //     for (var i = 0; i < elementLegendList.length; i++) {
    //         elementLegendList[i].style['stroke-width'] = '5';
    //     }
    // }, 250);

    // useEffect(async () => {
    //     const fetchPayoutTransactions = async () => {
    //         try {
    //             OutletStore.update(s => {
    //                 s.reportingApiLoading = true;
    //             });

    //             const body = {
    //                 isMasterAccount,
    //                 merchantId,
    //                 outletId: currOutletId,
    //                 // reportOutletIdList,
    //                 reportOutletIdList: allOutlets.map(outlet => outlet.uniqueId),
    //                 startDate: ptStartDate,
    //                 endDate: ptEndDate,
    //             };

    //             const result = await ApiClientReporting.POST(API.getRazerPayoutTransactionsParsed, body, false);

    //             if (result?.status === 'success') {
    //                 OutletStore.update(s => {
    //                     s.reportingApiLoading = false;

    //                     s.ptTimestamp = Date.now();
    //                     s.pteTimestamp = Date.now();
    //                 });

    //                 return {
    //                     payoutTransactions: result.payoutTransactions,
    //                     payoutTransactionsExtend: result.payoutTransactionsExtend
    //                 };
    //             }

    //             OutletStore.update(s => {
    //                 s.reportingApiLoading = false;

    //                 s.ptTimestamp = Date.now();
    //                 s.pteTimestamp = Date.now();
    //             });

    //             return {
    //                 payoutTransactions: [],
    //                 payoutTransactionsExtend: []
    //             };

    //         } catch (error) {
    //             OutletStore.update(s => {
    //                 s.reportingApiLoading = false;

    //                 s.ptTimestamp = Date.now();
    //                 s.pteTimestamp = Date.now();
    //             });

    //             console.error('Error fetching payout transactions:', error);
    //             return {
    //                 payoutTransactions: [],
    //                 payoutTransactionsExtend: []
    //             };
    //         }
    //     }

    //     const data = await fetchPayoutTransactions();

    //     setPayoutTransactions(data.payoutTransactions);
    //     setPayoutTransactionsExtend(data.payoutTransactionsExtend);
    // }, [
    //     ptStartDate,
    //     ptEndDate,
    //     merchantId,
    //     currOutletId,
    // ]);

    useEffect(() => {
        setChartDropdownValueXList(
            CHART_X_AXIS_DROPDOWN_LIST[CHART_TYPE.DASHBOARD_LINE_CHART_SALES].filter(
                (xAxisType) => {
                    if (salesLineChartPeriod === CHART_PERIOD.THIS_WEEK) {
                        if (
                            xAxisType.value === CHART_X_AXIS_TYPE.WEEK ||
                            xAxisType.value === CHART_X_AXIS_TYPE.MONTH
                        ) {
                            return false;
                        } else {
                            return true;
                        }
                    } else if (salesLineChartPeriod === CHART_PERIOD.THIS_MONTH) {
                        if (xAxisType.value === CHART_X_AXIS_TYPE.MONTH) {
                            return false;
                        } else {
                            return true;
                        }
                    } else if (salesLineChartPeriod === CHART_PERIOD.THREE_MONTHS) {
                        return true;
                    } else if (salesLineChartPeriod === CHART_PERIOD.SIX_MONTHS) {
                        if (xAxisType.value === CHART_X_AXIS_TYPE.DAY) {
                            return false;
                        } else {
                            return true;
                        }
                    } else if (salesLineChartPeriod === CHART_PERIOD.THIS_YEAR) {
                        if (xAxisType.value === CHART_X_AXIS_TYPE.DAY) {
                            return false;
                        } else {
                            return true;
                        }
                    } else if (salesLineChartPeriod === CHART_PERIOD.YTD) {
                        if (xAxisType.value === CHART_X_AXIS_TYPE.DAY) {
                            return false;
                        } else {
                            return true;
                        }
                    }
                }
            )
        );
    }, [salesLineChartPeriod]);

    const retrieveLatestUserAction = async (outletId) => {
        const userActionSnapshot = await firebase.firestore()
            .collection(Collections.UserAction)
            .where('outletId', '==', outletId)
            .orderBy('updatedAt', 'desc')
            .limit(1)
            .get();

        var userAction = null;
        if (userActionSnapshot && !userActionSnapshot.empty) {
            userAction = userActionSnapshot.docs[0].data();
        }

        return userAction;
    };

    useEffect(() => {
        if (showDetails && tableDataDetails) {
            setPageReturn(currentPage);
            console.log("currentPage value is");
            console.log(currentPage);
            setCurrentDetailsPage(1);
            setPageCount(Math.ceil(tableDataDetails.length / perPage));
        }
    }, [showDetails, tableDataDetails, perPage]);

    useEffect(async () => {
        const outletFilterDictRaw = await AsyncStorage.getItem('outletFilterDict');

        if (outletFilterDictRaw) {
            const outletFilterDictParsed = JSON.parse(outletFilterDictRaw);

            if (typeof outletFilterDictParsed === 'object') {
                window.pOutletFilterDict = outletFilterDictParsed;
            }
        }
    }, []);

    ///////////////////////////////////////////////////////////////////////////////

    const checkOutletFilterDict = (outlet, orderDateTime, dateTimeConst) => {
        const {
            startDate24H,
            endDate24H,
            startDate7D,
            endDate7D,
            startDate1M,
            endDate1M,
            startDate3M,
            endDate3M,
        } = dateTimeConst;

        if (!window.pOutletFilterDict[outlet.uniqueId]) {
            window.pOutletFilterDict[outlet.uniqueId] = {
                is24H: false,
                is7D: false,
                is1M: false,
                is3M: false,
            };
        }

        if (moment(startDate24H).isSameOrBefore(orderDateTime) &&
            moment(endDate24H).isAfter(orderDateTime)) {
            window.pOutletFilterDict[outlet.uniqueId]['is24H'] = true;
        }

        if (moment(startDate7D).isSameOrBefore(orderDateTime) &&
            moment(endDate7D).isAfter(orderDateTime)) {
            window.pOutletFilterDict[outlet.uniqueId]['is7D'] = true;
        }
        if (moment(startDate1M).isSameOrBefore(orderDateTime) &&
            moment(endDate1M).isAfter(orderDateTime)) {
            window.pOutletFilterDict[outlet.uniqueId]['is1M'] = true;
        }

        if (moment(startDate3M).isSameOrBefore(orderDateTime) &&
            moment(endDate3M).isAfter(orderDateTime)) {
            window.pOutletFilterDict[outlet.uniqueId]['is3M'] = true;
        }
    }

    // useEffect(async () => {
    //   const tempData = allOutletsEmployeesAction.map(merchant => {
    //     // var outlet = outletsOnboarded.find(outlet => outlet.merchantId === merchant.uniqueId);

    //     var employeeClock = userActions.find(clock => clock.merchantId === merchant.merchantId);      

    //     if (employeeClock) {
    //       return {
    //         ...merchant,
    //         lastActivity: employeeClock.updatedAt > merchant.lastActivity ? employeeClock.updatedAt : merchant.lastActivity,
    //       };
    //     }
    //     else {
    //       return merchant;
    //     }
    //   });

    //   const tempKCRMarray = tempData;

    //   const isEqual = JSON.prune(allOutletsEmployeesAction) === JSON.prune(tempKCRMarray);

    //   if (!isEqual) {
    //     setAllOutletsEmployeesAction(tempKCRMarray);

    //     setPageCount(Math.ceil(tempKCRMarray.length / perPage));
    //   }
    // }, [allOutletsEmployeesAction, userActions]);

    // useEffect(async () => {
    //   const tempData = allOutletsEmployeesAction.map(merchant => {
    //     // var outlet = outletsOnboarded.find(outlet => outlet.merchantId === merchant.uniqueId);

    //     var employeeClock = employeeClocks.find(clock => clock.merchantId === merchant.merchantId);

    //     if (employeeClock) {
    //       return {
    //         ...merchant,
    //         lastActivity: employeeClock.updatedAt > merchant.lastActivity ? employeeClock.updatedAt : merchant.lastActivity,
    //       };
    //     }
    //     else {
    //       return merchant;
    //     }
    //   });

    //   const tempKCRMarray = tempData;

    //   const isEqual = JSON.prune(allOutletsEmployeesAction) === JSON.prune(tempKCRMarray);

    //   if (!isEqual) {
    //     setAllOutletsEmployeesAction(tempKCRMarray);

    //     setPageCount(Math.ceil(tempKCRMarray.length / perPage));
    //   }
    // }, [allOutletsEmployeesAction, employeeClocks]);

    ///////////////////////////////////////////////////////////////////////////////////

    // useEffect(async () => {
    //   const tempData = allOutletsEmployeesAction.map(async merchant => {
    //     var outlet = outletsOnboarded.find(outlet => outlet.merchantId === merchant.uniqueId);

    //     const employeeClockSnapshot = await firebase.firestore().collection(Collections.EmployeeClock)
    //       .where('merchantId', '==', merchant.merchantId)
    //       .orderBy('updatedAt', 'desc')
    //       .limit(1)
    //       .get();

    //     var employeeClock = null;
    //     if (!employeeClockSnapshot.empty) {
    //       employeeClock = employeeClockSnapshot.docs[0].data();
    //     }

    //     if (employeeClock) {
    //       return {
    //         ...merchant,
    //         lastActivity: employeeClock.updatedAt,
    //       };
    //     }
    //     else {
    //       return merchant;
    //     }
    //   });

    //   const tempKCRMarray = tempData;

    //   const isEqual = JSON.prune(allOutletsEmployeesAction) === JSON.prune(tempKCRMarray);

    //   if (!isEqual) {
    //     setAllOutletsEmployeesAction(tempKCRMarray);

    //     setPageCount(Math.ceil(tempKCRMarray.length / perPage));
    //   }
    // }, [allOutletsEmployeesAction]);

    ///////////////////////////////////////////////////////////////////////////////////

    const setState = () => { };

    const nextPage = () => {
        setCurrentPage(currentPage + 1 > pageCount ? currentPage : currentPage + 1);
    };

    const prevPage = () => {
        setCurrentPage(currentPage - 1 < 1 ? currentPage : currentPage - 1);
    };

    const nextDetailsPage = () => {
        setCurrentDetailsPage(
            currentDetailsPage + 1 > pageCount
                ? currentDetailsPage
                : currentDetailsPage + 1
        );
    };

    const prevDetailsPage = () => {
        setCurrentDetailsPage(
            currentDetailsPage - 1 < 1 ? currentDetailsPage : currentDetailsPage - 1
        );
    };

    const renderItem = ({ item, index }) => {
        return (
            <TouchableOpacity
                onPress={() => {
                    setShowDetails(true);
                    setTableDataDetails(item.detailsList);

                    setShowDetailsOutletId(item.outletId);
                }}
                style={{
                    backgroundColor:
                        (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
                    paddingVertical: 10,
                    paddingHorizontal: 3,
                    paddingLeft: 1,
                    borderColor: "#BDBDBD",
                    borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
                    borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
                }}
            >
                <View style={{ flexDirection: "row" }}>
                    <Text
                        style={{
                            width: "2%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Regular",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {index + 1}
                    </Text>
                    <Text
                        style={{
                            width: "8%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Regular",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {item.merchantName}
                    </Text>

                    <Text
                        style={{
                            width: "7%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Regular",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {`RM ${item.userAppAndWebDineInOnlineOrdersAmount.toFixed(2)} (${item.userAppAndWebDineInOnlineOrdersQty.toFixed(0)})`}
                    </Text>

                    <Text
                        style={{
                            width: "7%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Regular",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {`RM ${item.userAppAndWebDineInOrdersAmount.toFixed(2)} (${item.userAppAndWebDineInOrdersQty.toFixed(0)})`}
                    </Text>

                    <Text
                        style={{
                            width: "7%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Regular",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {`RM ${item.merchantAppOrdersAmount.toFixed(2)} (${item.merchantAppOrdersQty.toFixed(0)})`}
                    </Text>
                    <Text
                        style={{
                            width: "7%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Regular",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {`RM ${item.waiterAppOrdersAmount.toFixed(2)} (${item.waiterAppOrdersQty.toFixed(0)})`}
                    </Text>
                    <Text
                        style={{
                            width: "7%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Regular",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {`RM ${item.userAppAndWebTakeawayOrdersAmount.toFixed(2)} (${item.userAppAndWebTakeawayOrdersQty.toFixed(0)})`}
                    </Text>
                    {/* <Text
              style={{
                width: "7%",
                fontSize: switchMerchant ? 10 : 13,
                fontFamily: "NunitoSans-Regular",
                textAlign: "left",
                paddingLeft: 10,
              }}
            >
              {`RM ${item.userAppAndWebDeliveryOrdersAmount.toFixed(2)} (${item.userAppAndWebDeliveryOrdersQty.toFixed(0)})`}
            </Text> */}
                    <Text
                        style={{
                            width: "7%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Regular",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {`RM ${item.totalOrdersAmount.toFixed(2)} (${item.totalOrdersQty.toFixed(0)})`}
                    </Text>
                    <Text
                        style={{
                            width: "7%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Bold",
                            textAlign: "left",
                            paddingLeft: 10,

                            color: Colors.primaryColor,
                        }}
                    >
                        {`RM ${item.gmvCommissionActual.toFixed(2)}`}
                    </Text>
                    <Text
                        style={{
                            width: "7%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Regular",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {`RM ${item.gmvPayoutActual.toFixed(2)}`}
                    </Text>
                    <Text
                        style={{
                            width: "7%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Regular",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {`RM ${item.gmvCommission.toFixed(2)}`}
                    </Text>
                    <Text
                        style={{
                            width: "7%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Regular",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {`RM ${item.gmvPayout.toFixed(2)}`}
                    </Text>
                    <Text
                        style={{
                            width: "7%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Regular",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {`RM ${item.gmvFees.toFixed(2)}`}
                    </Text>
                    <Text
                        style={{
                            width: "8%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Regular",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {item.lastOrderPlacedDateTime ? moment(item.lastOrderPlacedDateTime).format('YYYY-MM-DD hh:mm A') : 'N/A'}
                    </Text>

                    <TouchableOpacity
                        style={{
                            width: "5%",
                            // fontSize: switchMerchant ? 10 : 13,
                            // fontFamily: "NunitoSans-Regular",
                            // textAlign: "left",
                            paddingLeft: '1.5%',

                            // backgroundColor: 'red',
                        }}
                        onPress={() => {
                            setExpandedSummaryRow({
                                ...expandedSummaryRow,
                                [item.outletId]: expandedSummaryRow[item.outletId] ? false : true,
                            });
                        }}
                    >
                        {
                            !expandedSummaryRow[item.outletId]
                                ?
                                <Icon
                                    name="chevron-up"
                                    size={15}
                                    color={Colors.primaryColor}
                                // style={{ marginLeft: 15 }}
                                />
                                :
                                <Icon
                                    name="chevron-down"
                                    size={15}
                                    color={Colors.primaryColor}
                                // style={{ marginLeft: 15 }}
                                />
                        }
                    </TouchableOpacity>
                </View>

                {expandedSummaryRow[item.outletId] == true ? (
                    <View
                        style={{
                            minHeight: windowHeight * 0.35,
                            marginTop: 20,
                            paddingBottom: 20,

                            // backgroundColor: 'red',
                        }}>

                        <View style={{ marginTop: 10, flexDirection: "row" }}>
                            <View
                                style={{
                                    flexDirection: "row",
                                    width: "2%",
                                    borderRightWidth: 1,
                                    borderRightColor: "lightgrey",
                                    alignItems: "center",
                                    justifyContent: "flex-start",
                                    paddingLeft: 10,
                                }}
                            >
                                <View
                                >
                                    <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                        <View style={{ flexDirection: "column" }}>
                                            <Text
                                                numberOfLines={3}
                                                style={{
                                                    fontSize: switchMerchant ? 10 : 13,
                                                    fontFamily: "NunitoSans-Bold",
                                                    color: Colors.darkBgColor,
                                                }}
                                            >
                                                {"No.\n\n"}
                                            </Text>
                                            <Text
                                                style={{
                                                    fontSize: switchMerchant ? 8 : 10,
                                                    color: Colors.descriptionColor,
                                                }}
                                            ></Text>
                                        </View>
                                        <View style={{ marginLeft: "3%", opacity: 0 }}>
                                            <Text
                                                style={{
                                                    fontSize: 10,
                                                    color: Colors.descriptionColor,
                                                }}
                                            ></Text>
                                        </View>
                                    </View>
                                </View>
                            </View>
                            <View
                                style={{
                                    flexDirection: "row",
                                    width: "8%",
                                    borderRightWidth: 1,
                                    borderRightColor: "lightgrey",
                                    alignItems: "center",
                                    justifyContent: "flex-start",
                                    padding: 10,
                                }}
                            >
                                <TouchableOpacity
                                    onPress={() => {
                                    }}
                                >
                                    <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                        <View style={{ flexDirection: "column" }}>
                                            <Text
                                                numberOfLines={3}
                                                style={{
                                                    fontSize: switchMerchant ? 10 : 13,
                                                    fontFamily: "NunitoSans-Bold",
                                                    color: Colors.darkBgColor,
                                                }}
                                            >
                                                {"Settlement\nDate\n"}
                                            </Text>
                                            <Text
                                                style={{
                                                    fontSize: switchMerchant ? 8 : 10,
                                                    color: Colors.descriptionColor,
                                                }}
                                            ></Text>
                                        </View>
                                        <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                            <Text
                                                style={{
                                                    fontSize: 10,
                                                    color: Colors.descriptionColor,
                                                }}
                                            ></Text>
                                        </View>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            <View
                                style={{
                                    flexDirection: "row",
                                    width: "7%",
                                    borderRightWidth: 1,
                                    borderRightColor: "lightgrey",
                                    alignItems: "center",
                                    justifyContent: "flex-start",
                                    padding: 10,
                                }}
                            >
                                <TouchableOpacity
                                    onPress={() => {
                                    }}
                                >
                                    <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                        <View style={{ flexDirection: "column" }}>
                                            <Text
                                                numberOfLines={3}
                                                style={{
                                                    fontSize: switchMerchant ? 10 : 13,
                                                    fontFamily: "NunitoSans-Bold",
                                                    color: Colors.darkBgColor,
                                                }}
                                            >
                                                {"Prev.\nFunds\n"}
                                            </Text>
                                            <Text
                                                style={{
                                                    fontSize: switchMerchant ? 8 : 10,
                                                    color: Colors.descriptionColor,
                                                }}
                                            ></Text>
                                        </View>
                                        <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                            <Text
                                                style={{
                                                    fontSize: 10,
                                                    color: Colors.descriptionColor,
                                                }}
                                            ></Text>
                                        </View>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            <View
                                style={{
                                    flexDirection: "row",
                                    width: "7%",
                                    borderRightWidth: 1,
                                    borderRightColor: "lightgrey",
                                    alignItems: "center",
                                    justifyContent: "flex-start",
                                    padding: 10,
                                }}
                            >
                                <TouchableOpacity
                                    onPress={() => {
                                    }}
                                >
                                    <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                        <View style={{ flexDirection: "column" }}>
                                            <Text
                                                numberOfLines={3}
                                                style={{
                                                    fontSize: switchMerchant ? 10 : 13,
                                                    fontFamily: "NunitoSans-Bold",
                                                    color: Colors.darkBgColor,
                                                }}
                                            >
                                                {"Prev.\nOverdue\n"}
                                            </Text>
                                            <Text
                                                style={{
                                                    fontSize: switchMerchant ? 8 : 10,
                                                    color: Colors.descriptionColor,
                                                }}
                                            ></Text>
                                        </View>
                                        <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                            <Text
                                                style={{
                                                    fontSize: 10,
                                                    color: Colors.descriptionColor,
                                                }}
                                            ></Text>
                                        </View>
                                    </View>
                                </TouchableOpacity>
                            </View>
                            <View
                                style={{
                                    flexDirection: "row",
                                    width: "7%",
                                    borderRightWidth: 1,
                                    borderRightColor: "lightgrey",
                                    alignItems: "center",
                                    justifyContent: "flex-start",
                                    padding: 10,
                                }}
                            >
                                <TouchableOpacity
                                    onPress={() => {
                                    }}
                                >
                                    <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                        <View style={{ flexDirection: "column" }}>
                                            <Text
                                                numberOfLines={3}
                                                style={{
                                                    fontSize: switchMerchant ? 10 : 13,
                                                    fontFamily: "NunitoSans-Bold",
                                                    color: Colors.darkBgColor,
                                                }}
                                            >
                                                {/* {"Prev.\nPending\n"} */}
                                                {"N/A\n\n"}
                                            </Text>
                                            <Text
                                                style={{
                                                    fontSize: switchMerchant ? 8 : 10,
                                                    color: Colors.descriptionColor,
                                                }}
                                            ></Text>
                                        </View>
                                        <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                            <Text
                                                style={{
                                                    fontSize: 10,
                                                    color: Colors.descriptionColor,
                                                }}
                                            ></Text>
                                        </View>
                                    </View>
                                </TouchableOpacity>
                            </View>

                            <View
                                style={{
                                    flexDirection: "row",
                                    width: "7%",
                                    borderRightWidth: 1,
                                    borderRightColor: "lightgrey",
                                    alignItems: "center",
                                    justifyContent: "flex-start",
                                    padding: 10,
                                }}
                            >
                                <TouchableOpacity
                                    onPress={() => {
                                    }}
                                >
                                    <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                        <View style={{ flexDirection: "column" }}>
                                            <Text
                                                numberOfLines={3}
                                                style={{
                                                    fontSize: switchMerchant ? 10 : 13,
                                                    fontFamily: "NunitoSans-Bold",
                                                    color: Colors.darkBgColor,
                                                }}
                                            >
                                                {"Curr.\nFunds\n"}
                                            </Text>
                                            <Text
                                                style={{
                                                    fontSize: switchMerchant ? 8 : 10,
                                                    color: Colors.descriptionColor,
                                                }}
                                            ></Text>
                                        </View>
                                        <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                            <Text
                                                style={{
                                                    fontSize: 10,
                                                    color: Colors.descriptionColor,
                                                }}
                                            ></Text>
                                        </View>
                                    </View>
                                </TouchableOpacity>
                            </View>

                            <View
                                style={{
                                    flexDirection: "row",
                                    width: "7%",
                                    borderRightWidth: 1,
                                    borderRightColor: "lightgrey",
                                    alignItems: "center",
                                    justifyContent: "flex-start",
                                    padding: 10,
                                }}
                            >
                                <TouchableOpacity
                                    onPress={() => {
                                    }}
                                >
                                    <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                        <View style={{ flexDirection: "column" }}>
                                            <Text
                                                numberOfLines={3}
                                                style={{
                                                    fontSize: switchMerchant ? 10 : 13,
                                                    fontFamily: "NunitoSans-Bold",
                                                    color: Colors.darkBgColor,
                                                }}
                                            >
                                                {"Curr.\nOverdue\n"}
                                            </Text>
                                            <Text
                                                style={{
                                                    fontSize: switchMerchant ? 8 : 10,
                                                    color: Colors.descriptionColor,
                                                }}
                                            ></Text>
                                        </View>
                                        <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                            <Text
                                                style={{
                                                    fontSize: 10,
                                                    color: Colors.descriptionColor,
                                                }}
                                            ></Text>
                                        </View>
                                    </View>
                                </TouchableOpacity>
                            </View>

                            <View
                                style={{
                                    flexDirection: "row",
                                    width: "7%",
                                    borderRightWidth: 1,
                                    borderRightColor: "lightgrey",
                                    alignItems: "center",
                                    justifyContent: "flex-start",
                                    padding: 10,
                                }}
                            >
                                <TouchableOpacity
                                    onPress={() => {
                                    }}
                                >
                                    <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                        <View style={{ flexDirection: "column" }}>
                                            <Text
                                                numberOfLines={3}
                                                style={{
                                                    fontSize: switchMerchant ? 10 : 13,
                                                    fontFamily: "NunitoSans-Bold",
                                                    color: Colors.darkBgColor,
                                                }}
                                            >
                                                {/* {"Curr.\nPending\n"} */}
                                                {"N/A\n\n"}
                                            </Text>
                                            <Text
                                                style={{
                                                    fontSize: switchMerchant ? 8 : 10,
                                                    color: Colors.descriptionColor,
                                                }}
                                            ></Text>
                                        </View>
                                        <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                            {/* <Entypo
                          name="triangle-up"
                          size={switchMerchant ? 7 : 14}
                          color={Colors.descriptionColor}
                        ></Entypo>
  
                        <Entypo
                          name="triangle-down"
                          size={switchMerchant ? 7 : 14}
                          color={Colors.descriptionColor}
                        ></Entypo> */}
                                            <Text
                                                style={{
                                                    fontSize: 10,
                                                    color: Colors.descriptionColor,
                                                }}
                                            ></Text>
                                        </View>
                                    </View>
                                </TouchableOpacity>
                            </View>

                            {/* /////////////////////// */}

                            {/* 2022-12-10 - New columns */}

                            <View
                                style={{
                                    flexDirection: "row",
                                    width: "7%",
                                    borderRightWidth: 1,
                                    borderRightColor: "lightgrey",
                                    alignItems: "center",
                                    justifyContent: "flex-start",
                                    padding: 10,
                                }}
                            >
                                <TouchableOpacity
                                    onPress={() => {
                                    }}
                                >
                                    <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                        <View style={{ flexDirection: "column" }}>
                                            <Text
                                                numberOfLines={3}
                                                style={{
                                                    fontSize: switchMerchant ? 10 : 13,
                                                    fontFamily: "NunitoSans-Bold",

                                                    color: Colors.primaryColor,
                                                }}
                                            >
                                                {"GMV\nCommission\n(Payable)"}
                                            </Text>
                                            <Text
                                                style={{
                                                    fontSize: switchMerchant ? 8 : 10,
                                                    color: Colors.descriptionColor,
                                                }}
                                            ></Text>
                                        </View>
                                        <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                            <Text
                                                style={{
                                                    fontSize: 10,
                                                    color: Colors.descriptionColor,
                                                }}
                                            ></Text>
                                        </View>
                                    </View>
                                </TouchableOpacity>
                            </View>

                            <View
                                style={{
                                    flexDirection: "row",
                                    width: "7%",
                                    borderRightWidth: 1,
                                    borderRightColor: "lightgrey",
                                    alignItems: "center",
                                    justifyContent: "flex-start",
                                    padding: 10,
                                }}
                            >
                                <TouchableOpacity
                                    onPress={() => {
                                    }}
                                >
                                    <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                        <View style={{ flexDirection: "column" }}>
                                            <Text
                                                numberOfLines={3}
                                                style={{
                                                    fontSize: switchMerchant ? 10 : 13,
                                                    fontFamily: "NunitoSans-Bold",
                                                    color: Colors.darkBgColor,
                                                }}
                                            >
                                                {"O. Sales\nPayout\n(Actual)"}
                                            </Text>
                                            <Text
                                                style={{
                                                    fontSize: switchMerchant ? 8 : 10,
                                                    color: Colors.descriptionColor,
                                                }}
                                            ></Text>
                                        </View>
                                        <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                            <Text
                                                style={{
                                                    fontSize: 10,
                                                    color: Colors.descriptionColor,
                                                }}
                                            ></Text>
                                        </View>
                                    </View>
                                </TouchableOpacity>
                            </View>

                            {/* 2022-11-26 - New columns */}

                            <View
                                style={{
                                    flexDirection: "row",
                                    width: "7%",
                                    borderRightWidth: 1,
                                    borderRightColor: "lightgrey",
                                    alignItems: "center",
                                    justifyContent: "flex-start",
                                    padding: 10,
                                }}
                            >
                                <TouchableOpacity
                                    onPress={() => {
                                    }}
                                >
                                    <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                        <View style={{ flexDirection: "column" }}>
                                            <Text
                                                numberOfLines={3}
                                                style={{
                                                    fontSize: switchMerchant ? 10 : 13,
                                                    fontFamily: "NunitoSans-Bold",
                                                    color: Colors.darkBgColor,
                                                }}
                                            >
                                                {"GMV\nCommission\n(Total)"}
                                            </Text>
                                            <Text
                                                style={{
                                                    fontSize: switchMerchant ? 8 : 10,
                                                    color: Colors.descriptionColor,
                                                }}
                                            ></Text>
                                        </View>
                                        <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                            <Text
                                                style={{
                                                    fontSize: 10,
                                                    color: Colors.descriptionColor,
                                                }}
                                            ></Text>
                                        </View>
                                    </View>
                                </TouchableOpacity>
                            </View>

                            <View
                                style={{
                                    flexDirection: "row",
                                    width: "7%",
                                    borderRightWidth: 1,
                                    borderRightColor: "lightgrey",
                                    alignItems: "center",
                                    justifyContent: "flex-start",
                                    padding: 10,
                                }}
                            >
                                <TouchableOpacity
                                    onPress={() => {
                                    }}
                                >
                                    <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                        <View style={{ flexDirection: "column" }}>
                                            <Text
                                                numberOfLines={3}
                                                style={{
                                                    fontSize: switchMerchant ? 10 : 13,
                                                    fontFamily: "NunitoSans-Bold",
                                                    color: Colors.darkBgColor,
                                                }}
                                            >
                                                {"O. Sales\nPayout\n(Expected)"}
                                            </Text>
                                            <Text
                                                style={{
                                                    fontSize: switchMerchant ? 8 : 10,
                                                    color: Colors.descriptionColor,
                                                }}
                                            ></Text>
                                        </View>
                                        <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                            <Text
                                                style={{
                                                    fontSize: 10,
                                                    color: Colors.descriptionColor,
                                                }}
                                            ></Text>
                                        </View>
                                    </View>
                                </TouchableOpacity>
                            </View>

                            <View
                                style={{
                                    flexDirection: "row",
                                    width: "7%",
                                    borderRightWidth: 1,
                                    borderRightColor: "lightgrey",
                                    alignItems: "center",
                                    justifyContent: "flex-start",
                                    padding: 10,
                                }}
                            >
                                <TouchableOpacity
                                    onPress={() => {
                                    }}
                                >
                                    <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                        <View style={{ flexDirection: "column" }}>
                                            <Text
                                                numberOfLines={3}
                                                style={{
                                                    fontSize: switchMerchant ? 10 : 13,
                                                    fontFamily: "NunitoSans-Bold",
                                                    color: Colors.darkBgColor,
                                                }}
                                            >
                                                {"GMV\nFees\n"}
                                            </Text>
                                            <Text
                                                style={{
                                                    fontSize: switchMerchant ? 8 : 10,
                                                    color: Colors.descriptionColor,
                                                }}
                                            ></Text>
                                        </View>
                                        <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                            <Text
                                                style={{
                                                    fontSize: 10,
                                                    color: Colors.descriptionColor,
                                                }}
                                            ></Text>
                                        </View>
                                    </View>
                                </TouchableOpacity>
                            </View>

                            {/* /////////////////////// */}

                            <View
                                style={{
                                    flexDirection: "row",
                                    width: "8%",
                                    borderRightWidth: 1,
                                    borderRightColor: "lightgrey",
                                    alignItems: "center",
                                    justifyContent: "flex-start",
                                    padding: 10,
                                }}
                            >
                                <TouchableOpacity
                                    onPress={() => {
                                    }}
                                >
                                    <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                        <View style={{ flexDirection: "column" }}>
                                            <Text
                                                numberOfLines={3}
                                                style={{
                                                    fontSize: switchMerchant ? 10 : 13,
                                                    fontFamily: "NunitoSans-Bold",
                                                    color: Colors.darkBgColor,
                                                }}
                                            >
                                                {"GMV\nCommission\n(Online)"}
                                            </Text>
                                            <Text
                                                style={{
                                                    fontSize: switchMerchant ? 8 : 10,
                                                    color: Colors.descriptionColor,
                                                }}
                                            ></Text>
                                        </View>
                                        <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                            <Text
                                                style={{
                                                    fontSize: 10,
                                                    color: Colors.descriptionColor,
                                                }}
                                            ></Text>
                                        </View>
                                    </View>
                                </TouchableOpacity>
                            </View>

                            <View
                                style={{
                                    flexDirection: "row",
                                    width: "5%",
                                    borderRightWidth: 1,
                                    borderRightColor: "lightgrey",
                                    alignItems: "center",
                                    justifyContent: "flex-start",
                                    padding: 10,
                                }}
                            >
                                <View>
                                    <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                        <View style={{ flexDirection: "column" }}>
                                            <Text
                                                numberOfLines={3}
                                                style={{
                                                    fontSize: switchMerchant ? 10 : 13,
                                                    fontFamily: "NunitoSans-Bold",
                                                    color: Colors.darkBgColor,
                                                }}
                                            >
                                                {"O. Sales\nPayout\nFee"}
                                            </Text>
                                            <Text
                                                style={{
                                                    fontSize: switchMerchant ? 8 : 10,
                                                    color: Colors.descriptionColor,
                                                }}
                                            ></Text>
                                        </View>
                                        <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                            <Text
                                                style={{
                                                    fontSize: 10,
                                                    color: Colors.descriptionColor,
                                                }}
                                            ></Text>
                                        </View>
                                    </View>
                                </View>
                            </View>
                        </View>

                        <View style={{
                            marginTop: 10,
                        }}>

                        </View>
                        {
                            (
                                item.razerPayoutTransactionList.concat(item.recentPayoutTransactionList)
                            ).sort((a, b) => a.settlementDate - b.settlementDate).map((transaction, transactionIndex) => {
                                return (
                                    <View
                                        // onPress={() => {
                                        //   setShowDetails(true);
                                        //   setTableDataDetails(item.detailsList);

                                        //   setShowDetailsOutletId(item.outletId);
                                        // }}
                                        style={{
                                            backgroundColor:
                                                (transactionIndex + 1) % 2 == 0 ? Colors.lightGrey : '#d1d1d1',
                                            paddingVertical: 10,
                                            paddingHorizontal: 3,
                                            paddingLeft: 1,
                                            borderColor: "#BDBDBD",
                                            borderTopWidth: (transactionIndex + 1) % 2 == 0 ? 0 : 0.5,
                                            borderBottomWidth: (transactionIndex + 1) % 2 == 0 ? 0 : 0.5,
                                        }}
                                    >
                                        <View style={{ flexDirection: "row" }}>
                                            <Text
                                                style={{
                                                    width: "2%",
                                                    fontSize: switchMerchant ? 10 : 13,
                                                    fontFamily: "NunitoSans-Regular",
                                                    textAlign: "left",
                                                    paddingLeft: 10,
                                                    color: Colors.darkBgColor,
                                                }}
                                            >
                                                {transactionIndex + 1}
                                            </Text>
                                            <Text
                                                style={{
                                                    width: "8%",
                                                    fontSize: switchMerchant ? 10 : 13,
                                                    fontFamily: "NunitoSans-Regular",
                                                    textAlign: "left",
                                                    paddingLeft: 10,
                                                    color: Colors.darkBgColor,
                                                }}
                                                dataSet={{
                                                    uniqueId: transaction.uniqueId,
                                                }}
                                            >
                                                {transaction.settlementDate ? moment(transaction.settlementDate).format('YYYY-MM-DD hh:mm A') : 'N/A'}
                                                {/* {transaction.uniqueId} */}
                                            </Text>
                                            <Text
                                                style={{
                                                    width: "7%",
                                                    fontSize: switchMerchant ? 10 : 13,
                                                    fontFamily: "NunitoSans-Regular",
                                                    textAlign: "left",
                                                    paddingLeft: 10,
                                                    color: Colors.darkBgColor,
                                                }}
                                            >
                                                {transaction.prevStockUpAmount !== undefined ? `RM ${transaction.prevStockUpAmount.toFixed(2)}` : 'N/A'}
                                            </Text>
                                            <Text
                                                style={{
                                                    width: "7%",
                                                    fontSize: switchMerchant ? 10 : 13,
                                                    fontFamily: "NunitoSans-Regular",
                                                    textAlign: "left",
                                                    paddingLeft: 10,
                                                    color: Colors.darkBgColor,
                                                }}
                                            >
                                                {transaction.prevOverdueAmount !== undefined ? `RM ${transaction.prevOverdueAmount.toFixed(2)}` : 'N/A'}
                                            </Text>
                                            <Text
                                                style={{
                                                    width: "7%",
                                                    fontSize: switchMerchant ? 10 : 13,
                                                    fontFamily: "NunitoSans-Regular",
                                                    textAlign: "left",
                                                    paddingLeft: 10,
                                                    color: Colors.darkBgColor,
                                                }}
                                            >
                                                {/* {transaction.prevPendingAmount !== undefined ? `RM ${transaction.prevPendingAmount.toFixed(2)}` : 'N/A'} */}
                                                {'N/A'}
                                            </Text>
                                            <Text
                                                style={{
                                                    width: "7%",
                                                    fontSize: switchMerchant ? 10 : 13,
                                                    fontFamily: "NunitoSans-Regular",
                                                    textAlign: "left",
                                                    paddingLeft: 10,
                                                    color: Colors.darkBgColor,
                                                }}
                                            >
                                                {transaction.stockUpAmount !== undefined ? `RM ${transaction.stockUpAmount.toFixed(2)}` : 'N/A'}
                                            </Text>
                                            <Text
                                                style={{
                                                    width: "7%",
                                                    fontSize: switchMerchant ? 10 : 13,
                                                    fontFamily: "NunitoSans-Regular",
                                                    textAlign: "left",
                                                    paddingLeft: 10,
                                                    color: Colors.darkBgColor,
                                                }}
                                            >
                                                {transaction.outletCycleMerchantOverdueAmounts !== undefined ? `RM ${transaction.outletCycleMerchantOverdueAmounts.toFixed(2)}` : 'N/A'}
                                            </Text>
                                            <Text
                                                style={{
                                                    width: "7%",
                                                    fontSize: switchMerchant ? 10 : 13,
                                                    fontFamily: "NunitoSans-Regular",
                                                    textAlign: "left",
                                                    paddingLeft: 10,
                                                    color: Colors.darkBgColor,
                                                }}
                                            >
                                                {/* {transaction.outletCycleMerchantPendingAmounts !== undefined ? `RM ${transaction.outletCycleMerchantPendingAmounts.toFixed(2)}` : 'N/A'} */}
                                                {'N/A'}
                                            </Text>
                                            <Text
                                                style={{
                                                    width: "7%",
                                                    fontSize: switchMerchant ? 10 : 13,
                                                    fontFamily: "NunitoSans-Bold",
                                                    textAlign: "left",
                                                    paddingLeft: 10,

                                                    color: Colors.primaryColor,
                                                }}
                                            >
                                                {transaction.outletCycleKoodooPayoutsActual !== undefined ? `RM ${transaction.outletCycleKoodooPayoutsActual.toFixed(2)}` : 'N/A'}
                                            </Text>
                                            <Text
                                                style={{
                                                    width: "7%",
                                                    fontSize: switchMerchant ? 10 : 13,
                                                    fontFamily: "NunitoSans-Regular",
                                                    textAlign: "left",
                                                    paddingLeft: 10,
                                                    color: Colors.darkBgColor,
                                                }}
                                            >
                                                {transaction.outletCycleMerchantPayoutsActual !== undefined ? `RM ${transaction.outletCycleMerchantPayoutsActual.toFixed(2)}` : 'N/A'}
                                            </Text>
                                            <Text
                                                style={{
                                                    width: "7%",
                                                    fontSize: switchMerchant ? 10 : 13,
                                                    fontFamily: "NunitoSans-Regular",
                                                    textAlign: "left",
                                                    paddingLeft: 10,
                                                    color: Colors.darkBgColor,
                                                }}
                                            >
                                                {transaction.outletCycleKoodooPayoutsExpected !== undefined ? `RM ${transaction.outletCycleKoodooPayoutsExpected.toFixed(2)}` : 'N/A'}
                                            </Text>
                                            <Text
                                                style={{
                                                    width: "7%",
                                                    fontSize: switchMerchant ? 10 : 13,
                                                    fontFamily: "NunitoSans-Regular",
                                                    textAlign: "left",
                                                    paddingLeft: 10,
                                                    color: Colors.darkBgColor,
                                                }}
                                            >
                                                {transaction.outletCycleMerchantPayoutsExpected !== undefined ? `RM ${transaction.outletCycleMerchantPayoutsExpected.toFixed(2)}` : 'N/A'}
                                            </Text>
                                            <Text
                                                style={{
                                                    width: "7%",
                                                    fontSize: switchMerchant ? 10 : 13,
                                                    fontFamily: "NunitoSans-Regular",
                                                    textAlign: "left",
                                                    paddingLeft: 10,
                                                    color: Colors.darkBgColor,
                                                }}
                                            >
                                                {transaction.outletCycleRazerPayouts !== undefined ? `RM ${transaction.outletCycleRazerPayouts.toFixed(2)}` : 'N/A'}
                                            </Text>
                                            <Text
                                                style={{
                                                    width: "8%",
                                                    fontSize: switchMerchant ? 10 : 13,
                                                    fontFamily: "NunitoSans-Regular",
                                                    textAlign: "left",
                                                    paddingLeft: 10,
                                                    color: Colors.darkBgColor,
                                                }}
                                            >
                                                {transaction.outletCycleKoodooPayoutsOnlineOnlyExpected !== undefined ? `RM ${transaction.outletCycleKoodooPayoutsOnlineOnlyExpected.toFixed(2)}` : 'N/A'}
                                            </Text>

                                            <Text
                                                style={{
                                                    width: "5%",
                                                    fontSize: switchMerchant ? 10 : 13,
                                                    fontFamily: "NunitoSans-Regular",
                                                    textAlign: "left",
                                                    paddingLeft: 10,
                                                    color: Colors.darkBgColor,
                                                }}
                                            >
                                                {transaction.payoutFee !== undefined ? `RM ${transaction.payoutFee.toFixed(2)}` : 'N/A'}
                                            </Text>
                                        </View>
                                    </View>
                                );
                            })
                        }
                    </View>
                ) : null}
            </TouchableOpacity>
        );
    };

    const renderItemDetails = ({ item, index }) => {
        return (
            <View
                style={{
                    backgroundColor:
                        (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
                    paddingVertical: 10,
                    borderColor: "#BDBDBD",
                    borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
                    borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
                }}
            >
                <View style={{ flexDirection: "row" }}>
                    <Text
                        style={{
                            width: "8%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Regular",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {item.dateTime}
                    </Text>
                    <Text
                        style={{
                            width: "8%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Regular",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                        dataSet={{
                            uniqueId: item.orderId,
                        }}
                    >
                        {item.orderId}
                        {/* {item.uniqueId} */}
                        {/* {`${item.orderId} ${item.uniqueId}`} */}
                    </Text>
                    <Text
                        style={{
                            width: "8%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Regular",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {item.isQrOrder}
                    </Text>
                    {/* <Text
              style={{
                width: "8%",
                fontSize: switchMerchant ? 10 : 13,
                fontFamily: "NunitoSans-Regular",
                textAlign: "left",
                paddingLeft: 10,
              }}
            >
              {`RM ${item.netAmount.toFixed(2)}`}
            </Text> */}
                    {/* <Text
              style={{
                width: "8%",
                fontSize: switchMerchant ? 10 : 13,
                fontFamily: "NunitoSans-Regular",
                textAlign: "left",
                paddingLeft: 10,
              }}
            >
              {`RM ${item.tax.toFixed(2)}`}
            </Text> */}
                    {/* <Text
              style={{
                width: "8%",
                fontSize: switchMerchant ? 10 : 13,
                fontFamily: "NunitoSans-Regular",
                textAlign: "left",
                paddingLeft: 10,
              }}
            >
              {`RM ${item.sc.toFixed(2)}`}
            </Text> */}
                    <Text
                        style={{
                            width: "8%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Regular",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {`RM ${item.totalAmount.toFixed(2)}`}
                    </Text>
                    <Text
                        style={{
                            width: "8%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Regular",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {`RM ${item.razerCharges.toFixed(2)}`}
                    </Text>
                    <Text
                        style={{
                            width: "8%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Regular",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {`RM ${item.koodooCharges.toFixed(2)}`}
                    </Text>
                    <Text
                        style={{
                            width: "8%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Regular",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {`RM ${item.outletPayout.toFixed(2)}`}
                    </Text>
                    <Text
                        style={{
                            width: "8%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Regular",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {item.settlementDate}
                    </Text>
                    <Text
                        style={{
                            width: "8%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Regular",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {PAYMENT_CHANNEL_NAME_PARSED[item.pdChannel] ? PAYMENT_CHANNEL_NAME_PARSED[item.pdChannel] : item.pdChannel}
                    </Text>
                </View>
            </View>
        );
    };

    const convertDataToExcelFormat = () => {
        var excelData = [];

        if (!showDetails) {
            for (var i = 0; i < tableDataSummary.length; i++) {
                // var excelRow = {
                //     "Merchant Name": tableDataSummary[i].merchantName,
                //     "QR Orders Online": `RM ${tableDataSummary[i].userAppAndWebDineInOnlineOrdersAmount.toFixed(2)} (${tableDataSummary[i].userAppAndWebDineInOnlineOrdersQty.toFixed(0)})`,
                //     "QR Orders Offline": `RM ${tableDataSummary[i].userAppAndWebDineInOrdersAmount.toFixed(2)} (${tableDataSummary[i].userAppAndWebDineInOrdersQty.toFixed(0)})`,
                //     "POS Orders": `RM ${tableDataSummary[i].merchantAppOrdersAmount.toFixed(2)} (${tableDataSummary[i].merchantAppOrdersQty.toFixed(0)})`,
                //     "Waiter Orders": `RM ${tableDataSummary[i].waiterAppOrdersAmount.toFixed(2)} (${tableDataSummary[i].waiterAppOrdersQty.toFixed(0)})`,
                //     "Takeaway Orders": `RM ${tableDataSummary[i].userAppAndWebTakeawayOrdersAmount.toFixed(2)} (${tableDataSummary[i].userAppAndWebTakeawayOrdersQty.toFixed(0)})`,
                //     "Total Orders": `RM ${tableDataSummary[i].totalOrdersAmount.toFixed(2)} (${tableDataSummary[i].totalOrdersQty.toFixed(0)})`,
                //     "GMV Commission(Payable)": `RM ${tableDataSummary[i].gmvCommissionActual.toFixed(2)}`,
                //     "O. Sales Payout(Actual)": `RM ${tableDataSummary[i].gmvPayoutActual.toFixed(2)}`,
                //     "GMV Commission (Total)": `RM ${tableDataSummary[i].gmvCommission.toFixed(2)}`,
                //     "O. Sales Payout(Expected)": `RM ${tableDataSummary[i].gmvPayout.toFixed(2)}`,
                //     "GMV Fees": `RM ${tableDataSummary[i].gmvFees.toFixed(2)}`,
                //     "Last Order": tableDataSummary[i].lastOrderPlacedDateTime ? moment(tableDataSummary[i].lastOrderPlacedDateTime).format('YYYY-MM-DD hh:mm A') : 'N/A',
                // };

                // excelData.push(excelRow);

                let item = tableDataSummary[i];

                let razerPayoutTransactionList = (item.razerPayoutTransactionList.concat(item.recentPayoutTransactionList)
                ).sort((a, b) => a.settlementDate - b.settlementDate)

                for (let j = 0; j < razerPayoutTransactionList.length; j++) {
                    let transaction = razerPayoutTransactionList[j];

                    var excelRow = {
                        "Settlement Date": transaction.settlementDate ? moment(transaction.settlementDate).format('YYYY-MM-DD hh:mm A') : 'RM 0.00',
                        "Prev. Funds": transaction.prevStockUpAmount !== undefined ? `RM ${transaction.prevStockUpAmount.toFixed(2)}` : 'RM 0.00',
                        "Prev. Overdue": transaction.prevOverdueAmount !== undefined ? `RM ${transaction.prevOverdueAmount.toFixed(2)}` : 'RM 0.00',
                        // "Prev. Pending": transaction.prevPendingAmount !== undefined ? `RM ${transaction.prevPendingAmount.toFixed(2)}` : 'RM 0.00',
                        "N/A": "RM 0.00",
                        "Curr. Funds": transaction.stockUpAmount !== undefined ? `RM ${transaction.stockUpAmount.toFixed(2)}` : 'RM 0.00',
                        "Curr. Overdue": transaction.outletCycleMerchantOverdueAmounts !== undefined ? `RM ${transaction.outletCycleMerchantOverdueAmounts.toFixed(2)}` : 'RM 0.00',
                        // "Curr. Pending": transaction.outletCycleMerchantPendingAmounts !== undefined ? `RM ${transaction.outletCycleMerchantPendingAmounts.toFixed(2)}` : 'RM 0.00',
                        "N/A": "RM 0.00",
                        "GMV Commission (Payable)": transaction.outletCycleKoodooPayoutsActual !== undefined ? `RM ${transaction.outletCycleKoodooPayoutsActual.toFixed(2)}` : 'RM 0.00',
                        "O. Sales Payout (Actual)": transaction.outletCycleMerchantPayoutsActual !== undefined ? `RM ${transaction.outletCycleMerchantPayoutsActual.toFixed(2)}` : 'RM 0.00',
                        "GMV Commission (Total)": transaction.outletCycleKoodooPayoutsExpected !== undefined ? `RM ${transaction.outletCycleKoodooPayoutsExpected.toFixed(2)}` : 'RM 0.00',
                        "O. Sales Payout (Expected)": transaction.outletCycleMerchantPayoutsExpected !== undefined ? `RM ${transaction.outletCycleMerchantPayoutsExpected.toFixed(2)}` : 'RM 0.00',
                        "GMV Fees": transaction.outletCycleRazerPayouts !== undefined ? `RM ${transaction.outletCycleRazerPayouts.toFixed(2)}` : 'RM 0.00',
                        "GMV Commission (Online)": transaction.outletCycleKoodooPayoutsOnlineOnlyExpected !== undefined ? `RM ${transaction.outletCycleKoodooPayoutsOnlineOnlyExpected.toFixed(2)}` : 'RM 0.00',
                        "O. Sales Payout Fee": transaction.payoutFee !== undefined ? `RM ${transaction.payoutFee.toFixed(2)}` : 'RM 0.00',
                    };

                    excelData.push(excelRow);
                }

                var excelRow = {
                    "Settlement Date": '',
                    "Prev. Funds": '',
                    "Prev. Overdue": '',
                    // "Prev. Pending": '',
                    "N/A": '',
                    "Curr. Funds": '',
                    "Curr. Overdue": '',
                    // "Curr. Pending": '',
                    "N/A": '',
                    "GMV Commission (Payable)": '',
                    "O. Sales Payout (Actual)": '',
                    "GMV Commission (Total)": '',
                    "O. Sales Payout (Expected)": '',
                    "GMV Fees": '',
                    "GMV Commission (Online)": '',
                    "O. Sales Payout Fee": '',
                };

                excelData.push(excelRow);
            }
        } else {
            for (var i = 0; i < tableDataDetails.length; i++) {
                var excelRow = {
                    "Date Time": moment(tableDataDetails[i].dateTime).format("DD MMM hh:mm A"),
                    "Order Id(Joined)": tableDataDetails[i].orderId,
                    "QR Order": tableDataDetails[i].isQrOrder,
                    "Total Amount": `RM ${tableDataDetails[i].totalAmount.toFixed(2)}`,
                    "Razer Charges": `RM ${tableDataDetails[i].razerCharges.toFixed(2)}`,
                    "KooDoo Charges(Expected)": `RM ${tableDataDetails[i].koodooCharges.toFixed(2)}`,
                    "Outlet Payout(Expected)": `RM ${tableDataDetails[i].outletPayout.toFixed(2)}`,
                    "Settlement Date": tableDataDetails[i].settlementDate,
                    "Payment Method": tableDataDetails[i].pdChannel,
                };

                excelData.push(excelRow);
            }
        }

        console.log("excelData");
        console.log(excelData);

        return excelData;
    };

    const handleExportExcel = () => {
        var wb = XLSX.utils.book_new(),
            ws = XLSX.utils.json_to_sheet(convertDataToExcelFormat());

        XLSX.utils.book_append_sheet(wb, ws, "koodoo-payout-report");
        XLSX.writeFile(wb, `koodoo_payout_report-${moment(ptStartDate).format("YYYY-MM-DD")}-${moment(ptEndDate).format("YYYY-MM-DD")}.xlsx`);
    };

    const emailVariant = () => {
        const excelData = convertDataToExcelFormat();

        var body = {
            // data: CsvData,
            //data: convertArrayToCSV(todaySalesChart.dataSource.data),
            data: JSON.stringify(excelData),
            //data: convertDataToExcelFormat(),
            email: exportEmail,
        };

        ApiClient.POST(API.emailDashboard, body, false).then((result) => {
            if (result !== null) {
                Alert.alert(
                    "Success",
                    "Email has been sent",
                    [{ text: "OK", onPress: () => { } }],
                    { cancelable: false }
                );
            }
        });

        setVisible(false);
    };

    const flatListRef = useRef();

    const filterItem = (item) => {
        if (search !== "") {
            if (item.merchantName.toLowerCase().includes(search.toLowerCase())) {
                return true;
            } else if (item.outletName.toLowerCase().includes(search.toLowerCase())) {
                return true;
            } else {
                return false;
            }
        } else {
            return true;
        }
    };

    const filterItemDetails = (item) => {
        if (search !== "") {
            if (item.companyName.toLowerCase().includes(search.toLowerCase())) {
                return true;
                //   } else if (
                //     moment(item.actionDate, "x")
                //       .format("DD MMM YYY hh:mma")
                //       .toLowerCase()
                //       .includes(search.toLowerCase())
                //   ) {
                //     return true;
            } else {
                return false;
            }
        }
        // else {
        //   // check if there is data between the dates
        //   return moment(item.actionDate, "x").isBetween(ptStartDate, ptEndDate);
        // }
        return true;
    };

    // sort func
    const sortingAlphabetically = (a, b, compareName) => {
        if (a[compareName] < b[compareName]) {
            return -1;
        }
        if (a[compareName] > b[compareName]) {
            return 1;
        }
        return 0;
    };

    const sortingAlphabeticallyDesc = (a, b, compareName) => {
        if (a[compareName] < b[compareName]) {
            return 1;
        }
        if (a[compareName] > b[compareName]) {
            return -1;
        }
        return 0;
    };

    // sort by date
    const sortByCreatedDate = (a, b) =>
        moment(a["createdAt"]).valueOf() - moment(b["createdAt"]).valueOf();
    const sortByCreatedDateDesc = (a, b) =>
        moment(b["createdAt"]).valueOf() - moment(a["createdAt"]).valueOf();

    const sortByStartDate = (a, b) =>
        moment(a["createdAt"]).valueOf() - moment(b["createdAt"]).valueOf();
    const sortByStartDateDesc = (a, b) =>
        moment(b["createdAt"]).valueOf() - moment(a["createdAt"]).valueOf();

    const sortByCompName = (a, b) => {
        let compareName = "companyName";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByCompNameDesc = (a, b) => {
        let compareName = "companyName";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };
    const sortByCompRegistration = (a, b) => {
        let compareName = "companyRegistrationNo";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByCompRegistrationDesc = (a, b) => {
        let compareName = "companyRegistrationNo";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };
    const sortByCompAddr = (a, b) => {
        let compareName = "companyAddress";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByCompAddrDesc = (a, b) => {
        let compareName = "companyAddress";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };
    const sortByDirectorName = (a, b) => {
        let compareName = "directorName";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByDirectorNameDesc = (a, b) => {
        let compareName = "directorName";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };
    const sortByMerchantEmail = (a, b) => {
        let compareName = "merchantEmail";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByMerchantEmailDesc = (a, b) => {
        let compareName = "merchantEmail";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };
    const sortByMerchantName = (a, b) => {
        let compareName = "merchantName";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByMerchantNameDesc = (a, b) => {
        let compareName = "merchantName";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };

    const sortByOutletName = (a, b) => {
        let compareName = "outletName";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByOutletNameDesc = (a, b) => {
        let compareName = "outletName";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };
    const sortByOnboarderEmail = (a, b) => {
        let compareName = "onboarderEmail";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByOnboarderEmailDesc = (a, b) => {
        let compareName = "onboarderEmail";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };
    const sortByLastActivityDate = (a, b) =>
        moment(a["lastActivity"]).valueOf() - moment(b["lastActivity"]).valueOf();
    const sortByLastActivityDateDesc = (a, b) =>
        moment(b["lastActivity"]).valueOf() - moment(a["lastActivity"]).valueOf();

    //////////////////////////////////////////////////////////////

    const sortByUserAppAndWebDineInOrdersAmount = (a, b) => {
        let compareName = "userAppAndWebDineInOrdersAmount";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByUserAppAndWebDineInOrdersAmountDesc = (a, b) => {
        let compareName = "userAppAndWebDineInOrdersAmount";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };

    const sortByUserAppAndWebDineInOnlineOrdersAmount = (a, b) => {
        let compareName = "userAppAndWebDineInOnlineOrdersAmount";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByUserAppAndWebDineInOnlineOrdersAmountDesc = (a, b) => {
        let compareName = "userAppAndWebDineInOnlineOrdersAmount";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };

    const sortByMerchantAppOrdersAmount = (a, b) => {
        let compareName = "merchantAppOrdersAmount";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByMerchantAppOrdersAmountDesc = (a, b) => {
        let compareName = "merchantAppOrdersAmount";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };

    const sortByWaiterAppOrdersAmount = (a, b) => {
        let compareName = "waiterAppOrdersAmount";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByWaiterAppOrdersAmountDesc = (a, b) => {
        let compareName = "waiterAppOrdersAmount";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };

    const sortByUserAppAndWebTakeawayOrdersAmount = (a, b) => {
        let compareName = "userAppAndWebTakeawayOrdersAmount";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByUserAppAndWebTakeawayOrdersAmountDesc = (a, b) => {
        let compareName = "userAppAndWebTakeawayOrdersAmount";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };

    const sortByUserAppAndWebDeliveryOrdersAmount = (a, b) => {
        let compareName = "userAppAndWebDeliveryOrdersAmount";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByUserAppAndWebDeliveryOrdersAmountDesc = (a, b) => {
        let compareName = "userAppAndWebDeliveryOrdersAmount";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };

    const sortByTotalOrdersAmount = (a, b) => {
        let compareName = "totalOrdersAmount";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByTotalOrdersAmountDesc = (a, b) => {
        let compareName = "totalOrdersAmount";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };

    const sortByGmvCommission = (a, b) => {
        let compareName = "gmvCommission";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByGmvCommissionDesc = (a, b) => {
        let compareName = "gmvCommission";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };

    const sortByGmvFees = (a, b) => {
        let compareName = "gmvFees";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByGmvFeesDesc = (a, b) => {
        let compareName = "gmvFees";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };

    const sortByGmvPayout = (a, b) => {
        let compareName = "gmvPayout";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByGmvPayoutDesc = (a, b) => {
        let compareName = "gmvPayout";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };

    const sortByGmvCommissionActual = (a, b) => {
        let compareName = "gmvCommissionActual";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByGmvCommissionActualDesc = (a, b) => {
        let compareName = "gmvCommissionActual";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };

    const sortByGmvPayoutActual = (a, b) => {
        let compareName = "gmvPayoutActual";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByGmvPayoutActualDesc = (a, b) => {
        let compareName = "gmvPayoutActual";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };

    const sortByLastOrderPlacedDateTime = (a, b) => {
        let compareName = "lastOrderPlacedDateTime";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByLastOrderPlacedDateTimeDesc = (a, b) => {
        let compareName = "lastOrderPlacedDateTime";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };

    //////////////////////////////////////////////////////////////

    // for details

    const sortByDateTime = (a, b) => {
        let compareName = "dateTime";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByDateTimeDesc = (a, b) => {
        let compareName = "dateTime";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };

    const sortByOrderId = (a, b) => {
        let compareName = "orderId";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByOrderIdDesc = (a, b) => {
        let compareName = "orderId";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };

    // const sortByOrderId = (a, b) => {
    //   let compareName = "orderId";
    //   return sortingAlphabetically(a, b, compareName);
    // };
    // const sortByOrderIdDesc = (a, b) => {
    //   let compareName = "orderId";
    //   return sortingAlphabeticallyDesc(a, b, compareName);
    // };

    const sortByIsQrOrder = (a, b) => {
        let compareName = "isQrOrder";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByIsQrOrderDesc = (a, b) => {
        let compareName = "isQrOrder";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };

    const sortByNetAmount = (a, b) => {
        let compareName = "netAmount";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByNetAmountDesc = (a, b) => {
        let compareName = "netAmount";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };

    const sortByTax = (a, b) => {
        let compareName = "tax";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByTaxDesc = (a, b) => {
        let compareName = "tax";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };

    const sortBySc = (a, b) => {
        let compareName = "sc";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByScDesc = (a, b) => {
        let compareName = "sc";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };

    const sortByTotalAmount = (a, b) => {
        let compareName = "totalAmount";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByTotalAmountDesc = (a, b) => {
        let compareName = "totalAmount";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };

    const sortByRazerCharges = (a, b) => {
        let compareName = "razerCharges";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByRazerChargesDesc = (a, b) => {
        let compareName = "razerCharges";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };

    const sortByKoodooCharges = (a, b) => {
        let compareName = "koodooCharges";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByKoodooChargesDesc = (a, b) => {
        let compareName = "koodooCharges";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };

    const sortByOutletPayout = (a, b) => {
        let compareName = "outletPayout";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByOutletPayoutDesc = (a, b) => {
        let compareName = "outletPayout";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };

    const sortBySettlementDate = (a, b) => {
        let compareName = "settlementDate";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortBySettlementDateDesc = (a, b) => {
        let compareName = "settlementDate";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };
    const sortByPaymentMethod = (a, b) => {
        let compareName = "pdChannel";
        return sortingAlphabetically(a, b, compareName);
    };
    const sortByPaymentMethodDesc = (a, b) => {
        let compareName = "pdChannel";
        return sortingAlphabeticallyDesc(a, b, compareName);
    };

    //////////////////////////////////////////////////////////////

    // Fetch Data UseEffect
    useEffect(() => {
        if (global.fetchAllOutletUserOrderDoneProcessedTimer) {
          clearTimeout(global.fetchAllOutletUserOrderDoneProcessedTimer);
        }
    
        global.fetchAllOutletUserOrderDoneProcessedTimer = setTimeout(async () => {
          OutletStore.update(s => {s.reportingApiLoading = true});
    
            try {
                const requestBody = {
                    isMasterAccount,
                    merchantId,
                    outletId: currOutletId,
                    reportOutletIdList: allOutlets.map(outlet => outlet.uniqueId),
                    startDate: ptStartDate,
                    endDate: ptEndDate,
                };

                await ApiClientReporting.POST(API.getRazerPayoutTransactionsParsed, requestBody)
                    .then(response => {
                        if (response.status === 'success' && response.payoutTransactions) {
                            console.log('response.payoutTransactions', response.payoutTransactions);
                            setPayoutTransactions(response.payoutTransactions);
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching dashboard report:', error);
                        setPayoutTransactions([]);
                    });
            } 
            catch (error) {
                console.error('Error fetching dashboard report:', error);
                setPayoutTransactions([]);
            }
            finally {
                OutletStore.update(s => {s.reportingApiLoading = false});
            }
        }, 1000);
      }, [
        reportOutletIdList,
        ptStartDate,
        ptEndDate,
        isMasterAccount,
        merchantId,
        currOutletId,
        outletsOnboarded,
    ]);

    // Compute Data UseEffect
    useEffect(async () => {
        if (global.payoutTransactionTimer) {
            clearTimeout(global.payoutTransactionTimer);
        }

        global.payoutTransactionTimer = setTimeout(async () => {
            ///////////////////////////////////////////////////////////////////////

            // for gmv orders recent

            var ytdStartDate = moment().add(-1, 'day').startOf('day').valueOf();
            // var todayEndDate = moment().endOf('day').valueOf();
            var todayEndDate = moment(window.currToDateTime).valueOf();

            // if (ptStartDate >= ytdStartDate) {
            //   ytdStartDate = ptStartDate;
            // }

            if (moment().isSameOrAfter(moment().set('hour', 6))) {
                ytdStartDate = moment().startOf('day').valueOf();
            }

            if (ptEndDate < todayEndDate) {
                todayEndDate = ptEndDate;
            }

            ///////////////////////////////////////////////////////////////////////

            var startDate24H = moment().startOf('day').valueOf();
            // var endDate24H = moment().endOf('day').valueOf();
            var endDate24H = moment(window.currToDateTime).valueOf();

            var startDate7D = moment().subtract(7, "day").startOf('day').valueOf();
            // var endDate7D = moment().endOf('day').valueOf();
            var endDate7D = moment(window.currToDateTime).valueOf();

            var startDate1M = moment().subtract(1, "month").startOf('day').valueOf();
            // var endDate1M = moment().endOf('day').valueOf();
            var endDate1M = moment(window.currToDateTime).valueOf();

            var startDate3M = moment().subtract(3, "month").startOf('day').valueOf();
            // var endDate3M = moment().endOf('day').valueOf();
            var endDate3M = moment(window.currToDateTime).valueOf();

            var tempDataBefore = outletsOnboarded.filter(outlet =>
                // moment(outlet.createdAt).isSameOrAfter(ptStartDate, 'day') &&
                // moment(outlet.createdAt).isSameOrBefore(ptEndDate, 'day') &&
                merchantsOnboarded.find(merchant => {
                    if (isMasterAccount) {
                        if (outlet.merchantId === merchant.uniqueId) {
                            return true;
                        }
                    }
                    else {
                        if (outlet.merchantId === merchant.uniqueId &&
                            outlet.uniqueId === currOutletId) {
                            return true;
                        }
                    }
                })
            ).map(outlet => {
                var merchant = merchantsOnboarded.find(merchant => outlet.merchantId === merchant.uniqueId);

                var lastActivity = outletUserActionDict[outlet.uniqueId];
                if (lastActivity) {
                    lastActivity = lastActivity.updatedAt;
                }

                // if (!window.pOutletFilterDict[outlet.uniqueId]) {
                //   window.pOutletFilterDict[outlet.uniqueId] = {
                //     is24H: false,
                //     is7D: false,
                //     is1M: false,
                //     is3M: false,
                //   };
                // }
                // else {
                //   window.pOutletFilterDict[outlet.uniqueId]['is24H'] = false;
                // }      

                var recentPayoutTransactionDictTemp = {};
                if (moment(ytdStartDate).isSame(moment().add(-1, 'day').startOf('day'), 'day') &&
                    moment(ytdStartDate).isBetween(ptStartDate, ptEndDate, null, '[]')) {
                    recentPayoutTransactionDictTemp[ytdStartDate] = {
                        outletCycleKoodooPayoutsExpected: 0,
                        outletCycleRazerPayouts: 0,
                        outletCycleMerchantPayoutsExpected: 0,
                        outletCycleKoodooPayoutsActual: undefined,
                        outletCycleMerchantPayoutsActual: undefined,

                        outletCycleFunds: undefined,
                        outletCycleMerchantOverdueAmounts: undefined,
                        outletCycleMerchantPendingAmounts: undefined,

                        stockUpAmount: undefined,

                        outletCycleMerchantPendingRefundOrdersAmount: undefined,
                        outletCycleMerchantRefundOrdersAmount: undefined,

                        payoutFee: undefined,

                        prevStockUpAmount: undefined,
                        prevOverdueAmount: undefined,
                        prevPendingAmount: undefined,

                        transactionDate: undefined,
                        settlementDate: moment(ytdStartDate).add(1, 'day').set('hour', 6).set('minute', 0).set('second', 0).valueOf(),

                        trackDate: ytdStartDate,

                        outletCycleKoodooPayoutsOnlineOnlyExpected: 0,
                    };
                }

                if (
                    // moment(todayEndDate).isSame(moment().endOf('day'), 'day') &&
                    moment(todayEndDate).isSame(moment(window.currToDateTime), 'day') &&
                    moment(todayEndDate).isBetween(ptStartDate, ptEndDate, null, '[]')) {
                    recentPayoutTransactionDictTemp[todayEndDate] = {
                        outletCycleKoodooPayoutsExpected: 0,
                        outletCycleRazerPayouts: 0,
                        outletCycleMerchantPayoutsExpected: 0,
                        outletCycleKoodooPayoutsActual: undefined,
                        outletCycleMerchantPayoutsActual: undefined,

                        outletCycleFunds: undefined,
                        outletCycleMerchantOverdueAmounts: undefined,
                        outletCycleMerchantPendingAmounts: undefined,

                        stockUpAmount: undefined,

                        outletCycleMerchantPendingRefundOrdersAmount: undefined,
                        outletCycleMerchantRefundOrdersAmount: undefined,

                        payoutFee: undefined,

                        prevStockUpAmount: undefined,
                        prevOverdueAmount: undefined,
                        prevPendingAmount: undefined,

                        transactionDate: undefined,
                        settlementDate: moment(todayEndDate).add(1, 'day').set('hour', 6).set('minute', 0).set('second', 0).valueOf(),

                        trackDate: todayEndDate,

                        outletCycleKoodooPayoutsOnlineOnlyExpected: 0,
                    };
                }

                var colorRandom = `#${Math.floor(Math.random() * 16777215).toString(16)}`;
                if (window.pOutletPaletteColorDict[outlet.uniqueId]) {
                    colorRandom = window.pOutletPaletteColorDict[outlet.uniqueId];
                }
                else {
                    window.pOutletPaletteColorDict[outlet.uniqueId] = colorRandom;
                }

                return {
                    paletteColor: colorRandom,

                    merchantName: outlet.name,
                    outletName: outlet.name,
                    createdAt: outlet.createdAt,
                    onboarderEmail: outlet.onboarderEmail || '-',
                    // lastActivity: merchant.updatedAt > merchant.updatedAt ? merchant.updatedAt : outlet.updatedAt,
                    lastActivity: lastActivity ? lastActivity : outlet.createdAt,

                    merchantId: outlet.merchantId,
                    outletId: outlet.uniqueId,

                    //////////////////////////////////////////////

                    // new info

                    userAppAndWebDineInOrdersAmount: 0,
                    userAppAndWebDineInOrdersQty: 0,

                    userAppAndWebDineInOnlineOrdersAmount: 0,
                    userAppAndWebDineInOnlineOrdersQty: 0,

                    merchantAppOrdersAmount: 0,
                    merchantAppOrdersQty: 0,

                    waiterAppOrdersAmount: 0,
                    waiterAppOrdersQty: 0,

                    userAppAndWebTakeawayOrdersAmount: 0,
                    userAppAndWebTakeawayOrdersQty: 0,

                    userAppAndWebDeliveryOrdersAmount: 0,
                    userAppAndWebDeliveryOrdersQty: 0,

                    totalOrdersAmount: 0,
                    totalOrdersQty: 0,

                    gmvCommission: BigNumber(0),
                    gmvPayout: BigNumber(0),
                    gmvFees: BigNumber(0),

                    gmvCommissionActual: BigNumber(0),
                    gmvPayoutActual: BigNumber(0),

                    razerPayoutTransactionList: [],

                    lastOrderPlacedDateTime: null,

                    recentPayoutTransactionDict: recentPayoutTransactionDictTemp,
                    recentPayoutTransactionList: [],

                    //////////////////////////////////////////////

                    // details info

                    detailsList: [],

                    //////////////////////////////////////////////
                };
            });

            var outletDataDict = Object.assign({}, ...tempDataBefore.map(data => ({
                [data.outletId]: data,
            })));

            //////////////////////////////////////////////////////////////////////////

            // calculate orders

            // var userAppAndWebDineInOrdersAmount = 0;
            // var userAppAndWebDineInOrdersQty = 0;

            // var merchantAppOrdersAmount = 0;
            // var merchantAppOrdersQty = 0;

            // var waiterAppOrdersAmount = 0;
            // var waiterAppOrdersQty = 0;

            // var userAppAndWebTakeawayOrdersAmount = 0;
            // var userAppAndWebTakeawayOrdersQty = 0;

            // var userAppAndWebDeliveryOrdersAmount = 0;
            // var userAppAndWebDeliveryOrdersQty = 0;

            // var totalOrdersAmount = 0;
            // var totalOrdersQty = 0;

            // var lastOrderPlacedDateTime = null;

            // var payoutTransactionsFiltered = payoutTransactions.filter(order => order.outletId === outlet.uniqueId);

            // 2023-01-04 - No needed
            // const payoutTransactionsFiltered = payoutTransactions.filter(transaction => {
            //     if (clickedBarChartDate !== null && clickedBarChartDateUnit !== null) {
            //         if (moment(clickedBarChartDate).isSame(
            //             moment(transaction.transactionDate),
            //             clickedBarChartDateUnit,
            //         )) {
            //             return true;
            //         }
            //         else {
            //             return false
            //         }
            //     }
            //     else {
            //         return true;
            //     }

            //     // if (clickedChartDate !== null && moment(clickedChartDate).isSame(
            //     //   moment(transaction.transactionDate),
            //     //   'day',
            //     // )) {
            //     //   return true;
            //     // }
            //     // else {
            //     //   return true;
            //     // }
            // });

            // const fetchPayoutTransactions = async () => {
            //     try {
            //         const body = {
            //             isMasterAccount,
            //             merchantId,
            //             outletId: currOutletId,
            //             reportOutletIdList,
            //             ptStartDate,
            //             ptEndDate,
            //         };

            //         const result = await ApiClientReporting.POST(API.getRazerPayoutTransactionsParsed, body, false);

            //         if (result?.status === 'success') {
            //             return {
            //                 payoutTransactions: result.payoutTransactions,
            //                 payoutTransactionsExtend: result.payoutTransactionsExtend
            //             };
            //         }

            //         return {
            //             payoutTransactions: [],
            //             payoutTransactionsExtend: []
            //         };

            //     } catch (error) {
            //         console.error('Error fetching payout transactions:', error);
            //         return {
            //             payoutTransactions: [],
            //             payoutTransactionsExtend: []
            //         };
            //     }
            // }

            // const {
            //     payoutTransactions,
            //     payoutTransactionsExtend,
            // } = await fetchPayoutTransactions();

            console.log('Payout Results:', {
                payoutTransactions: payoutTransactions.length,
                payoutTransactionsExtend: payoutTransactionsExtend.length
            });

            const payoutTransactionsFiltered = payoutTransactions.concat(payoutTransactionsExtend);

            console.log('loop');
            console.log(payoutTransactionsFiltered.length);

            for (let i = 0; i < payoutTransactionsFiltered.length; i++) {
                if (outletDataDict[payoutTransactionsFiltered[i].outletId]) {
                    // var gmvRate = 0;
                    // if (payoutTransactionsFiltered[i].settlementDetails) {
                    //   if (payoutTransactionsFiltered[i].settlementDetails && payoutTransactionsFiltered[i].settlementDetails.processingRateTotalSalesActive) {
                    //     gmvRate = payoutTransactionsFiltered[i].settlementDetails.processingRateTotalSales;
                    //   }
                    //   else if (payoutTransactionsFiltered[i].settlementDetails && payoutTransactionsFiltered[i].settlementDetails.processingRateRazerActive) {
                    //     gmvRate = payoutTransactionsFiltered[i].settlementDetails.processingRateRazer;
                    //   }
                    // }

                    for (let j = 0; j < payoutTransactionsFiltered[i].userOrdersFigures.length; j++) {
                        if (payoutTransactionsFiltered[i].userOrdersFigures[j].combinedOrderList && payoutTransactionsFiltered[i].userOrdersFigures[j].combinedOrderList.length > 0) {
                            // means this order already merged with other orders         
                        }
                        else {
                            let userOrderFigure = payoutTransactionsFiltered[i].userOrdersFigures[j];

                            // push to details list

                            let toAddObj = {
                                dateTime: moment(userOrderFigure.createdAt || payoutTransactionsFiltered[i].createdAt).format('YYYY/MM/DD HH:mm'),
                                orderId: (userOrderFigure.orderIdHuman && userOrderFigure.orderType) ? `#${userOrderFigure.orderType === ORDER_TYPE.DINEIN ? '' : 'T'}${userOrderFigure.orderIdHuman}` : 'N/A',
                                uniqueId: userOrderFigure.orderId,

                                // uniqueId: payoutTransactionsFiltered[i].uniqueId,
                                isQrOrder: (userOrderFigure.appType !== undefined ? (userOrderFigure.appType === APP_TYPE.WEB_ORDER ? 'Yes' : 'No') : 'N/A'),
                                netAmount: BigNumber(0).dp(2).toNumber(),
                                tax: BigNumber(0).dp(2).toNumber(),
                                sc: BigNumber(0).dp(2).toNumber(),
                                totalAmount: BigNumber(userOrderFigure.userOrderPriceBeforeCommission).dp(2).toNumber(),
                                razerCharges: BigNumber(userOrderFigure.commissionFeeFinal).dp(2).toNumber(),
                                koodooCharges: BigNumber(userOrderFigure.koodooProcessingFee).dp(2).toNumber(),
                                outletPayout: BigNumber(userOrderFigure.commissionFeeFinal > 0 ? userOrderFigure.userOrderPriceAfterCommissionAndFee : 0).dp(2).toNumber(),
                                settlementDate: moment(payoutTransactionsFiltered[i].createdAt).format('YYYY/MM/DD'),

                                isSettled: true,

                                // gmvRate: gmvRate,

                                orderTypeDetails: (userOrderFigure.appType === APP_TYPE.WEB_ORDER ?
                                    ((userOrderFigure.commissionFeeFinal > 0) ? ORDER_TYPE_DETAILS.QR_ONLINE : ORDER_TYPE_DETAILS.QR_OFFLINE)
                                    :
                                    ORDER_TYPE_DETAILS.POS),

                                outletId: payoutTransactionsFiltered[i].outletId,

                                koodooProcessingRate: payoutTransactionsFiltered[i].koodooProcessingRate || 0,

                                pdChannel: (userOrderFigure.pdChannel) ? userOrderFigure.pdChannel : OFFLINE_PAYMENT_METHOD_TYPE.CASH.channel,
                            };

                            // let foundIndex = outletDataDict[payoutTransactionsFiltered[i].outletId].detailsList.findIndex(order => order.uniqueId === toAddObj.uniqueId);

                            // if (foundIndex >= 0) {
                            //   outletDataDict[payoutTransactionsFiltered[i].outletId].detailsList[foundIndex] = toAddObj;
                            // }
                            // else {
                            //   outletDataDict[payoutTransactionsFiltered[i].outletId].detailsList.push(toAddObj);
                            // }           

                            // if (!outletDataDict[payoutTransactionsFiltered[i].outletId].detailsList.find(order => order.uniqueId === toAddObj.uniqueId)) {
                            //   outletDataDict[payoutTransactionsFiltered[i].outletId].detailsList.push(toAddObj);
                            // }            

                            outletDataDict[payoutTransactionsFiltered[i].outletId].detailsList.push(toAddObj);

                            //////////////////////////////////////////////////////////////////////

                            if ((userOrderFigure.appType === APP_TYPE.MERCHANT ||
                                userOrderFigure.appType === undefined ||
                                userOrderFigure.appType === APP_TYPE.UNKNOWN) &&
                                !userOrderFigure.isOnlineOrdering) {
                                outletDataDict[payoutTransactionsFiltered[i].outletId].merchantAppOrdersAmount += userOrderFigure.userOrderPriceBeforeCommission;
                                // console.log(outletDataDict[payoutTransactionsFiltered[i].outletId].merchantAppOrdersAmount);
                                outletDataDict[payoutTransactionsFiltered[i].outletId].merchantAppOrdersQty += 1;

                                outletDataDict[payoutTransactionsFiltered[i].outletId].lastOrderPlacedDateTime = userOrderFigure.createdAt >
                                    outletDataDict[payoutTransactionsFiltered[i].outletId].lastOrderPlacedDateTime ? userOrderFigure.createdAt :
                                    outletDataDict[payoutTransactionsFiltered[i].outletId].lastOrderPlacedDateTime;

                                // checkOutletFilterDict({
                                //   uniqueId: payoutTransactionsFiltered[i].outletId,
                                // }, userOrderFigure.createdAt, {
                                //   startDate24H,
                                //   endDate24H,
                                //   startDate7D,
                                //   endDate7D,
                                //   startDate1M,
                                //   endDate1M,
                                //   startDate3M,
                                //   endDate3M,
                                // });

                                continue;
                            }

                            if (userOrderFigure.appType === APP_TYPE.USER ||
                                userOrderFigure.appType === APP_TYPE.WEB_ORDER) {
                                if (userOrderFigure.orderType === ORDER_TYPE.DINEIN) {
                                    if (userOrderFigure.commissionFeeFinal > 0) {
                                        outletDataDict[payoutTransactionsFiltered[i].outletId].userAppAndWebDineInOnlineOrdersAmount += userOrderFigure.userOrderPriceBeforeCommission;
                                        outletDataDict[payoutTransactionsFiltered[i].outletId].userAppAndWebDineInOnlineOrdersQty += 1;
                                    }
                                    else {
                                        outletDataDict[payoutTransactionsFiltered[i].outletId].userAppAndWebDineInOrdersAmount += userOrderFigure.userOrderPriceBeforeCommission;
                                        outletDataDict[payoutTransactionsFiltered[i].outletId].userAppAndWebDineInOrdersQty += 1;
                                    }

                                    outletDataDict[payoutTransactionsFiltered[i].outletId].lastOrderPlacedDateTime = userOrderFigure.createdAt >
                                        outletDataDict[payoutTransactionsFiltered[i].outletId].lastOrderPlacedDateTime ? userOrderFigure.createdAt :
                                        outletDataDict[payoutTransactionsFiltered[i].outletId].lastOrderPlacedDateTime;

                                    // checkOutletFilterDict({
                                    //   uniqueId: payoutTransactionsFiltered[i].outletId,
                                    // }, userOrderFigure.createdAt, {
                                    //   startDate24H,
                                    //   endDate24H,
                                    //   startDate7D,
                                    //   endDate7D,
                                    //   startDate1M,
                                    //   endDate1M,
                                    //   startDate3M,
                                    //   endDate3M,
                                    // });

                                    continue;
                                }
                                else if (userOrderFigure.orderType === ORDER_TYPE.PICKUP) {
                                    outletDataDict[payoutTransactionsFiltered[i].outletId].userAppAndWebTakeawayOrdersAmount += userOrderFigure.userOrderPriceBeforeCommission;
                                    outletDataDict[payoutTransactionsFiltered[i].outletId].userAppAndWebTakeawayOrdersQty += 1;

                                    outletDataDict[payoutTransactionsFiltered[i].outletId].lastOrderPlacedDateTime = userOrderFigure.createdAt >
                                        outletDataDict[payoutTransactionsFiltered[i].outletId].lastOrderPlacedDateTime ? userOrderFigure.createdAt :
                                        outletDataDict[payoutTransactionsFiltered[i].outletId].lastOrderPlacedDateTime;

                                    // checkOutletFilterDict({
                                    //   uniqueId: payoutTransactionsFiltered[i].outletId,
                                    // }, userOrderFigure.createdAt, {
                                    //   startDate24H,
                                    //   endDate24H,
                                    //   startDate7D,
                                    //   endDate7D,
                                    //   startDate1M,
                                    //   endDate1M,
                                    //   startDate3M,
                                    //   endDate3M,
                                    // });

                                    continue;
                                }
                                else if (userOrderFigure.orderType === ORDER_TYPE.DELIVERY) {
                                    outletDataDict[payoutTransactionsFiltered[i].outletId].userAppAndWebDeliveryOrdersAmount += userOrderFigure.userOrderPriceBeforeCommission;
                                    outletDataDict[payoutTransactionsFiltered[i].outletId].userAppAndWebDeliveryOrdersQty += 1;

                                    outletDataDict[payoutTransactionsFiltered[i].outletId].lastOrderPlacedDateTime = userOrderFigure.createdAt >
                                        outletDataDict[payoutTransactionsFiltered[i].outletId].lastOrderPlacedDateTime ? userOrderFigure.createdAt :
                                        outletDataDict[payoutTransactionsFiltered[i].outletId].lastOrderPlacedDateTime;

                                    // checkOutletFilterDict({
                                    //   uniqueId: payoutTransactionsFiltered[i].outletId,
                                    // }, userOrderFigure.createdAt, {
                                    //   startDate24H,
                                    //   endDate24H,
                                    //   startDate7D,
                                    //   endDate7D,
                                    //   startDate1M,
                                    //   endDate1M,
                                    //   startDate3M,
                                    //   endDate3M,
                                    // });

                                    continue;
                                }
                            }

                            ////////////////////////////////////////////////////////////

                            // to compatible with previous web orders

                            if (
                                // payoutTransactionsFiltered[i].appType === undefined &&
                                userOrderFigure.isOnlineOrdering) {
                                if (payoutTransactionsFiltered[i].orderType === ORDER_TYPE.DINEIN) {
                                    if (userOrderFigure.commissionFeeFinal > 0) {
                                        outletDataDict[payoutTransactionsFiltered[i].outletId].userAppAndWebDineInOnlineOrdersAmount += userOrderFigure.userOrderPriceBeforeCommission;
                                        outletDataDict[payoutTransactionsFiltered[i].outletId].userAppAndWebDineInOnlineOrdersQty += 1;
                                    }
                                    else {
                                        outletDataDict[payoutTransactionsFiltered[i].outletId].userAppAndWebDineInOrdersAmount += userOrderFigure.userOrderPriceBeforeCommission;
                                        outletDataDict[payoutTransactionsFiltered[i].outletId].userAppAndWebDineInOrdersQty += 1;
                                    }

                                    outletDataDict[payoutTransactionsFiltered[i].outletId].lastOrderPlacedDateTime = userOrderFigure.createdAt >
                                        outletDataDict[payoutTransactionsFiltered[i].outletId].lastOrderPlacedDateTime ? userOrderFigure.createdAt :
                                        outletDataDict[payoutTransactionsFiltered[i].outletId].lastOrderPlacedDateTime;

                                    // checkOutletFilterDict({
                                    //   uniqueId: payoutTransactionsFiltered[i].outletId,
                                    // }, userOrderFigure.createdAt, {
                                    //   startDate24H,
                                    //   endDate24H,
                                    //   startDate7D,
                                    //   endDate7D,
                                    //   startDate1M,
                                    //   endDate1M,
                                    //   startDate3M,
                                    //   endDate3M,
                                    // });

                                    continue;
                                }
                                else if (userOrderFigure.orderType === ORDER_TYPE.PICKUP) {
                                    outletDataDict[payoutTransactionsFiltered[i].outletId].userAppAndWebTakeawayOrdersAmount += userOrderFigure.userOrderPriceBeforeCommission;
                                    outletDataDict[payoutTransactionsFiltered[i].outletId].userAppAndWebTakeawayOrdersQty += 1;

                                    outletDataDict[payoutTransactionsFiltered[i].outletId].lastOrderPlacedDateTime = userOrderFigure.createdAt >
                                        outletDataDict[payoutTransactionsFiltered[i].outletId].lastOrderPlacedDateTime ? userOrderFigure.createdAt :
                                        outletDataDict[payoutTransactionsFiltered[i].outletId].lastOrderPlacedDateTime;

                                    // checkOutletFilterDict({
                                    //   uniqueId: payoutTransactionsFiltered[i].outletId,
                                    // }, userOrderFigure.createdAt, {
                                    //   startDate24H,
                                    //   endDate24H,
                                    //   startDate7D,
                                    //   endDate7D,
                                    //   startDate1M,
                                    //   endDate1M,
                                    //   startDate3M,
                                    //   endDate3M,
                                    // });

                                    continue;
                                }
                                else if (userOrderFigure.orderType === payoutTransactionsFiltered.DELIVERY) {
                                    outletDataDict[payoutTransactionsFiltered[i].outletId].userAppAndWebDeliveryOrdersAmount += userOrderFigure.userOrderPriceBeforeCommission;
                                    outletDataDict[payoutTransactionsFiltered[i].outletId].userAppAndWebDeliveryOrdersQty += 1;

                                    outletDataDict[payoutTransactionsFiltered[i].outletId].lastOrderPlacedDateTime = userOrderFigure.createdAt >
                                        outletDataDict[payoutTransactionsFiltered[i].outletId].lastOrderPlacedDateTime ? userOrderFigure.createdAt :
                                        outletDataDict[payoutTransactionsFiltered[i].outletId].lastOrderPlacedDateTime;

                                    // checkOutletFilterDict({
                                    //   uniqueId: payoutTransactionsFiltered[i].outletId,
                                    // }, userOrderFigure.createdAt, {
                                    //   startDate24H,
                                    //   endDate24H,
                                    //   startDate7D,
                                    //   endDate7D,
                                    //   startDate1M,
                                    //   endDate1M,
                                    //   startDate3M,
                                    //   endDate3M,
                                    // });

                                    continue;
                                }
                            }

                            ////////////////////////////////////////////////////////////

                            if (userOrderFigure.appType === APP_TYPE.WAITER) {
                                outletDataDict[payoutTransactionsFiltered[i].outletId].waiterAppOrdersAmount += userOrderFigure.userOrderPriceBeforeCommission;
                                outletDataDict[payoutTransactionsFiltered[i].outletId].waiterAppOrdersQty += 1;

                                outletDataDict[payoutTransactionsFiltered[i].outletId].lastOrderPlacedDateTime = userOrderFigure.createdAt >
                                    outletDataDict[payoutTransactionsFiltered[i].outletId].lastOrderPlacedDateTime ? userOrderFigure.createdAt :
                                    outletDataDict[payoutTransactionsFiltered[i].outletId].lastOrderPlacedDateTime;

                                // checkOutletFilterDict({
                                //   uniqueId: payoutTransactionsFiltered[i].outletId,
                                // }, userOrderFigure.createdAt, {
                                //   startDate24H,
                                //   endDate24H,
                                //   startDate7D,
                                //   endDate7D,
                                //   startDate1M,
                                //   endDate1M,
                                //   startDate3M,
                                //   endDate3M,
                                // });

                                continue;
                            }
                        }
                    }

                    outletDataDict[payoutTransactionsFiltered[i].outletId].gmvCommission = BigNumber(outletDataDict[payoutTransactionsFiltered[i].outletId].gmvCommission).plus(BigNumber(payoutTransactionsFiltered[i].outletCycleKoodooPayoutsExpected)).toNumber();
                    outletDataDict[payoutTransactionsFiltered[i].outletId].gmvFees = BigNumber(outletDataDict[payoutTransactionsFiltered[i].outletId].gmvFees).plus(BigNumber(payoutTransactionsFiltered[i].outletCycleRazerPayouts)).toNumber();
                    outletDataDict[payoutTransactionsFiltered[i].outletId].gmvPayout = BigNumber(outletDataDict[payoutTransactionsFiltered[i].outletId].gmvPayout).plus(BigNumber(payoutTransactionsFiltered[i].outletCycleMerchantPayoutsExpected)).toNumber();

                    outletDataDict[payoutTransactionsFiltered[i].outletId].gmvCommissionActual = BigNumber(outletDataDict[payoutTransactionsFiltered[i].outletId].gmvCommissionActual).plus(BigNumber(payoutTransactionsFiltered[i].outletCycleKoodooPayoutsActual)).toNumber();
                    outletDataDict[payoutTransactionsFiltered[i].outletId].gmvPayoutActual = BigNumber(outletDataDict[payoutTransactionsFiltered[i].outletId].gmvPayoutActual).plus(BigNumber(payoutTransactionsFiltered[i].outletCycleMerchantPayoutsActual)).toNumber();

                    outletDataDict[payoutTransactionsFiltered[i].outletId].razerPayoutTransactionList.push({
                        outletCycleKoodooPayoutsExpected: payoutTransactionsFiltered[i].outletCycleKoodooPayoutsExpected,
                        outletCycleRazerPayouts: payoutTransactionsFiltered[i].outletCycleRazerPayouts,
                        outletCycleMerchantPayoutsExpected: payoutTransactionsFiltered[i].outletCycleMerchantPayoutsExpected,
                        outletCycleKoodooPayoutsActual: payoutTransactionsFiltered[i].outletCycleKoodooPayoutsActual,
                        outletCycleMerchantPayoutsActual: payoutTransactionsFiltered[i].outletCycleMerchantPayoutsActual,

                        outletCycleFunds: payoutTransactionsFiltered[i].outletCycleFunds,
                        outletCycleMerchantOverdueAmounts: payoutTransactionsFiltered[i].outletCycleMerchantOverdueAmounts,
                        outletCycleMerchantPendingAmounts: payoutTransactionsFiltered[i].outletCycleMerchantPendingAmounts,

                        stockUpAmount: payoutTransactionsFiltered[i].stockUpAmount,

                        outletCycleMerchantPendingRefundOrdersAmount: payoutTransactionsFiltered[i].outletCycleMerchantPendingRefundOrdersAmount ? payoutTransactionsFiltered[i].outletCycleMerchantPendingRefundOrdersAmount : 0,
                        outletCycleMerchantRefundOrdersAmount: payoutTransactionsFiltered[i].outletCycleMerchantRefundOrdersAmount ? payoutTransactionsFiltered[i].outletCycleMerchantRefundOrdersAmount : 0,

                        payoutFee: payoutTransactionsFiltered[i].payoutFee,

                        prevStockUpAmount: payoutTransactionsFiltered[i].prevStockUpAmount ? payoutTransactionsFiltered[i].prevStockUpAmount : 0,
                        prevOverdueAmount: payoutTransactionsFiltered[i].prevOverdueAmount ? payoutTransactionsFiltered[i].prevOverdueAmount : 0,
                        prevPendingAmount: payoutTransactionsFiltered[i].prevPendingAmount ? payoutTransactionsFiltered[i].prevPendingAmount : 0,

                        transactionDate: payoutTransactionsFiltered[i].transactionDate,
                        settlementDate: payoutTransactionsFiltered[i].createdAt,

                        uniqueId: payoutTransactionsFiltered[i].uniqueId,

                        outletCycleKoodooPayoutsOnlineOnlyExpected: payoutTransactionsFiltered[i].userOrdersFigures.reduce((accum, order) => {
                            return BigNumber(accum).plus(
                                (order.commissionFeeFinal > 0) ? order.koodooProcessingFee : 0);
                        }, BigNumber(0)).toNumber(),
                    });

                    //////////////////////////////////////////////////////////////////////

                    // outletDataDict[payoutTransactionsFiltered[i].outletId].totalOrdersAmount = outletDataDict[payoutTransactionsFiltered[i].outletId].userAppAndWebDineInOrdersAmount +
                    //   outletDataDict[payoutTransactionsFiltered[i].outletId].merchantAppOrdersAmount + outletDataDict[payoutTransactionsFiltered[i].outletId].waiterAppOrdersAmount +
                    //   outletDataDict[payoutTransactionsFiltered[i].outletId].userAppAndWebTakeawayOrdersAmount + outletDataDict[payoutTransactionsFiltered[i].outletId].userAppAndWebDeliveryOrdersAmount;
                    // outletDataDict[payoutTransactionsFiltered[i].outletId].totalOrdersQty = outletDataDict[payoutTransactionsFiltered[i].outletId].userAppAndWebDineInOrdersQty +
                    //   outletDataDict[payoutTransactionsFiltered[i].outletId].merchantAppOrdersQty + outletDataDict[payoutTransactionsFiltered[i].outletId].waiterAppOrdersQty +
                    //   outletDataDict[payoutTransactionsFiltered[i].outletId].userAppAndWebTakeawayOrdersQty + outletDataDict[payoutTransactionsFiltered[i].outletId].userAppAndWebDeliveryOrdersQty;
                }
            }

            ////////////////////////////////////////////////////////////

            // for gmvOrdersRecent

            const gmvOrdersRecentFiltered = gmvOrdersRecent.filter(order => {
                if (clickedBarChartDate !== null && clickedBarChartDateUnit !== null) {
                    if (moment(clickedBarChartDate).isSame(
                        moment(order.createdAt),
                        clickedBarChartDateUnit,
                    )) {
                        return true;
                    }
                    else {
                        return false
                    }
                }
                else {
                    return true;
                }

                // if (clickedChartDate !== null && moment(clickedChartDate).isSame(
                //   moment(order.createdAt),
                //   'day',
                // )) {
                //   return true;
                // }
                // else {
                //   return true;
                // }
            });;

            console.log('loop');
            console.log(gmvOrdersRecentFiltered.length);

            for (let i = 0; i < gmvOrdersRecentFiltered.length; i++) {
                if (
                    outletDataDict[gmvOrdersRecentFiltered[i].outletId] &&
                    !outletDataDict[gmvOrdersRecentFiltered[i].outletId].detailsList.find(order => order.uniqueId === gmvOrdersRecentFiltered[i].uniqueId) &&
                    moment(gmvOrdersRecentFiltered[i].createdAt).isBetween(ptStartDate, ptEndDate, null, '[]')
                ) {

                    outletDataDict[gmvOrdersRecentFiltered[i].outletId].detailsList.push({
                        dateTime: moment(gmvOrdersRecentFiltered[i].createdAt).format('YYYY/MM/DD HH:mm'),
                        orderId: `#${gmvOrdersRecentFiltered[i].orderType === ORDER_TYPE.DINEIN ? '' : 'T'}${gmvOrdersRecentFiltered[i].orderId}`,
                        uniqueId: gmvOrdersRecentFiltered[i].uniqueId,
                        isQrOrder: gmvOrdersRecentFiltered[i].appType === APP_TYPE.WEB_ORDER ? 'Yes' : 'No',
                        netAmount: BigNumber(gmvOrdersRecentFiltered[i].finalPrice).minus(gmvOrdersRecentFiltered[i].tax).minus(gmvOrdersRecentFiltered[i].sc).dp(2).toNumber(),
                        tax: BigNumber(gmvOrdersRecentFiltered[i].tax).dp(2).toNumber(),
                        sc: BigNumber(gmvOrdersRecentFiltered[i].sc).dp(2).toNumber(),
                        totalAmount: BigNumber(gmvOrdersRecentFiltered[i].finalPrice).dp(2).toNumber(),
                        razerCharges: BigNumber((gmvOrdersRecentFiltered[i].settlementDetails && gmvOrdersRecentFiltered[i].settlementDetails.commissionFeeFinal) ? gmvOrdersRecentFiltered[i].settlementDetails.commissionFeeFinal : 0).dp(2).toNumber(),
                        koodooCharges: BigNumber((gmvOrdersRecentFiltered[i].settlementDetails && gmvOrdersRecentFiltered[i].settlementDetails.koodooProcessingFee) ? gmvOrdersRecentFiltered[i].settlementDetails.koodooProcessingFee : 0).dp(2).toNumber(),
                        outletPayout: BigNumber(
                            (gmvOrdersRecentFiltered[i].paymentDetails &&
                                (gmvOrdersRecentFiltered[i].paymentDetails.txn_ID !== undefined ||
                                    gmvOrdersRecentFiltered[i].paymentDetails.txnId !== undefined)
                                &&
                                (gmvOrdersRecentFiltered[i].settlementDetails && gmvOrdersRecentFiltered[i].settlementDetails.userOrderPriceAfterCommissionAndFee)
                            )
                                ?
                                gmvOrdersRecentFiltered[i].settlementDetails.userOrderPriceAfterCommissionAndFee
                                :
                                0)
                            .dp(2).toNumber(),
                        settlementDate: gmvOrdersRecentFiltered[i].settlementDate ? moment(gmvOrdersRecentFiltered[i].settlementDate).format('YYYY/MM/DD') : 'N/A',

                        isSettled: gmvOrdersRecentFiltered[i].settlementDate ? true : false,

                        orderTypeDetails: (gmvOrdersRecentFiltered[i].appType === APP_TYPE.WEB_ORDER ?
                            ((gmvOrdersRecentFiltered[i].commissionFeeFinal > 0) ? ORDER_TYPE_DETAILS.QR_ONLINE : ORDER_TYPE_DETAILS.QR_OFFLINE)
                            :
                            ORDER_TYPE_DETAILS.POS),

                        outletId: gmvOrdersRecentFiltered[i].outletId,

                        koodooProcessingRate: gmvOrdersRecentFiltered[i].koodooProcessingRate || 0,

                        pdChannel: (gmvOrdersRecentFiltered[i].paymentDetails && gmvOrdersRecentFiltered[i].paymentDetails.channel) ? gmvOrdersRecentFiltered[i].paymentDetails.channel : OFFLINE_PAYMENT_METHOD_TYPE.CASH.channel,
                    });

                    outletDataDict[gmvOrdersRecentFiltered[i].outletId].gmvCommission = BigNumber(outletDataDict[gmvOrdersRecentFiltered[i].outletId].gmvCommission).plus(BigNumber((gmvOrdersRecentFiltered[i].settlementDetails && gmvOrdersRecentFiltered[i].settlementDetails.koodooProcessingFee) ? gmvOrdersRecentFiltered[i].settlementDetails.koodooProcessingFee : 0)).toNumber();
                    outletDataDict[gmvOrdersRecentFiltered[i].outletId].gmvFees = BigNumber(outletDataDict[gmvOrdersRecentFiltered[i].outletId].gmvFees).plus(BigNumber((gmvOrdersRecentFiltered[i].settlementDetails && gmvOrdersRecentFiltered[i].settlementDetails.commissionFeeFinal) ? gmvOrdersRecentFiltered[i].settlementDetails.commissionFeeFinal : 0)).toNumber();
                    outletDataDict[gmvOrdersRecentFiltered[i].outletId].gmvPayout = BigNumber(outletDataDict[gmvOrdersRecentFiltered[i].outletId].gmvPayout).plus(BigNumber(
                        (gmvOrdersRecentFiltered[i].paymentDetails &&
                            (gmvOrdersRecentFiltered[i].paymentDetails.txn_ID !== undefined ||
                                gmvOrdersRecentFiltered[i].paymentDetails.txnId !== undefined)
                            &&
                            (gmvOrdersRecentFiltered[i].settlementDetails && gmvOrdersRecentFiltered[i].settlementDetails.userOrderPriceAfterCommissionAndFee)
                        )
                            ?
                            gmvOrdersRecentFiltered[i].settlementDetails.userOrderPriceAfterCommissionAndFee
                            :
                            0)).toNumber();

                    // outletDataDict[payoutTransactionsFiltered[i].outletId].gmvCommissionActual = BigNumber(outletDataDict[gmvOrdersRecentFiltered[i].outletId].gmvCommissionActual).plus(BigNumber(userOrderFigure.outletCycleKoodooPayoutsActual)).toNumber();
                    // outletDataDict[payoutTransactionsFiltered[i].outletId].gmvPayoutActual = BigNumber(outletDataDict[gmvOrdersRecentFiltered[i].outletId].gmvPayoutActual).plus(BigNumber(userOrderFigure.outletCycleMerchantPayoutsActual)).toNumber();

                    //////////////////////////////////////////////////////////////////////

                    // 2022-12-11 - To show under expanded summary

                    var orderStartDate = moment(gmvOrdersRecentFiltered[i].createdAt).startOf('day').valueOf();
                    var orderEndDate = moment(gmvOrdersRecentFiltered[i].createdAt).endOf('day').valueOf();

                    if (moment(orderEndDate).isSame(window.currToDateTime, 'day')) {
                        orderEndDate = window.currToDateTime;
                    }

                    if (outletDataDict[gmvOrdersRecentFiltered[i].outletId].recentPayoutTransactionDict[orderStartDate]) {
                        outletDataDict[gmvOrdersRecentFiltered[i].outletId].recentPayoutTransactionDict[orderStartDate].outletCycleKoodooPayoutsExpected =
                            BigNumber(outletDataDict[gmvOrdersRecentFiltered[i].outletId].recentPayoutTransactionDict[orderStartDate].outletCycleKoodooPayoutsExpected).plus(
                                (gmvOrdersRecentFiltered[i].settlementDetails && gmvOrdersRecentFiltered[i].settlementDetails.koodooProcessingFee) ? gmvOrdersRecentFiltered[i].settlementDetails.koodooProcessingFee : 0).toNumber();

                        outletDataDict[gmvOrdersRecentFiltered[i].outletId].recentPayoutTransactionDict[orderStartDate].outletCycleRazerPayouts =
                            BigNumber(outletDataDict[gmvOrdersRecentFiltered[i].outletId].recentPayoutTransactionDict[orderStartDate].outletCycleRazerPayouts).plus(
                                (gmvOrdersRecentFiltered[i].settlementDetails && gmvOrdersRecentFiltered[i].settlementDetails.commissionFeeFinal) ? gmvOrdersRecentFiltered[i].settlementDetails.commissionFeeFinal : 0).toNumber();

                        outletDataDict[gmvOrdersRecentFiltered[i].outletId].recentPayoutTransactionDict[orderStartDate].outletCycleMerchantPayoutsExpected =
                            BigNumber(outletDataDict[gmvOrdersRecentFiltered[i].outletId].recentPayoutTransactionDict[orderStartDate].outletCycleMerchantPayoutsExpected).plus(
                                (gmvOrdersRecentFiltered[i].paymentDetails &&
                                    (gmvOrdersRecentFiltered[i].paymentDetails.txn_ID !== undefined ||
                                        gmvOrdersRecentFiltered[i].paymentDetails.txnId !== undefined)
                                    &&
                                    (gmvOrdersRecentFiltered[i].settlementDetails && gmvOrdersRecentFiltered[i].settlementDetails.userOrderPriceAfterCommissionAndFee)
                                )
                                    ?
                                    gmvOrdersRecentFiltered[i].settlementDetails.userOrderPriceAfterCommissionAndFee
                                    :
                                    0).toNumber();

                        outletDataDict[gmvOrdersRecentFiltered[i].outletId].recentPayoutTransactionDict[orderStartDate].outletCycleKoodooPayoutsOnlineOnlyExpected =
                            BigNumber(outletDataDict[gmvOrdersRecentFiltered[i].outletId].recentPayoutTransactionDict[orderStartDate].outletCycleKoodooPayoutsOnlineOnlyExpected).plus(
                                (gmvOrdersRecentFiltered[i].settlementDetails && gmvOrdersRecentFiltered[i].settlementDetails.commissionFeeFinal > 0) ? gmvOrdersRecentFiltered[i].settlementDetails.koodooProcessingFee : 0).toNumber();
                    }

                    if (outletDataDict[gmvOrdersRecentFiltered[i].outletId].recentPayoutTransactionDict[orderEndDate]) {
                        outletDataDict[gmvOrdersRecentFiltered[i].outletId].recentPayoutTransactionDict[orderEndDate].outletCycleKoodooPayoutsExpected =
                            BigNumber(outletDataDict[gmvOrdersRecentFiltered[i].outletId].recentPayoutTransactionDict[orderEndDate].outletCycleKoodooPayoutsExpected).plus(
                                (gmvOrdersRecentFiltered[i].settlementDetails && gmvOrdersRecentFiltered[i].settlementDetails.koodooProcessingFee) ? gmvOrdersRecentFiltered[i].settlementDetails.koodooProcessingFee : 0).toNumber();

                        outletDataDict[gmvOrdersRecentFiltered[i].outletId].recentPayoutTransactionDict[orderEndDate].outletCycleRazerPayouts =
                            BigNumber(outletDataDict[gmvOrdersRecentFiltered[i].outletId].recentPayoutTransactionDict[orderEndDate].outletCycleRazerPayouts).plus(
                                (gmvOrdersRecentFiltered[i].settlementDetails && gmvOrdersRecentFiltered[i].settlementDetails.commissionFeeFinal) ? gmvOrdersRecentFiltered[i].settlementDetails.commissionFeeFinal : 0).toNumber();

                        outletDataDict[gmvOrdersRecentFiltered[i].outletId].recentPayoutTransactionDict[orderEndDate].outletCycleMerchantPayoutsExpected =
                            BigNumber(outletDataDict[gmvOrdersRecentFiltered[i].outletId].recentPayoutTransactionDict[orderEndDate].outletCycleMerchantPayoutsExpected).plus(
                                (gmvOrdersRecentFiltered[i].paymentDetails &&
                                    (gmvOrdersRecentFiltered[i].paymentDetails.txn_ID !== undefined ||
                                        gmvOrdersRecentFiltered[i].paymentDetails.txnId !== undefined)
                                    &&
                                    (gmvOrdersRecentFiltered[i].settlementDetails && gmvOrdersRecentFiltered[i].settlementDetails.userOrderPriceAfterCommissionAndFee)
                                )
                                    ?
                                    gmvOrdersRecentFiltered[i].settlementDetails.userOrderPriceAfterCommissionAndFee
                                    :
                                    0).toNumber();

                        outletDataDict[gmvOrdersRecentFiltered[i].outletId].recentPayoutTransactionDict[orderEndDate].outletCycleKoodooPayoutsOnlineOnlyExpected =
                            BigNumber(outletDataDict[gmvOrdersRecentFiltered[i].outletId].recentPayoutTransactionDict[orderEndDate].outletCycleKoodooPayoutsOnlineOnlyExpected).plus(
                                (gmvOrdersRecentFiltered[i].settlementDetails && gmvOrdersRecentFiltered[i].settlementDetails.commissionFeeFinal > 0) ? gmvOrdersRecentFiltered[i].settlementDetails.koodooProcessingFee : 0).toNumber();
                    }

                    //////////////////////////////////////////////////////////////////////

                    if ((gmvOrdersRecentFiltered[i].appType === APP_TYPE.MERCHANT ||
                        gmvOrdersRecentFiltered[i].appType === undefined ||
                        gmvOrdersRecentFiltered[i].appType === APP_TYPE.UNKNOWN) &&
                        !gmvOrdersRecentFiltered[i].isOnlineOrdering) {
                        outletDataDict[gmvOrdersRecentFiltered[i].outletId].merchantAppOrdersAmount += gmvOrdersRecentFiltered[i].finalPrice;
                        // console.log(outletDataDict[payoutTransactionsFiltered[i].outletId].merchantAppOrdersAmount);
                        outletDataDict[gmvOrdersRecentFiltered[i].outletId].merchantAppOrdersQty += 1;

                        outletDataDict[gmvOrdersRecentFiltered[i].outletId].lastOrderPlacedDateTime = gmvOrdersRecentFiltered[i].createdAt >
                            outletDataDict[gmvOrdersRecentFiltered[i].outletId].lastOrderPlacedDateTime ? gmvOrdersRecentFiltered[i].createdAt :
                            outletDataDict[gmvOrdersRecentFiltered[i].outletId].lastOrderPlacedDateTime;

                        // checkOutletFilterDict({
                        //   uniqueId: gmvOrdersRecent[i].outletId,
                        // }, gmvOrdersRecent[i].createdAt, {
                        //   startDate24H,
                        //   endDate24H,
                        //   startDate7D,
                        //   endDate7D,
                        //   startDate1M,
                        //   endDate1M,
                        //   startDate3M,
                        //   endDate3M,
                        // });

                        continue;
                    }

                    if (gmvOrdersRecentFiltered[i].appType === APP_TYPE.USER ||
                        gmvOrdersRecentFiltered[i].appType === APP_TYPE.WEB_ORDER) {
                        if (gmvOrdersRecentFiltered[i].orderType === ORDER_TYPE.DINEIN) {
                            if (gmvOrdersRecentFiltered[i].settlementDetails && gmvOrdersRecentFiltered[i].settlementDetails.commissionFeeFinal > 0) {
                                outletDataDict[gmvOrdersRecentFiltered[i].outletId].userAppAndWebDineInOnlineOrdersAmount += gmvOrdersRecentFiltered[i].finalPrice;
                                outletDataDict[gmvOrdersRecentFiltered[i].outletId].userAppAndWebDineInOnlineOrdersQty += 1;
                            }
                            else {
                                outletDataDict[gmvOrdersRecentFiltered[i].outletId].userAppAndWebDineInOrdersAmount += gmvOrdersRecentFiltered[i].finalPrice;
                                outletDataDict[gmvOrdersRecentFiltered[i].outletId].userAppAndWebDineInOrdersQty += 1;
                            }

                            outletDataDict[gmvOrdersRecentFiltered[i].outletId].lastOrderPlacedDateTime = gmvOrdersRecentFiltered[i].createdAt >
                                outletDataDict[gmvOrdersRecentFiltered[i].outletId].lastOrderPlacedDateTime ? gmvOrdersRecentFiltered[i].createdAt :
                                outletDataDict[gmvOrdersRecentFiltered[i].outletId].lastOrderPlacedDateTime;

                            // checkOutletFilterDict({
                            //   uniqueId: gmvOrdersRecent[i].outletId,
                            // }, gmvOrdersRecent[i].createdAt, {
                            //   startDate24H,
                            //   endDate24H,
                            //   startDate7D,
                            //   endDate7D,
                            //   startDate1M,
                            //   endDate1M,
                            //   startDate3M,
                            //   endDate3M,
                            // });

                            continue;
                        }
                        else if (gmvOrdersRecentFiltered[i].orderType === ORDER_TYPE.PICKUP) {
                            outletDataDict[gmvOrdersRecentFiltered[i].outletId].userAppAndWebTakeawayOrdersAmount += gmvOrdersRecentFiltered[i].finalPrice;
                            outletDataDict[gmvOrdersRecentFiltered[i].outletId].userAppAndWebTakeawayOrdersQty += 1;

                            outletDataDict[gmvOrdersRecentFiltered[i].outletId].lastOrderPlacedDateTime = gmvOrdersRecentFiltered[i].createdAt >
                                outletDataDict[gmvOrdersRecentFiltered[i].outletId].lastOrderPlacedDateTime ? gmvOrdersRecentFiltered[i].createdAt :
                                outletDataDict[gmvOrdersRecentFiltered[i].outletId].lastOrderPlacedDateTime;

                            // checkOutletFilterDict({
                            //   uniqueId: gmvOrdersRecent[i].outletId,
                            // }, gmvOrdersRecent[i].createdAt, {
                            //   startDate24H,
                            //   endDate24H,
                            //   startDate7D,
                            //   endDate7D,
                            //   startDate1M,
                            //   endDate1M,
                            //   startDate3M,
                            //   endDate3M,
                            // });

                            continue;
                        }
                        else if (gmvOrdersRecentFiltered[i].orderType === ORDER_TYPE.DELIVERY) {
                            outletDataDict[gmvOrdersRecentFiltered[i].outletId].userAppAndWebDeliveryOrdersAmount += gmvOrdersRecentFiltered[i].finalPrice;
                            outletDataDict[gmvOrdersRecentFiltered[i].outletId].userAppAndWebDeliveryOrdersQty += 1;

                            outletDataDict[gmvOrdersRecentFiltered[i].outletId].lastOrderPlacedDateTime = gmvOrdersRecentFiltered[i].createdAt >
                                outletDataDict[gmvOrdersRecentFiltered[i].outletId].lastOrderPlacedDateTime ? gmvOrdersRecentFiltered[i].createdAt :
                                outletDataDict[gmvOrdersRecentFiltered[i].outletId].lastOrderPlacedDateTime;

                            // checkOutletFilterDict({
                            //   uniqueId: gmvOrdersRecent[i].outletId,
                            // }, gmvOrdersRecent[i].createdAt, {
                            //   startDate24H,
                            //   endDate24H,
                            //   startDate7D,
                            //   endDate7D,
                            //   startDate1M,
                            //   endDate1M,
                            //   startDate3M,
                            //   endDate3M,
                            // });

                            continue;
                        }
                    }

                    ////////////////////////////////////////////////////////////

                    // to compatible with previous web orders

                    if (gmvOrdersRecentFiltered[i].appType === undefined &&
                        gmvOrdersRecentFiltered[i].isOnlineOrdering) {
                        if (gmvOrdersRecentFiltered[i].orderType === ORDER_TYPE.DINEIN) {
                            if (gmvOrdersRecentFiltered[i].settlementDetails && gmvOrdersRecentFiltered[i].settlementDetails.commissionFeeFinal > 0) {
                                outletDataDict[gmvOrdersRecentFiltered[i].outletId].userAppAndWebDineInOnlineOrdersAmount += gmvOrdersRecentFiltered[i].finalPrice;
                                outletDataDict[gmvOrdersRecentFiltered[i].outletId].userAppAndWebDineInOnlineOrdersQty += 1;
                            }
                            else {
                                outletDataDict[gmvOrdersRecentFiltered[i].outletId].userAppAndWebDineInOrdersAmount += gmvOrdersRecentFiltered[i].finalPrice;
                                outletDataDict[gmvOrdersRecentFiltered[i].outletId].userAppAndWebDineInOrdersQty += 1;
                            }

                            outletDataDict[gmvOrdersRecentFiltered[i].outletId].lastOrderPlacedDateTime = gmvOrdersRecentFiltered[i].createdAt >
                                outletDataDict[gmvOrdersRecentFiltered[i].outletId].lastOrderPlacedDateTime ? gmvOrdersRecentFiltered[i].createdAt :
                                outletDataDict[gmvOrdersRecentFiltered[i].outletId].lastOrderPlacedDateTime;

                            // checkOutletFilterDict({
                            //   uniqueId: gmvOrdersRecent[i].outletId,
                            // }, gmvOrdersRecent[i].createdAt, {
                            //   startDate24H,
                            //   endDate24H,
                            //   startDate7D,
                            //   endDate7D,
                            //   startDate1M,
                            //   endDate1M,
                            //   startDate3M,
                            //   endDate3M,
                            // });

                            continue;
                        }
                        else if (gmvOrdersRecentFiltered[i].orderType === ORDER_TYPE.PICKUP) {
                            outletDataDict[gmvOrdersRecentFiltered[i].outletId].userAppAndWebTakeawayOrdersAmount += gmvOrdersRecentFiltered[i].finalPrice;
                            outletDataDict[gmvOrdersRecentFiltered[i].outletId].userAppAndWebTakeawayOrdersQty += 1;

                            outletDataDict[gmvOrdersRecentFiltered[i].outletId].lastOrderPlacedDateTime = gmvOrdersRecentFiltered[i].createdAt >
                                outletDataDict[gmvOrdersRecentFiltered[i].outletId].lastOrderPlacedDateTime ? gmvOrdersRecentFiltered[i].createdAt :
                                outletDataDict[gmvOrdersRecentFiltered[i].outletId].lastOrderPlacedDateTime;

                            // checkOutletFilterDict({
                            //   uniqueId: gmvOrdersRecent[i].outletId,
                            // }, gmvOrdersRecent[i].createdAt, {
                            //   startDate24H,
                            //   endDate24H,
                            //   startDate7D,
                            //   endDate7D,
                            //   startDate1M,
                            //   endDate1M,
                            //   startDate3M,
                            //   endDate3M,
                            // });

                            continue;
                        }
                        else if (gmvOrdersRecentFiltered[i].orderType === gmvOrdersRecentFiltered.DELIVERY) {
                            outletDataDict[gmvOrdersRecentFiltered[i].outletId].userAppAndWebDeliveryOrdersAmount += gmvOrdersRecentFiltered[i].finalPrice;
                            outletDataDict[gmvOrdersRecentFiltered[i].outletId].userAppAndWebDeliveryOrdersQty += 1;

                            outletDataDict[gmvOrdersRecentFiltered[i].outletId].lastOrderPlacedDateTime = gmvOrdersRecentFiltered[i].createdAt >
                                outletDataDict[gmvOrdersRecentFiltered[i].outletId].lastOrderPlacedDateTime ? gmvOrdersRecentFiltered[i].createdAt :
                                outletDataDict[gmvOrdersRecentFiltered[i].outletId].lastOrderPlacedDateTime;

                            // checkOutletFilterDict({
                            //   uniqueId: gmvOrdersRecent[i].outletId,
                            // }, gmvOrdersRecent[i].createdAt, {
                            //   startDate24H,
                            //   endDate24H,
                            //   startDate7D,
                            //   endDate7D,
                            //   startDate1M,
                            //   endDate1M,
                            //   startDate3M,
                            //   endDate3M,
                            // });

                            continue;
                        }
                    }

                    ////////////////////////////////////////////////////////////

                    if (gmvOrdersRecentFiltered[i].appType === APP_TYPE.WAITER) {
                        outletDataDict[gmvOrdersRecentFiltered[i].outletId].waiterAppOrdersAmount += gmvOrdersRecentFiltered[i].finalPrice;
                        outletDataDict[gmvOrdersRecentFiltered[i].outletId].waiterAppOrdersQty += 1;

                        outletDataDict[gmvOrdersRecentFiltered[i].outletId].lastOrderPlacedDateTime = gmvOrdersRecentFiltered[i].createdAt >
                            outletDataDict[gmvOrdersRecentFiltered[i].outletId].lastOrderPlacedDateTime ? gmvOrdersRecentFiltered[i].createdAt :
                            outletDataDict[gmvOrdersRecentFiltered[i].outletId].lastOrderPlacedDateTime;

                        // checkOutletFilterDict({
                        //   uniqueId: gmvOrdersRecent[i].outletId,
                        // }, gmvOrdersRecent[i].createdAt, {
                        //   startDate24H,
                        //   endDate24H,
                        //   startDate7D,
                        //   endDate7D,
                        //   startDate1M,
                        //   endDate1M,
                        //   startDate3M,
                        //   endDate3M,
                        // });

                        continue;
                    }

                    console.log('orders not categorized');
                    console.log(gmvOrdersRecentFiltered[i]);

                    // outletDataDict[payoutTransactionsFiltered[i].outletId].totalOrdersAmount = outletDataDict[payoutTransactionsFiltered[i].outletId].userAppAndWebDineInOrdersAmount +
                    //   outletDataDict[payoutTransactionsFiltered[i].outletId].merchantAppOrdersAmount + outletDataDict[payoutTransactionsFiltered[i].outletId].waiterAppOrdersAmount +
                    //   outletDataDict[payoutTransactionsFiltered[i].outletId].userAppAndWebTakeawayOrdersAmount + outletDataDict[payoutTransactionsFiltered[i].outletId].userAppAndWebDeliveryOrdersAmount;
                    // outletDataDict[payoutTransactionsFiltered[i].outletId].totalOrdersQty = outletDataDict[payoutTransactionsFiltered[i].outletId].userAppAndWebDineInOrdersQty +
                    //   outletDataDict[payoutTransactionsFiltered[i].outletId].merchantAppOrdersQty + outletDataDict[payoutTransactionsFiltered[i].outletId].waiterAppOrdersQty +
                    //   outletDataDict[payoutTransactionsFiltered[i].outletId].userAppAndWebTakeawayOrdersQty + outletDataDict[payoutTransactionsFiltered[i].outletId].userAppAndWebDeliveryOrdersQty;
                }
            }

            console.log('outletDataDict');
            console.log(outletDataDict);

            ////////////////////////////////////////////////////////////

            // update details automatically

            if (outletDataDict[showDetailsOutletId] && outletDataDict[showDetailsOutletId].detailsList !== undefined) {
                // var detailsListTemp = outletDataDict[showDetailsOutletId].detailsList;
                // detailsListTemp.sort(sortByDateTime);
                // setAllOutletsEmployeesDetails(detailsListTemp);
                // setToggleCompare(!toggleCompare);

                setTableDataDetails(outletDataDict[showDetailsOutletId].detailsList);
            }

            ////////////////////////////////////////////////////////////

            var tempData = Object.entries(outletDataDict).map(([key, value]) => {
                // console.log('total');
                // console.log(value.userAppAndWebDineInOrdersAmount);
                // console.log(value.merchantAppOrdersAmount);
                // console.log(value.waiterAppOrdersAmount);
                // console.log(value.userAppAndWebTakeawayOrdersAmount);
                // console.log(value.userAppAndWebDeliveryOrdersAmount);

                return {
                    ...value,
                    totalOrdersAmount: value.userAppAndWebDineInOrdersAmount + value.userAppAndWebDineInOnlineOrdersAmount + value.merchantAppOrdersAmount + value.waiterAppOrdersAmount +
                        value.userAppAndWebTakeawayOrdersAmount + value.userAppAndWebDeliveryOrdersAmount,
                    totalOrdersQty: value.userAppAndWebDineInOrdersQty + value.userAppAndWebDineInOnlineOrdersQty + value.merchantAppOrdersQty + value.waiterAppOrdersQty +
                        value.userAppAndWebTakeawayOrdersQty + value.userAppAndWebDeliveryOrdersQty,

                    recentPayoutTransactionList: Object.entries(value.recentPayoutTransactionDict).map(([key, value]) => ({ ...value })),
                };
            });

            console.log('tempData');
            console.log(tempData);

            const outletLastOrderArray = Object.entries(window.pOutletFilterDict).map(
                ([key, value]) => ({ key: key, value: value }),
            );

            if (clickedChartDate !== null) {

            }
            else {
                // setMerchantDataAllLength(tempData.length);

                // // var startDate24H = moment().subtract(1, "day").startOf('day').valueOf();
                // // var endDate24H = moment().subtract(1, "day").endOf('day').valueOf();
                // setMerchantData24HLength(outletLastOrderArray.filter(temp => {
                //   // if (moment(startDate24H).isSameOrBefore(temp.value) &&
                //   //   moment(endDate24H).isAfter(temp.value)) {
                //   if (temp.value['is24H']) {
                //     return true;
                //   }
                //   else {
                //     return false;
                //   }
                // }).length);

                // // var startDate7D = moment().subtract(7, "day").startOf('day').valueOf();
                // // var endDate7D = moment().subtract(1, "day").endOf('day').valueOf();
                // setMerchantData7DLength(outletLastOrderArray.filter(temp => {
                //   // if (moment(startDate7D).isSameOrBefore(temp.value) &&
                //   //   moment(endDate7D).isAfter(temp.value)) {
                //   if (temp.value['is7D']) {
                //     return true;
                //   }
                //   else {
                //     return false;
                //   }
                // }).length);

                // // var startDate1M = moment().subtract(1, "month").startOf('month').startOf('day').valueOf();
                // // var endDate1M = moment().subtract(1, "month").endOf('month').endOf('day').valueOf();
                // setMerchantData1MLength(outletLastOrderArray.filter(temp => {
                //   // if (moment(startDate1M).isSameOrBefore(temp.value) &&
                //   //   moment(endDate1M).isAfter(temp.value)) {
                //   if (temp.value['is1M']) {
                //     return true;
                //   }
                //   else {
                //     return false;
                //   }
                // }).length);

                // // var startDate3M = moment().subtract(3, "month").startOf('month').startOf('day').valueOf();
                // // var endDate3M = moment().subtract(1, "month").endOf('month').endOf('day').valueOf();
                // setMerchantData3MLength(outletLastOrderArray.filter(temp => {
                //   // if (moment(startDate3M).isSameOrBefore(temp.value) &&
                //   //   moment(endDate3M).isAfter(temp.value)) {
                //   if (temp.value['is3M']) {
                //     return true;
                //   }
                //   else {
                //     return false;
                //   }
                // }).length);
            }

            // CommonStore.update(s => {
            //   s.outletFilterDict = window.pOutletFilterDict;
            // });

            // setMerchantData3MLength(tempData.filter(temp => {
            //   // if (moment().diff(temp.lastOrderPlacedDateTime || window.outletLastOrderDict[temp.outletId], 'month') <= 3) {
            //   if (moment(startDate3M).isSameOrBefore(temp.lastOrderPlacedDateTime || window.outletLastOrderDict[temp.outletId]) &&
            //     moment(endDate3M).isAfter(temp.lastOrderPlacedDateTime || window.outletLastOrderDict[temp.outletId])) {
            //     if (temp.lastOrderPlacedDateTime || window.outletLastOrderDict[temp.outletId]) {
            //       return true;
            //     }
            //     else {
            //       return false;
            //     }
            //   }
            //   else {
            //     return false;
            //   }
            // }).length);

            if (
                // clickedChartDate !== null
                clickedBarChartDate !== null &&
                clickedBarChartDateUnit !== null
            ) {

            }
            else {
                if (merchantDataFilterType === MERCHANT_DATA_FILTER.ALL) {
                    tempData = tempData;
                }
                else if (merchantDataFilterType === MERCHANT_DATA_FILTER.ACTIVE_24H) {
                    tempData = tempData.filter(temp => {
                        // if (moment().diff(temp.lastOrderPlacedDateTime || window.outletLastOrderDict[temp.outletId], 'hour') <= 24) {
                        if (moment(startDate24H).isSameOrBefore(temp.lastOrderPlacedDateTime) &&
                            moment(endDate24H).isAfter(temp.lastOrderPlacedDateTime)) {
                            if (temp.lastOrderPlacedDateTime) {
                                return true;
                            }
                            else {
                                return false;
                            }
                        }
                        else {
                            return false;
                        }
                    });
                }
                else if (merchantDataFilterType === MERCHANT_DATA_FILTER.ACTIVE_7D) {
                    tempData = tempData.filter(temp => {
                        // if (moment().diff(temp.lastOrderPlacedDateTime || window.outletLastOrderDict[temp.outletId], 'day') <= 7) {
                        if (moment(startDate7D).isSameOrBefore(temp.lastOrderPlacedDateTime) &&
                            moment(endDate7D).isAfter(temp.lastOrderPlacedDateTime)) {
                            if (temp.lastOrderPlacedDateTime) {
                                return true;
                            }
                            else {
                                return false;
                            }
                        }
                        else {
                            return false;
                        }
                    });
                }
                else if (merchantDataFilterType === MERCHANT_DATA_FILTER.ACTIVE_1M) {
                    tempData = tempData.filter(temp => {
                        // if (moment().diff(temp.lastOrderPlacedDateTime || window.outletLastOrderDict[temp.outletId], 'month') <= 1) {
                        if (moment(startDate1M).isSameOrBefore(temp.lastOrderPlacedDateTime) &&
                            moment(endDate1M).isAfter(temp.lastOrderPlacedDateTime)) {
                            if (temp.lastOrderPlacedDateTime) {
                                return true;
                            }
                            else {
                                return false;
                            }
                        }
                        else {
                            return false;
                        }
                    });
                }
                else if (merchantDataFilterType === MERCHANT_DATA_FILTER.ACTIVE_3M) {
                    tempData = tempData.filter(temp => {
                        // if (moment().diff(temp.lastOrderPlacedDateTime || window.outletLastOrderDict[temp.outletId], 'month') <= 3) {
                        if (moment(startDate3M).isSameOrBefore(temp.lastOrderPlacedDateTime) &&
                            moment(endDate3M).isAfter(temp.lastOrderPlacedDateTime)) {
                            if (temp.lastOrderPlacedDateTime) {
                                return true;
                            }
                            else {
                                return false;
                            }
                        }
                        else {
                            return false;
                        }
                    });
                }
            }

            //////////////////////////////////////////////////////////////////

            // sort by default

            tempData.sort(sortByTotalOrdersAmountDesc);

            //////////////////////////////////////////////////////////////////

            const tempKCRMarray = tempData;

            console.log("detailsList", tempKCRMarray);

            // setAllOutletsEmployeesAction(tempKCRMarray);
            setTableDataSummary(tempKCRMarray);

            setPageCount(Math.ceil(tempKCRMarray.length / perPage));

            //////////////////////////////////////////////////////////////////

            // save data

            await AsyncStorage.setItem('outletFilterDict', JSON.stringify(window.pOutletFilterDict));
        }, 2000)
    }, [
        merchantsOnboarded, 
        outletsOnboarded, 
        ptStartDate, 
        ptEndDate, 
        merchantDataFilterType,
        outletUserActionDict,
        payoutTransactions,
        ptTimestamp,
        pteTimestamp,
        gmvOrdersRecent,
        clickedBarChartDate,
        clickedBarChartDateUnit,
        isMasterAccount,
        currOutletId,
    ]);

    //////////////////////////////////////////////////////////////

    navigation.setOptions({
        headerLeft: () => (
            <View
                style={[
                    styles.headerLeftStyle,
                    {
                        width: windowWidth * 0.17,
                    },
                ]}
            >
                <img src={headerLogo} width={124} height={26} />
                {/* <Image
              style={{
                width: 124,
                height: 26,
              }}
              resizeMode="contain"
              source={require('../assets/image/logo.png')}
            /> */}
            </View>
        ),
        headerTitle: () => (
            <View
                style={[
                    {
                        justifyContent: "center",
                        alignItems: "center",
                        // marginRight: Platform.OS === 'ios' ? "27%" : 0,
                        // bottom: switchMerchant ? '2%' : 0,
                        //width:  "55%",
                    },
                    Dimensions.get("screen").width <= 768
                        ? { right: Dimensions.get("screen").width * 0.12 }
                        : {},
                ]}
            >
                <Text
                    style={{
                        fontSize: 24,
                        // lineHeight: 25,
                        textAlign: "center",
                        fontFamily: "NunitoSans-Bold",
                        color: Colors.whiteColor,
                        opacity: 1,
                    }}
                >
                    Payout Report
                </Text>
            </View>
        ),
        headerRight: () => (
            <View
                style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between",
                }}
            >
                {/* {console.log('edward test')} */}
                {/* {console.log(outletSelectDropdownView)} */}
                {outletSelectDropdownView && outletSelectDropdownView()}
                <View
                    style={{
                        backgroundColor: "white",
                        width: 0.5,
                        height: Dimensions.get("screen").height * 0.025,
                        opacity: 0.8,
                        marginHorizontal: 15,
                        bottom: -1,
                        // borderWidth: 1
                    }}
                ></View>
                <TouchableOpacity
                    onPress={() => {
                        navigation.navigate("General Settings - KooDoo BackOffice");
                    }}
                    style={{ flexDirection: "row", alignItems: "center" }}
                >
                    <Text
                        style={{
                            fontFamily: "NunitoSans-SemiBold",
                            fontSize: 16,
                            color: Colors.secondaryColor,
                            marginRight: 15,
                        }}
                    >
                        {userName}
                    </Text>
                    <View
                        style={{
                            //backgroundColor: 'red',
                            marginRight: 30,
                            width: windowHeight * 0.05,
                            height: windowHeight * 0.05,
                            borderRadius: windowHeight * 0.05 * 0.5,
                            alignItems: "center",
                            justifyContent: "center",
                            backgroundColor: "white",
                        }}
                    >
                        <img
                            src={personicon}
                            width={windowHeight * 0.035}
                            height={windowHeight * 0.035}
                        />
                    </View>
                </TouchableOpacity>
            </View>
        ),
    });

    return (
        <View
            style={[
                styles.container,
                {
                    height: windowHeight,
                    width: windowWidth,
                },
            ]}
        >
            <View style={{ flex: 0.8 }}>
                <SideBar navigation={navigation} selectedTab={8} expandReport={true} />
                {/* <Text>soimething</Text> */}
            </View>

            <View style={{ height: windowHeight, flex: 9 }}>
                <ScrollView
                    showsVerticalScrollIndicator={false}
                    style={{}}
                    contentContainerStyle={{
                        paddingBottom: windowHeight * 0.1,
                        backgroundColor: Colors.highlightColor,
                    }}
                >
                    {/* <ScrollView horizontal={true} showsHorizontalScrollIndicator={true}> */}

                    <View
                        style={{
                            flexDirection: "row",
                            alignItems: "center",
                            alignSelf: "center",
                            justifyContent: "space-between",
                            paddingHorizontal: 18,
                            marginTop: 30,
                            width: "100%",
                        }}
                    >
                        <Text
                            style={{
                                fontSize: switchMerchant ? 20 : 26,
                                fontFamily: "NunitoSans-Bold",

                                ...isMobile() && {
                                    fontSize: 16,
                                },
                            }}
                        >
                            Payout Report
                        </Text>
                        <View
                            style={{
                                flexDirection: "row",
                            }}>
                            {/* <View style={{ marginRight: 10, }}>
                                <DropDownPicker
                                    style={{
                                        backgroundColor: Colors.fieldtBgColor,
                                        width: 210,
                                        height: 40,
                                        borderRadius: 10,
                                        borderWidth: 1,
                                        borderColor: "#E5E5E5",
                                        flexDirection: "row",
                                    }}
                                    dropDownContainerStyle={{
                                        width: 210,
                                        backgroundColor: Colors.fieldtBgColor,
                                        borderColor: "#E5E5E5",
                                    }}
                                    labelStyle={{
                                        marginLeft: 5,
                                        flexDirection: "row",
                                    }}
                                    textStyle={{
                                        fontSize: 14,
                                        fontFamily: 'NunitoSans-Regular',

                                        marginLeft: 5,
                                        paddingVertical: 10,
                                        flexDirection: "row",
                                    }}
                                    selectedItemContainerStyle={{
                                        flexDirection: "row",
                                    }}

                                    showArrowIcon={true}
                                    ArrowDownIconComponent={({ style }) => (
                                        <Ionicon
                                            size={25}
                                            color={Colors.fieldtTxtColor}
                                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                                            name="chevron-down-outline"
                                        />
                                    )}
                                    ArrowUpIconComponent={({ style }) => (
                                        <Ionicon
                                            size={25}
                                            color={Colors.fieldtTxtColor}
                                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                                            name="chevron-up-outline"
                                        />
                                    )}

                                    showTickIcon={true}
                                    TickIconComponent={({ press }) => (
                                        <Ionicon
                                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                                            color={
                                                press ? Colors.fieldtBgColor : Colors.primaryColor
                                            }
                                            name={'md-checkbox'}
                                            size={25}
                                        />
                                    )}

                                    placeholder="Choose Outlet"
                                    multipleText={`${selectedOutletList ? selectedOutletList.length : '0'} outlet(s) selected`}
                                    placeholderStyle={{
                                        color: Colors.fieldtTxtColor,
                                        // marginTop: 15,
                                    }}
                                    // multipleText={'%d outlet(s) selected'}
                                    items={outletDropdownList}
                                    value={selectedOutletList}
                                    multiple={true}
                                    open={openOS}
                                    setOpen={setOpenOS}
                                    onSelectItem={(items) => {
                                        // setSelectedOutletList(items.map(item => item.value))
                                        CommonStore.update((s) => {
                                            s.reportOutletIdList = items.map(item => item.value)
                                        })
                                    }}
                                    dropDownDirection="BOTTOM"
                                />
                            </View> */}
                            <TouchableOpacity
                                style={{
                                    justifyContent: "center",
                                    flexDirection: "row",
                                    borderWidth: 1,
                                    borderColor: Colors.primaryColor,
                                    backgroundColor: "#4E9F7D",
                                    borderRadius: 5,
                                    paddingHorizontal: 10,
                                    height: 40,
                                    alignItems: "center",
                                    shadowOpacity: 0,
                                    shadowColor: "#000",
                                    shadowOffset: {
                                        width: 0,
                                        height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                    zIndex: -1,

                                    marginRight: 10,

                                    top: 1,

                                    ...isMobile() && {
                                        marginRight: 30,
                                        marginBottom: 15,
                                    },
                                }}
                                onPress={() => {
                                    setExportModalVisibility(true);
                                }}
                            >
                                <View
                                    style={{ flexDirection: "row", alignItems: "center" }}
                                >
                                    <Icon
                                        name="download"
                                        size={20}
                                        color={Colors.whiteColor}
                                    />
                                    <Text
                                        style={{
                                            color: Colors.whiteColor,
                                            marginLeft: 5,
                                            fontSize: 16,
                                            fontFamily: "NunitoSans-Bold",
                                        }}
                                    >
                                        DOWNLOAD
                                    </Text>
                                </View>
                            </TouchableOpacity>

                            <View
                                style={[
                                    {
                                        height: switchMerchant ? 35 : 40,
                                    },
                                ]}
                            >
                                <View
                                    style={{
                                        width: switchMerchant ? 200 : 250,
                                        height: switchMerchant ? 35 : 40,
                                        backgroundColor: "white",
                                        borderRadius: 5,
                                        flexDirection: "row",
                                        alignContent: "center",
                                        alignItems: "center",

                                        shadowColor: "#000",
                                        shadowOffset: {
                                            width: 0,
                                            height: 2,
                                        },
                                        shadowOpacity: 0.22,
                                        shadowRadius: 3.22,
                                        elevation: 3,
                                        borderWidth: 1,
                                        borderColor: "#E5E5E5",

                                        ...isMobile() && {
                                            width: 150,

                                            marginRight: 5,
                                        },
                                    }}
                                >
                                    <Icon
                                        name="search"
                                        size={switchMerchant ? 13 : 18}
                                        color={Colors.primaryColor}
                                        style={{ marginLeft: 15 }}
                                    />
                                    <TextInput
                                        editable={!loading}
                                        underlineColorAndroid={Colors.whiteColor}
                                        style={{
                                            width: switchMerchant ? 180 : 220,
                                            fontSize: switchMerchant ? 10 : 15,
                                            fontFamily: "NunitoSans-Regular",
                                            paddingLeft: 5,
                                            height: 45,
                                        }}
                                        placeholderTextColor={Platform.select({
                                            ios: "#a9a9a9",
                                        })}
                                        clearButtonMode="while-editing"
                                        placeholder=" Search"
                                        onChangeText={(text) => {
                                            setSearch(text);
                                        }}
                                        value={search}
                                    />
                                </View>
                            </View>
                        </View>
                    </View>

                    <View
                        style={{
                            flexDirection: "row",
                            //backgroundColor: '#ffffff',
                            justifyContent: "space-between",
                            //padding: 18,
                            marginTop: 20,
                            width: "100%",

                            ...!isMobile() && {
                                paddingLeft: switchMerchant
                                    ? 0
                                    : windowWidth <= 1823 && windowWidth >= 1820
                                        ? "1.5%"
                                        : "1%",
                                paddingRight: switchMerchant
                                    ? 0
                                    : windowWidth <= 1823 && windowWidth >= 1820
                                        ? "1.5%"
                                        : "1%",
                            },
                        }}
                    >
                        <TouchableOpacity
                            style={[
                                {
                                    justifyContent: "center",
                                    flexDirection: "row",
                                    borderWidth: 1,
                                    borderColor: Colors.primaryColor,
                                    backgroundColor: "#4E9F7D",
                                    borderRadius: 5,
                                    //width: 160,
                                    paddingHorizontal: 10,
                                    height: switchMerchant ? 35 : 40,
                                    alignItems: "center",
                                    shadowOffset: {
                                        width: 0,
                                        height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                    zIndex: -1,

                                    opacity: !showDetails ? 0 : 100,
                                    // opacity: 100,

                                    ...isMobile() && {
                                        marginLeft: 30,
                                    },
                                },
                            ]}
                            onPress={() => {
                                setShowDetails(false);
                                setPageCount(
                                    Math.ceil(tableDataSummary.length / perPage)
                                );
                                setCurrentPage(pageReturn);

                                setShowDetailsOutletId('');

                                console.log("Returning to page");
                                console.log(pageReturn);
                            }}
                            disabled={!showDetails}
                        >
                            <AntDesign
                                name="arrowleft"
                                size={switchMerchant ? 10 : 20}
                                color={Colors.whiteColor}
                                style={{}}
                            />
                            <Text
                                style={{
                                    color: Colors.whiteColor,
                                    marginLeft: 5,
                                    fontSize: switchMerchant ? 10 : 16,
                                    fontFamily: "NunitoSans-Bold",

                                    ...isMobile() && {
                                        marginLeft: 0,
                                    },
                                }}
                            >
                                {isMobile() ? '' : 'SUMMARY'}
                            </Text>
                        </TouchableOpacity>

                        <View style={{
                            flexDirection: "row",

                            ...isMobile() && {
                                flexDirection: 'column',
                            },
                        }}>
                            <View
                                style={[
                                    {
                                        paddingHorizontal: 15,
                                        flexDirection: "row",
                                        alignItems: "center",
                                        borderRadius: 10,
                                        paddingVertical: 10,
                                        justifyContent: "center",
                                        backgroundColor: Colors.whiteColor,
                                        shadowOpacity: 0,
                                        shadowColor: "#000",
                                        shadowOffset: {
                                            width: 0,
                                            height: 2,
                                        },
                                        shadowOpacity: 0.22,
                                        shadowRadius: 3.22,
                                        elevation: 1,

                                        ...isMobile() && {
                                            marginRight: 30,
                                            // marginBottom: 10,
                                        },
                                    },
                                ]}
                            >
                                <View
                                    style={{ alignSelf: "center", marginRight: 5 }}
                                    onPress={() => {
                                        setState({
                                            pickerMode: "date",
                                            showDateTimePicker: true,
                                        });
                                    }}
                                >
                                    <GCalendar
                                        width={switchMerchant ? 15 : 20}
                                        height={switchMerchant ? 15 : 20}
                                    />
                                </View>

                                {/* <TouchableOpacity
                      onPress={() => {
                        setShowDateTimePicker(true);
                        setShowDateTimePicker1(false);
                      }}
                      style={{
                        marginHorizontal: 4,
                      }}
                    >
                      <Text
                        style={
                          switchMerchant
                            ? {
                                fontSize: 10,
                                fontFamily: "NunitoSans-Regular",
                              }
                            : { fontFamily: "NunitoSans-Regular" }
                        }
                      >
                        {moment(ptStartDate).format("DD MMM yyyy")}
                      </Text>
                    </TouchableOpacity> */}

                                <DatePicker
                                    // selected={ptStartDate.toDate()}
                                    selected={moment(ptStartDate).toDate()}
                                    onChange={(date) => {
                                        // setRev_date(moment(date).startOf('day'));

                                        setClickedBarChartDate(null);
                                        setClickedBarChartDateUnit(null);

                                        setMerchantDataFilterType(MERCHANT_DATA_FILTER.ALL);

                                        CommonStore.update(s => {
                                            s.historyStartDate = moment(date).startOf("day").valueOf();
                                        });
                                    }}
                                    maxDate={moment(ptEndDate).toDate()}
                                />

                                <Text
                                    style={
                                        switchMerchant
                                            ? { fontSize: 10, fontFamily: "NunitoSans-Regular" }
                                            : { fontFamily: "NunitoSans-Regular" }
                                    }
                                >
                                    -
                                </Text>

                                {/* <TouchableOpacity
                      onPress={() => {
                        setShowDateTimePicker(false);
                        setShowDateTimePicker1(true);
                      }}
                      style={{
                        marginHorizontal: 4,
                      }}
                    >
                      <Text
                        style={
                          switchMerchant
                            ? {
                              fontSize: 10,
                              fontFamily: "NunitoSans-Regular",
                            }
                            : { fontFamily: "NunitoSans-Regular" }
                        }
                      >
                        {moment(ptEndDate).format("DD MMM yyyy")}
                      </Text>
                    </TouchableOpacity> */}

                                <DatePicker
                                    // selected={ptEndDate.toDate()}
                                    selected={moment(ptEndDate).toDate()}
                                    onChange={(dateParam) => {
                                        // setRev_date1(moment(date).endOf('day'));

                                        setClickedBarChartDate(null);
                                        setClickedBarChartDateUnit(null);

                                        setMerchantDataFilterType(MERCHANT_DATA_FILTER.ALL);

                                        var date = moment(dateParam).endOf("day").valueOf();
                                        if (moment(date).isSame(window.currToDateTime, 'day')) {
                                            date = window.currToDateTime;
                                        }

                                        CommonStore.update(s => {
                                            // s.ptEndDate = moment(date).endOf("day").valueOf();
                                            s.historyEndDate = moment(date).valueOf();
                                        });
                                    }}
                                    minDate={moment(ptStartDate).toDate()}
                                />
                            </View>
                        </View>
                    </View>

                    {/* <View
                            style={{
                                // backgroundColor: 'red',

                                zIndex: -1,

                                marginTop: 5,
                                marginBottom: -15,

                                padding: 20,
                                // paddingHorizontal: '2%',
                                paddingBottom: 0,
                            }}
                        >
                            <FusionCharts
                                {...{
                                    width: "100%",
                                    // width: windowWidth *
                                    //   (0.84 - Styles.sideBarWidth),
                                    // height: windowHeight * 0.5,
                                    type: CHART_DATA[CHART_TYPE.DASHBOARD_LINE_CHART_SALES]
                                        .type, //msline
                                    dataFormat: salesLineChart.dataFormat, //json
                                    dataSource: salesLineChart.dataSource,
                                    events: {
                                        'dataPlotClick': function (ev, props) {
                                            // var infoElem = document.getElementById("infolbl");
                                            console.log(ev);
                                            console.log(props);

                                            // setClickedChartDate(props.id);
                                        },
                                    }
                                }}
                            />

                            <View
                                style={{
                                    // backgroundColor: 'red',

                                    zIndex: -1,

                                    marginTop: 5,
                                    marginBottom: -15,

                                    // padding: 20,
                                    paddingTop: 10,

                                    // paddingHorizontal: '2%',
                                    paddingBottom: 10,
                                }}
                            >
                                <FusionCharts
                                    {...{
                                        width: "100%",
                                        // width: windowWidth *
                                        //   (0.84 - Styles.sideBarWidth),
                                        // height: windowHeight * 0.5,
                                        type: CHART_DATA[CHART_TYPE.REPORT_PRODUCT_SALES]
                                            .type, //msline
                                        dataFormat: productSalesChart.dataFormat, //json
                                        dataSource: productSalesChart.dataSource,
                                        events: {
                                            'dataPlotClick': function (ev, props) {
                                                // var infoElem = document.getElementById("infolbl");
                                                console.log(ev);
                                                console.log(props);

                                                if (props && props.id) {
                                                    var clickedBarChartDateTemp = props.id.split('|')[0];
                                                    var clickedBarChartDateUnitTemp = props.id.split('|')[1];

                                                    setClickedBarChartDate(parseInt(clickedBarChartDateTemp));
                                                    setClickedBarChartDateUnit(clickedBarChartDateUnitTemp);
                                                }

                                                // setClickedChartDate(props.id);
                                            },
                                        }
                                    }}
                                />
                            </View>

                            {/* 2023-02-12 - Online QR sales line chart *

                            <View
                                style={{
                                    // backgroundColor: 'red',

                                    zIndex: -1,

                                    marginTop: 5,
                                    marginBottom: -15,

                                    // padding: 20,
                                    paddingTop: 10,

                                    // paddingHorizontal: '2%',
                                    paddingBottom: 10,
                                }}
                            >
                                <FusionCharts
                                    {...{
                                        width: "100%",
                                        // width: windowWidth *
                                        //   (0.84 - Styles.sideBarWidth),
                                        height: windowHeight * 0.9,
                                        ...isMobile() && {
                                            height: windowHeight * 0.9,
                                        },
                                        type: CHART_DATA[CHART_TYPE.REPORT_ONLINE_QR_SALES]
                                            .type, //msline
                                        dataFormat: onlineQrSalesChart.dataFormat, //json
                                        dataSource: onlineQrSalesChart.dataSource,
                                        events: {
                                            'dataPlotClick': function (ev, props) {
                                                // var infoElem = document.getElementById("infolbl");
                                                console.log(ev);
                                                console.log(props);

                                                if (props && props.id) {
                                                    // var clickedBarChartDateTemp = props.id.split('|')[0];
                                                    // var clickedBarChartDateUnitTemp = props.id.split('|')[1];

                                                    // setClickedBarChartDate(parseInt(clickedBarChartDateTemp));
                                                    // setClickedBarChartDateUnit(clickedBarChartDateUnitTemp);

                                                    var clickedOutletId = props.id;

                                                    // if (changedBoldTimestamp !== clickedOutletId) {
                                                    //   setChangedBoldTimestamp(clickedOutletId);

                                                    //   window.boldOutletQrSalesLineDict[clickedOutletId] = !window.boldOutletQrSalesLineDict[clickedOutletId];
                                                    // }

                                                    // setChangedBoldTimestamp(Date.now());

                                                    // setBoldOutletQrSalesLineDict({
                                                    //   ...boldOutletQrSalesLineDict,
                                                    //   [clickedOutletId]: !boldOutletQrSalesLineDict[clickedOutletId],
                                                    // });
                                                }

                                                // setClickedChartDate(props.id);
                                            },
                                        }
                                    }}
                                />
                            </View>
                        </View> */}

                    {/* <View style={{
              width: '100%',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'flex-start',
  
              marginTop: 50,
              marginBottom: 10,
  
              padding: 20,
              paddingTop: 0,
              paddingBottom: 0,
              zIndex: -50,
  
              // backgroundColor: 'red',
            }}>
              <Text
                style={{
                  color: Colors.primaryColor,
                  fontSize: switchMerchant ? 10 : 16,
                  fontFamily: 'NunitoSans-Bold',
                }}>
                {`QR Orders Online (Sales): RM ${tableDataSummary.reduce((accum, row) => accum + row.userAppAndWebDineInOnlineOrdersAmount, 0).toFixed(2)} (${tableDataSummary.reduce((accum, row) => accum + row.userAppAndWebDineInOnlineOrdersQty, 0).toFixed(0)})`}
              </Text>
  
              <Text
                style={{
                  color: Colors.primaryColor,
                  fontSize: switchMerchant ? 10 : 16,
                  fontFamily: 'NunitoSans-Bold',
                }}>
                {`QR Orders Online (Commission): RM ${tableDataSummary.reduce((accum, row) =>
                  BigNumber(accum).plus(row.detailsList.reduce((accumDetails, rowDetails) => {
                    if (rowDetails.razerCharges > 0 && rowDetails.koodooCharges > 0) {
                      return BigNumber(accumDetails).plus(rowDetails.koodooCharges).toNumber();
                    }
                    else {
                      return accumDetails;
                    }
                  }, 0)).toNumber()
                  , 0).toFixed(2)} (${tableDataSummary.reduce((accum, row) => accum + row.userAppAndWebDineInOnlineOrdersQty, 0).toFixed(0)})`}
              </Text>
  
              <Text
                style={{
                  color: Colors.primaryColor,
                  fontSize: switchMerchant ? 10 : 16,
                  fontFamily: 'NunitoSans-Bold',
                }}>
                {`GMV Commission (Payable): RM ${tableDataSummary.reduce((accum, row) => accum + BigNumber(row.gmvCommissionActual).toNumber(), 0).toFixed(2)}`}
              </Text>
  
            </View> */}

                    <ScrollView
                        horizontal={true}
                        nestedScrollEnabled={true}
                        style={{
                            width: '100%',
                            // display: 'flex',
                            // flexDirection: 'row',
                            // alignItems: 'center',

                            // marginTop: 30,
                            padding: 20,
                            paddingBottom: 0,
                            zIndex: -50,

                            // backgroundColor: 'red',
                        }}
                        contentContainerStyle={{
                            width: '100%',
                            display: 'flex',
                            flexDirection: 'row',
                            alignItems: 'center',

                            paddingBottom: 5,
                            paddingRight: 5,
                        }}
                        showsHorizontalScrollIndicator={false}
                    >
                        <TouchableOpacity
                            style={{
                                justifyContent: 'center',
                                flexDirection: 'row',
                                borderWidth: 1,
                                borderColor: merchantDataFilterType === MERCHANT_DATA_FILTER.ALL ? Colors.primaryColor : Colors.whiteColor,
                                backgroundColor: merchantDataFilterType === MERCHANT_DATA_FILTER.ALL ? Colors.primaryColor : Colors.whiteColor,
                                borderRadius: 5,
                                paddingHorizontal: 15,
                                height: switchMerchant ? 35 : 40,
                                alignItems: 'center',
                                shadowOffset: {
                                    width: 0,
                                    height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                                zIndex: -1,

                                paddingTop: 1,

                                marginRight: 15,
                            }}
                            onPress={() => {
                                setClickedBarChartDate(null);
                                setClickedBarChartDateUnit(null);

                                setMerchantDataFilterType(MERCHANT_DATA_FILTER.ALL);

                                CommonStore.update(s => {
                                    s.historyStartDate = moment().subtract(12, "month").startOf("day").valueOf();
                                    // s.ptEndDate = moment().endOf('day').valueOf();
                                    s.historyEndDate = moment(window.currToDateTime).valueOf();
                                });
                            }}>
                            <Text
                                style={{
                                    color: merchantDataFilterType === MERCHANT_DATA_FILTER.ALL ? Colors.whiteColor : Colors.primaryColor,
                                    fontSize: switchMerchant ? 10 : 16,
                                    fontFamily: 'NunitoSans-Bold',
                                }}>
                                {/* {`All (${merchantDataAllLength} in total${merchantDataAllLength > 1 ? 's' : ''})`} */}
                                {`All`}
                            </Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                            style={{
                                justifyContent: 'center',
                                flexDirection: 'row',
                                borderWidth: 1,
                                borderColor: merchantDataFilterType === MERCHANT_DATA_FILTER.ACTIVE_24H ? Colors.primaryColor : Colors.whiteColor,
                                backgroundColor: merchantDataFilterType === MERCHANT_DATA_FILTER.ACTIVE_24H ? Colors.primaryColor : Colors.whiteColor,
                                borderRadius: 5,
                                paddingHorizontal: 15,
                                height: switchMerchant ? 35 : 40,
                                alignItems: 'center',
                                shadowOffset: {
                                    width: 0,
                                    height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                                zIndex: -1,

                                paddingTop: 1,

                                marginRight: 15,
                            }}
                            onPress={() => {
                                setClickedBarChartDate(null);
                                setClickedBarChartDateUnit(null);

                                setMerchantDataFilterType(MERCHANT_DATA_FILTER.ACTIVE_24H);

                                CommonStore.update(s => {
                                    s.historyStartDate = moment().startOf('day').valueOf();
                                    // s.ptEndDate = moment().endOf('day').valueOf();
                                    s.historyEndDate = moment(window.currToDateTime).valueOf();
                                });
                            }}>
                            <Text
                                style={{
                                    color: merchantDataFilterType === MERCHANT_DATA_FILTER.ACTIVE_24H ? Colors.whiteColor : Colors.primaryColor,
                                    fontSize: switchMerchant ? 10 : 16,
                                    fontFamily: 'NunitoSans-Bold',
                                }}>
                                {/* {`Today (${merchantData24HLength} active${merchantData24HLength > 1 ? 's' : ''})`} */}
                                {`Today`}
                            </Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                            style={{
                                justifyContent: 'center',
                                flexDirection: 'row',
                                borderWidth: 1,
                                borderColor: merchantDataFilterType === MERCHANT_DATA_FILTER.ACTIVE_7D ? Colors.primaryColor : Colors.whiteColor,
                                backgroundColor: merchantDataFilterType === MERCHANT_DATA_FILTER.ACTIVE_7D ? Colors.primaryColor : Colors.whiteColor,
                                borderRadius: 5,
                                paddingHorizontal: 15,
                                height: switchMerchant ? 35 : 40,
                                alignItems: 'center',
                                shadowOffset: {
                                    width: 0,
                                    height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                                zIndex: -1,

                                paddingTop: 1,

                                marginRight: 15,
                            }}
                            onPress={() => {
                                setClickedBarChartDate(null);
                                setClickedBarChartDateUnit(null);

                                setMerchantDataFilterType(MERCHANT_DATA_FILTER.ACTIVE_7D);

                                CommonStore.update(s => {
                                    s.historyStartDate = moment().subtract(7, "day").startOf('day').valueOf();
                                    // s.ptEndDate = moment().endOf('day').valueOf();
                                    s.historyEndDate = moment(window.currToDateTime).valueOf();
                                });
                            }}>
                            <Text
                                style={{
                                    color: merchantDataFilterType === MERCHANT_DATA_FILTER.ACTIVE_7D ? Colors.whiteColor : Colors.primaryColor,
                                    fontSize: switchMerchant ? 10 : 16,
                                    fontFamily: 'NunitoSans-Bold',
                                }}>
                                {/* {`Last 7d (${merchantData7DLength} active${merchantData7DLength > 1 ? 's' : ''})`} */}
                                {`Last 7d`}
                            </Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                            style={{
                                justifyContent: 'center',
                                flexDirection: 'row',
                                borderWidth: 1,
                                borderColor: merchantDataFilterType === MERCHANT_DATA_FILTER.ACTIVE_1M ? Colors.primaryColor : Colors.whiteColor,
                                backgroundColor: merchantDataFilterType === MERCHANT_DATA_FILTER.ACTIVE_1M ? Colors.primaryColor : Colors.whiteColor,
                                borderRadius: 5,
                                paddingHorizontal: 15,
                                height: switchMerchant ? 35 : 40,
                                alignItems: 'center',
                                shadowOffset: {
                                    width: 0,
                                    height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                                zIndex: -1,

                                paddingTop: 1,

                                marginRight: 15,
                            }}
                            onPress={() => {
                                setClickedBarChartDate(null);
                                setClickedBarChartDateUnit(null);

                                setMerchantDataFilterType(MERCHANT_DATA_FILTER.ACTIVE_1M);

                                CommonStore.update(s => {
                                    s.historyStartDate = moment().subtract(1, "month").startOf('day').valueOf();
                                    // s.ptEndDate = moment().endOf('day').valueOf();
                                    s.historyEndDate = moment(window.currToDateTime).valueOf();
                                });
                            }}>
                            <Text
                                style={{
                                    color: merchantDataFilterType === MERCHANT_DATA_FILTER.ACTIVE_1M ? Colors.whiteColor : Colors.primaryColor,
                                    fontSize: switchMerchant ? 10 : 16,
                                    fontFamily: 'NunitoSans-Bold',
                                }}>
                                {/* {`Last 1m (${merchantData1MLength} active${merchantData1MLength > 1 ? 's' : ''})`} */}
                                {`Last 1m`}
                            </Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                            style={{
                                justifyContent: 'center',
                                flexDirection: 'row',
                                borderWidth: 1,
                                borderColor: merchantDataFilterType === MERCHANT_DATA_FILTER.ACTIVE_3M ? Colors.primaryColor : Colors.whiteColor,
                                backgroundColor: merchantDataFilterType === MERCHANT_DATA_FILTER.ACTIVE_3M ? Colors.primaryColor : Colors.whiteColor,
                                borderRadius: 5,
                                paddingHorizontal: 15,
                                height: switchMerchant ? 35 : 40,
                                alignItems: 'center',
                                shadowOffset: {
                                    width: 0,
                                    height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                                zIndex: -1,

                                paddingTop: 1,

                                marginRight: 15,
                            }}
                            onPress={() => {
                                setClickedBarChartDate(null);
                                setClickedBarChartDateUnit(null);

                                setMerchantDataFilterType(MERCHANT_DATA_FILTER.ACTIVE_3M);

                                CommonStore.update(s => {
                                    s.historyStartDate = moment().subtract(3, "month").startOf('day').valueOf();
                                    // s.ptEndDate = moment().endOf('day').valueOf();
                                    s.historyEndDate = moment(window.currToDateTime).valueOf();
                                });
                            }}>
                            <Text
                                style={{
                                    color: merchantDataFilterType === MERCHANT_DATA_FILTER.ACTIVE_3M ? Colors.whiteColor : Colors.primaryColor,
                                    fontSize: switchMerchant ? 10 : 16,
                                    fontFamily: 'NunitoSans-Bold',
                                }}>
                                {/* {`Last 3m (${merchantData3MLength} active${merchantData3MLength > 1 ? 's' : ''})`} */}
                                {`Last 3m`}
                            </Text>
                        </TouchableOpacity>

                        {
                            (clickedBarChartDate !== null && clickedBarChartDateUnit !== null)
                                ?
                                <Text
                                    style={{
                                        color: Colors.primaryColor,
                                        fontSize: switchMerchant ? 10 : 16,
                                        fontFamily: 'NunitoSans-Bold',
                                    }}>
                                    {
                                        clickedBarChartDateUnit === 'hour'
                                            ?
                                            `Chart selection applied for ${moment(clickedBarChartDate).format('YYYY-MM-DD, hh:mm A')} (${clickedBarChartDateUnit}), click any date filter/range selection to reset`
                                            :
                                            ``
                                    }

                                    {
                                        clickedBarChartDateUnit === 'day'
                                            ?
                                            `Chart selection applied for ${moment(clickedBarChartDate).format('YYYY-MM-DD')} (${clickedBarChartDateUnit}), click any date filter/range selection to reset`
                                            :
                                            ``
                                    }

                                    {
                                        clickedBarChartDateUnit === 'week'
                                            ?
                                            `Chart selection applied for ${moment(clickedBarChartDate).format('YYYY-MM')} W${countWeekdayOccurrencesInMonth(clickedBarChartDate)} (${clickedBarChartDateUnit}), click any date filter/range selection to reset`
                                            :
                                            ``
                                    }

                                    {
                                        clickedBarChartDateUnit === 'month'
                                            ?
                                            `Chart selection applied for ${moment(clickedBarChartDate).format('YYYY-MM')} (${clickedBarChartDateUnit}), click any date filter/range selection to reset`
                                            :
                                            ``
                                    }
                                </Text>
                                :
                                <></>
                        }
                    </ScrollView>

                    {/* /////////////////////////////////////////////////////// */}

                    <ScrollView
                        horizontal={true}
                        showsHorizontalScrollIndicator={false}
                        nestedScrollEnabled={true}
                        contentContainerStyle={{
                            // minWidth: windowWidth,
                            width: "100%",
                            // paddingLeft: 0,

                            // ...isMobile() && {
                            //   flexDirection: 'column',
                            // },

                            flexDirection: 'column',

                            ...isMobile() && {
                                width: 1280,
                            },
                        }}
                        style={{
                            // minWidth: windowWidth,
                            // width: "100%",
                            marginTop: 0,
                            padding: 20,
                            zIndex: -50,
                            // backgroundColor: 'red',
                        }}

                    >
                        <View
                            style={{
                                backgroundColor: Colors.whiteColor,
                                width: "100%",
                                height: windowHeight * 0.66,
                                marginTop: 0,
                                // marginHorizontal: 30,
                                marginBottom: 10,
                                alignSelf: "center",
                                borderRadius: 5,
                                shadowOpacity: 0,
                                shadowColor: "#000",
                                shadowOffset: {
                                    width: 0,
                                    height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 3,

                                // ...isMobile() && {
                                //   width: 1280,
                                // },
                            }}
                        >
                            {!showDetails ? (
                                <View style={{ marginTop: 10, flexDirection: "row" }}>
                                    <View
                                        style={{
                                            flexDirection: "row",
                                            width: "2%",
                                            borderRightWidth: 1,
                                            borderRightColor: "lightgrey",
                                            alignItems: "center",
                                            justifyContent: "flex-start",
                                            paddingLeft: 10,
                                        }}
                                    >
                                        <View
                                        >
                                            <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                                <View style={{ flexDirection: "column" }}>
                                                    <Text
                                                        numberOfLines={3}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: "NunitoSans-Bold",
                                                        }}
                                                    >
                                                        {"No.\n\n"}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                                <View style={{ marginLeft: "3%", opacity: 0 }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                            </View>
                                        </View>
                                    </View>
                                    <View
                                        style={{
                                            flexDirection: "row",
                                            width: "8%",
                                            borderRightWidth: 1,
                                            borderRightColor: "lightgrey",
                                            alignItems: "center",
                                            justifyContent: "flex-start",
                                            padding: 10,
                                        }}
                                    >
                                        <TouchableOpacity
                                            onPress={() => {
                                                let temp = [...tableDataSummary];

                                                if (toggleCompare) {
                                                    temp.sort(sortByMerchantName);
                                                } else {
                                                    temp.sort(sortByMerchantNameDesc);
                                                }

                                                setTableDataSummary(temp);

                                                setToggleCompare(!toggleCompare);
                                            }}
                                        >
                                            <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                                <View style={{ flexDirection: "column" }}>
                                                    <Text
                                                        numberOfLines={3}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: "NunitoSans-Bold",
                                                        }}
                                                    >
                                                        {"Merchant\nName\n"}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                                <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>

                                    <View
                                        style={{
                                            flexDirection: "row",
                                            width: "7%",
                                            borderRightWidth: 1,
                                            borderRightColor: "lightgrey",
                                            alignItems: "center",
                                            justifyContent: "flex-start",
                                            padding: 10,
                                        }}
                                    >
                                        <TouchableOpacity
                                            onPress={() => {
                                                let temp = [...tableDataSummary];

                                                if (toggleCompare) {
                                                    temp.sort(sortByUserAppAndWebDineInOnlineOrdersAmount);
                                                } else {
                                                    temp.sort(sortByUserAppAndWebDineInOnlineOrdersAmountDesc);
                                                }

                                                setTableDataSummary(temp);

                                                setToggleCompare(!toggleCompare);
                                            }}
                                        >
                                            <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                                <View style={{ flexDirection: "column" }}>
                                                    <Text
                                                        numberOfLines={3}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: "NunitoSans-Bold",
                                                        }}
                                                    >
                                                        {"QR\nOrders\nOnline"}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                                <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>

                                    <View
                                        style={{
                                            flexDirection: "row",
                                            width: "7%",
                                            borderRightWidth: 1,
                                            borderRightColor: "lightgrey",
                                            alignItems: "center",
                                            justifyContent: "flex-start",
                                            padding: 10,
                                        }}
                                    >
                                        <TouchableOpacity
                                            onPress={() => {
                                                let temp = [...tableDataSummary];

                                                if (toggleCompare) {
                                                    temp.sort(sortByUserAppAndWebDineInOrdersAmount);
                                                } else {
                                                    temp.sort(sortByUserAppAndWebDineInOrdersAmountDesc);
                                                }

                                                setTableDataSummary(temp);

                                                setToggleCompare(!toggleCompare);
                                            }}
                                        >
                                            <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                                <View style={{ flexDirection: "column" }}>
                                                    <Text
                                                        numberOfLines={3}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: "NunitoSans-Bold",
                                                        }}
                                                    >
                                                        {"QR\nOrders\nOffline"}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                                <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>

                                    <View
                                        style={{
                                            flexDirection: "row",
                                            width: "7%",
                                            borderRightWidth: 1,
                                            borderRightColor: "lightgrey",
                                            alignItems: "center",
                                            justifyContent: "flex-start",
                                            padding: 10,
                                        }}
                                    >
                                        <TouchableOpacity
                                            onPress={() => {
                                                let temp = [...tableDataSummary];

                                                if (toggleCompare) {
                                                    temp.sort(sortByMerchantAppOrdersAmount);
                                                } else {
                                                    temp.sort(sortByMerchantAppOrdersAmountDesc);
                                                }

                                                setTableDataSummary(temp);

                                                setToggleCompare(!toggleCompare);
                                            }}
                                        >
                                            <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                                <View style={{ flexDirection: "column" }}>
                                                    <Text
                                                        numberOfLines={3}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: "NunitoSans-Bold",
                                                        }}
                                                    >
                                                        {"POS\nOrders\n"}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                                <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                    <View
                                        style={{
                                            flexDirection: "row",
                                            width: "7%",
                                            borderRightWidth: 1,
                                            borderRightColor: "lightgrey",
                                            alignItems: "center",
                                            justifyContent: "flex-start",
                                            padding: 10,
                                        }}
                                    >
                                        <TouchableOpacity
                                            onPress={() => {
                                                let temp = [...tableDataSummary];

                                                if (toggleCompare) {
                                                    temp.sort(sortByWaiterAppOrdersAmount);
                                                } else {
                                                    temp.sort(sortByWaiterAppOrdersAmountDesc);
                                                }

                                                setTableDataSummary(temp);

                                                setToggleCompare(!toggleCompare);
                                            }}
                                        >
                                            <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                                <View style={{ flexDirection: "column" }}>
                                                    <Text
                                                        numberOfLines={3}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: "NunitoSans-Bold",
                                                        }}
                                                    >
                                                        {"Waiter\nOrders\n"}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                                <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>

                                    <View
                                        style={{
                                            flexDirection: "row",
                                            width: "7%",
                                            borderRightWidth: 1,
                                            borderRightColor: "lightgrey",
                                            alignItems: "center",
                                            justifyContent: "flex-start",
                                            padding: 10,
                                        }}
                                    >
                                        <TouchableOpacity
                                            onPress={() => {
                                                let temp = [...tableDataSummary];

                                                if (toggleCompare) {
                                                    temp.sort(sortByUserAppAndWebTakeawayOrdersAmount);
                                                } else {
                                                    temp.sort(sortByUserAppAndWebTakeawayOrdersAmountDesc);
                                                }

                                                setTableDataSummary(temp);

                                                setToggleCompare(!toggleCompare);
                                            }}
                                        >
                                            <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                                <View style={{ flexDirection: "column" }}>
                                                    <Text
                                                        numberOfLines={3}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: "NunitoSans-Bold",
                                                        }}
                                                    >
                                                        {"Takeaway\nOrders\n"}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                                <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>

                                    {/* <View
                    style={{
                      flexDirection: "row",
                      width: "7%",
                      borderRightWidth: 1,
                      borderRightColor: "lightgrey",
                      alignItems: "center",
                      justifyContent: "flex-start",
                      padding: 10,
                    }}
                  >
                    <TouchableOpacity
                      onPress={() => {
                        let temp = [...tableDataSummary];
  
                        if (toggleCompare) {
                          temp.sort(sortByUserAppAndWebDeliveryOrdersAmount);
                        } else {
                          temp.sort(sortByUserAppAndWebDeliveryOrdersAmountDesc);
                        }
  
                        setTableDataSummary(temp);
  
                        setToggleCompare(!toggleCompare);
                      }}
                    >
                      <View style={{ flexDirection: "row", alignItems: 'center' }}>
                        <View style={{ flexDirection: "column" }}>
                          <Text
                            numberOfLines={3}
                            style={{
                              fontSize: switchMerchant ? 10 : 13,
                              fontFamily: "NunitoSans-Bold",
                            }}
                          >
                            {"Delivery\nOrders\n"}
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 8 : 10,
                              color: Colors.descriptionColor,
                            }}
                          ></Text>
                        </View>
                        <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                          <Entypo
                            name="triangle-up"
                            size={switchMerchant ? 7 : 14}
                            color={Colors.descriptionColor}
                          ></Entypo>
  
                          <Entypo
                            name="triangle-down"
                            size={switchMerchant ? 7 : 14}
                            color={Colors.descriptionColor}
                          ></Entypo>
                          <Text
                            style={{
                              fontSize: 10,
                              color: Colors.descriptionColor,
                            }}
                          ></Text>
                        </View>
                      </View>
                    </TouchableOpacity>
                  </View> */}

                                    <View
                                        style={{
                                            flexDirection: "row",
                                            width: "7%",
                                            borderRightWidth: 1,
                                            borderRightColor: "lightgrey",
                                            alignItems: "center",
                                            justifyContent: "flex-start",
                                            padding: 10,
                                        }}
                                    >
                                        <TouchableOpacity
                                            onPress={() => {
                                                let temp = [...tableDataSummary];

                                                if (toggleCompare) {
                                                    temp.sort(sortByTotalOrdersAmount);
                                                } else {
                                                    temp.sort(sortByTotalOrdersAmountDesc);
                                                }

                                                setTableDataSummary(temp);

                                                setToggleCompare(!toggleCompare);
                                            }}
                                        >
                                            <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                                <View style={{ flexDirection: "column" }}>
                                                    <Text
                                                        numberOfLines={3}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: "NunitoSans-Bold",
                                                        }}
                                                    >
                                                        {"Total\nOrders\n"}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                                <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>

                                    {/* /////////////////////// */}

                                    {/* 2022-12-10 - New columns */}

                                    <View
                                        style={{
                                            flexDirection: "row",
                                            width: "7%",
                                            borderRightWidth: 1,
                                            borderRightColor: "lightgrey",
                                            alignItems: "center",
                                            justifyContent: "flex-start",
                                            padding: 10,
                                        }}
                                    >
                                        <TouchableOpacity
                                            onPress={() => {
                                                let temp = [...tableDataSummary];

                                                if (toggleCompare) {
                                                    temp.sort(sortByGmvCommissionActual);
                                                } else {
                                                    temp.sort(sortByGmvCommissionActualDesc);
                                                }

                                                setTableDataSummary(temp);

                                                setToggleCompare(!toggleCompare);
                                            }}
                                        >
                                            <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                                <View style={{ flexDirection: "column" }}>
                                                    <Text
                                                        numberOfLines={3}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: "NunitoSans-Bold",

                                                            color: Colors.primaryColor,
                                                        }}
                                                    >
                                                        {"GMV\nCommission\n(Payable)"}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                                <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>

                                    <View
                                        style={{
                                            flexDirection: "row",
                                            width: "7%",
                                            borderRightWidth: 1,
                                            borderRightColor: "lightgrey",
                                            alignItems: "center",
                                            justifyContent: "flex-start",
                                            padding: 10,
                                        }}
                                    >
                                        <TouchableOpacity
                                            onPress={() => {
                                                let temp = [...tableDataSummary];

                                                if (toggleCompare) {
                                                    temp.sort(sortByGmvPayoutActual);
                                                } else {
                                                    temp.sort(sortByGmvPayoutActualDesc);
                                                }

                                                setTableDataSummary(temp);

                                                setToggleCompare(!toggleCompare);
                                            }}
                                        >
                                            <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                                <View style={{ flexDirection: "column" }}>
                                                    <Text
                                                        numberOfLines={3}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: "NunitoSans-Bold",
                                                        }}
                                                    >
                                                        {"O. Sales\nPayout\n(Actual)"}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                                <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>

                                    {/* 2022-11-26 - New columns */}

                                    <View
                                        style={{
                                            flexDirection: "row",
                                            width: "7%",
                                            borderRightWidth: 1,
                                            borderRightColor: "lightgrey",
                                            alignItems: "center",
                                            justifyContent: "flex-start",
                                            padding: 10,
                                        }}
                                    >
                                        <TouchableOpacity
                                            onPress={() => {
                                                let temp = [...tableDataSummary];

                                                if (toggleCompare) {
                                                    temp.sort(sortByGmvCommission);
                                                } else {
                                                    temp.sort(sortByGmvCommissionDesc);
                                                }

                                                setTableDataSummary(temp);

                                                setToggleCompare(!toggleCompare);
                                            }}
                                        >
                                            <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                                <View style={{ flexDirection: "column" }}>
                                                    <Text
                                                        numberOfLines={3}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: "NunitoSans-Bold",
                                                        }}
                                                    >
                                                        {"GMV\nCommission\n(Total)"}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                                <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>

                                    <View
                                        style={{
                                            flexDirection: "row",
                                            width: "7%",
                                            borderRightWidth: 1,
                                            borderRightColor: "lightgrey",
                                            alignItems: "center",
                                            justifyContent: "flex-start",
                                            padding: 10,
                                        }}
                                    >
                                        <TouchableOpacity
                                            onPress={() => {
                                                let temp = [...tableDataSummary];

                                                if (toggleCompare) {
                                                    temp.sort(sortByGmvPayout);
                                                } else {
                                                    temp.sort(sortByGmvPayoutDesc);
                                                }

                                                setTableDataSummary(temp);

                                                setToggleCompare(!toggleCompare);
                                            }}
                                        >
                                            <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                                <View style={{ flexDirection: "column" }}>
                                                    <Text
                                                        numberOfLines={3}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: "NunitoSans-Bold",
                                                        }}
                                                    >
                                                        {"O. Sales\nPayout\n(Expected)"}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                                <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>

                                    <View
                                        style={{
                                            flexDirection: "row",
                                            width: "7%",
                                            borderRightWidth: 1,
                                            borderRightColor: "lightgrey",
                                            alignItems: "center",
                                            justifyContent: "flex-start",
                                            padding: 10,
                                        }}
                                    >
                                        <TouchableOpacity
                                            onPress={() => {
                                                let temp = [...tableDataSummary];

                                                if (toggleCompare) {
                                                    temp.sort(sortByGmvFees);
                                                } else {
                                                    temp.sort(sortByGmvFeesDesc);
                                                }

                                                setTableDataSummary(temp);

                                                setToggleCompare(!toggleCompare);
                                            }}
                                        >
                                            <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                                <View style={{ flexDirection: "column" }}>
                                                    <Text
                                                        numberOfLines={3}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: "NunitoSans-Bold",
                                                        }}
                                                    >
                                                        {"GMV\nFees\n"}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                                <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>

                                    {/* /////////////////////// */}

                                    <View
                                        style={{
                                            flexDirection: "row",
                                            width: "8%",
                                            borderRightWidth: 1,
                                            borderRightColor: "lightgrey",
                                            alignItems: "center",
                                            justifyContent: "flex-start",
                                            padding: 10,
                                        }}
                                    >
                                        <TouchableOpacity
                                            onPress={() => {
                                                let temp = [...tableDataSummary];

                                                if (toggleCompare) {
                                                    temp.sort(sortByLastOrderPlacedDateTime);
                                                } else {
                                                    temp.sort(sortByLastOrderPlacedDateTimeDesc);
                                                }

                                                setTableDataSummary(temp);

                                                setToggleCompare(!toggleCompare);
                                            }}
                                        >
                                            <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                                <View style={{ flexDirection: "column" }}>
                                                    <Text
                                                        numberOfLines={3}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: "NunitoSans-Bold",
                                                        }}
                                                    >
                                                        {"Last\nOrder"}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                                <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>

                                    <View
                                        style={{
                                            flexDirection: "row",
                                            width: "5%",
                                            borderRightWidth: 1,
                                            borderRightColor: "lightgrey",
                                            alignItems: "center",
                                            justifyContent: "flex-start",
                                            padding: 10,
                                        }}
                                    >
                                        <View>
                                            <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                                <View style={{ flexDirection: "column" }}>
                                                    <Text
                                                        numberOfLines={3}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: "NunitoSans-Bold",
                                                        }}
                                                    >
                                                        {"Action"}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                                <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                            </View>
                                        </View>
                                    </View>
                                </View>
                            ) : (
                                // for details page

                                <View style={{ marginTop: 10, flexDirection: "row" }}>
                                    <View
                                        style={{
                                            flexDirection: "row",
                                            width: "8%",
                                            borderRightWidth: 1,
                                            borderRightColor: "lightgrey",
                                            alignItems: "center",
                                            justifyContent: "flex-start",
                                            paddingLeft: 10,
                                        }}
                                    >
                                        <TouchableOpacity
                                            onPress={() => {
                                                let temp = [...tableDataDetails];

                                                if (toggleCompare) {
                                                    temp.sort(sortByDateTime);
                                                } else {
                                                    temp.sort(sortByDateTimeDesc);
                                                }

                                                setTableDataDetails(temp);

                                                setToggleCompare(!toggleCompare);
                                            }}
                                        >
                                            <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                                <View style={{ flexDirection: "column" }}>
                                                    <Text
                                                        numberOfLines={3}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: "NunitoSans-Bold",
                                                        }}
                                                    >
                                                        {"Date\nTime"}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                                <View style={{ marginLeft: "3%", opacity: 100 }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                    <View
                                        style={{
                                            flexDirection: "row",
                                            width: "8%",
                                            borderRightWidth: 1,
                                            borderRightColor: "lightgrey",
                                            alignItems: "center",
                                            justifyContent: "flex-start",
                                            padding: 10,
                                        }}
                                    >
                                        <TouchableOpacity
                                            onPress={() => {
                                                let temp = [...tableDataDetails];

                                                if (toggleCompare) {
                                                    temp.sort(sortByOrderId);
                                                } else {
                                                    temp.sort(sortByOrderIdDesc);
                                                }

                                                setTableDataDetails(temp);

                                                setToggleCompare(!toggleCompare);
                                            }}
                                        >
                                            <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                                <View style={{ flexDirection: "column" }}>
                                                    <Text
                                                        numberOfLines={3}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: "NunitoSans-Bold",
                                                        }}
                                                    >
                                                        {"Order\nID\n(Joined)"}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                                <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                    <View
                                        style={{
                                            flexDirection: "row",
                                            width: "8%",
                                            borderRightWidth: 1,
                                            borderRightColor: "lightgrey",
                                            alignItems: "center",
                                            justifyContent: "flex-start",
                                            padding: 10,
                                        }}
                                    >
                                        <TouchableOpacity
                                            onPress={() => {
                                                let temp = [...tableDataDetails];

                                                if (toggleCompare) {
                                                    temp.sort(sortByIsQrOrder);
                                                } else {
                                                    temp.sort(sortByIsQrOrderDesc);
                                                }

                                                setTableDataDetails(temp);

                                                setToggleCompare(!toggleCompare);
                                            }}
                                        >
                                            <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                                <View style={{ flexDirection: "column" }}>
                                                    <Text
                                                        numberOfLines={3}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: "NunitoSans-Bold",
                                                        }}
                                                    >
                                                        {"QR\nOrder"}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                                <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                    {/* <View
                        style={{
                          flexDirection: "row",
                          width: "8%",
                          borderRightWidth: 1,
                          borderRightColor: "lightgrey",
                          alignItems: "center",
                          justifyContent: "flex-start",
                          padding: 10,
                        }}
                      >
                        <TouchableOpacity
                          onPress={() => {
                            let temp = [...allOutletsEmployeesDetails];
  
                            if (toggleCompare) {
                              temp.sort(sortByNetAmount);
                            } else {
                              temp.sort(sortByNetAmountDesc);
                            }
  
                            setAllOutletsEmployeesDetails(temp);
  
                            setToggleCompare(!toggleCompare);
                          }}
                        >
                          <View style={{ flexDirection: "row", alignItems: 'center' }}>
                            <View style={{ flexDirection: "column" }}>
                              <Text
                                numberOfLines={3}
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                  fontFamily: "NunitoSans-Bold",
                                }}
                              >
                                {"Net\nAmount"}
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 8 : 10,
                                  color: Colors.descriptionColor,
                                }}
                              ></Text>
                            </View>
                            <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 7 : 14}
                                color={Colors.descriptionColor}
                              ></Entypo>
  
                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 7 : 14}
                                color={Colors.descriptionColor}
                              ></Entypo>
                              <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }}
                              ></Text>
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View> */}

                                    {/* <View
                        style={{
                          flexDirection: "row",
                          width: "8%",
                          borderRightWidth: 1,
                          borderRightColor: "lightgrey",
                          alignItems: "center",
                          justifyContent: "flex-start",
                          padding: 10,
                        }}
                      >
                        <TouchableOpacity
                          onPress={() => {
                            let temp = [...allOutletsEmployeesDetails];
  
                            if (toggleCompare) {
                              temp.sort(sortByTax);
                            } else {
                              temp.sort(sortByTaxDesc);
                            }
  
                            setAllOutletsEmployeesDetails(temp);
  
                            setToggleCompare(!toggleCompare);
                          }}
                        >
                          <View style={{ flexDirection: "row", alignItems: 'center' }}>
                            <View style={{ flexDirection: "column" }}>
                              <Text
                                numberOfLines={3}
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                  fontFamily: "NunitoSans-Bold",
                                }}
                              >
                                {"Tax\n"}
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 8 : 10,
                                  color: Colors.descriptionColor,
                                }}
                              ></Text>
                            </View>
                            <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 7 : 14}
                                color={Colors.descriptionColor}
                              ></Entypo>
  
                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 7 : 14}
                                color={Colors.descriptionColor}
                              ></Entypo>
                              <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }}
                              ></Text>
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View> */}

                                    {/* <View
                        style={{
                          flexDirection: "row",
                          width: "8%",
                          borderRightWidth: 1,
                          borderRightColor: "lightgrey",
                          alignItems: "center",
                          justifyContent: "flex-start",
                          padding: 10,
                        }}
                      >
                        <TouchableOpacity
                          onPress={() => {
                            let temp = [...allOutletsEmployeesDetails];
  
                            if (toggleCompare) {
                              temp.sort(sortBySc);
                            } else {
                              temp.sort(sortByScDesc);
                            }
  
                            setAllOutletsEmployeesDetails(temp);
  
                            setToggleCompare(!toggleCompare);
                          }}
                        >
                          <View style={{ flexDirection: "row", alignItems: 'center' }}>
                            <View style={{ flexDirection: "column" }}>
                              <Text
                                numberOfLines={3}
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                  fontFamily: "NunitoSans-Bold",
                                }}
                              >
                                {"SC\n"}
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 8 : 10,
                                  color: Colors.descriptionColor,
                                }}
                              ></Text>
                            </View>
                            <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 7 : 14}
                                color={Colors.descriptionColor}
                              ></Entypo>
  
                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 7 : 14}
                                color={Colors.descriptionColor}
                              ></Entypo>
                              <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }}
                              ></Text>
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View> */}

                                    <View
                                        style={{
                                            flexDirection: "row",
                                            width: "8%",
                                            borderRightWidth: 1,
                                            borderRightColor: "lightgrey",
                                            alignItems: "center",
                                            justifyContent: "flex-start",
                                            padding: 10,
                                        }}
                                    >
                                        <TouchableOpacity
                                            onPress={() => {
                                                let temp = [...tableDataDetails];

                                                if (toggleCompare) {
                                                    temp.sort(sortByTotalAmount);
                                                } else {
                                                    temp.sort(sortByTotalAmountDesc);
                                                }

                                                setTableDataDetails(temp);

                                                setToggleCompare(!toggleCompare);
                                            }}
                                        >
                                            <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                                <View style={{ flexDirection: "column" }}>
                                                    <Text
                                                        numberOfLines={3}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: "NunitoSans-Bold",
                                                        }}
                                                    >
                                                        {"Total\nAmount"}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                                <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>

                                    <View
                                        style={{
                                            flexDirection: "row",
                                            width: "8%",
                                            borderRightWidth: 1,
                                            borderRightColor: "lightgrey",
                                            alignItems: "center",
                                            justifyContent: "flex-start",
                                            padding: 10,
                                        }}
                                    >
                                        <TouchableOpacity
                                            onPress={() => {
                                                let temp = [...tableDataDetails];

                                                if (toggleCompare) {
                                                    temp.sort(sortByRazerCharges);
                                                } else {
                                                    temp.sort(sortByRazerChargesDesc);
                                                }

                                                setTableDataDetails(temp);

                                                setToggleCompare(!toggleCompare);
                                            }}
                                        >
                                            <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                                <View style={{ flexDirection: "column" }}>
                                                    <Text
                                                        numberOfLines={3}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: "NunitoSans-Bold",
                                                        }}
                                                    >
                                                        {"Razer\nCharges"}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                                <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>

                                    <View
                                        style={{
                                            flexDirection: "row",
                                            width: "8%",
                                            borderRightWidth: 1,
                                            borderRightColor: "lightgrey",
                                            alignItems: "center",
                                            justifyContent: "flex-start",
                                            padding: 10,
                                        }}
                                    >
                                        <TouchableOpacity
                                            onPress={() => {
                                                let temp = [...tableDataDetails];

                                                if (toggleCompare) {
                                                    temp.sort(sortByKoodooCharges);
                                                } else {
                                                    temp.sort(sortByKoodooChargesDesc);
                                                }

                                                setTableDataDetails(temp);

                                                setToggleCompare(!toggleCompare);
                                            }}
                                        >
                                            <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                                <View style={{ flexDirection: "column" }}>
                                                    <Text
                                                        numberOfLines={3}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: "NunitoSans-Bold",
                                                        }}
                                                    >
                                                        {"KooDoo\nCharges\n(Expected)"}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                                <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>

                                    <View
                                        style={{
                                            flexDirection: "row",
                                            width: "8%",
                                            borderRightWidth: 1,
                                            borderRightColor: "lightgrey",
                                            alignItems: "center",
                                            justifyContent: "flex-start",
                                            padding: 10,
                                        }}
                                    >
                                        <TouchableOpacity
                                            onPress={() => {
                                                let temp = [...tableDataDetails];

                                                if (toggleCompare) {
                                                    temp.sort(sortByOutletPayout);
                                                } else {
                                                    temp.sort(sortByOutletPayoutDesc);
                                                }

                                                setTableDataDetails(temp);

                                                setToggleCompare(!toggleCompare);
                                            }}
                                        >
                                            <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                                <View style={{ flexDirection: "column" }}>
                                                    <Text
                                                        numberOfLines={3}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: "NunitoSans-Bold",
                                                        }}
                                                    >
                                                        {"Outlet\nPayout\n(Expected)"}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                                <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>

                                    <View
                                        style={{
                                            flexDirection: "row",
                                            width: "8%",
                                            borderRightWidth: 1,
                                            borderRightColor: "lightgrey",
                                            alignItems: "center",
                                            justifyContent: "flex-start",
                                            padding: 10,
                                        }}
                                    >
                                        <TouchableOpacity
                                            onPress={() => {
                                                let temp = [...tableDataDetails];

                                                if (toggleCompare) {
                                                    temp.sort(sortBySettlementDate);
                                                } else {
                                                    temp.sort(sortBySettlementDateDesc);
                                                }

                                                setTableDataDetails(temp);

                                                setToggleCompare(!toggleCompare);
                                            }}
                                        >
                                            <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                                <View style={{ flexDirection: "column" }}>
                                                    <Text
                                                        numberOfLines={3}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: "NunitoSans-Bold",
                                                        }}
                                                    >
                                                        {"Settlement\nDate"}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                                <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                    <View
                                        style={{
                                            flexDirection: "row",
                                            width: "8%",
                                            borderRightWidth: 1,
                                            borderRightColor: "lightgrey",
                                            alignItems: "center",
                                            justifyContent: "flex-start",
                                            padding: 10,
                                        }}
                                    >
                                        <TouchableOpacity
                                            onPress={() => {
                                                let temp = [...tableDataDetails];

                                                if (toggleCompare) {
                                                    temp.sort(sortByPaymentMethod);
                                                } else {
                                                    temp.sort(sortByPaymentMethodDesc);
                                                }

                                                setTableDataDetails(temp);

                                                setToggleCompare(!toggleCompare);
                                            }}
                                        >
                                            <View style={{ flexDirection: "row", alignItems: 'center' }}>
                                                <View style={{ flexDirection: "column" }}>
                                                    <Text
                                                        numberOfLines={3}
                                                        style={{
                                                            fontSize: switchMerchant ? 10 : 13,
                                                            fontFamily: "NunitoSans-Bold",
                                                        }}
                                                    >
                                                        {"Payment\nMethod"}
                                                    </Text>
                                                    <Text
                                                        style={{
                                                            fontSize: switchMerchant ? 8 : 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                                <View style={{ marginLeft: "3%", marginTop: '0.5%' }}>
                                                    <Entypo
                                                        name="triangle-up"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>

                                                    <Entypo
                                                        name="triangle-down"
                                                        size={switchMerchant ? 7 : 14}
                                                        color={Colors.descriptionColor}
                                                    ></Entypo>
                                                    <Text
                                                        style={{
                                                            fontSize: 10,
                                                            color: Colors.descriptionColor,
                                                        }}
                                                    ></Text>
                                                </View>
                                            </View>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                            )}

                            {!showDetails ? (
                                <>
                                    {tableDataSummary.filter((item) => {
                                        return filterItem(item);
                                    }).length > 0 ? (
                                        <FlatList
                                            showsVerticalScrollIndicator={false}
                                            ref={flatListRef}
                                            data={tableDataSummary
                                                .filter((item) => {
                                                    return filterItem(item);
                                                })
                                                .slice(
                                                    (currentPage - 1) * perPage,
                                                    currentPage * perPage
                                                )}
                                            renderItem={renderItem}
                                            keyExtractor={(item, index) => String(index)}
                                            style={{ marginTop: 10 }}
                                        />
                                    ) : (
                                        <View
                                            style={{
                                                height: windowHeight * 0.5,
                                            }}
                                        >
                                            <View
                                                style={{
                                                    alignItems: "center",
                                                    justifyContent: "center",
                                                    height: "100%",
                                                }}
                                            >
                                                {/* <Text style={{ color: Colors.descriptionColor }}>
                              Loading...
                            </Text> */}
                                            </View>
                                        </View>
                                    )}
                                </>
                            ) : (
                                <>
                                    {tableDataDetails.filter((item) => {
                                        return filterItemDetails(item);
                                    }).length > 0 ? (
                                        <FlatList
                                            showsVerticalScrollIndicator={false}
                                            ref={flatListRef}
                                            data={tableDataDetails
                                                .filter((item) => {
                                                    return filterItemDetails(item);
                                                })
                                                .slice(
                                                    (currentDetailsPage - 1) * perPage,
                                                    currentDetailsPage * perPage
                                                )}
                                            renderItem={renderItemDetails}
                                            keyExtractor={(item, index) => String(index)}
                                            style={{ marginTop: 10 }}
                                        />
                                    ) : (
                                        <View
                                            style={{
                                                height: windowHeight * 0.5,
                                            }}
                                        >
                                            <View
                                                style={{
                                                    alignItems: "center",
                                                    justifyContent: "center",
                                                    height: "100%",
                                                }}
                                            >
                                                {/* <Text style={{ color: Colors.descriptionColor }}>
                              - No Data Available -
                            </Text> */}
                                            </View>
                                        </View>
                                    )}
                                </>
                            )}
                        </View>

                        {!showDetails ? (
                            <View
                                style={{
                                    flexDirection: "row",
                                    marginTop: 10,
                                    width: "100%",
                                    alignItems: "center",
                                    alignSelf: "center",
                                    justifyContent: "flex-end",
                                    top:
                                        Platform.OS == "ios"
                                            ? pushPagingToTop && keyboardHeight > 0
                                                ? -keyboardHeight * 1
                                                : 0
                                            : 0,
                                    borderRadius: pushPagingToTop && keyboardHeight > 0 ? 8 : 0,
                                    paddingHorizontal:
                                        pushPagingToTop && keyboardHeight > 0 ? 10 : 0,
                                }}
                            >
                                <Text
                                    style={{
                                        fontSize: switchMerchant ? 10 : 14,
                                        fontFamily: "NunitoSans-Bold",
                                        marginRight: "1%",
                                    }}
                                >
                                    Items Showed
                                </Text>
                                <View
                                    style={{
                                        width: Platform.OS === "ios" ? 65 : "13%", //65,
                                        height: switchMerchant ? 20 : 35,
                                        backgroundColor: Colors.whiteColor,
                                        borderRadius: 10,
                                        justifyContent: "center",
                                        paddingHorizontal: Platform.OS === "ios" ? 0 : 0,
                                        //paddingLeft:switchMerchant ? '4.5%': Platform.OS === 'ios' ? '2%' : '4%',
                                        // paddingTop: '-60%',
                                        borderWidth: 1,
                                        borderColor: "#E5E5E5",
                                        marginRight: "1%",
                                    }}
                                >
                                    {/* <RNPickerSelect
                          useNativeAndroidPickerStyle={false}
                          style={{
                            inputIOS: {
                              fontSize: switchMerchant ? 10 : 14,
                              fontFamily: "NunitoSans-Regular",
                              textAlign: "center",
                            },
                            inputAndroid: {
                              fontSize: switchMerchant ? 10 : 14,
                              fontFamily: "NunitoSans-Regular",
                              justifyContent: "center",
                              textAlign: "center",
                              height: 40,
                              color: "black",
                            },
                            inputAndroidContainer: { width: "100%" },
                            //backgroundColor: 'red',
                            height: 35,
  
                            chevronContainer: {
                              display: "none",
                            },
                            chevronDown: {
                              display: "none",
                            },
                            chevronUp: {
                              display: "none",
                            },
                          }}
                          items={TABLE_PAGE_SIZE_DROPDOWN_LIST.concat({
                            label: "All",
                            value: !showDetails
                              ? allOutletsEmployeesAction.length
                              : allOutletsEmployeesDetails.length,
                          })}
                          value={perPage}
                          onValueChange={(value) => {
                            setPerPage(value);
                          }}
                        /> */}
                                    <Picker
                                        selectedValue={perPage}
                                        style={{}}
                                        onValueChange={(value, text) => {
                                            setPerPage(value);
                                        }}
                                    >
                                        {TABLE_PAGE_SIZE_DROPDOWN_LIST.concat({
                                            label: "All",
                                            value: !showDetails
                                                ? tableDataSummary.length
                                                : tableDataDetails.length,
                                        }).map((value, index) => {
                                            return (
                                                <Picker.Item
                                                    key={index}
                                                    label={value.label}
                                                    value={value.value}
                                                />
                                            );
                                        })}
                                    </Picker>
                                </View>

                                <Text
                                    style={{
                                        fontSize: switchMerchant ? 10 : 14,
                                        fontFamily: "NunitoSans-Bold",
                                        marginRight: "1%",
                                    }}
                                >
                                    Page
                                </Text>
                                <View
                                    style={{
                                        width: switchMerchant ? 65 : 70,
                                        height: switchMerchant ? 20 : 35,
                                        backgroundColor: Colors.whiteColor,
                                        borderRadius: 10,
                                        justifyContent: "center",
                                        paddingHorizontal: 22,
                                        borderWidth: 1,
                                        borderColor: "#E5E5E5",
                                    }}
                                >
                                    {console.log("currentPage")}
                                    {console.log(currentPage)}

                                    <TextInput
                                        onChangeText={(text) => {
                                            var currentPageTemp =
                                                text.length > 0 ? parseInt(text) : 1;

                                            setCurrentPage(
                                                currentPageTemp > pageCount
                                                    ? pageCount
                                                    : currentPageTemp < 1
                                                        ? 1
                                                        : currentPageTemp
                                            );
                                        }}
                                        placeholder={
                                            pageCount !== 0 ? currentPage.toString() : "0"
                                        }
                                        placeholderTextColor={Platform.select({
                                            ios: "#a9a9a9",
                                        })}
                                        style={{
                                            color: "black",
                                            fontSize: switchMerchant ? 10 : 14,
                                            fontFamily: "NunitoSans-Regular",
                                            marginTop: Platform.OS === "ios" ? 0 : -15,
                                            marginBottom: Platform.OS === "ios" ? 0 : -15,
                                            textAlign: "center",
                                            width: "100%",
                                        }}
                                        value={pageCount !== 0 ? currentPage.toString() : "0"}
                                        defaultValue={
                                            pageCount !== 0 ? currentPage.toString() : "0"
                                        }
                                        keyboardType={"numeric"}
                                        onFocus={() => {
                                            setPushPagingToTop(true);
                                        }}
                                    />
                                </View>
                                <Text
                                    style={{
                                        fontSize: switchMerchant ? 10 : 14,
                                        fontFamily: "NunitoSans-Bold",
                                        marginLeft: "1%",
                                        marginRight: "1%",
                                    }}
                                >
                                    of {pageCount}
                                </Text>
                                <TouchableOpacity
                                    style={{
                                        width: switchMerchant ? 30 : 45,
                                        height: switchMerchant ? 20 : 28,
                                        backgroundColor: Colors.primaryColor,
                                        alignItems: "center",
                                        justifyContent: "center",
                                    }}
                                    onPress={() => {
                                        prevPage();
                                    }}
                                >
                                    <ArrowLeft color={Colors.whiteColor} />
                                </TouchableOpacity>
                                <TouchableOpacity
                                    style={{
                                        width: switchMerchant ? 30 : 45,
                                        height: switchMerchant ? 20 : 28,
                                        backgroundColor: Colors.primaryColor,
                                        alignItems: "center",
                                        justifyContent: "center",
                                    }}
                                    onPress={() => {
                                        nextPage();
                                    }}
                                >
                                    <ArrowRight color={Colors.whiteColor} />
                                </TouchableOpacity>
                            </View>
                        ) : (
                            <View
                                style={{
                                    flexDirection: "row",
                                    marginTop: 10,
                                    width: '100%',
                                    alignItems: "center",
                                    alignSelf: "center",
                                    justifyContent: "flex-end",
                                    top:
                                        Platform.OS == "ios"
                                            ? pushPagingToTop && keyboardHeight > 0
                                                ? -keyboardHeight * 1
                                                : 0
                                            : 0,
                                    borderRadius: pushPagingToTop && keyboardHeight > 0 ? 8 : 0,
                                    paddingHorizontal:
                                        pushPagingToTop && keyboardHeight > 0 ? 10 : 0,
                                }}
                            >
                                <Text
                                    style={{
                                        fontSize: switchMerchant ? 10 : 14,
                                        fontFamily: "NunitoSans-Bold",
                                        marginRight: "1%",
                                    }}
                                >
                                    Items Showed
                                </Text>
                                <View
                                    style={{
                                        width: Platform.OS === "ios" ? 65 : "13%", //65,
                                        height: switchMerchant ? 20 : 35,
                                        backgroundColor: Colors.whiteColor,
                                        borderRadius: 10,
                                        justifyContent: "center",
                                        paddingHorizontal: Platform.OS === "ios" ? 0 : 0,
                                        //paddingLeft:switchMerchant ? '4.5%': Platform.OS === 'ios' ? '2%' : '4%',
                                        // paddingTop: '-60%',
                                        borderWidth: 1,
                                        borderColor: "#E5E5E5",
                                        marginRight: "1%",
                                    }}
                                >
                                    {/* <RNPickerSelect
                          useNativeAndroidPickerStyle={false}
                          style={{
                            inputIOS: {
                              fontSize: switchMerchant ? 10 : 14,
                              fontFamily: "NunitoSans-Regular",
                              textAlign: "center",
                            },
                            inputAndroid: {
                              fontSize: switchMerchant ? 10 : 14,
                              fontFamily: "NunitoSans-Regular",
                              justifyContent: "center",
                              textAlign: "center",
                              height: 40,
                              color: "black",
                            },
                            inputAndroidContainer: { width: "100%" },
                            //backgroundColor: 'red',
                            height: 35,
  
                            chevronContainer: {
                              display: "none",
                            },
                            chevronDown: {
                              display: "none",
                            },
                            chevronUp: {
                              display: "none",
                            },
                          }}
                          items={TABLE_PAGE_SIZE_DROPDOWN_LIST.concat({
                            label: "All",
                            value: !showDetails
                              ? allOutletsEmployeesAction.length
                              : allOutletsEmployeesDetails.length,
                          })}
                          value={perPage}
                          onValueChange={(value) => {
                            setPerPage(value);
                          }}
                        /> */}
                                    <Picker
                                        selectedValue={perPage}
                                        style={{}}
                                        onValueChange={(value, text) => {
                                            setPerPage(value);
                                        }}
                                    >
                                        {TABLE_PAGE_SIZE_DROPDOWN_LIST.concat({
                                            label: "All",
                                            value: !showDetails
                                                ? tableDataSummary.length
                                                : tableDataDetails.length,
                                        }).map((value, index) => {
                                            return (
                                                <Picker.Item
                                                    key={index}
                                                    label={value.label}
                                                    value={value.value}
                                                />
                                            );
                                        })}
                                    </Picker>
                                </View>

                                <Text
                                    style={{
                                        fontSize: switchMerchant ? 10 : 14,
                                        fontFamily: "NunitoSans-Bold",
                                        marginRight: "1%",
                                    }}
                                >
                                    Page
                                </Text>
                                <View
                                    style={{
                                        width: switchMerchant ? 65 : 70,
                                        height: switchMerchant ? 20 : 35,
                                        backgroundColor: Colors.whiteColor,
                                        borderRadius: 10,
                                        justifyContent: "center",
                                        paddingHorizontal: 22,
                                        borderWidth: 1,
                                        borderColor: "#E5E5E5",
                                    }}
                                >
                                    {console.log("currentDetailsPage")}
                                    {console.log(currentDetailsPage)}

                                    <TextInput
                                        onChangeText={(text) => {
                                            var currentPageTemp =
                                                text.length > 0 ? parseInt(text) : 1;
                                            console.log("currentDetailsPage pending");
                                            console.log(
                                                currentPageTemp > pageCount
                                                    ? pageCount
                                                    : currentPageTemp < 1
                                                        ? 1
                                                        : currentPageTemp
                                            );
                                            setCurrentDetailsPage(
                                                currentPageTemp > pageCount
                                                    ? pageCount
                                                    : currentPageTemp < 1
                                                        ? 1
                                                        : currentPageTemp
                                            );
                                        }}
                                        placeholder={
                                            pageCount !== 0 ? currentDetailsPage.toString() : "0"
                                        }
                                        placeholderTextColor={Platform.select({
                                            ios: "#a9a9a9",
                                        })}
                                        style={{
                                            color: "black",
                                            fontSize: switchMerchant ? 10 : 14,
                                            fontFamily: "NunitoSans-Regular",
                                            marginTop: Platform.OS === "ios" ? 0 : -15,
                                            marginBottom: Platform.OS === "ios" ? 0 : -15,
                                            textAlign: "center",
                                            width: "100%",
                                        }}
                                        value={
                                            pageCount !== 0 ? currentDetailsPage.toString() : "0"
                                        }
                                        defaultValue={
                                            pageCount !== 0 ? currentDetailsPage.toString() : "0"
                                        }
                                        keyboardType={"numeric"}
                                        onFocus={() => {
                                            setPushPagingToTop(true);
                                        }}
                                    />
                                </View>
                                <Text
                                    style={{
                                        fontSize: switchMerchant ? 10 : 14,
                                        fontFamily: "NunitoSans-Bold",
                                        marginLeft: "1%",
                                        marginRight: "1%",
                                    }}
                                >
                                    of {pageCount}
                                </Text>
                                <TouchableOpacity
                                    style={{
                                        width: switchMerchant ? 30 : 45,
                                        height: switchMerchant ? 20 : 28,
                                        backgroundColor: Colors.primaryColor,
                                        alignItems: "center",
                                        justifyContent: "center",
                                    }}
                                    onPress={() => {
                                        prevDetailsPage();
                                    }}
                                >
                                    <ArrowLeft color={Colors.whiteColor} />
                                </TouchableOpacity>
                                <TouchableOpacity
                                    style={{
                                        width: switchMerchant ? 30 : 45,
                                        height: switchMerchant ? 20 : 28,
                                        backgroundColor: Colors.primaryColor,
                                        alignItems: "center",
                                        justifyContent: "center",
                                    }}
                                    onPress={() => {
                                        nextDetailsPage();
                                    }}
                                >
                                    <ArrowRight color={Colors.whiteColor} />
                                </TouchableOpacity>
                            </View>
                        )}
                    </ScrollView>
                </ScrollView>
                {/* </ScrollView> */}
            </View>

            <Modal
                style={{}}
                visible={exportModalVisibility}
                supportedOrientations={["portrait", "landscape"]}
                transparent={true}
                animationType={"fade"}
            >
                <View
                    style={{
                        flex: 1,
                        backgroundColor: Colors.modalBgColor,
                        alignItems: "center",
                        justifyContent: "center",
                    }}
                >
                    <View
                        style={{
                            height: Dimensions.get("screen").width * 0.12,
                            width: Dimensions.get("screen").width * 0.18,
                            backgroundColor: Colors.whiteColor,
                            borderRadius: 12,
                            padding: Dimensions.get("screen").width * 0.03,
                            alignItems: "center",
                            justifyContent: "center",

                            ...isMobile() && {
                                height: 200,
                                width: 300,
                            },
                        }}
                    >
                        <TouchableOpacity
                            disabled={isLoading}
                            style={{
                                position: "absolute",
                                right: Dimensions.get("screen").width * 0.015,
                                top: Dimensions.get("screen").width * 0.01,

                                elevation: 1000,
                                zIndex: 1000,

                                ...isMobile() && {
                                    right: 20,
                                    top: 20,
                                },
                            }}
                            onPress={() => {
                                setExportModalVisibility(false);
                            }}
                        >
                            <AntDesign
                                name="closecircle"
                                size={isMobile() ? 20 : 25}
                                color={Colors.fieldtTxtColor}
                            />
                        </TouchableOpacity>
                        <View
                            style={{
                                alignItems: "center",
                                top: "20%",
                                position: "absolute",
                            }}
                        >
                            <Text
                                style={{
                                    fontFamily: "NunitoSans-Bold",
                                    textAlign: "center",
                                    fontSize: isMobile() ? 16 : 24,
                                }}
                            >
                                Download Report
                            </Text>
                        </View>
                        <View style={{ top: switchMerchant ? "14%" : "10%" }}>
                            <View
                                style={{
                                    alignItems: "center",
                                    justifyContent: "center",
                                    flexDirection: "row",
                                    marginTop: 30,
                                }}
                            >
                                <TouchableOpacity
                                    disabled={isLoading}
                                    style={{
                                        justifyContent: "center",
                                        flexDirection: "row",
                                        borderWidth: 1,
                                        borderColor: Colors.primaryColor,
                                        backgroundColor: "#4E9F7D",
                                        borderRadius: 5,
                                        width: switchMerchant ? 100 : 100,
                                        paddingHorizontal: 10,
                                        height: switchMerchant ? 35 : 40,
                                        alignItems: "center",
                                        shadowOffset: {
                                            width: 0,
                                            height: 2,
                                        },
                                        shadowOpacity: 0.22,
                                        shadowRadius: 3.22,
                                        elevation: 1,
                                        zIndex: -1,

                                        // marginRight: 15,
                                    }}
                                    onPress={() => {
                                        handleExportExcel();
                                    }}
                                >
                                    {isLoading && isExcel ? (
                                        <ActivityIndicator
                                            size={"small"}
                                            color={Colors.whiteColor}
                                        />
                                    ) : (
                                        <Text
                                            style={{
                                                color: Colors.whiteColor,
                                                //marginLeft: 5,
                                                fontSize: switchMerchant ? 10 : 16,
                                                fontFamily: "NunitoSans-Bold",
                                            }}
                                        >
                                            EXCEL
                                        </Text>
                                    )}
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </View>
            </Modal>
            {/* <Modal
          style={{}}
          visible={exportModalVisibility}
          supportedOrientations={["portrait", "landscape"]}
          transparent={true}
          animationType={"fade"}
        >
          <View
            style={{
              flex: 1,
              backgroundColor: Colors.modalBgColor,
              alignItems: "center",
              justifyContent: "center",
              top:
                Platform.OS === "android"
                  ? 0
                  : keyboardHeight > 0
                    ? -keyboardHeight * 0.45
                    : 0,
            }}
          >
            <View
              style={{
                backgroundColor: Colors.whiteColor,
                borderRadius: 12,
                padding: windowWidth * 0.03,
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <TouchableOpacity
                disabled={isLoading}
                style={{
                  position: "absolute",
                  right: windowWidth * 0.02,
                  top: windowWidth * 0.02,
  
                  elevation: 1000,
                  zIndex: 1000,
                }}
                onPress={() => {
                  setExportModalVisibility(false);
                }}
              >
                <AntDesign
                  name="closecircle"
                  size={switchMerchant ? 15 : 25}
                  color={Colors.fieldtTxtColor}
                />
              </TouchableOpacity>
              <View
                style={{
                  alignItems: "center",
                  top: "20%",
                  position: "absolute",
                }}
              >
                <Text
                  style={{
                    fontFamily: "NunitoSans-Bold",
                    textAlign: "center",
                    fontSize: switchMerchant ? 16 : 24,
                  }}
                >
                  Download Report
                </Text>
              </View>
              <View style={{ top: switchMerchant ? "14%" : "10%" }}>
                <Text
                  style={{
                    fontSize: switchMerchant ? 10 : 20,
                    fontFamily: "NunitoSans-Bold",
                  }}
                >
                  Email Address:
                </Text>
                <TextInput
                  underlineColorAndroid={Colors.fieldtBgColor}
                  style={{
                    backgroundColor: Colors.fieldtBgColor,
                    width: switchMerchant ? 240 : 370,
                    height: switchMerchant ? 35 : 50,
                    borderRadius: 5,
                    padding: 5,
                    marginVertical: 5,
                    borderWidth: 1,
                    borderColor: "#E5E5E5",
                    paddingLeft: 10,
                    fontSize: switchMerchant ? 10 : 14,
                  }}
                  placeholderStyle={{ padding: 5 }}
                  placeholderTextColor={Platform.select({ ios: "#a9a9a9" })}
                  placeholder="Enter Your Email"
                  onChangeText={(text) => {
                    setExportEmail(text);
                  }}
                  value={exportEmail}
                />
                <Text
                  style={{
                    fontSize: switchMerchant ? 10 : 20,
                    fontFamily: "NunitoSans-Bold",
                    marginTop: 15,
                  }}
                >
                  Send As:
                </Text>
  
                <View
                  style={{
                    alignItems: "center",
                    justifyContent: "center",
                    flexDirection: "row",
                    marginTop: 10,
                  }}
                >
                  <TouchableOpacity
                    disabled={isLoading}
                    style={{
                      justifyContent: "center",
                      flexDirection: "row",
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: "#4E9F7D",
                      borderRadius: 5,
                      width: switchMerchant ? 100 : 100,
                      paddingHorizontal: 10,
                      height: switchMerchant ? 35 : 40,
                      alignItems: "center",
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                      marginRight: 15,
                    }}
                    onPress={() => {
                      if (exportEmail.length > 0) {
                        CommonStore.update((s) => {
                          s.isLoading = true;
                        });
  
                        setIsExcel(true);
  
                        const excelData = convertDataToExcelFormat();
  
                        generateEmailReport(
                          EMAIL_REPORT_TYPE.EXCEL,
                          excelData,
                          "KooDoo Add-Ons Sales Report",
                          "KooDoo Add-Ons Sales Report.xlsx",
                          `/merchant/${merchantId}/reports/${uuidv4()}.xlsx`,
                          exportEmail,
                          "KooDoo Add-Ons Sales Report",
                          "KooDoo Add-Ons Sales Report",
                          () => {
                            CommonStore.update((s) => {
                              s.isLoading = false;
                            });
  
                            setIsExcel(false);
  
                            Alert.alert(
                              "Success",
                              "Report will be sent to the email address soon"
                            );
  
                            setExportModalVisibility(false);
                          }
                        );
                      } else {
                        Alert.alert("Info", "Invalid email address");
                      }
                    }}
                  >
                    {isLoading && isExcel ? (
                      <ActivityIndicator
                        size={"small"}
                        color={Colors.whiteColor}
                      />
                    ) : (
                      <Text
                        style={{
                          color: Colors.whiteColor,
                          //marginLeft: 5,
                          fontSize: switchMerchant ? 10 : 16,
                          fontFamily: "NunitoSans-Bold",
                        }}
                      >
                        EXCEL
                      </Text>
                    )}
                  </TouchableOpacity>
  
                  <TouchableOpacity
                    disabled={isLoading}
                    style={{
                      justifyContent: "center",
                      flexDirection: "row",
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: "#4E9F7D",
                      borderRadius: 5,
                      width: switchMerchant ? 100 : 100,
                      paddingHorizontal: 10,
                      height: switchMerchant ? 35 : 40,
                      alignItems: "center",
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                    }}
                    onPress={() => {
                      if (exportEmail.length > 0) {
                        CommonStore.update((s) => {
                          s.isLoading = true;
                        });
  
                        setIsCsv(true);
  
                        const csvData = convertArrayToCSV(
                          tableDataSummary
                        );
  
                        generateEmailReport(
                          EMAIL_REPORT_TYPE.CSV,
                          csvData,
                          "KooDoo Add-Ons Sales Report",
                          "KooDoo Add-Ons Sales Report.csv",
                          `/merchant/${merchantId}/reports/${uuidv4()}.csv`,
                          exportEmail,
                          "KooDoo Add-Ons Sales Report",
                          "KooDoo Add-Ons Sales Report",
                          () => {
                            CommonStore.update((s) => {
                              s.isLoading = false;
                            });
  
                            setIsCsv(false);
  
                            Alert.alert(
                              "Success",
                              "Report will be sent to the email address soon"
                            );
  
                            setExportModalVisibility(false);
                          }
                        );
                      } else {
                        Alert.alert("Info", "Invalid email address");
                      }
                    }}
                  >
                    {isLoading && isCsv ? (
                      <ActivityIndicator
                        size={"small"}
                        color={Colors.whiteColor}
                      />
                    ) : (
                      <Text
                        style={{
                          color: Colors.whiteColor,
                          //marginLeft: 5,
                          fontSize: switchMerchant ? 10 : 16,
                          fontFamily: "NunitoSans-Bold",
                        }}
                      >
                        CSV
                      </Text>
                    )}
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
        </Modal> */}

            <DateTimePickerModal
                isVisible={showDateTimePicker}
                mode={"date"}
                onConfirm={(text) => {
                    // setRev_date(moment(text).startOf("day"));

                    setClickedBarChartDate(null);
                    setClickedBarChartDateUnit(null);

                    CommonStore.update(s => {
                        s.historyStartDate = moment(text).startOf("day").valueOf();
                    });

                    setShowDateTimePicker(false);

                    setSalesLineChartPeriod(CHART_PERIOD.NONE);
                }}
                onCancel={() => {
                    setShowDateTimePicker(false);
                }}
                maximumDate={moment(ptStartDate).toDate()}
            />

            <DateTimePickerModal
                isVisible={showDateTimePicker1}
                mode={"date"}
                onConfirm={(dateParam) => {
                    // setRev_date1(moment(text).endOf("day"));

                    setClickedBarChartDate(null);
                    setClickedBarChartDateUnit(null);

                    var date = moment(dateParam).endOf("day").valueOf();
                    if (moment(date).isSame(window.currToDateTime, 'day')) {
                        date = window.currToDateTime;
                    }

                    CommonStore.update(s => {
                        // s.ptEndDate = moment(dateParam).endOf("day").valueOf();
                        s.historyEndDate = moment(date).valueOf();
                    });

                    setShowDateTimePicker1(false);

                    setSalesLineChartPeriod(CHART_PERIOD.NONE);
                }}
                onCancel={() => {
                    setShowDateTimePicker1(false);
                }}
                minimumDate={moment(ptEndDate).toDate()}
            />

            <Modal
                style={{ flex: 1 }}
                visible={visible}
                transparent={true}
                animationType="slide"
            >
                <KeyboardAvoidingView
                    style={{
                        backgroundColor: "rgba(0,0,0,0.5)",
                        flex: 1,
                        justifyContent: "center",
                        alignItems: "center",
                        minHeight: windowHeight,
                        top:
                            Platform.OS === "ios" && keyboardHeight > 0
                                ? -keyboardHeight * 0.5
                                : 0,
                    }}
                >
                    <View style={styles.confirmBox1}>
                        <Text
                            style={{
                                fontSize: 24,
                                justifyContent: "center",
                                alignSelf: "center",
                                marginTop: 40,
                                fontFamily: "NunitoSans-Bold",
                            }}
                        >
                            Enter your email
                        </Text>
                        <View
                            style={{
                                justifyContent: "center",
                                alignSelf: "center",
                                alignContent: "center",
                                marginTop: 20,
                                flexDirection: "row",
                                width: "80%",
                            }}
                        >
                            <View style={{ justifyContent: "center", marginHorizontal: 5 }}>
                                <Text
                                    style={{ color: Colors.descriptionColor, fontSize: 20 }}
                                >
                                    email:
                                </Text>
                            </View>
                            <TextInput
                                underlineColorAndroid={Colors.fieldtBgColor}
                                style={[styles.textInput8, { paddingLeft: 5 }]}
                                placeholder="Enter your email"
                                // style={{
                                //     // paddingLeft: 1,
                                // }}
                                //defaultValue={extentionCharges}
                                onChangeText={(text) => {
                                    // setState({ exportEmail: text });
                                    setExportEmail(text);
                                }}
                                placeholderTextColor={Platform.select({ ios: "#a9a9a9" })}
                                value={exportEmail}
                            />
                        </View>
                        <Text
                            style={{
                                fontSize: 20,
                                fontFamily: "NunitoSans-Bold",
                                marginTop: 25,
                                justifyContent: "center",
                                alignSelf: "center",
                                alignContent: "center",
                            }}
                        >
                            Share As:
                        </Text>

                        {/* Share file using email */}
                        <View
                            style={{
                                justifyContent: "space-between",
                                alignSelf: "center",
                                marginTop: 10,
                                flexDirection: "row",
                                width: "80%",
                            }}
                        >
                            <TouchableOpacity
                                style={[
                                    styles.modalSaveButton1,
                                    {
                                        zIndex: -1,
                                    },
                                ]}
                                onPress={() => { }}
                            >
                                <Text
                                    style={[
                                        styles.modalDescText,
                                        { color: Colors.primaryColor },
                                    ]}
                                >
                                    Excel
                                </Text>
                            </TouchableOpacity>

                            <TouchableOpacity
                                style={[
                                    styles.modalSaveButton1,
                                    {
                                        zIndex: -1,
                                    },
                                ]}
                                onPress={() => { }}
                            >
                                <Text
                                    style={[
                                        styles.modalDescText,
                                        { color: Colors.primaryColor },
                                    ]}
                                >
                                    CSV
                                </Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={[
                                    styles.modalSaveButton1,
                                    {
                                        zIndex: -1,
                                    },
                                ]}
                                onPress={() => { }}
                            >
                                <Text
                                    style={[
                                        styles.modalDescText,
                                        { color: Colors.primaryColor },
                                    ]}
                                >
                                    PDF
                                </Text>
                            </TouchableOpacity>
                        </View>
                        <View
                            style={{
                                alignSelf: "center",
                                marginTop: 20,
                                justifyContent: "center",
                                alignItems: "center",
                                // width: 260,
                                width: windowWidth * 0.2,
                                height: 60,
                                alignContent: "center",
                                flexDirection: "row",
                                marginTop: 40,
                            }}
                        >
                            <TouchableOpacity
                                onPress={emailVariant}
                                style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    width: "100%",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    alignContent: "center",
                                    height: 60,
                                    borderBottomLeftRadius: 10,
                                    borderRightWidth: StyleSheet.hairlineWidth,
                                    borderTopWidth: StyleSheet.hairlineWidth,
                                }}
                            >
                                <Text
                                    style={{
                                        fontSize: 22,
                                        color: Colors.primaryColor,
                                        fontFamily: "NunitoSans-SemiBold",
                                    }}
                                >
                                    Email
                                </Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                onPress={() => {
                                    // setState({ visible: false });
                                    setVisible(false);
                                }}
                                style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    width: "100%",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    alignContent: "center",
                                    height: 60,
                                    borderBottomRightRadius: 10,
                                    borderTopWidth: StyleSheet.hairlineWidth,
                                }}
                            >
                                <Text
                                    style={{
                                        fontSize: 22,
                                        color: Colors.descriptionColor,
                                        fontFamily: "NunitoSans-SemiBold",
                                    }}
                                >
                                    Cancel
                                </Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </KeyboardAvoidingView>
            </Modal>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        // flex: 1,
        backgroundColor: "#ffffff",
        flexDirection: "row",
    },
    sidebar: {
        width: Dimensions.get("screen").width * Styles.sideBarWidth,
        shadowColor: "#000",
        shadowOffset: {
            width: 0,
            height: 8,
        },
        shadowOpacity: 0.44,
        shadowRadius: 10.32,

        elevation: 16,
    },
    content: {
        // padding: 20,
        paddingVertical: 20,
        backgroundColor: Colors.highlightColor,
    },
    headerLogo: {
        width: 112,
        height: 25,
        marginLeft: 10,
    },
    confirmBox: {
        // width: '30%',
        // height: '30%',
        // borderRadius: 30,
        // backgroundColor: Colors.whiteColor,
        width: Dimensions.get("screen").width * 0.4,
        height: Dimensions.get("screen").height * 0.3,
        borderRadius: 12,
        backgroundColor: Colors.whiteColor,
        justifyContent: "space-between",
    },
    modalContainer: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: "center",
        justifyContent: "center",
    },
    modalView: {
        height: Dimensions.get("screen").width * 0.2,
        width: Dimensions.get("screen").width * 0.3,
        backgroundColor: Colors.whiteColor,
        borderRadius: 12,
        padding: Dimensions.get("screen").width * 0.03,
        alignItems: "center",
        justifyContent: "center",
    },
    closeButton: {
        position: "absolute",
        right: Dimensions.get("screen").width * 0.02,
        top: Dimensions.get("screen").width * 0.02,

        elevation: 1000,
        zIndex: 1000,
    },
    modalTitle: {
        alignItems: "center",
        top: "20%",
        position: "absolute",
    },
    modalBody: {
        flex: 1,
        alignItems: "center",
        justifyContent: "center",
    },
    modalTitleText: {
        fontFamily: "NunitoSans-Bold",
        textAlign: "center",
        fontSize: 20,
    },
    modalDescText: {
        fontFamily: "NunitoSans-SemiBold",
        fontSize: 18,
        color: Colors.fieldtTxtColor,
    },
    modalBodyText: {
        flex: 1,
        fontFamily: "NunitoSans-SemiBold",
        fontSize: 25,
        width: "20%",
    },
    modalSaveButton: {
        width: Dimensions.get("screen").width * 0.15,
        backgroundColor: Colors.fieldtBgColor,
        height: 40,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 8,

        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 1,

        marginVertical: 10,
    },
    modalSaveButton1: {
        width: Dimensions.get("screen").width * 0.1,
        backgroundColor: Colors.fieldtBgColor,
        height: 40,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 8,

        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 1,

        marginVertical: 10,
    },
    confirmBox1: {
        width: Dimensions.get("screen").width * 0.4,
        height: Dimensions.get("screen").height * 0.4,
        borderRadius: 12,
        backgroundColor: Colors.whiteColor,
        justifyContent: "space-between",
    },
    submitText: {
        height:
            Platform.OS == "ios"
                ? Dimensions.get("screen").height * 0.06
                : Dimensions.get("screen").height * 0.07,
        paddingVertical: 5,
        paddingHorizontal: 20,
        flexDirection: "row",
        color: "#4cd964",
        textAlign: "center",
        borderRadius: 10,
        borderWidth: 1,
        borderColor: Colors.primaryColor,
        justifyContent: "center",
        alignContent: "center",
        alignItems: "center",
        marginRight: 10,
    },
    headerLeftStyle: {
        width: Dimensions.get("screen").width * 0.17,
        justifyContent: "center",
        alignItems: "center",
    },
});
export default KooDooPayoutScreen;
