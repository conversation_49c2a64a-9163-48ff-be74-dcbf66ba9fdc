import React, {
    Component,
    useReducer,
    useState,
    useEffect,
    useRef,
    useCallback,
} from 'react';
import {
    StyleSheet,
    ScrollView,
    Image,
    View,
    Text,
    Alert,
    TouchableOpacity,
    Dimensions,
    Switch,
    Modal,
    KeyboardAvoidingView,
    Platform,
    TextInput,
    ActivityIndicator,
    useWindowDimensions,
} from 'react-native';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Feather';
import Ionicons from 'react-native-vector-icons/Ionicons';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Entypo from 'react-native-vector-icons/Entypo';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { FlatList } from 'react-native-gesture-handler';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage';
// import CheckBox from 'react-native-check-box';
import moment from 'moment';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import Styles from '../constant/Styles';
import { getAddOnChoicePrice, getAddOnChoiceQuantity, getCartItemPriceWithoutAddOn, getOrderDiscountInfo, getOrderDiscountInfoInclOrderBased, getTransformForModalInsideNavigation, getTransformForScreenInsideNavigation } from '../util/common';
import FusionCharts from 'react-fusioncharts';
import { OutletStore } from '../store/outletStore';
import { MerchantStore } from '../store/merchantStore';
import { UserStore } from '../store/userStore';
// import Upload from '../assets/svg/Upload';
// import Download from '../assets/svg/Download';
import { ReactComponent as GCalendar } from "../assets/svg/GCalendar.svg";
import { ReactComponent as Dish } from "../assets/svg/Dish.svg";
import { ReactComponent as Hand } from "../assets/svg/Hand.svg";
import { ReactComponent as Coins } from "../assets/svg/Coins.svg";
// import RNFetchBlob from 'rn-fetch-blob';
import {
    listenToUserChangesMerchant,
    listenToMerchantIdChangesMerchant,
    listenToCurrOutletIdChangesWaiter,
    listenToAllOutletsChangesMerchant,
    listenToCommonChangesMerchant,
    listenToSelectedOutletItemChanges,
    convertArrayToCSV,
    listenToSelectedOutletTableIdChanges,
    requestNotificationsPermission,
    sortReportDataList,
    generateEmailReport,
} from '../util/common';
import {
    filterChartItems,
    getDataForChartReportProductSales,
    getDataForChartReportUpsellSales,
} from '../util/chart';
import {
    CHART_DATA,
    CHART_TYPE,
    FS_LIBRARY_PATH,
    CHART_Y_AXIS_DROPDOWN_LIST,
    CHART_FIELD_COMPARE_DROPDOWN_LIST,
    CHART_FIELD_NAME_DROPDOWN_LIST,
    CHART_FIELD_TYPE,
    CHART_FIELD_COMPARE_DICT,
    CHART_PERIOD,
    CHART_X_AXIS_DROPDOWN_LIST,
} from '../constant/chart';
import {
    EMAIL_REPORT_TYPE,
    REPORT_SORT_FIELD_TYPE,
    TABLE_PAGE_SIZE_DROPDOWN_LIST,
    ORDER_TYPE,
    EXPAND_TAB_TYPE,
    OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST,
    ORDER_TYPE_DROP_DOWN_LIST,
    PRODUCT_PRICE_TYPE,
    UNIT_TYPE_SHORT,
    APP_TYPE,
    UPSELLING_SECTION_PARSED,
    UPSELLING_SECTION_CODE,
    UPSELLING_SECTION_CODE_PARSED,
    SERVER_REPORT_FILTER_TYPE,
} from '../constant/common';
import XLSX from 'xlsx';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
// const RNFS = require('react-native-fs');
import DropDownPicker from 'react-native-dropdown-picker';
// import RNPickerSelect from 'react-native-picker-select';
import Feather from 'react-native-vector-icons/Feather';
//   import { useKeyboard } from '../hooks';
import { CommonStore } from '../store/commonStore';
import AsyncImage from '../components/asyncImage';
// import Tooltip from 'react-native-walkthrough-tooltip';
import { useFocusEffect } from '@react-navigation/native';
// import UserIdleWrapper from '../components/userIdleWrapper';
import { CSVLink } from "react-csv";
import DatePicker from "react-datepicker";

import personicon from "../assets/image/default-profile.png";
import headerLogo from "../assets/image/logo.png";

import Ionicon from "react-native-vector-icons/Ionicons";
import ApiClientReporting from '../util/ApiClientReporting';
import { TempStore } from '../store/tempStore';

const { nanoid } = require('nanoid');

const ReportSalesUpsellingRevenue = (props) => {
    const { navigation } = props;

    ///////////////////////////////////////////////////////////

    const [isMounted, setIsMounted] = useState(true);

    useFocusEffect(
        useCallback(() => {
            setIsMounted(true);
            return () => {
                setIsMounted(false);
            };
        }, []),
    );

    ///////////////////////////////////////////////////////////

    const { width: windowWidth, height: windowHeight } = useWindowDimensions();

    // const [keyboardHeight] = useKeyboard();
    const [list, setList] = useState([]);
    const [page, setPage] = useState(0);
    const [name, setName] = useState('Upselling');
    const [visible, setVisible] = useState(false);
    const [visible1, setVisible1] = useState(false);
    const [isChecked, setIsChecked] = useState(false);
    const [isChecked1, setIsChecked1] = useState(false);
    const [endDate, setEndDate] = useState(new Date());
    const [startDate, setStartDate] = useState(new Date());
    const [oriList, setOriList] = useState([]);
    const [offset, setOffset] = useState(0);
    const [perPage, setPerPage] = useState(10);
    const [pageCount, setPageCount] = useState(0);
    const [detailsPageCount, setDetailsPageCount] = useState(0);
    const [currentPage, setCurrentPage] = useState(1);
    const [currentDetailsPage, setCurrentDetailsPage] = useState(1);
    const [pageReturn, setPageReturn] = useState(1);
    const [day, setDay] = useState(false);
    const [pick, setPick] = useState(null);
    const [pick1, setPick1] = useState(null);
    const [search, setSearch] = useState('');
    const [lists, setLists] = useState([]);
    const [list1, setList1] = useState(true);
    const [searchList, setSearchList] = useState(false);

    const [pushPagingToTop, setPushPagingToTop] = useState(false);

    const [loading, setLoading] = useState(false);
    const [switchMerchant, setSwitchMerchant] = useState(false);
    const [showDateTimePicker, setShowDateTimePicker] = useState(false);
    const [showDateTimePicker1, setShowDateTimePicker1] = useState(false);
    const [showDateTimePickerFilter, setShowDateTimePickerFilter] =
        useState(false);
    const [pickerMode, setPickerMode] = useState('datetime');
    const [rev_date, setRev_date] = useState(
        moment().subtract(6, 'days').startOf('day'),
    );
    const [rev_date1, setRev_date1] = useState(
        moment().endOf(Date.now()).endOf('day'),
    );

    const reportOutletShifts = OutletStore.useState((s) => s.reportOutletShifts);
    const reportDisplayType = OutletStore.useState((s) => s.reportDisplayType);

    const historyStartDate = CommonStore.useState(s => s.historyStartDate);
    const historyEndDate = CommonStore.useState(s => s.historyEndDate);

    const [productSales, setProductSales] = useState([]);
    const [productSalesSku, setProductSalesSku] = useState([]);
    const [selectedFilterProductList, setSelectedFilterProductList] = useState([]);
    const [selectedFilterChannelList, setSelectedFilterChannelList] = useState([ORDER_TYPE_DROP_DOWN_LIST[0].value, ORDER_TYPE_DROP_DOWN_LIST[1].value]);

    ////////////////////////////////////////////////////////////////////////////

    // 2022-02-23 - Upselling reports related

    const [productSalesUpselling, setProductSalesUpselling] = useState([]);
    const [selectedFilterProductId, setSelectedFilterProductId] = useState('');
    const [selectedFilterProductIdUpselling, setSelectedFilterProductIdUpselling] = useState('');

    const [outletItemsDropdownList, setOutletItemsDropdownList] = useState([]);
    const [outletItemsUpsellingDropdownList, setOutletItemsUpsellingDropdownList] = useState([]);

    const upsellingCampaigns = OutletStore.useState(s => s.upsellingCampaigns);

    ////////////////////////////////////////////////////////////////////////////

    const [productSalesChart, setProductSalesChart] = useState({});

    const [filteredOutletItems, setFilteredOutletItems] = useState([]);
    const [filteredOutletItemsUpselling, setFilteredOutletItemsUpselling] = useState([]);

    const [selectedItemSummary, setSelectedItemSummary] = useState({});

    const [expandDetailsDict, setExpandDetailsDict] = useState({});
    const crmUsers = OutletStore.useState((s) => s.crmUsers);

    const allOutletsUserOrdersDone = TempStore.useState((s) => s.allOutletUserOrderDoneProcessed);
    // const allOutletsUserOrdersDone = OutletStore.useState(
    //     (s) => s.allOutletsUserOrdersDone,
    // );
    const outletItems = OutletStore.useState((s) => s.outletItems);
    const outletCategories = OutletStore.useState((s) => s.outletCategories);
    const outletCategoriesDict = OutletStore.useState(
        (s) => s.outletCategoriesDict,
    );

    const currOutletId = MerchantStore.useState((s) => s.currOutletId);
    const currOutlet = MerchantStore.useState((s) => s.currOutlet);
    const allOutletsRaw = MerchantStore.useState((s) => s.allOutlets);

    const userName = UserStore.useState((s) => s.name);
    const merchantName = MerchantStore.useState((s) => s.name);

    const [exportEmail, setExportEmail] = useState('');
    const [CsvData, setCsvData] = useState([]);

    const [showDetails, setShowDetails] = useState(false);
    const [productSalesDetails, setProductSalesDetails] = useState([]);

    const [exportModalVisibility, setExportModalVisibility] = useState(false);
    const [detailsTitle, setDetailsTitle] = useState('');

    const [expandSelection, setExpandSelection] = useState(
        props.expandSelection === undefined ? false : props.expandSelection,
    );
    const [filterTapped, setFilterTapped] = useState(
        props.threeDotsTapped === undefined ? 0 : props.threeDotsTapped,
    );

    const [expandGroupBy, setExpandGroupBy] = useState(
        props.expandGroupBy === undefined ? false : props.expandGroupBy,
    );
    const [groupByTapped, setGroupByTapped] = useState(
        props.threeDotsTapped === undefined ? 0 : props.threeDotsTapped,
    );

    const [selectedChartDropdownValue, setSelectedChartDropdownValue] = useState(
        CHART_Y_AXIS_DROPDOWN_LIST[CHART_TYPE.REPORT_PRODUCT_SALES][0].value,
    );
    const [selectedChartFilterQueries, setSelectedChartFilterQueries] = useState([
        {
            fieldNameKey:
                CHART_FIELD_NAME_DROPDOWN_LIST[CHART_TYPE.REPORT_PRODUCT_SALES][0]
                    .value,
            fieldNameType:
                CHART_FIELD_NAME_DROPDOWN_LIST[CHART_TYPE.REPORT_PRODUCT_SALES][0]
                    .fieldType,
            fieldCompare:
                CHART_FIELD_COMPARE_DROPDOWN_LIST[CHART_TYPE.REPORT_PRODUCT_SALES][0]
                    .value,
            fieldDataValue: null,
            fieldSpecial:
                CHART_FIELD_NAME_DROPDOWN_LIST[CHART_TYPE.REPORT_PRODUCT_SALES][0]
                    .special,
        },
    ]);
    const [salesBarChartPeriod, setSalesBarChartPeriod] = useState(
        CHART_PERIOD.THIS_WEEK,
    );

    const [selectedChartDropdownValueX, setSelectedChartDropdownValueX] =
        useState(
            CHART_X_AXIS_DROPDOWN_LIST[CHART_TYPE.REPORT_PRODUCT_SALES][0].value,
        );

    const [appliedChartFilterQueries, setAppliedChartFilterQueries] = useState(
        [],
    );

    const [currReportSummarySort, setCurrReportSummarySort] = useState('');
    const [currReportDetailsSort, setCurrReportDetailsSort] = useState('');

    const currPageStack = CommonStore.useState((s) => s.currPageStack);
    const pageStack = CommonStore.useState((s) => s.pageStack);

    const merchantId = UserStore.useState((s) => s.merchantId);
    const isLoading = CommonStore.useState((s) => s.isLoading);
    const [isCsv, setIsCsv] = useState(false);
    const [isExcel, setIsExcel] = useState(false);

    const outletSelectDropdownView = CommonStore.useState(
        (s) => s.outletSelectDropdownView,
    );

    const [saleTip, setSaleTip] = useState(false);
    const [netSaleTip, setNetSaleTip] = useState(false);

    const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
    const expandTab = CommonStore.useState((s) => s.expandTab);

    const [filterAppType, setFilterAppType] = useState([APP_TYPE.WEB_ORDER, APP_TYPE.MERCHANT, APP_TYPE.USER, APP_TYPE.WAITER]);

    const [allOutlets, setAllOutlets] = useState([]);
    const [openFA, setOpenFA] = useState(false);
    const [openPage, setOpenPage] = useState(false);
    const [openChartDropdown, setOpenChartDropdown] = useState(false);
    const [openProductup, setOpenProductup] = useState(false);
    const [openProduct, setOpenProduct] = useState(false);
    const [openChannel, setOpenChannel] = useState(false);
    const [openOS, setOpenOS] = useState(false);
    // const [selectedOutletList, setSelectedOutletList] = useState([]); // multi-outlets
    const selectedOutletList = CommonStore.useState((s) => s.reportOutletIdList);
    const [outletDropdownList, setOutletDropdownList] = useState([]);
    const [selectedOutletId, setSelectedOutletId] = useState("");
    const isMasterAccount = UserStore.useState(s => s.isMasterAccount);

    ////////////////////////////////////////////////////////////////////////////////////

    const [totalOrdersNum, setTotalOrdersNum] = useState(0);

    const [totalUpsellingRevenue, setTotalUpsellingRevenue] = useState(0);
    const [totalUpsellingItems, setTotalUpsellingItems] = useState(0);
    const [totalUpsellingDiscounts, setTotalUpsellingDiscounts] = useState(0);

    ////////////////////////////////////////////////////////////////////////////////////

    // 2024-01-24 - chart no needed first
    // useEffect(() => {
    //     if (isMounted) {
    //         // const result = getDataForChartReportProductSales(allOutlets, allOutletsUserOrdersDone, rev_date, rev_date1);
    //         const result = getDataForChartReportUpsellSales(
    //             productSalesSku,
    //             filteredOutletItems,
    //             allOutlets,
    //             historyStartDate,
    //             historyEndDate,
    //             selectedChartDropdownValue,
    //             selectedChartDropdownValueX,

    //             productSales,
    //             productSalesUpselling,
    //             selectedFilterProductId,
    //             selectedFilterProductIdUpselling,

    //             filteredOutletItemsUpselling,

    //             reportDisplayType,
    //             reportOutletShifts,
    //         );

    //         if (result && result.chartData) {
    //             setProductSalesChart(result.chartData);
    //         }
    //     }
    // }, [
    //     // allOutlets,
    //     // allOutletsUserOrdersDone,

    //     // productSalesSku,
    //     filteredOutletItems,
    //     historyStartDate,
    //     historyEndDate,
    //     selectedChartDropdownValue,
    //     selectedChartDropdownValueX,

    //     productSales,
    //     productSalesUpselling,
    //     selectedFilterProductId,
    //     selectedFilterProductIdUpselling,

    //     filteredOutletItemsUpselling,

    //     isMounted,

    //     reportDisplayType,
    //     reportOutletShifts,
    // ]);

    const todayUserOrdersRealTime = OutletStore.useState(s => s.allOutletsUserOrdersDoneRealTime.filter(o => o.createdAt >= moment().startOf('day').valueOf()));
    const payoutTransactions = OutletStore.useState(s => s.payoutTransactions.filter(p => p.v >= '3')); // only check for v3
    const payoutTransactionsExtend = OutletStore.useState(s => s.payoutTransactionsExtend.filter(p => p.v >= '3')); // only check for v3

    const ptTimestamp = OutletStore.useState(s => s.ptTimestamp);
    const pteTimestamp = OutletStore.useState(s => s.pteTimestamp);

    const [isTableApiLoading, setIsTableApiLoading] = useState(true);
    const reportOutletIdList = CommonStore.useState((s) => s.reportOutletIdList);

    // 2024-01-23 - no need the dropdown pickers for chart first
    // useEffect(() => {
    //     var outletItemsDropdownListTemp = [];
    //     var outletItemsUpsellingDropdownListTemp = [];

    //     for (var i = 0; i < outletItems.length; i++) {
    //         var isValid = false;

    //         for (var j = 0; j < upsellingCampaigns.length; j++) {
    //             if (upsellingCampaigns[j].productList.find(product => product.productId === outletItems[i].uniqueId)) {
    //                 isValid = true;
    //                 break;
    //             }
    //         }

    //         if (isValid) {
    //             outletItemsUpsellingDropdownListTemp.push({
    //                 label: outletItems[i].name,
    //                 value: outletItems[i].uniqueId,
    //             });
    //         }

    //         outletItemsDropdownListTemp.push({
    //             label: outletItems[i].name,
    //             value: outletItems[i].uniqueId,
    //         });
    //     }

    //     setOutletItemsUpsellingDropdownList(outletItemsUpsellingDropdownListTemp);
    //     setOutletItemsDropdownList(outletItemsDropdownListTemp);

    //     if (
    //         // selectedFilterProductIdUpselling === '' ||
    //         outletItemsUpsellingDropdownListTemp.length > 0 &&
    //         !outletItemsUpsellingDropdownListTemp.find(option => option.value === selectedFilterProductIdUpselling)) {
    //         setSelectedFilterProductIdUpselling(outletItemsUpsellingDropdownListTemp[0].value);
    //     }

    //     if (
    //         // selectedFilterProductId === '' ||
    //         outletItemsDropdownListTemp.length > 0 &&
    //         !outletItemsDropdownListTemp.find(option => option.value === selectedFilterProductId)) {
    //         setSelectedFilterProductId(outletItemsDropdownListTemp[0].value);
    //     }
    // }, [outletItems, upsellingCampaigns]);


    useEffect(() => {
        const fetchData = async () => {
            OutletStore.update(s => {
                s.reportingApiLoading = true;
            });
            setIsTableApiLoading(true);

            // Calculate pagination for categories instead of days
            const startIndex = (currentPage - 1) * perPage;
            const endIndex = Math.min(startIndex + perPage, upsellingCampaigns.length);
            const filterIdList = upsellingCampaigns.slice(startIndex, endIndex).map(item => item.uniqueId);

            // Calculate the start and end dates for the API request
            let startReportDate = moment(historyStartDate).startOf('day');
            let endReportDate = moment(historyEndDate).endOf('day');

            try {
                const requestBody = {
                    isMasterAccount,
                    merchantId,
                    outletId: currOutletId,
                    reportOutletIdList,
                    startDate: historyStartDate,
                    endDate: historyEndDate,
                    filterType: SERVER_REPORT_FILTER_TYPE.UPSELLING_REVENUE,
                    filterIdList: filterIdList,
                };

                console.log('API Request Body:', requestBody);

                const data = await ApiClientReporting.POST(API.getOutletUserOrderDoneProcessed, requestBody);

                console.log('data returned', data);

                TempStore.update(s => {
                    s.allOutletUserOrderDoneProcessed = data.allOutletUserOrderDoneProcessed || [];
                });

                setPageCount(Math.ceil(upsellingCampaigns.length / perPage));
                setCurrentPage(1);
            } catch (error) {
                console.error('Error fetching sales overtime report:', error);
            }

            OutletStore.update(s => {
                s.reportingApiLoading = false;
            });

            setTimeout(() => {
                setIsTableApiLoading(false);
            }, 1000);
        };

        if (upsellingCampaigns.length > 0) {
            if (global.getRazerPayoutTransactionsParsedTimer) {
                clearTimeout(global.getRazerPayoutTransactionsParsedTimer);
            }

            global.getRazerPayoutTransactionsParsedTimer = setTimeout(() => {
                fetchData();
            }, 1000);
        }
    }, [
        reportOutletIdList,
        historyStartDate,
        historyEndDate,
        perPage,
        isMasterAccount,
        upsellingCampaigns,
    ]);

    useEffect(() => {
        setAllOutlets(allOutletsRaw.filter(outlet => {
            if (outlet.uniqueId === currOutletId || isMasterAccount) {
                return true;
            }
            else {
                return false;
            }
        }));
    }, [allOutletsRaw, currOutletId, isMasterAccount]);

    useEffect(() => {
        setOutletDropdownList(
            allOutlets.map((item) => ({
                label: item.name,
                value: item.uniqueId,
            })),
        );
        if (selectedOutletId === "" && allOutlets.length > 0 && currOutletId) {
            setSelectedOutletId(currOutletId);

            // setSelectedOutletList([currOutletId]);
            CommonStore.update((s) => {
                s.reportOutletIdList = [currOutletId];
            })
        }
    }, [allOutlets, currOutletId]);

    useEffect(() => {

        if (global.reportTableTimerId) {
            clearTimeout(global.reportTableTimerId);
        }

        global.reportTableTimerId = setTimeout(() => {
            setIsTableApiLoading(true);
            if (isMounted) {
                if (
                    currOutletId !== '' &&
                    allOutlets.length > 0 &&
                    outletCategories.length > 0 &&
                    outletItems.length > 0 &&
                    upsellingCampaigns.length > 0 &&
                    Object.keys(outletCategoriesDict).length > 0
                ) {
                    //   setHistoryOrders(allOutletsUserOrdersDone.filter(order => order.outletId === currOutletId));

                    // console.log('outletCategoriesDict');
                    // console.log(outletCategoriesDict);

                    // console.log('report outlet items');

                    var productSalesDict = {};
                    var productSalesSkuDict = {};

                    var productSalesUpsellingDict = {};

                    var processedCartItemDict = {};

                    var filteredOutletItemsTemp = [];
                    var filteredOutletItemsUpsellingTemp = [];

                    var totalOrdersNumTemp = 0;

                    var totalUpsellingRevenueTemp = 0;
                    var totalUpsellingItemsTemp = 0;
                    var totalUpsellingDiscountsTemp = 0;

                    for (var upsellingIndex = 0; upsellingIndex < upsellingCampaigns.length; upsellingIndex++) {
                        if (upsellingCampaigns[upsellingIndex] &&
                            upsellingCampaigns[upsellingIndex].productList &&
                            upsellingCampaigns[upsellingIndex].productList.length > 0) {
                            const upsellingItems = upsellingCampaigns[upsellingIndex].productList;

                            for (var i = 0; i < upsellingItems.length; i++) {
                                // console.log(upsellingItems[i].categoryId);
                                // console.log(outletCategoriesDict[upsellingItems[i].categoryId]);

                                const foundOutletItem = outletItems.find(outletItem => outletItem.uniqueId === upsellingItems[i].productId);

                                if (foundOutletItem && outletCategoriesDict[foundOutletItem.categoryId]) {
                                    var productSaleRecord = {
                                        productId: upsellingItems[i].productId,
                                        summaryId: nanoid(),
                                        productName: foundOutletItem.name,
                                        productCategory:
                                            outletCategoriesDict[foundOutletItem.categoryId].name,
                                        // productCategory: '',
                                        // productSku: outletItems[i].sku,
                                        productSku: foundOutletItem.skuMerchant || 'N/A',
                                        totalItems: 0,
                                        totalSales: 0,
                                        totalSalesReturn: 0,
                                        totalDiscount: 0,
                                        discount: 0,
                                        itemNetSales: 0,
                                        paymentDetails: '',
                                        detailsList: [],

                                        /////////////////////////////////

                                        upsellingPriceDiff: (upsellingItems[i].productPrice - upsellingItems[i].upsellPrice) < 0 ? 0 : (upsellingItems[i].productPrice - upsellingItems[i].upsellPrice),
                                        upsellingPrice: upsellingItems[i].upsellPrice,

                                        campaignName: upsellingCampaigns[upsellingIndex].campaignName,
                                        upsellingType: UPSELLING_SECTION_PARSED[upsellingCampaigns[upsellingIndex].upsellingSection],

                                        upsellingDiscount: 0, // counting upselling discount only
                                        upsellingRevenue: 0,

                                        totalOrders: 0,
                                        purchaseRate: 0,

                                        /////////////////////////////////
                                    };

                                    // productSalesUpsellingDict[foundOutletItem.uniqueId] = productSaleRecord;

                                    productSalesUpsellingDict[(upsellingCampaigns[upsellingIndex].uniqueId) + foundOutletItem.uniqueId] = productSaleRecord;

                                    // productSalesDict[foundOutletItem.uniqueId] = productSaleRecord;
                                    // productSalesSkuDict[foundOutletItem.name] = productSaleRecord; // change to sku in future
                                }
                            }
                        }
                        else {

                        }
                    }

                    /////////////////////////////////////////////////////////

                    // 2023-02-14 - For the past orders (those that inside daily transactions)

                    const userOrdersFigures = allOutletsUserOrdersDone;

                    totalOrdersNumTemp += userOrdersFigures.length;

                    for (var i = 0; i < userOrdersFigures.length; i++) {
                        if (
                            moment(historyStartDate).isSameOrBefore(
                                userOrdersFigures[i].createdAt,
                            ) &&
                            moment(historyEndDate).isAfter(userOrdersFigures[i].createdAt) &&
                            (filterAppType.includes(userOrdersFigures[i].appType) || filterAppType.length === 0)
                        ) {
                            /////////////////////////////////////////
                            // apply filter here

                            // const isValid = filterChartItems(
                            //   todayUserOrders[i],
                            //   appliedChartFilterQueries,
                            // );

                            const isValid = true;

                            /////////////////////////////////////////

                            if (
                                isValid &&
                                // todayUserOrders[i].outletId === currOutletId &&
                                // ((moment(historyStartDate).isSameOrBefore(
                                //   userOrdersFigures[i].createdAt,
                                // ) &&
                                //   moment(historyEndDate).isAfter(
                                //     userOrdersFigures[i].createdAt,
                                //   )))
                                // &&
                                selectedFilterChannelList.includes(userOrdersFigures[i].orderType)
                            ) {
                                for (
                                    var j = 0;
                                    j < userOrdersFigures[i].cartItems.length;
                                    j++
                                ) {
                                    const cartItem = userOrdersFigures[i].cartItems[j];

                                    ////////////////////////////////////////////////////////////

                                    // 2024-01-23 - no need anymore, already created on top

                                    // if (cartItem.upsellingCampaignId && productSalesUpsellingDict[cartItem.id] === undefined) {
                                    //     // create one first

                                    //     var matchedItem = outletItems.find(o => o.uniqueId === cartItem.id);

                                    //     if (matchedItem && outletCategoriesDict[matchedItem.categoryId]) {
                                    //         var productSaleUpsellingRecord = {
                                    //             productId: matchedItem.uniqueId,
                                    //             summaryId: nanoid(),
                                    //             productName: matchedItem.name,
                                    //             productCategory:
                                    //                 outletCategoriesDict[matchedItem.categoryId].name,
                                    //             // productCategory: '',
                                    //             // productSku: outletItems[i].sku,
                                    //             productSku: matchedItem.skuMerchant || 'N/A',
                                    //             totalItems: 0,
                                    //             totalSales: 0,
                                    //             totalSalesReturn: 0,
                                    //             totalDiscount: 0,
                                    //             discount: 0,
                                    //             itemNetSales: 0,
                                    //             paymentDetails: '',
                                    //             detailsList: [],
                                    //         };

                                    //         productSalesUpsellingDict[cartItem.id] = productSaleUpsellingRecord;
                                    //         // productSalesSkuDict[outletItems[i].name] = productSaleUpsellingRecord; // change to sku in future
                                    //     }
                                    // }

                                    ////////////////////////////////////////////////////////////

                                    if ((cartItem.upsellingCampaignId || cartItem.upsellId) && productSalesUpsellingDict[(cartItem.upsellingCampaignId ? cartItem.upsellingCampaignId : cartItem.upsellId) + cartItem.itemId
                                    ]) {
                                        // productSalesDict[cartItem.itemId].detailsList.push(allOutletsUserOrdersDone[i]);

                                        let upsellingDictId = (cartItem.upsellingCampaignId ? cartItem.upsellingCampaignId : cartItem.upsellId) + cartItem.itemId
                                            ;

                                        ////////////////////////////////////////////////////////////

                                        // 2024-01-24 - for upselling 3rd page tracking support

                                        if (cartItem.upc && cartItem.upc === UPSELLING_SECTION_CODE.IN_CART) {
                                            // means this item recommended in third page (inside cart screen)

                                            upsellingDictId = cartItem.upc + (cartItem.upsellingCampaignId ? cartItem.upsellingCampaignId : cartItem.upsellId) + cartItem.itemId
                                                ;

                                            if (!productSalesUpsellingDict[upsellingDictId]) {
                                                // if not existed, can create a new one

                                                const existingDictId = (cartItem.upsellingCampaignId ? cartItem.upsellingCampaignId : cartItem.upsellId) + cartItem.itemId
                                                    ;

                                                var productSaleRecord = {
                                                    productId: productSalesUpsellingDict[existingDictId].productId,
                                                    summaryId: nanoid(),
                                                    productName: productSalesUpsellingDict[existingDictId].productName,
                                                    productCategory:
                                                        productSalesUpsellingDict[existingDictId].productCategory,
                                                    // productCategory: '',
                                                    // productSku: outletItems[i].sku,
                                                    productSku: productSalesUpsellingDict[existingDictId].productSku,
                                                    totalItems: 0,
                                                    totalSales: 0,
                                                    totalSalesReturn: 0,
                                                    totalDiscount: 0,
                                                    discount: 0,
                                                    itemNetSales: 0,
                                                    paymentDetails: '',
                                                    detailsList: [],

                                                    /////////////////////////////////

                                                    upsellingPriceDiff: productSalesUpsellingDict[existingDictId].upsellingPriceDiff,
                                                    upsellingPrice: productSalesUpsellingDict[existingDictId].upsellingPrice,

                                                    campaignName: productSalesUpsellingDict[existingDictId].campaignName,
                                                    upsellingType: UPSELLING_SECTION_CODE_PARSED[cartItem.upc],

                                                    upsellingDiscount: 0, // counting upselling discount only
                                                    upsellingRevenue: 0,

                                                    totalOrders: 0,
                                                    purchaseRate: 0,

                                                    /////////////////////////////////
                                                };

                                                // productSalesUpsellingDict[foundOutletItem.uniqueId] = productSaleRecord;

                                                productSalesUpsellingDict[upsellingDictId] = productSaleRecord;
                                            }
                                        }

                                        ////////////////////////////////////////////////////////////

                                        if (
                                            // !productSalesDict[cartItem.itemId].detailsList.find(
                                            //   (order) =>
                                            //     order.uniqueId === allOutletsUserOrdersDone[i].uniqueId,
                                            // )

                                            // !processedCartItemDict[
                                            // cartItem.itemId + cartItem.cartItemDate
                                            // ]
                                            true
                                        ) {
                                            // add if not existed

                                            // processedCartItemDict[
                                            //   cartItem.itemId + cartItem.cartItemDate
                                            // ] = true;

                                            ///////////////////////////////////////////////////////////////////

                                            var cartItemTax = 0;
                                            var cartItemSc = 0;
                                            // var finalPriceWithTaxOnly = allOutletsUserOrdersDone[i].finalPriceBefore - (allOutletsUserOrdersDone[i].sc || 0);
                                            // var finalPriceWithTaxOnly = allOutletsUserOrdersDone[i].finalPrice;
                                            // var finalPriceWithScOnly = allOutletsUserOrdersDone[i].finalPrice;

                                            var cartItemRatioTax = 0;
                                            var cartItemRatioSc = 0;

                                            if (cartItem.price !== 0 || userOrdersFigures[i].totalPrice !== 0 || userOrdersFigures[i].userOrderPriceBeforeCommission !== 0) {
                                                cartItemRatioTax = (cartItem.price /
                                                    userOrdersFigures[i].totalPrice) *
                                                    userOrdersFigures[i].userOrderPriceBeforeCommission;
                                            }
                                            else {
                                                cartItemRatioTax = 0;
                                            }

                                            if (cartItem.price !== 0 || userOrdersFigures[i].totalPrice !== 0 || userOrdersFigures[i].userOrderPriceBeforeCommission !== 0) {
                                                cartItemTax = ((userOrdersFigures[i].tax || 0) *
                                                    cartItemRatioTax) /
                                                    userOrdersFigures[i].userOrderPriceBeforeCommission;
                                            }
                                            else {
                                                cartItemTax = 0;
                                            }

                                            if (cartItem.price !== 0 || userOrdersFigures[i].totalPrice !== 0 || userOrdersFigures[i].userOrderPriceBeforeCommission !== 0) {
                                                cartItemRatioSc = (cartItem.price /
                                                    userOrdersFigures[i].totalPrice) *
                                                    userOrdersFigures[i].userOrderPriceBeforeCommission;
                                            }
                                            else {
                                                cartItemRatioSc = 0;
                                            }

                                            if (cartItem.price !== 0 || userOrdersFigures[i].totalPrice !== 0 || userOrdersFigures[i].userOrderPriceBeforeCommission !== 0) {
                                                cartItemSc = ((userOrdersFigures[i].sc || 0) *
                                                    cartItemRatioSc) /
                                                    userOrdersFigures[i].userOrderPriceBeforeCommission;
                                            }
                                            else {
                                                cartItemSc = 0;
                                            }

                                            ///////////////////////////////////////////////////////////////////

                                            productSalesUpsellingDict[upsellingDictId].totalItems +=
                                                cartItem.quantity;
                                            productSalesUpsellingDict[upsellingDictId].totalSales +=
                                                cartItem.price + userOrdersFigures[i].cartItems.reduce((accum, o) => accum + (o.disc ? o.disc : 0), 0,);
                                            //allOutletsUserOrdersDone[i].finalPriceBefore ? allOutletsUserOrdersDone[i].finalPriceBefore : allOutletsUserOrdersDone[i].finalPrice;
                                            // productSalesDict[cartItem.itemId].totalSalesReturn += cartItem.price * cartItem.quantity;
                                            // productSalesUpsellingDict[cartItem.id].totalDiscount += userOrdersFigures[i].disc + userOrdersFigures[i].cartItems.reduce((accum, o) => accum + (o.disc ? o.disc : 0), 0);
                                            productSalesUpsellingDict[upsellingDictId].totalDiscount += userOrdersFigures[i].cartItems.reduce((accum, o) => accum + (o.disc ? o.disc : 0), 0);
                                            productSalesUpsellingDict[upsellingDictId].itemNetSales +=
                                                // cartItem.price + cartItemTax + cartItemSc;
                                                (isNaN(cartItem.price) ? 0 : cartItem.price) + (isNaN(cartItemTax) ? 0 : cartItemTax) + (isNaN(cartItemSc) ? 0 : cartItemSc);

                                            ///////////////////////////////////////////////////////////

                                            // hide first

                                            // filteredOutletItemsUpsellingTemp.push({
                                            //     ...cartItem,
                                            //     orderCompletedDate:
                                            //         userOrdersFigures[i].createdAt,
                                            //     outletId: global.payoutTransactions[transactionIndex].outletId,

                                            //     totalItems: cartItem.qty,
                                            //     totalSales: cartItem.price,
                                            //     paymentDetails: cartItem.paymentDetails ? cartItem.paymentDetails : null,
                                            //     // totalSalesReturn: cartItem.price * cartItem.quantity,
                                            //     itemNetSales: cartItem.price,
                                            //     discount: 0,
                                            //     totalDiscount: 0,

                                            //     itemName: productSalesUpsellingDict[cartItem.id].productName,
                                            //     quantity: cartItem.qty,
                                            // });

                                            ///////////////////////////////////////////////////////////

                                            var accumDiscount = userOrdersFigures[i].cartItems.reduce((accum, c) => accum + (c.disc ? c.disc : 0), 0);

                                            productSalesUpsellingDict[upsellingDictId].detailsList.push({
                                                ...userOrdersFigures[i],
                                                discountPercentage: parseFloat(
                                                    isFinite(
                                                        accumDiscount /
                                                        (userOrdersFigures[i].userOrderPriceBeforeCommission +
                                                            accumDiscount),
                                                    )
                                                        ? (accumDiscount /
                                                            (userOrdersFigures[i].userOrderPriceBeforeCommission +
                                                                accumDiscount)) *
                                                        100
                                                        : 0,
                                                ),
                                                itemPrice: cartItem.price,
                                                // itemTax: cartItem.price * currOutlet.taxRate,
                                                itemTax: cartItemTax,
                                                itemSc: cartItemSc,
                                                //salesReturn:

                                                upsellingDiscount: cartItem.priceOriginal - cartItem.price,
                                                totalItems: cartItem.quantity,
                                                upsellingRevenue: cartItem.price,

                                            });

                                            if (selectedFilterProductIdUpselling === '') {
                                                setSelectedFilterProductIdUpselling(cartItem.itemId);
                                            }

                                            // productSalesUpsellingDict[upsellingDictId].upsellingDiscount += (cartItem.qty * productSalesUpsellingDict[upsellingDictId].upsellingPriceDiff);
                                            productSalesUpsellingDict[upsellingDictId].upsellingDiscount += cartItem.priceOriginal - cartItem.price;
                                            productSalesUpsellingDict[upsellingDictId].totalOrders += 1;
                                            // productSalesUpsellingDict[upsellingDictId].upsellingRevenue += (cartItem.qty * productSalesUpsellingDict[upsellingDictId].upsellingPrice);
                                            productSalesUpsellingDict[upsellingDictId].upsellingRevenue += cartItem.price;

                                            // totalUpsellingDiscountsTemp += (cartItem.qty * productSalesUpsellingDict[upsellingDictId].upsellingPriceDiff);
                                            totalUpsellingDiscountsTemp += cartItem.priceOriginal - cartItem.price;
                                            totalUpsellingItemsTemp += cartItem.quantity;
                                            // totalUpsellingRevenueTemp += (cartItem.qty * productSalesUpsellingDict[upsellingDictId].upsellingPrice);
                                            totalUpsellingRevenueTemp += cartItem.price;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    /////////////////////////////////////////////////////////

                    // 2023-02-14 - For today 6:00am orders until now

                    const todayUserOrders = todayUserOrdersRealTime.filter(o => o.createdAt >= historyStartDate && o.createdAt <= historyEndDate);

                    totalOrdersNumTemp += todayUserOrders.length;

                    for (var i = 0; i < todayUserOrders.length; i++) {
                        if (
                            moment(historyStartDate).isSameOrBefore(
                                todayUserOrders[i].createdAt,
                            ) &&
                            moment(historyEndDate).isAfter(todayUserOrders[i].createdAt) &&
                            (filterAppType.includes(todayUserOrders[i].appType) || filterAppType.length === 0)
                        ) {
                            /////////////////////////////////////////
                            // apply filter here

                            // const isValid = filterChartItems(
                            //   todayUserOrders[i],
                            //   appliedChartFilterQueries,
                            // );

                            const isValid = true;

                            /////////////////////////////////////////

                            if (
                                isValid
                                // &&
                                // todayUserOrders[i].outletId === currOutletId &&
                                // ((moment(historyStartDate).isSameOrBefore(
                                //   todayUserOrders[i].createdAt,
                                // ) &&
                                //   moment(historyEndDate).isAfter(
                                //     todayUserOrders[i].createdAt,
                                //   )))
                                &&
                                selectedFilterChannelList.includes(todayUserOrders[i].orderType)
                            ) {
                                for (
                                    var j = 0;
                                    j < todayUserOrders[i].cartItems.length;
                                    j++
                                ) {
                                    const cartItem = todayUserOrders[i].cartItems[j];

                                    ////////////////////////////////////////////////////////////

                                    // if (cartItem.upsellingCampaignId && productSalesUpsellingDict[cartItem.itemId] === undefined) {
                                    //     // create one first

                                    //     var matchedItem = outletItems.find(o => o.uniqueId === cartItem.itemId);

                                    //     if (matchedItem && outletCategoriesDict[matchedItem.categoryId]) {
                                    //         var productSaleUpsellingRecord = {
                                    //             productId: matchedItem.uniqueId,
                                    //             summaryId: nanoid(),
                                    //             productName: matchedItem.name,
                                    //             productCategory:
                                    //                 outletCategoriesDict[matchedItem.categoryId].name,
                                    //             // productCategory: '',
                                    //             // productSku: outletItems[i].sku,
                                    //             productSku: matchedItem.skuMerchant || 'N/A',
                                    //             totalItems: 0,
                                    //             totalSales: 0,
                                    //             totalSalesReturn: 0,
                                    //             totalDiscount: 0,
                                    //             discount: 0,
                                    //             itemNetSales: 0,
                                    //             paymentDetails: '',
                                    //             detailsList: [],
                                    //         };

                                    //         productSalesUpsellingDict[cartItem.itemId] = productSaleUpsellingRecord;
                                    //         // productSalesSkuDict[outletItems[i].name] = productSaleUpsellingRecord; // change to sku in future
                                    //     }
                                    // }

                                    ////////////////////////////////////////////////////////////

                                    if (
                                        (cartItem.upsellingCampaignId || cartItem.upsellId)
                                        && productSalesUpsellingDict[(cartItem.upsellingCampaignId ? cartItem.upsellingCampaignId : cartItem.upsellId) + cartItem.itemId]) {
                                        // productSalesDict[cartItem.itemId].detailsList.push(allOutletsUserOrdersDone[i]);

                                        let upsellingDictId = (cartItem.upsellingCampaignId ? cartItem.upsellingCampaignId : cartItem.upsellId) + cartItem.itemId;

                                        ////////////////////////////////////////////////////////////

                                        // 2024-01-24 - for upselling 3rd page tracking support

                                        if (cartItem.upc && cartItem.upc === UPSELLING_SECTION_CODE.IN_CART) {
                                            // means this item recommended in third page (inside cart screen)

                                            upsellingDictId = cartItem.upc + (cartItem.upsellingCampaignId ? cartItem.upsellingCampaignId : cartItem.upsellId) + cartItem.itemId;

                                            if (!productSalesUpsellingDict[upsellingDictId]) {
                                                // if not existed, can create a new one

                                                const existingDictId = (cartItem.upsellingCampaignId ? cartItem.upsellingCampaignId : cartItem.upsellId) + cartItem.itemId;

                                                var productSaleRecord = {
                                                    productId: productSalesUpsellingDict[existingDictId].productId,
                                                    summaryId: nanoid(),
                                                    productName: productSalesUpsellingDict[existingDictId].productName,
                                                    productCategory:
                                                        productSalesUpsellingDict[existingDictId].productCategory,
                                                    // productCategory: '',
                                                    // productSku: outletItems[i].sku,
                                                    productSku: productSalesUpsellingDict[existingDictId].productSku,
                                                    totalItems: 0,
                                                    totalSales: 0,
                                                    totalSalesReturn: 0,
                                                    totalDiscount: 0,
                                                    discount: 0,
                                                    itemNetSales: 0,
                                                    paymentDetails: '',
                                                    detailsList: [],

                                                    /////////////////////////////////

                                                    upsellingPriceDiff: productSalesUpsellingDict[existingDictId].upsellingPriceDiff,
                                                    upsellingPrice: productSalesUpsellingDict[existingDictId].upsellingPrice,

                                                    campaignName: productSalesUpsellingDict[existingDictId].campaignName,
                                                    upsellingType: UPSELLING_SECTION_CODE_PARSED[cartItem.upc],

                                                    upsellingDiscount: 0, // counting upselling discount only
                                                    upsellingRevenue: 0,

                                                    totalOrders: 0,
                                                    purchaseRate: 0,

                                                    /////////////////////////////////
                                                };

                                                // productSalesUpsellingDict[foundOutletItem.uniqueId] = productSaleRecord;

                                                productSalesUpsellingDict[upsellingDictId] = productSaleRecord;
                                            }
                                        }

                                        ////////////////////////////////////////////////////////////

                                        if (
                                            // !productSalesDict[cartItem.itemId].detailsList.find(
                                            //   (order) =>
                                            //     order.uniqueId === allOutletsUserOrdersDone[i].uniqueId,
                                            // )
                                            !processedCartItemDict[
                                            cartItem.itemId + cartItem.cartItemDate
                                            ]
                                        ) {
                                            // add if not existed

                                            processedCartItemDict[
                                                cartItem.itemId + cartItem.cartItemDate
                                            ] = true;

                                            ///////////////////////////////////////////////////////////////////

                                            var cartItemTax = 0;
                                            var cartItemSc = 0;
                                            // var finalPriceWithTaxOnly = allOutletsUserOrdersDone[i].finalPriceBefore - (allOutletsUserOrdersDone[i].sc || 0);
                                            // var finalPriceWithTaxOnly = allOutletsUserOrdersDone[i].finalPrice;
                                            // var finalPriceWithScOnly = allOutletsUserOrdersDone[i].finalPrice;

                                            var cartItemRatioTax = 0;
                                            var cartItemRatioSc = 0;

                                            if (cartItem.price !== 0 || todayUserOrders[i].totalPrice !== 0 || todayUserOrders[i].finalPrice !== 0) {
                                                cartItemRatioTax = (cartItem.price /
                                                    todayUserOrders[i].totalPrice) *
                                                    todayUserOrders[i].finalPrice;
                                            }
                                            else {
                                                cartItemRatioTax = 0;
                                            }

                                            if (cartItem.price !== 0 || todayUserOrders[i].totalPrice !== 0 || todayUserOrders[i].finalPrice !== 0) {
                                                cartItemTax = ((todayUserOrders[i].tax || 0) *
                                                    cartItemRatioTax) /
                                                    todayUserOrders[i].finalPrice;
                                            }
                                            else {
                                                cartItemTax = 0;
                                            }

                                            if (cartItem.price !== 0 || todayUserOrders[i].totalPrice !== 0 || todayUserOrders[i].finalPrice !== 0) {
                                                cartItemRatioSc = (cartItem.price /
                                                    todayUserOrders[i].totalPrice) *
                                                    todayUserOrders[i].finalPrice;
                                            }
                                            else {
                                                cartItemRatioSc = 0;
                                            }

                                            if (cartItem.price !== 0 || todayUserOrders[i].totalPrice !== 0 || todayUserOrders[i].finalPrice !== 0) {
                                                cartItemSc = ((todayUserOrders[i].sc || 0) *
                                                    cartItemRatioSc) /
                                                    todayUserOrders[i].finalPrice;
                                            }
                                            else {
                                                cartItemSc = 0;
                                            }

                                            // var cartItemTax =
                                            //   ((allOutletsUserOrdersDone[i].tax || 0) *
                                            //     cartItemRatioTax) /
                                            //   allOutletsUserOrdersDone[i].finalPrice;
                                            // var cartItemRatioSc =
                                            //   (cartItem.price /
                                            //     allOutletsUserOrdersDone[i].totalPrice) *
                                            //   allOutletsUserOrdersDone[i].finalPrice;
                                            // var cartItemSc =
                                            //   ((allOutletsUserOrdersDone[i].sc || 0) *
                                            //     cartItemRatioSc) /
                                            //   allOutletsUserOrdersDone[i].finalPrice;

                                            ///////////////////////////////////////////////////////////////////

                                            productSalesUpsellingDict[upsellingDictId].totalItems +=
                                                cartItem.quantity;
                                            productSalesUpsellingDict[upsellingDictId].totalSales +=
                                                cartItem.price + getOrderDiscountInfo(todayUserOrders[i]);
                                            //allOutletsUserOrdersDone[i].finalPriceBefore ? allOutletsUserOrdersDone[i].finalPriceBefore : allOutletsUserOrdersDone[i].finalPrice;
                                            // productSalesDict[cartItem.itemId].totalSalesReturn += cartItem.price * cartItem.quantity;
                                            productSalesUpsellingDict[upsellingDictId].totalDiscount += getOrderDiscountInfoInclOrderBased(todayUserOrders[i]);
                                            productSalesUpsellingDict[upsellingDictId].itemNetSales +=
                                                // cartItem.price + cartItemTax + cartItemSc;
                                                (isNaN(cartItem.price) ? 0 : cartItem.price) + (isNaN(cartItemTax) ? 0 : cartItemTax) + (isNaN(cartItemSc) ? 0 : cartItemSc);
                                            if (
                                                todayUserOrders[i].paymentDetails &&
                                                todayUserOrders[i].paymentDetails.channel
                                            ) {
                                                if (
                                                    OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST.find(
                                                        (paymentMethod) =>
                                                            paymentMethod.value.channel ===
                                                            todayUserOrders[i].paymentDetails.channel,
                                                    )
                                                ) {
                                                    productSalesUpsellingDict[upsellingDictId].paymentDetails =
                                                        'Offline';
                                                } else {
                                                    productSalesUpsellingDict[upsellingDictId].paymentDetails =
                                                        'Online';
                                                }
                                            }
                                            //(cartItem.price) - allOutletsUserOrdersDone[i].tax - (allOutletsUserOrdersDone[i].sc ? allOutletsUserOrdersDone[i].sc : 0);
                                            //(allOutletsUserOrdersDone[i].finalPriceBefore ? allOutletsUserOrdersDone[i].finalPriceBefore : allOutletsUserOrdersDone[i].finalPrice) - allOutletsUserOrdersDone[i].tax - (allOutletsUserOrdersDone[i].sc ? allOutletsUserOrdersDone[i].sc : 0);

                                            // need change to sku in future
                                            // productSalesSkuDict[cartItem.itemName].totalItems += cartItem.price * cartItem.quantity;
                                            // productSalesSkuDict[cartItem.itemName].totalSales += cartItem.price;
                                            // productSalesSkuDict[cartItem.itemName].totalSalesReturn += cartItem.price * cartItem.quantity;
                                            // productSalesSkuDict[cartItem.itemName].itemNetSales += cartItem.price * cartItem.quantity;

                                            //////////////////////////////////////////////////

                                            // 2024-01-23 - no need first

                                            // filteredOutletItemsUpsellingTemp.push({
                                            //     ...cartItem,
                                            //     orderCompletedDate:
                                            //         todayUserOrders[i].createdAt,
                                            //     outletId: todayUserOrders[i].outletId,

                                            //     totalItems: cartItem.quantity,
                                            //     totalSales: cartItem.price,
                                            //     paymentDetails: cartItem.paymentDetails,
                                            //     // totalSalesReturn: cartItem.price * cartItem.quantity,
                                            //     itemNetSales: cartItem.price,
                                            //     discount: 0,
                                            //     totalDiscount: 0,
                                            // });

                                            //////////////////////////////////////////////////

                                            var accumDiscount = todayUserOrders[i].cartItems.reduce((accum, c) => accum + (c.discount ? c.discount : 0), 0);

                                            productSalesUpsellingDict[upsellingDictId].detailsList.push({
                                                ...todayUserOrders[i],
                                                discountPercentage: parseFloat(
                                                    isFinite(
                                                        accumDiscount /
                                                        (todayUserOrders[i].finalPrice +
                                                            accumDiscount),
                                                    )
                                                        ? (accumDiscount /
                                                            (todayUserOrders[i].finalPrice +
                                                                accumDiscount)) *
                                                        100
                                                        : 0,
                                                ),
                                                itemPrice: cartItem.price,
                                                // itemTax: cartItem.price * currOutlet.taxRate,
                                                itemTax: cartItemTax,
                                                itemSc: cartItemSc,
                                                //salesReturn:

                                                upsellingDiscount: cartItem.priceOriginal - cartItem.price,
                                                totalItems: cartItem.quantity,
                                                upsellingRevenue: cartItem.price,
                                            });

                                            if (selectedFilterProductIdUpselling === '') {
                                                setSelectedFilterProductIdUpselling(cartItem.itemId);
                                            }

                                            // productSalesUpsellingDict[upsellingDictId].upsellingDiscount += (cartItem.quantity * productSalesUpsellingDict[upsellingDictId].upsellingPriceDiff);
                                            productSalesUpsellingDict[upsellingDictId].upsellingDiscount += cartItem.priceOriginal - cartItem.price;
                                            productSalesUpsellingDict[upsellingDictId].totalOrders += 1;
                                            // productSalesUpsellingDict[upsellingDictId].upsellingRevenue += (cartItem.quantity * productSalesUpsellingDict[upsellingDictId].upsellingPrice);
                                            productSalesUpsellingDict[upsellingDictId].upsellingRevenue += cartItem.price;

                                            // totalUpsellingDiscountsTemp += (cartItem.quantity * productSalesUpsellingDict[upsellingDictId].upsellingPriceDiff);
                                            totalUpsellingDiscountsTemp += cartItem.priceOriginal - cartItem.price;
                                            totalUpsellingItemsTemp += cartItem.quantity;
                                            // totalUpsellingRevenueTemp += (cartItem.quantity * productSalesUpsellingDict[upsellingDictId].upsellingPrice);
                                            totalUpsellingRevenueTemp += cartItem.price;
                                        }
                                    }
                                }

                                if (todayUserOrders[i].cartItemsCancelled) {
                                    for (
                                        var k = 0;
                                        k < todayUserOrders[i].cartItemsCancelled.length;
                                        k++
                                    ) {
                                        const cartItem =
                                            todayUserOrders[i].cartItemsCancelled[k];

                                        if (productSalesDict[cartItem.itemId]) {
                                            productSalesDict[cartItem.itemId].totalSalesReturn +=
                                                cartItem.price;

                                            // do for cancelled items
                                            // filteredOutletItemsTemp.push({
                                            //   ...cartItem,
                                            //   orderCompletedDate:
                                            //     allOutletsUserOrdersDone[i].createdAt,
                                            //   outletId: allOutletsUserOrdersDone[i].outletId,

                                            //   totalItems: cartItem.quantity,
                                            //   totalSales: 0,
                                            //   totalSalesReturn: cartItem.price,
                                            //   itemNetSales: 0,
                                            //   discount: 0,
                                            //   totalDiscount: 0,
                                            // });
                                        }
                                    }
                                }
                            }
                        }
                    }

                    /////////////////////////////////////////////////////////

                    const productSalesTemp = Object.entries(productSalesDict).map(
                        ([key, value]) => ({ ...value }),
                    );
                    const productSalesSkuTemp = Object.entries(productSalesSkuDict).map(
                        ([key, value]) => ({ ...value }),
                    );

                    const productSalesUpsellingTemp = Object.entries(productSalesUpsellingDict).map(
                        ([key, value]) => ({ ...value }),
                    );

                    setTotalOrdersNum(totalOrdersNumTemp);
                    setTotalUpsellingRevenue(totalUpsellingRevenueTemp);
                    setTotalUpsellingItems(totalUpsellingItemsTemp);
                    setTotalUpsellingDiscounts(totalUpsellingDiscountsTemp);

                    setProductSales(productSalesTemp);
                    setProductSalesSku(productSalesSkuTemp);

                    setProductSalesUpselling(productSalesUpsellingTemp);

                    //setCurrentPage(1);
                    setPageCount(Math.ceil(productSalesUpsellingTemp.length / perPage));

                    setFilteredOutletItems(filteredOutletItemsTemp);
                    setFilteredOutletItemsUpselling(filteredOutletItemsUpsellingTemp);

                    setShowDetails(false);
                }
            }
            setIsTableApiLoading(false);
        }, 2000);

    }, [
        // allOutletsUserOrdersDone,

        outletItems,

        outletCategoriesDict,
        outletCategories,
        currOutletId,
        allOutlets,
        historyStartDate,
        historyEndDate,
        appliedChartFilterQueries,
        perPage,

        isMounted,

        selectedFilterProductList,
        selectedFilterChannelList,
        filterAppType,

        todayUserOrdersRealTime,
        // payoutTransactions,
        // payoutTransactionsExtend,

        ptTimestamp,
        pteTimestamp,

        upsellingCampaigns,

        allOutletsUserOrdersDone,
    ]);

    useEffect(() => {
        if (showDetails && selectedItemSummary.detailsList) {
            setProductSalesDetails(selectedItemSummary.detailsList);

            setPageReturn(currentPage);
            // console.log('currentPage value is');
            // console.log(currentPage);
            setCurrentDetailsPage(1);
            setDetailsPageCount(
                Math.ceil(selectedItemSummary.detailsList.length / perPage),
            );
        }
    }, [showDetails, selectedItemSummary, perPage, filterAppType,]);

    const setState = () => { };

    // navigation.dangerouslyGetParent().setOptions({
    //     tabBarVisible: false,
    // });
    navigation.setOptions({
        headerLeft: () => (
            <View
                style={[
                    styles.headerLeftStyle,
                    {
                        width: windowWidth * 0.17,
                    },
                ]}
            >
                <img src={headerLogo} width={124} height={26} />
                {/* <Image
            style={{
              width: 124,
              height: 26,
            }}
            resizeMode="contain"
            source={require('../assets/image/logo.png')}
          /> */}
            </View>
        ),
        headerTitle: () => (
            <View
                style={[
                    {
                        justifyContent: "center",
                        alignItems: "center",
                        // marginRight: Platform.OS === 'ios' ? "27%" : 0,
                        // bottom: switchMerchant ? '2%' : 0,
                        //width:  "55%",
                    },
                    Dimensions.get("screen").width <= 768
                        ? { right: Dimensions.get("screen").width * 0.12 }
                        : {},
                ]}
            >
                <Text
                    style={{
                        fontSize: 24,
                        // lineHeight: 25,
                        textAlign: "center",
                        fontFamily: "NunitoSans-Bold",
                        color: Colors.whiteColor,
                        opacity: 1,
                    }}
                >
                    Upselling Revenue Report
                </Text>
            </View>
        ),
        headerRight: () => (
            <View
                style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between",
                }}
            >
                {/* {console.log('edward test')} */}
                {/* {console.log(outletSelectDropdownView)} */}
                {outletSelectDropdownView && outletSelectDropdownView()}
                <View
                    style={{
                        backgroundColor: "white",
                        width: 0.5,
                        height: Dimensions.get("screen").height * 0.025,
                        opacity: 0.8,
                        marginHorizontal: 15,
                        bottom: -1,
                        // borderWidth: 1
                    }}
                ></View>
                <TouchableOpacity
                    onPress={() => {
                        if (global.currUserRole === 'admin') {
                            navigation.navigate("General Settings - KooDoo BackOffice")
                        }
                    }}
                    style={{ flexDirection: "row", alignItems: "center" }}
                >
                    <Text
                        style={{
                            fontFamily: "NunitoSans-SemiBold",
                            fontSize: 16,
                            color: Colors.secondaryColor,
                            marginRight: 15,
                        }}
                    >
                        {userName}
                    </Text>
                    <View
                        style={{
                            //backgroundColor: 'red',
                            marginRight: 30,
                            width: windowHeight * 0.05,
                            height: windowHeight * 0.05,
                            borderRadius: windowHeight * 0.05 * 0.5,
                            alignItems: "center",
                            justifyContent: "center",
                            backgroundColor: "white",
                        }}
                    >
                        <img
                            src={personicon}
                            width={windowHeight * 0.035}
                            height={windowHeight * 0.035}
                        />
                        {/* <Image
                style={{
                  width: windowHeight * 0.05,
                height: windowHeight * 0.05,
                  alignSelf: 'center',
                }}
                source={require('../assets/image/profile-pic.jpg')}
              /> */}
                    </View>
                </TouchableOpacity>
            </View>
        ),
    });

    // useEffect(() => {
    //     var title = 'Upselling Sales Report';

    //     if (
    //         pageStack.length > 1 &&
    //         pageStack[pageStack.length - 2] === 'Dashboard'
    //     ) {
    //         title = 'Today Upselling Sales Report';

    //         setSalesBarChartPeriod(CHART_PERIOD.TODAY);
    //         // setRev_date(moment().startOf('day'));
    //         // setRev_date1(moment().endOf('day'));
    //         CommonStore.update(s => {
    //             s.historyStartDate = moment().startOf('day');
    //             s.historyEndDate = moment().endOf('day');
    //         });
    //     } else {
    //         title = 'Upselling Sales Report';

    //         setSalesBarChartPeriod(CHART_PERIOD.THIS_MONTH);
    //         // setRev_date(moment().subtract(1, 'month').startOf('day'));
    //         // setRev_date1(moment().endOf('day'));
    //         CommonStore.update(s => {
    //             s.historyStartDate = moment().subtract(1, 'month').startOf('day');
    //             s.historyEndDate = moment().endOf('day');
    //         });
    //     }

    //     navigation.setOptions({
    //         headerLeft: () => (
    //             <View
    //                 style={[
    //                     styles.headerLeftStyle,
    //                     {
    //                         width: windowWidth * 0.17,
    //                     },
    //                 ]}
    //             >
    //                 <img src={headerLogo} width={124} height={26} />
    //                 {/* <Image
    //                 style={{
    //                   width: 124,
    //                   height: 26,
    //                 }}
    //                 resizeMode="contain"
    //                 source={require('../assets/image/logo.png')}
    //               /> */}
    //             </View>
    //         ),
    //         headerTitle: () => (
    //             <View
    //                 style={[
    //                     {
    //                         justifyContent: "center",
    //                         alignItems: "center",
    //                         // marginRight: Platform.OS === 'ios' ? "27%" : 0,
    //                         // bottom: switchMerchant ? '2%' : 0,
    //                         //width:  "55%",
    //                     },
    //                     Dimensions.get("screen").width <= 768
    //                         ? { right: Dimensions.get("screen").width * 0.12 }
    //                         : {},
    //                 ]}
    //             >
    //                 <Text
    //                     style={{
    //                         fontSize: 24,
    //                         // lineHeight: 25,
    //                         textAlign: "center",
    //                         fontFamily: "NunitoSans-Bold",
    //                         color: Colors.whiteColor,
    //                         opacity: 1,
    //                     }}
    //                 >
    //                     Upselling Sales Report
    //                 </Text>
    //             </View>
    //         ),
    //         headerRight: () => (
    //             <View
    //                 style={{
    //                     flexDirection: "row",
    //                     alignItems: "center",
    //                     justifyContent: "space-between",
    //                 }}
    //             >
    //                 {/* {console.log('edward test')} */}
    //                 {/* {console.log(outletSelectDropdownView)} */}
    //                 {outletSelectDropdownView && outletSelectDropdownView()}
    //                 <View
    //                     style={{
    //                         backgroundColor: "white",
    //                         width: 0.5,
    //                         height: Dimensions.get("screen").height * 0.025,
    //                         opacity: 0.8,
    //                         marginHorizontal: 15,
    //                         bottom: -1,
    //                         // borderWidth: 1
    //                     }}
    //                 ></View>
    //                 <TouchableOpacity
    //                     onPress={() => {
    //                         if (global.currUserRole === 'admin') {
    //                             navigation.navigate("General Settings - KooDoo BackOffice")
    //                         }
    //                     }}
    //                     style={{ flexDirection: "row", alignItems: "center" }}
    //                 >
    //                     <Text
    //                         style={{
    //                             fontFamily: "NunitoSans-SemiBold",
    //                             fontSize: 16,
    //                             color: Colors.secondaryColor,
    //                             marginRight: 15,
    //                         }}
    //                     >
    //                         {userName}
    //                     </Text>
    //                     <View
    //                         style={{
    //                             //backgroundColor: 'red',
    //                             marginRight: 30,
    //                             width: windowHeight * 0.05,
    //                             height: windowHeight * 0.05,
    //                             borderRadius: windowHeight * 0.05 * 0.5,
    //                             alignItems: "center",
    //                             justifyContent: "center",
    //                             backgroundColor: "white",
    //                         }}
    //                     >
    //                         <img
    //                             src={personicon}
    //                             width={windowHeight * 0.035}
    //                             height={windowHeight * 0.035}
    //                         />
    //                         {/* <Image
    //                     style={{
    //                       width: windowHeight * 0.05,
    //                     height: windowHeight * 0.05,
    //                       alignSelf: 'center',
    //                     }}
    //                     source={require('../assets/image/profile-pic.jpg')}
    //                   /> */}
    //                     </View>
    //                 </TouchableOpacity>
    //             </View>
    //         ),
    //     });
    // }, [pageStack]);

    // componentDidMount = () => {
    //     moment()
    // }

    /* const email = () => {
          var body = {
              email: '<EMAIL>',
              data: list
          }
          ApiClient.POST(API.emailReportPdf, body, false).then((result) => {
              try {
                  if (result != null) {
                      Alert.alert(
                          'Congratulation!',
                          'You Have Successfull',
                          [
                              {
                                  text: 'OK',
                                  onPress: () => { setState({ visible1: false }) },
                              },
                          ],
                          { cancelable: false },
                      );
                  }
              } catch (error) {
                  Alert.alert('Failed', [{ text: 'OK', onPress: () => { } }], {
                      cancelable: false,
                  });
              }
          })
      } */

    /* const download = () => {
          var body = {
              data: list
          }
          ApiClient.POST(API.generateReportPDF, body, false).then((result) => {
              try {
                  if (result != null) {
                      Alert.alert(
                          'Congratulation!',
                          'You Have Successfull',
                          [
                              {
                                  text: 'OK',
                                  onPress: () => { setState({ visible: false }) },
                              },
                          ],
                          { cancelable: false },
                      );
                  }
              } catch (error) {
                  Alert.alert('Failed', [{ text: 'OK', onPress: () => { } }], {
                      cancelable: false,
                  });
              }
          })
      } */

    // const add = async () => {
    //     if (page + 1 < pageCount) {
    //         await setState({ page: page + 1, currentPage: currentPage + 1 })
    //         // console.log(page)
    //         var e = page
    //         next(e)
    //     }
    // }

    // const next = (e) => {
    //     const offset = e * perPage;
    //     setState({ offset: offset })
    //     loadMoreData()
    // }

    // const less = async () => {
    //     if (page > 0) {
    //         await setState({ page: page - 1, currentPage: currentPage - 1 })
    //         // console.log(page)
    //         var y = page
    //         pre(y)
    //     }
    // }

    // const pre = (y) => {

    //     const offset = y * perPage;
    //     setState({ offset: offset })
    //     loadMoreData()

    // }

    const nextPage = () => {
        setCurrentPage(currentPage + 1 > pageCount ? currentPage : currentPage + 1);
    };

    const prevPage = () => {
        setCurrentPage(currentPage - 1 < 1 ? currentPage : currentPage - 1);
    };

    const nextDetailsPage = () => {
        setCurrentDetailsPage(
            currentDetailsPage + 1 > detailsPageCount
                ? currentDetailsPage
                : currentDetailsPage + 1,
        );
    };

    const prevDetailsPage = () => {
        setCurrentDetailsPage(
            currentDetailsPage - 1 < 1 ? currentDetailsPage : currentDetailsPage - 1,
        );
    };

    const prevPage2 = async () => {
        if (currentPage > 1) {
            OutletStore.update(s => {
                s.reportingApiLoading = true;
            });
            setIsTableApiLoading(true);

            // Calculate pagination for categories in the previous page
            const prevPage = currentPage - 1;
            const startIndex = (prevPage - 1) * perPage;
            const endIndex = Math.min(startIndex + perPage, upsellingCampaigns.length);
            const filterList = upsellingCampaigns.slice(startIndex, endIndex).map(item => item.uniqueId);

            try {
                const requestBody = {
                    isMasterAccount,
                    merchantId,
                    outletId: currOutletId,
                    reportOutletIdList,
                    startDate: moment(historyStartDate).startOf('day'),
                    endDate: moment(historyEndDate).endOf('day'),
                    filterType: SERVER_REPORT_FILTER_TYPE.UPSELLING_REVENUE,
                    filterIdList: filterList,
                };

                console.log('Prev Page API Request Body:', requestBody);

                const data = await ApiClientReporting.POST(API.getOutletUserOrderDoneProcessed, requestBody);

                console.log('data returned', data);

                OutletStore.update(s => {
                    s.reportingApiLoading = false;
                });

                setTimeout(() => {
                    setIsTableApiLoading(false);
                }, 1000);

                TempStore.update(s => {
                    s.allOutletUserOrderDoneProcessed = data.allOutletUserOrderDoneProcessed;
                });

                setCurrentPage(currentPage - 1);
            } catch (error) {
                console.error('Error fetching previous page data:', error);

                OutletStore.update(s => {
                    s.reportingApiLoading = false;
                });

                setTimeout(() => {
                    setIsTableApiLoading(false);
                }, 1000);
            }
        }
    };

    const nextPage2 = async () => {
        if (currentPage < pageCount) {
            OutletStore.update(s => {
                s.reportingApiLoading = true;
            });
            setIsTableApiLoading(true);

            // Calculate pagination for categories in the next page
            const nextPage = currentPage + 1;
            const startIndex = (nextPage - 1) * perPage;
            const endIndex = Math.min(startIndex + perPage, upsellingCampaigns.length);
            const filterList = upsellingCampaigns.slice(startIndex, endIndex).map(item => item.uniqueId);

            try {
                const requestBody = {
                    isMasterAccount,
                    merchantId,
                    outletId: currOutletId,
                    reportOutletIdList,
                    startDate: moment(historyStartDate).startOf('day'),
                    endDate: moment(historyEndDate).endOf('day'),
                    filterType: SERVER_REPORT_FILTER_TYPE.UPSELLING_REVENUE,
                    filterIdList: filterList,
                };

                console.log('Next Page API Request Body:', requestBody);

                const data = await ApiClientReporting.POST(API.getOutletUserOrderDoneProcessed, requestBody);

                console.log('data returned', data);

                OutletStore.update(s => {
                    s.reportingApiLoading = false;
                });

                setTimeout(() => {
                    setIsTableApiLoading(false);
                }, 1000);

                TempStore.update(s => {
                    s.allOutletUserOrderDoneProcessed = data.allOutletUserOrderDoneProcessed;
                });

                setCurrentPage(currentPage + 1);
            } catch (error) {
                console.error('Error fetching next page data:', error);

                OutletStore.update(s => {
                    s.reportingApiLoading = false;
                });

                setTimeout(() => {
                    setIsTableApiLoading(false);
                }, 1000);
            }
        }
    };


    const loadMoreData = () => {
        const data = oriList;
        const slice = data.slice(offset, offset + perPage);
        setState({ list: slice, pageCount: Math.ceil(data.length / perPage) });
    };

    // moment = async () => {
    //     const today = new Date();
    //     const day = new Date(today.getTime() - (7 * 24 * 60 * 60 * 1000));
    //     await setState({ startDate: moment(day).format('YYYY-MM-DD'), endDate: moment(today).format('YYYY-MM-DD') })
    //     getDetail()
    // }

    const getDetail = () => {
        ApiClient.GET(
            `${API.getSalesBySku + 1}&startDate=${startDate}&endDate=${endDate}`,
        ).then((result) => {
            var data = result;
            var slice = data.slice(offset, offset + perPage);
            setState({
                list: slice,
                oriList: data,
                pageCount: Math.ceil(data.length / perPage),
            });
        });
    };

    const decimal = (value) => {
        return value.toFixed(2);
    };

    const renderSearchItem = ({ item, index }) =>
        (index + 1) % 2 == 0 ? (
            <View style={{ backgroundColor: Colors.whiteColor, padding: 12 }}>
                <View style={{ flexDirection: 'row' }}>
                    <Text
                        style={{
                            flex: 3,
                            fontSize: 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                        }}>
                        {item.productName}
                    </Text>
                    <Text
                        style={{
                            flex: 3,
                            fontSize: 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                        }}>
                        {item.category}
                    </Text>
                    <View style={{ flex: 3 }}>
                        <Text
                            style={{
                                fontSize: 13,
                                fontFamily: 'NunitoSans-Regular',
                                textAlign: 'center',
                            }}>
                            {item.productName}
                        </Text>
                        <Text
                            style={{
                                fontSize: 13,
                                fontFamily: 'NunitoSans-Regular',
                                textAlign: 'center',
                            }}>
                            -
                        </Text>
                    </View>
                    <Text
                        style={{
                            flex: 2,
                            fontSize: 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                        }}>
                        {item.quantity}
                    </Text>
                    <Text
                        style={{
                            flex: 2,
                            fontSize: 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                        }}>
                        {item.totalPrice}
                    </Text>
                    <Text
                        style={{
                            flex: 2,
                            fontSize: 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                        }}>
                        0.00
                    </Text>
                    <Text
                        style={{
                            flex: 2,
                            fontSize: 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                        }}>
                        {item.totalDiscount}
                    </Text>
                    <Text
                        style={{
                            flex: 2,
                            fontSize: 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                        }}>
                        {item.discount}
                    </Text>
                    <Text
                        style={{
                            flex: 2,
                            fontSize: 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                        }}>
                        {item.netSale}
                    </Text>
                </View>
            </View>
        ) : (
            <View style={{ backgroundColor: Colors.fieldtBgColor, padding: 12 }}>
                <View style={{ flexDirection: 'row' }}>
                    <Text
                        style={{
                            flex: 3,
                            fontSize: 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                        }}>
                        {item.productName}
                    </Text>
                    <Text
                        style={{
                            flex: 3,
                            fontSize: 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                        }}>
                        {item.category}
                    </Text>
                    <View style={{ flex: 3 }}>
                        <Text
                            style={{
                                fontSize: 13,
                                fontFamily: 'NunitoSans-Regular',
                                textAlign: 'center',
                            }}>
                            {item.productName}
                        </Text>
                        <Text
                            style={{
                                fontSize: 13,
                                fontFamily: 'NunitoSans-Regular',
                                textAlign: 'center',
                            }}>
                            -
                        </Text>
                    </View>
                    <Text
                        style={{
                            flex: 2,
                            fontSize: 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                        }}>
                        {item.quantity}
                    </Text>
                    <Text
                        style={{
                            flex: 2,
                            fontSize: 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                        }}>
                        {item.totalPrice}
                    </Text>
                    <Text
                        style={{
                            flex: 2,
                            fontSize: 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                        }}>
                        0.00 ({item.totalDiscount}%)
                    </Text>
                    {/* <Text style={{ flex: 2, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>{item.totalDiscount}</Text> */}
                    <Text
                        style={{
                            flex: 2,
                            fontSize: 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                        }}>
                        {item.discount}
                    </Text>
                    <Text
                        style={{
                            flex: 2,
                            fontSize: 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                        }}>
                        {item.netSale}
                    </Text>
                </View>
            </View>
        );

    const onItemSummaryClicked = (item) => {
        // setProductSalesDetails(item.detailsList);
        setSelectedItemSummary(item);
        setShowDetails(true);

        // setCurrentPage(1);
        // setPageCount(Math.ceil(item.detailsList.length / perPage));

        // console.log('item.detailsList');
        // console.log(item.detailsList);
    };

    const renderItem = ({ item, index }) => {
        let tempTotalSales = 0;
        let tempNetSales = 0;
        for (let i = 0; i < item.detailsList.length; i++) {
            // console.log(item.detailsList[i], 'here');
            tempTotalSales += item.detailsList[i].finalPrice;
            tempNetSales +=
                (item.detailsList[i].finalPriceBefore
                    ? item.detailsList[i].finalPriceBefore
                    : item.detailsList[i].finalPrice) -
                item.detailsList[i].tax -
                (item.detailsList[i].sc ? item.detailsList[i].sc : 0);
        }
        return (
            // (index + 1) % 2 == 0 ?
            <TouchableOpacity
                onPress={() => {
                    onItemSummaryClicked(item);
                    setDetailsTitle(item.productName);
                    // console.log(item, 'here');
                }}
                style={{
                    backgroundColor:
                        (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
                    paddingVertical: 10,
                    //paddingHorizontal: 3,
                    //paddingLeft: 1,
                    borderColor: '#BDBDBD',
                    borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
                    borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
                    // width: '100%',
                }}>
                {/* <View style={{ backgroundColor: (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.fieldtBgColor, padding: 12 }}> */}
                <View style={{ flexDirection: 'row' }}>
                    <Text
                        style={{
                            width: '5%',
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'left',
                            paddingLeft: 10,
                        }}>
                        {((currentPage - 1) * perPage) + index + 1}
                    </Text>
                    <Text
                        style={{
                            width: '10%',
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'left',
                            paddingLeft: 10,
                        }}>
                        {item.campaignName}
                    </Text>
                    <Text
                        style={{
                            width: '19%',
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'left',
                            paddingLeft: 10,
                        }}>
                        {item.productName}
                    </Text>
                    <Text
                        style={{
                            width: '11%',
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'left',
                            paddingLeft: 10,
                        }}>
                        {item.productCategory}
                    </Text>
                    <Text
                        style={{
                            width: '9%',
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'left',
                            paddingLeft: 10,
                        }}>
                        {item.upsellingType}
                    </Text>
                    <Text
                        style={{
                            width: '9%',
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'left',
                            paddingLeft: 10,
                        }}>
                        {item.upsellingRevenue
                            .toFixed(2)
                            .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                    </Text>
                    <Text
                        style={{
                            width: '9%',
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'left',
                            paddingLeft: 10,
                        }}>
                        {item.totalItems}
                    </Text>
                    {/* <Text style={{ flex: 1.6, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>{(item.totalSalesReturn).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}</Text> */}
                    <Text
                        style={{
                            width: '8%',
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'left',
                            paddingLeft: 10,
                        }}>
                        {item.upsellingDiscount
                            .toFixed(2)
                            .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                    </Text>
                    <Text
                        style={{
                            width: '8%',
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'left',
                            paddingLeft: 10,
                        }}>
                        {(item.totalOrders ?
                            item.totalOrders / totalOrdersNum * 100
                            : 0).toFixed(1)}
                    </Text>
                </View>
            </TouchableOpacity>
            // : <View style={{ backgroundColor: Colors.fieldtBgColor, padding: 12 }}>
            //     <View style={{ flexDirection: 'row', }}>
            //         <Text style={{ flex: 3, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>{item.productName}</Text>
            //         <Text style={{ flex: 3, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>{item.category}</Text>
            //         <View style={{ flex: 3 }}>
            //             <Text style={{ fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>{item.productName}</Text>
            //             <Text style={{ fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>-</Text>
            //         </View>
            //         <Text style={{ flex: 2, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>{item.quantity}</Text>
            //         <Text style={{ flex: 2, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>{item.totalPrice}</Text>
            //         <Text style={{ flex: 2, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>0.00</Text>
            //         <Text style={{ flex: 2, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>{item.totalDiscount}</Text>
            //         <Text style={{ flex: 2, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>{item.discount}</Text>
            //         <Text style={{ flex: 2, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>{item.netSale}</Text>
            //     </View>
            // </View>
        );
    };

    const onClickItemDetails = (item) => {
        setExpandDetailsDict({
            ...expandDetailsDict,
            [item.uniqueId]: expandDetailsDict[item.uniqueId] ? false : true,
        });
    };

    const renderItemDetails = ({ item, index }) => {
        ///////////////////////////

        // console.log('order id');
        // console.log(item.orderId);

        // calculate longest

        var longestStr = 5;

        // for (var i = 0; i < item.cartItems.length; i++) {
        //     const cartItemPriceWIthoutAddOn =
        //         item.cartItems[i].price -
        //         item.cartItems[i].addOns.reduce(
        //             (accum, addOn) => accum + addOn.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0),
        //             0,
        //         );

        //     if (cartItemPriceWIthoutAddOn.toFixed(0).length > longestStr) {
        //         longestStr = cartItemPriceWIthoutAddOn.toFixed(0).length;
        //     }

        //     for (var j = 0; j < item.cartItems[i].addOns.length; j++) {
        //         if (
        //             item.cartItems[i].addOns[j].prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(0).length > longestStr
        //         ) {
        //             longestStr = item.cartItems[i].addOns[j].prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(0).length;
        //         }
        //     }
        // }

        // if (item.totalPrice.toFixed(0).length > longestStr) {
        //     longestStr = item.totalPrice.toFixed(0).length;
        // }

        // if (item.discount.toFixed(0).length > longestStr) {
        //     longestStr = item.discount.toFixed(0).length;
        // }

        // if (item.tax.toFixed(0).length > longestStr) {
        //     longestStr = item.tax.toFixed(0).length;
        // }

        // if (item.finalPrice.toFixed(0).length > longestStr) {
        //     longestStr = item.finalPrice.toFixed(0).length;
        // }

        // // console.log(longestStr);

        ///////////////////////////

        // calculate spacing

        var cartItemPriceWIthoutAddOnSpacingList = [];
        var addOnsSpacingList = [];

        // for (var i = 0; i < item.cartItems.length; i++) {
        //   const cartItemPriceWIthoutAddOn =
        //     item.cartItems[i].price -
        //     item.cartItems[i].addOns.reduce(
        //       (accum, addOn) => accum + addOn.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0),
        //       0,
        //     );

        //   cartItemPriceWIthoutAddOnSpacingList.push(
        //     Math.max(longestStr - cartItemPriceWIthoutAddOn.toFixed(0).length, 0) +
        //       1,
        //   );

        //   for (var j = 0; j < item.cartItems[i].addOns.length; j++) {
        //     addOnsSpacingList.push(
        //       Math.max(
        //         longestStr -
        //           item.cartItems[i].addOns[j].prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(0).length,
        //         0,
        //       ) + 1,
        //     );
        //   }
        // }

        var totalPriceSpacing =
            Math.max(longestStr - item.totalPrice.toFixed(0).length, 0) + 1;
        var discountSpacing =
            Math.max(longestStr - item.discount.toFixed(0).length, 0) + 1;
        var taxSpacing = Math.max(longestStr - item.tax.toFixed(0).length, 0) + 1;
        var finalPriceSpacing =
            Math.max(longestStr - item.finalPrice.toFixed(0).length, 0) + 1;

        ///////////////////////////
        return (
            <View
                // onPress={() => {
                //   onClickItemDetails(item);
                //   // console.log(item);
                // }}
                style={{
                    backgroundColor:
                        (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
                    paddingVertical: 10,
                    //paddingHorizontal: 3,
                    //paddingLeft: 1,
                    borderColor: '#BDBDBD',
                    borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
                    borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
                }}>
                <View style={{ flexDirection: 'row' }}>
                    <Text
                        style={{
                            width: '6%',
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'left',
                            paddingLeft: 10,
                        }}>
                        {index + 1}
                    </Text>
                    <Text
                        style={{
                            width: '14%',
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'left',
                            paddingLeft: 10,
                        }}>
                        {item.orderId ? `#${item.orderType !== ORDER_TYPE.DINEIN ? 'T' : ''}${item.orderId}` : 'N/A'}
                    </Text>
                    <Text
                        style={{
                            width: '18%',
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'left',
                            paddingLeft: 10,
                        }}>
                        {moment(item.createdAt).format('DD MMM YY hh:mm A')}
                    </Text>
                    <Text
                        style={{
                            width: '10%',
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'left',
                            paddingLeft: 10,
                        }}>
                        {item.upsellingRevenue
                            .toFixed(2)
                            .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                    </Text>
                    <Text
                        style={{
                            width: '7%',
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'left',
                            paddingLeft: 10,
                        }}>
                        {item.totalItems}
                    </Text>
                    {/* <Text
              style={{
                width: '7%',
                fontSize: switchMerchant ? 10 : 13,
                fontFamily: 'NunitoSans-Regular',
                textAlign: 'left',
                paddingLeft: 10,
              }}>
              {parseFloat(item.discountPercentage)
                .toFixed(2)
                .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
            </Text> */}
                    {/* <Text style={{ flex: 2, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>{parseFloat(item.discountPercentage).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}</Text> */}
                    <Text
                        style={{
                            width: '8%',
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'left',
                            paddingLeft: 10,
                        }}>
                        {item.upsellingDiscount
                            .toFixed(2)
                            .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                    </Text>
                </View>

                {expandDetailsDict[item.uniqueId] == true ? (
                    <View
                        style={{
                            minheight: windowHeight * 0.35,
                            marginTop: 30,
                            paddingBottom: 20,
                        }}>
                        {item.cartItems.map((cartItem, index) => {
                            const cartItemPriceWIthoutAddOn = getCartItemPriceWithoutAddOn(cartItem);

                            return (
                                <View
                                    style={{
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                    }}>
                                    <View
                                        style={{
                                            width: '100%',
                                            alignItems: 'flex-start',
                                            flexDirection: 'row',
                                            marginBottom: Platform.OS == 'ios' ? 10 : 10,
                                            minHeight: 80,
                                            //backgroundColor: 'yellow',
                                        }}>
                                        <View
                                            style={{
                                                flexDirection: 'row',
                                                width: '100%',
                                                //backgroundColor: 'blue',
                                            }}>
                                            {index == 0 ? (
                                                <View
                                                    style={{
                                                        marginHorizontal: 1,
                                                        width: Platform.OS == 'ios' ? '8%' : '8%',
                                                        //justifyContent: 'center',
                                                        alignItems: 'center',
                                                        //backgroundColor: 'blue',
                                                    }}>
                                                    <TouchableOpacity
                                                        style={{
                                                            alignItems: 'center',
                                                            marginTop: 0,
                                                        }}
                                                        onPress={() => {
                                                            var crmUser = null;

                                                            if (item.crmUserId !== undefined) {
                                                                for (var i = 0; i < crmUsers.length; i++) {
                                                                    if (item.crmUserId === crmUsers[i].uniqueId) {
                                                                        crmUser = crmUsers[i];
                                                                        break;
                                                                    }
                                                                }
                                                            }

                                                            if (!crmUser) {
                                                                for (var i = 0; i < crmUsers.length; i++) {
                                                                    if (item.userId === crmUsers[i].firebaseUid) {
                                                                        crmUser = crmUsers[i];
                                                                        break;
                                                                    }
                                                                }
                                                            }

                                                            if (crmUser) {
                                                                CommonStore.update(
                                                                    (s) => {
                                                                        s.selectedCustomerEdit = crmUser;
                                                                        // s.selectedCustomerEdit = userReservations[item.userId] && crmUsers[item.userId] ? crmUsers[item.userId] : null ;

                                                                        s.routeParams = {
                                                                            pageFrom: 'Reservation',
                                                                        };
                                                                    },
                                                                    () => {
                                                                        navigation.navigate('NewCustomer');
                                                                    },
                                                                );
                                                            }
                                                        }}>
                                                        <Image
                                                            style={{
                                                                width: switchMerchant ? 30 : 60,
                                                                height: switchMerchant ? 30 : 60,
                                                            }}
                                                            resizeMode="contain"
                                                            source={require('../assets/image/default-profile.png')}
                                                        />

                                                        <View
                                                            style={{
                                                                alignItems: 'center',
                                                                justifyContent: 'center',
                                                            }}>
                                                            <Text
                                                                style={[
                                                                    {
                                                                        fontFamily: 'NunitoSans-Bold',
                                                                        marginTop: 0,
                                                                        fontSize: 13,
                                                                        textAlign: 'center',
                                                                    },
                                                                    switchMerchant
                                                                        ? {
                                                                            fontFamily: 'NunitoSans-Bold',
                                                                            marginTop: 0,
                                                                            fontSize: 10,
                                                                            textAlign: 'center',
                                                                        }
                                                                        : {},
                                                                ]}
                                                                numberOfLines={1}>
                                                                {item.userName ? item.userName : 'Guest'}
                                                            </Text>
                                                        </View>
                                                    </TouchableOpacity>
                                                </View>
                                            ) : (
                                                <View
                                                    style={{
                                                        marginHorizontal: 1,
                                                        width: Platform.OS == 'ios' ? '8%' : '8%',
                                                        justifyContent: 'center',
                                                        alignItems: 'center',
                                                    }}
                                                />
                                            )}

                                            <View
                                                style={{
                                                    // flex: 0.3,
                                                    width: '5%',
                                                    //justifyContent: 'center',
                                                    alignItems: 'center',
                                                    //backgroundColor: 'red',
                                                    //paddingLeft: '1.2%',
                                                }}>
                                                <Text
                                                    style={[
                                                        {
                                                            fontFamily: 'NunitoSans-Bold',
                                                            fontSize: 13,
                                                        },
                                                        switchMerchant
                                                            ? {
                                                                fontFamily: 'NunitoSans-Bold',
                                                                fontSize: 10,
                                                            }
                                                            : {},
                                                    ]}>
                                                    {index + 1}.
                                                </Text>
                                            </View>

                                            <View
                                                style={{
                                                    //flex: 0.5,
                                                    width: '10%',
                                                    //backgroundColor: 'green',
                                                    alignItems: 'center',
                                                }}>
                                                {cartItem.image ? (
                                                    <AsyncImage
                                                        source={{ uri: cartItem.image }}
                                                        // item={cartItem}
                                                        style={{
                                                            width: switchMerchant ? 30 : 60,
                                                            height: switchMerchant ? 30 : 60,
                                                            borderWidth: 1,
                                                            borderColor: '#E5E5E5',
                                                            borderRadius: 5,
                                                        }}
                                                    />
                                                ) : (
                                                    <View
                                                        style={{
                                                            justifyContent: 'center',
                                                            alignItems: 'center',
                                                            width: switchMerchant ? 30 : 60,
                                                            height: switchMerchant ? 30 : 60,
                                                            borderWidth: 1,
                                                            borderColor: '#E5E5E5',
                                                            borderRadius: 5,
                                                        }}>
                                                        <Ionicons
                                                            name="fast-food-outline"
                                                            size={switchMerchant ? 25 : 35}
                                                        />
                                                    </View>
                                                )}
                                            </View>
                                            <View style={{ width: '75%' }}>
                                                <View
                                                    style={{
                                                        marginLeft: Platform.OS == 'ios' ? 14 : 14,
                                                        marginBottom: 10,
                                                        //backgroundColor: 'blue',
                                                        width: '100%',
                                                        flexDirection: 'row',
                                                    }}>
                                                    <View style={{ width: '59.7%' }}>
                                                        <Text
                                                            style={[
                                                                {
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                    fontSize: 13,
                                                                },
                                                                switchMerchant
                                                                    ? {
                                                                        fontFamily: 'NunitoSans-Bold',
                                                                        fontSize: 10,
                                                                    }
                                                                    : {},
                                                            ]}>
                                                            {cartItem.name}{cartItem.priceType === PRODUCT_PRICE_TYPE.UNIT ? ` (${UNIT_TYPE_SHORT[cartItem.unitType]})` : ''}
                                                        </Text>
                                                    </View>

                                                    <View
                                                        style={{
                                                            width: '13%',
                                                        }}>
                                                        <View
                                                            style={{
                                                                alignItems: 'center',
                                                                //backgroundColor: 'yellow',
                                                            }}>
                                                            <Text
                                                                style={[
                                                                    {
                                                                        fontFamily: 'NunitoSans-Bold',
                                                                        fontSize: 13,
                                                                    },
                                                                    // Platform.OS === 'android'
                                                                    //   ? {
                                                                    //       width: '200%',
                                                                    //     }
                                                                    //   : {},
                                                                    switchMerchant
                                                                        ? {
                                                                            fontFamily: 'NunitoSans-Bold',
                                                                            fontSize: 10,
                                                                        }
                                                                        : {},
                                                                ]}>
                                                                x{cartItem.quantity}
                                                            </Text>
                                                        </View>
                                                    </View>
                                                    <View
                                                        style={{
                                                            flexDirection: 'row',
                                                            justifyContent: 'space-between',
                                                            width: '18.8%',
                                                        }}>
                                                        <Text
                                                            style={
                                                                switchMerchant
                                                                    ? {
                                                                        fontSize: 10,
                                                                    }
                                                                    : { fontSize: 13 }
                                                            }>
                                                            RM
                                                        </Text>
                                                        <Text
                                                            style={
                                                                switchMerchant
                                                                    ? {
                                                                        fontSize: 10,
                                                                        paddingRight: 20,
                                                                        fontFamily: 'NunitoSans-Regular',
                                                                    }
                                                                    : {
                                                                        fontSize: 13,
                                                                        paddingRight: 20,
                                                                        fontFamily: 'NunitoSans-Regular',
                                                                    }
                                                            }>
                                                            {cartItemPriceWIthoutAddOn
                                                                .toFixed(2)
                                                                .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                                                        </Text>
                                                    </View>
                                                </View>

                                                {cartItem.remarks && cartItem.remarks.length > 0 ? (
                                                    <View
                                                        style={{
                                                            alignItems: 'center',
                                                            flexDirection: 'row',
                                                            marginLeft: Platform.OS == 'ios' ? 14 : 14,
                                                        }}>
                                                        <View style={{ justifyContent: 'center' }}>
                                                            <Text
                                                                style={[
                                                                    {
                                                                        fontFamily: 'NunitoSans-SemiBold',
                                                                        fontSize: 13,
                                                                    },
                                                                    switchMerchant
                                                                        ? {
                                                                            fontFamily: 'NunitoSans-SemiBold',
                                                                            fontSize: 10,
                                                                        }
                                                                        : {},
                                                                ]}>
                                                                {cartItem.remarks}
                                                            </Text>
                                                        </View>
                                                    </View>
                                                ) : (
                                                    <></>
                                                )}

                                                {cartItem.addOns.map((addOnChoice, i) => {
                                                    return (
                                                        <View
                                                            style={{
                                                                flexDirection: 'row',
                                                                // marginLeft: -5,
                                                                width: '100%',
                                                            }}>
                                                            <View
                                                                style={{
                                                                    width: '59.7%',
                                                                    flexDirection: 'row',
                                                                    marginLeft: Platform.OS == 'ios' ? 14 : 14,
                                                                }}>
                                                                <Text
                                                                    style={[
                                                                        {
                                                                            fontFamily: 'NunitoSans-Bold',
                                                                            fontSize: 13,
                                                                            color: Colors.descriptionColor,
                                                                            width: '25%',
                                                                            // marginLeft: 5,
                                                                        },
                                                                        switchMerchant
                                                                            ? {
                                                                                fontFamily: 'NunitoSans-Bold',
                                                                                fontSize: 10,
                                                                                color: Colors.descriptionColor,
                                                                                width: '25%',
                                                                            }
                                                                            : {},
                                                                    ]}>
                                                                    {`${addOnChoice.name}:`}
                                                                </Text>
                                                                <Text
                                                                    style={[
                                                                        {
                                                                            fontFamily: 'NunitoSans-Bold',
                                                                            fontSize: 13,
                                                                            color: Colors.descriptionColor,
                                                                            width: '75%',
                                                                            // marginLeft: 5,
                                                                        },
                                                                        switchMerchant
                                                                            ? {
                                                                                fontFamily: 'NunitoSans-Bold',
                                                                                fontSize: 10,
                                                                                color: Colors.descriptionColor,
                                                                                width: '75%',
                                                                            }
                                                                            : {},
                                                                    ]}>
                                                                    {`${addOnChoice.choiceNames[0]}`}
                                                                </Text>
                                                            </View>

                                                            <View
                                                                style={[
                                                                    {
                                                                        width: '13%',
                                                                        flexDirection: 'row',
                                                                        justifyContent: 'center',
                                                                        //backgroundColor: 'blue',
                                                                    },
                                                                ]}>
                                                                <Text
                                                                    style={[
                                                                        {
                                                                            fontFamily: 'NunitoSans-Bold',
                                                                            fontSize: 13,
                                                                            color: Colors.descriptionColor,
                                                                            width: '28%',
                                                                            // right: 38,
                                                                            //backgroundColor: 'green',
                                                                            textAlign: 'center',
                                                                        },
                                                                        switchMerchant
                                                                            ? {
                                                                                fontFamily: 'NunitoSans-Bold',
                                                                                fontSize: 10,
                                                                                color: Colors.descriptionColor,
                                                                                width: '28%',
                                                                                textAlign: 'center',
                                                                            }
                                                                            : {},
                                                                    ]}>
                                                                    {`${addOnChoice.quantities
                                                                        ? `x${getAddOnChoiceQuantity(addOnChoice, cartItem)}`
                                                                        : ''
                                                                        }`}
                                                                </Text>
                                                            </View>

                                                            <View
                                                                style={{
                                                                    flexDirection: 'row',
                                                                    justifyContent: 'space-between',
                                                                    width: '18.8%',
                                                                    alignItems: 'center',
                                                                }}>
                                                                <Text
                                                                    style={[
                                                                        switchMerchant
                                                                            ? {
                                                                                color: Colors.descriptionColor,
                                                                                fontSize: 10,
                                                                            }
                                                                            : {
                                                                                color: Colors.descriptionColor,
                                                                                fontSize: 13,
                                                                            },
                                                                    ]}>
                                                                    RM
                                                                </Text>
                                                                <Text
                                                                    style={
                                                                        switchMerchant
                                                                            ? {
                                                                                color: Colors.descriptionColor,
                                                                                paddingRight: 20,
                                                                                fontSize: 10,
                                                                                fontFamily: 'NunitoSans-Regular',
                                                                            }
                                                                            : {
                                                                                color: Colors.descriptionColor,
                                                                                paddingRight: 20,
                                                                                fontSize: 13,
                                                                                fontFamily: 'NunitoSans-Regular',
                                                                            }
                                                                    }>
                                                                    {(getAddOnChoicePrice(addOnChoice, cartItem))
                                                                        .toFixed(2)
                                                                        .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                                                                </Text>
                                                            </View>
                                                        </View>
                                                    );
                                                })}
                                            </View>
                                        </View>
                                    </View>
                                    <View style={{ flexDirection: 'row', width: '100%' }}>
                                        <View style={{ width: '63%' }} />
                                        <View style={{ width: 15 }} />
                                        {index === item.cartItems.length - 1 ? (
                                            <View
                                                style={{
                                                    flexDirection: 'row',
                                                    //backgroundColor: 'yellow',
                                                    width: '28.65%',
                                                }}>
                                                <View
                                                    style={{
                                                        justifyContent: 'center',
                                                        width: '100%',
                                                    }}>
                                                    <View
                                                        style={{
                                                            flexDirection: 'row',
                                                        }}>
                                                        <Text
                                                            style={
                                                                switchMerchant
                                                                    ? {
                                                                        fontSize: 10,
                                                                        width: '50.9%',
                                                                        fontFamily: 'Nunitosans-Bold',
                                                                    }
                                                                    : {
                                                                        fontSize: 13,
                                                                        width: '50.9%',
                                                                        fontFamily: 'Nunitosans-Bold',
                                                                    }
                                                            }>
                                                            Subtotal:
                                                        </Text>
                                                        <View
                                                            style={{
                                                                flexDirection: 'row',
                                                                justifyContent: 'space-between',
                                                                width: '49.2%',
                                                            }}>
                                                            <Text
                                                                style={
                                                                    switchMerchant
                                                                        ? { fontSize: 10 }
                                                                        : { fontSize: 13 }
                                                                }>
                                                                RM
                                                            </Text>
                                                            <Text
                                                                style={
                                                                    switchMerchant
                                                                        ? {
                                                                            fontSize: 10,
                                                                            paddingRight: 20,
                                                                            fontFamily: 'NunitoSans-Regular',
                                                                        }
                                                                        : {
                                                                            fontSize: 13,
                                                                            paddingRight: 20,
                                                                            fontFamily: 'NunitoSans-Regular',
                                                                        }
                                                                }>
                                                                {((item.isRefundOrder && item.finalPrice <= 0)
                                                                    ? 0
                                                                    : item.totalPrice +
                                                                    getOrderDiscountInfo(item)
                                                                )
                                                                    .toFixed(2)
                                                                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                                                            </Text>
                                                        </View>
                                                    </View>
                                                    {cartItem.orderType === ORDER_TYPE.DELIVERY ? (
                                                        <View
                                                            style={{
                                                                flexDirection: 'row',
                                                            }}>
                                                            <Text
                                                                style={
                                                                    switchMerchant
                                                                        ? {
                                                                            fontSize: 10,
                                                                            width: '50.9%',
                                                                            fontFamily: 'Nunitosans-Bold',
                                                                        }
                                                                        : {
                                                                            fontSize: 13,
                                                                            width: '50.9%',
                                                                            fontFamily: 'Nunitosans-Bold',
                                                                        }
                                                                }>
                                                                Delivery Fee:
                                                            </Text>
                                                            <View
                                                                style={{
                                                                    flexDirection: 'row',
                                                                    justifyContent: 'space-between',
                                                                    width: '49.2%',
                                                                }}>
                                                                <Text
                                                                    style={
                                                                        switchMerchant
                                                                            ? { fontSize: 10 }
                                                                            : { fontSize: 13 }
                                                                    }>
                                                                    RM
                                                                </Text>
                                                                <Text
                                                                    style={
                                                                        switchMerchant
                                                                            ? {
                                                                                fontSize: 10,
                                                                                paddingRight: 20,
                                                                                fontFamily: 'NunitoSans-Regular',
                                                                            }
                                                                            : {
                                                                                fontSize: 13,
                                                                                paddingRight: 20,
                                                                                fontFamily: 'NunitoSans-Regular',
                                                                            }
                                                                    }>
                                                                    {item.deliveryFee
                                                                        .toFixed(2)
                                                                        .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                                                                </Text>
                                                            </View>
                                                        </View>
                                                    ) : (
                                                        <></>
                                                    )}

                                                    <View
                                                        style={{
                                                            flexDirection: 'row',
                                                        }}>
                                                        <Text
                                                            style={
                                                                switchMerchant
                                                                    ? {
                                                                        fontSize: 10,
                                                                        width: '50.9%',
                                                                        fontFamily: 'Nunitosans-Bold',
                                                                    }
                                                                    : {
                                                                        fontSize: 13,
                                                                        width: '50.9%',
                                                                        fontFamily: 'Nunitosans-Bold',
                                                                    }
                                                            }>
                                                            Discount:
                                                        </Text>
                                                        <View
                                                            style={{
                                                                flexDirection: 'row',
                                                                justifyContent: 'space-between',
                                                                width: '49.2%',
                                                            }}>
                                                            <Text
                                                                style={{
                                                                    fontSize: switchMerchant ? 10 : 13,
                                                                }}>
                                                                RM
                                                            </Text>
                                                            <Text
                                                                style={
                                                                    switchMerchant
                                                                        ? {
                                                                            fontSize: 10,
                                                                            paddingRight: 20,
                                                                            fontFamily: 'NunitoSans-Regular',
                                                                        }
                                                                        : {
                                                                            fontSize: 13,
                                                                            paddingRight: 20,
                                                                            fontFamily: 'NunitoSans-Regular',
                                                                        }
                                                                }>
                                                                {' '}
                                                                {((item.isRefundOrder && item.finalPrice <= 0)
                                                                    ? 0
                                                                    :
                                                                    // item.discount +
                                                                    getOrderDiscountInfoInclOrderBased(item)
                                                                )
                                                                    .toFixed(2)
                                                                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                                                            </Text>
                                                        </View>
                                                    </View>

                                                    <View
                                                        style={{
                                                            flexDirection: 'row',
                                                        }}>
                                                        <Text
                                                            style={
                                                                switchMerchant
                                                                    ? {
                                                                        fontSize: 10,
                                                                        width: '50.9%',
                                                                        fontFamily: 'Nunitosans-Bold',
                                                                    }
                                                                    : {
                                                                        fontSize: 13,
                                                                        width: '50.9%',
                                                                        fontFamily: 'Nunitosans-Bold',
                                                                    }
                                                            }>
                                                            Tax:
                                                        </Text>
                                                        <View
                                                            style={{
                                                                flexDirection: 'row',
                                                                justifyContent: 'space-between',
                                                                width: '49.2%',
                                                            }}>
                                                            <Text
                                                                style={
                                                                    switchMerchant
                                                                        ? {
                                                                            fontSize: 10,
                                                                            paddingRight: 20,
                                                                        }
                                                                        : { fontSize: 13, paddingRight: 20 }
                                                                }>
                                                                RM
                                                            </Text>
                                                            <Text
                                                                style={
                                                                    switchMerchant
                                                                        ? {
                                                                            fontSize: 10,
                                                                            paddingRight: 20,
                                                                            fontFamily: 'NunitoSans-Regular',
                                                                        }
                                                                        : {
                                                                            fontSize: 13,
                                                                            paddingRight: 20,
                                                                            fontFamily: 'NunitoSans-Regular',
                                                                        }
                                                                }>
                                                                {item.tax
                                                                    .toFixed(2)
                                                                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                                                            </Text>
                                                        </View>
                                                    </View>

                                                    <View
                                                        style={{
                                                            flexDirection: 'row',
                                                        }}>
                                                        <Text
                                                            style={
                                                                switchMerchant
                                                                    ? {
                                                                        fontSize: 10,
                                                                        width: '50.85%',
                                                                        fontFamily: 'Nunitosans-Bold',
                                                                    }
                                                                    : {
                                                                        fontSize: 13,
                                                                        width: '50.85%',
                                                                        fontFamily: 'Nunitosans-Bold',
                                                                    }
                                                            }>
                                                            Service Charge:
                                                        </Text>
                                                        <View
                                                            style={{
                                                                flexDirection: 'row',
                                                                justifyContent: 'space-between',
                                                                width: switchMerchant ? '49.15%' : '49.1%',
                                                            }}>
                                                            <Text
                                                                style={
                                                                    switchMerchant
                                                                        ? {
                                                                            fontSize: 10,
                                                                        }
                                                                        : { fontSize: 13 }
                                                                }>
                                                                RM
                                                            </Text>
                                                            <Text
                                                                style={
                                                                    switchMerchant
                                                                        ? {
                                                                            fontSize: 10,
                                                                            paddingRight: 20,
                                                                            fontFamily: 'NunitoSans-Regular',
                                                                        }
                                                                        : {
                                                                            fontSize: 13,
                                                                            paddingRight: 20,
                                                                            fontFamily: 'NunitoSans-Regular',
                                                                        }
                                                                }>
                                                                {(item.sc || 0)
                                                                    .toFixed(2)
                                                                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                                                            </Text>
                                                        </View>
                                                    </View>
                                                    <View
                                                        style={{
                                                            flexDirection: 'row',
                                                        }}>
                                                        <Text
                                                            style={
                                                                switchMerchant
                                                                    ? {
                                                                        fontSize: 10,
                                                                        width: '50.9%',
                                                                        fontFamily: 'Nunitosans-Bold',
                                                                    }
                                                                    : {
                                                                        fontSize: 13,
                                                                        width: '50.9%',
                                                                        fontFamily: 'Nunitosans-Bold',
                                                                    }
                                                            }>
                                                            Rounding:
                                                        </Text>
                                                        <View
                                                            style={{
                                                                flexDirection: 'row',
                                                                justifyContent: 'space-between',
                                                                width: '49.2%',
                                                            }}>
                                                            <Text
                                                                style={{
                                                                    fontSize: switchMerchant ? 10 : 13,
                                                                }}>
                                                                RM
                                                            </Text>
                                                            <Text
                                                                style={
                                                                    switchMerchant
                                                                        ? {
                                                                            fontSize: 10,
                                                                            paddingRight: 20,
                                                                            fontFamily: 'NunitoSans-Regular',
                                                                        }
                                                                        : {
                                                                            fontSize: 13,
                                                                            paddingRight: 20,
                                                                            fontFamily: 'NunitoSans-Regular',
                                                                        }
                                                                }>
                                                                {(item.finalPrice
                                                                    ? item.finalPrice - item.finalPriceBefore
                                                                    : 0
                                                                )
                                                                    .toFixed(2)
                                                                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                                                            </Text>
                                                        </View>
                                                    </View>

                                                    <View
                                                        style={{
                                                            flexDirection: 'row',
                                                        }}>
                                                        <Text
                                                            style={
                                                                switchMerchant
                                                                    ? {
                                                                        fontSize: 10,
                                                                        width: '50.9%',
                                                                        fontFamily: 'Nunitosans-Bold',
                                                                    }
                                                                    : {
                                                                        fontSize: 13,
                                                                        width: '50.9%',
                                                                        fontFamily: 'Nunitosans-Bold',
                                                                    }
                                                            }>
                                                            Total:
                                                        </Text>
                                                        <View
                                                            style={{
                                                                flexDirection: 'row',
                                                                justifyContent: 'space-between',
                                                                width: '49.2%',
                                                            }}>
                                                            <Text
                                                                style={{
                                                                    fontSize: switchMerchant ? 10 : 13,
                                                                }}>
                                                                RM
                                                            </Text>
                                                            <Text
                                                                style={
                                                                    switchMerchant
                                                                        ? {
                                                                            fontSize: 10,
                                                                            paddingRight: 20,
                                                                            fontFamily: 'NunitoSans-Regular',
                                                                        }
                                                                        : {
                                                                            fontSize: 13,
                                                                            paddingRight: 20,
                                                                            fontFamily: 'NunitoSans-Regular',
                                                                        }
                                                                }>
                                                                {item.finalPrice
                                                                    .toFixed(2)
                                                                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                                                            </Text>
                                                        </View>
                                                    </View>
                                                </View>
                                            </View>
                                        ) : (
                                            <></>
                                        )}
                                    </View>

                                    {/* <View style={{alignItems:'flex-end'}}>
                      <View style={{ flexDirection: 'row' }}>
                        <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 16 }}>Subtotal: {(Math.ceil(cartItem.price * 20-0.05) /20).toFixed(2)}</Text>
                      </View>
                    </View> */}
                                    {/* {(cartItem.remarks && cartItem.remarks.length > 0) ?
                    <View style={{ alignItems: 'center', flexDirection: 'row' }}>
                      
                      <View style={{ flex: 1, justifyContent: 'center', }}>
                        <Text style={{ fontFamily: 'NunitoSans-SemiBold', fontSize: 15 }}>{cartItem.remarks}</Text>
                      </View>
                      
                    </View>
                    : <></>
                  } */}
                                </View>
                            );
                        })}
                    </View>
                ) : null}
            </View>
        );
    };

    // const downloadCsv = () => {
    //     //if (productSales && productSales.dataSource && productSales.dataSource.data) {
    //     //const csvData = convertArrayToCSV(productSales.dataSource.data);
    //     const csvData = convertArrayToCSV(CsvData);

    //     const pathToWrite = `${RNFetchBlob.fs.dirs.DownloadDir
    //         }/koodoo-report-Product-Sales${moment().format('YYYY-MM-DD-HH-mm-ss')}.csv`;
    //     // console.log('PATH', pathToWrite);
    //     RNFetchBlob.fs
    //         .writeFile(pathToWrite, csvData, 'utf8')
    //         .then(() => {
    //             // console.log(`wrote file ${pathToWrite}`);
    //             // wrote file /storage/emulated/0/Download/data.csv
    //             Alert.alert(
    //                 'Success',
    //                 `Send to ${pathToWrite}`,
    //                 [{ text: 'OK', onPress: () => { } }],
    //                 { cancelable: false },
    //             );
    //         })
    //         .catch((error) => console.error(error));
    //     //}
    // };

    const convertDataToExcelFormat = () => {
        var excelData = [];

        if (!showDetails) {
            for (var i = 0; i < productSalesUpselling.length; i++) {
                var excelRow = {
                    'Campaign': productSalesUpselling[i].campaignName,
                    'Product': productSalesUpselling[i].productName,
                    'Category': productSalesUpselling[i].productCategory,
                    'Type': productSalesUpselling[i].upsellingType,
                    'Upselling Revenue (RM)': +parseFloat(productSalesUpselling[i].upsellingRevenue).toFixed(2),
                    'Items (Qty)': +parseFloat(productSalesUpselling[i].totalItems),
                    'Upselling Disc (RM)': +parseFloat(productSalesUpselling[i].upsellingDiscount).toFixed(2),
                    'Purchase Rate (%)': +parseFloat(productSalesUpselling[i].totalOrders / totalOrdersNum * 100).toFixed(1),
                };

                excelData.push(excelRow);
            }
        } else {
            for (var i = 0; i < productSalesDetails.length; i++) {
                const calculatedDiscount = getOrderDiscountInfoInclOrderBased(productSalesDetails[i]);

                var excelRow = {
                    //Have to change to product sales details
                    // 'Transaction Category': ORDER_TYPE_PARSED[transactionTypeSalesDetails[i].orderType],
                    // 'Sales (RM)': parseFloat(transactionTypeSalesDetails[i].finalPrice).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,'),
                    // 'Transaction Date': moment(transactionTypeSalesDetails[i].createdAt).format('DD MMM hh:mma'),
                    // 'Total Discount (RM)': parseFloat(transactionTypeSalesDetails[i].discount).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,'),
                    // 'Discount (%)': parseFloat(isFinite(transactionTypeSalesDetails[i].finalPrice / transactionTypeSalesDetails[i].discount) ? transactionTypeSalesDetails[i].finalPrice / transactionTypeSalesDetails[i].discount * 100 : 0).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,'),
                    // 'Tax (RM)': parseFloat(transactionTypeSalesDetails[i].tax).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,'),
                    // 'Tax (RM)': parseFloat(0).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,'),
                    // 'GP (%)': parseFloat(0).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,'),
                    // 'Net Sales (RM)': parseFloat(transactionTypeSalesDetails[i].totalPrice).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,'),
                    'Order ID': productSalesDetails[i].orderId
                        ? `#${productSalesDetails[i].orderId}`
                        : 'N/A',
                    'Transaction Date': moment(
                        productSalesDetails[i].createdAt,
                    ).format('DD MMM hh:mm A'),
                    'Sales (RM)': parseFloat((productSalesDetails[i].itemPrice) + getOrderDiscountInfo(productSalesDetails[i]))
                        .toFixed(2)
                        .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,'),
                    'Disc (RM)': parseFloat(getOrderDiscountInfoInclOrderBased(productSalesDetails[i]))
                        .toFixed(2)
                        .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,'),
                    // 'Discount (%)': parseFloat(
                    //   isFinite(
                    //     calculatedDiscount /
                    //     (productSalesDetails[i].finalPrice + calculatedDiscount),
                    //   )
                    //     ? (calculatedDiscount /
                    //       (productSalesDetails[i].finalPrice + calculatedDiscount)) *
                    //     100
                    //     : 0,
                    // )
                    //   .toFixed(2)
                    //   .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,'),
                    'Tax (RM)': parseFloat(productSalesDetails[i].tax)
                        .toFixed(2)
                        .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,'),
                    'Service Chage (RM)': parseFloat(productSalesDetails[i].sc || 0)
                        .toFixed(2)
                        .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,'),
                    // 'GP (%)': parseFloat(0).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,'),
                    'Sales Return (RM)': parseFloat(productSalesDetails[i].salesReturn || 0)
                        .toFixed(2),
                    'Net Sales (RM)': parseFloat(productSalesDetails[i].itemPrice + productSalesDetails[i].itemTax + productSalesDetails[i].itemSc)
                        .toFixed(2)
                        .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,'),
                };

                excelData.push(excelRow);
            }
        }

        // console.log('excelData');
        // console.log(excelData);

        return excelData;
    };

    const handleExportExcel = () => {
        var wb = XLSX.utils.book_new(),
            ws = XLSX.utils.json_to_sheet(convertDataToExcelFormat());

        XLSX.utils.book_append_sheet(wb, ws, "ReportUpsellingRevenue");
        XLSX.writeFile(wb, "ReportUpsellingRevenue.xlsx");
    }

    const convertDataToCSVFormat = () => {
        var csvData = [];

        if (!showDetails) {
            csvData.push(
                `Campaign,Product,Category,Type,Upselling Revenue (RM),Items (Qty),Upselling Disc (RM),Purchase Rate (%)`,
            );

            for (var i = 0; i < productSalesUpselling.length; i++) {
                var csvRow = `${productSalesUpselling[i].campaignName.split(', ').join(' ')},${productSalesUpselling[i].productName.split(', ').join(' ')},${productSalesUpselling[i].productCategory
                    },${productSalesUpselling[i].upsellingType},${+parseFloat(productSalesUpselling[i].upsellingRevenue).toFixed(2)},${+parseFloat(
                        productSalesUpselling[i].totalItems,
                    )},${+parseFloat(productSalesUpselling[i].upsellingDiscount).toFixed(2)},${+parseFloat(productSalesUpselling[i].totalOrders / totalOrdersNum * 100).toFixed(1)}`;

                csvData.push(csvRow);
            }
        } else {
            // csvData.push(
            //   `Order ID,Transaction Date,Sales (RM),Disc (RM),Disc (%),Tax (RM),Service Charge (RM),Sales Return (RM),Net Sales (RM)`,
            // );

            csvData.push(
                `Order ID,Transaction Date,Sales (RM),Disc (RM),Tax (RM),Service Charge (RM),Sales Return (RM),Net Sales (RM)`,
            );

            for (var i = 0; i < productSalesDetails.length; i++) {
                // var csvRow = `${productSalesDetails[i].orderId
                //   ? `#${productSalesDetails[i].orderId.split(', ').join(' | ')}`
                //   : 'N/A'
                //   },${moment(productSalesDetails[i].createdAt).format(
                //     'DD MMM YY hh:mm A',
                //   )},${+parseFloat(
                //     productSalesDetails[i].itemPrice +
                //     productSalesDetails[i].itemTax +
                //     productSalesDetails[i].itemSc,
                //   ).toFixed(2)},${+parseFloat(productSalesDetails[i].discount).toFixed(
                //     2,
                //   )},${+parseFloat(productSalesDetails[i].discountPercentage).toFixed(
                //     2,
                //   )},${productSalesDetails[i].tax || 0},${productSalesDetails[i].sc || 0
                //   },${productSalesDetails[i].salesReturn || 0},${productSalesDetails[i].itemPrice
                //   }`;

                var csvRow = `${productSalesDetails[i].orderId
                    ? `#${productSalesDetails[i].orderId.split(', ').join(' | ')}`
                    : 'N/A'
                    },${moment(productSalesDetails[i].createdAt).format(
                        'DD MMM YY hh:mm A',
                    )},${+parseFloat((productSalesDetails[i].itemPrice) + getOrderDiscountInfo(productSalesDetails[i]))},${+parseFloat(getOrderDiscountInfoInclOrderBased(productSalesDetails[i])).toFixed(
                        2,
                    )},${+parseFloat(productSalesDetails[i].tax || 0).toFixed(2)},${+parseFloat(productSalesDetails[i].sc || 0).toFixed(2)
                    },${+parseFloat(productSalesDetails[i].salesReturn || 0).toFixed(2)},${parseFloat(productSalesDetails[i].itemPrice + productSalesDetails[i].itemTax + productSalesDetails[i].itemSc).toFixed(2)
                    }`;

                csvData.push(csvRow);
            }
        }

        // console.log('excelData');
        // console.log(excelData);

        return csvData.join('\r\n');
    };

    // const downloadExcel = () => {
    //     const excelData = convertDataToExcelFormat();

    //     var excelFile = `${Platform.OS === 'ios'
    //         ? RNFS.DocumentDirectoryPath
    //         : RNFS.DownloadDirectoryPath
    //         }/koodoo-report-Product-Sales${moment().format(
    //             'YYYY-MM-DD-HH-mm-ss',
    //         )}.xlsx`;
    //     var excelWorkSheet = XLSX.utils.json_to_sheet(excelData);
    //     var excelWorkBook = XLSX.utils.book_new();
    //     XLSX.utils.book_append_sheet(
    //         excelWorkBook,
    //         excelWorkSheet,
    //         'Upselling Sales Report',
    //     );

    //     const workBookData = XLSX.write(excelWorkBook, {
    //         type: 'binary',
    //         bookType: 'xlsx',
    //     });

    //     RNFS.writeFile(excelFile, workBookData, 'ascii')
    //         .then((success) => {
    //             // console.log(`wrote file ${excelFile}`);

    //             Alert.alert(
    //                 'Success',
    //                 `Send to ${excelFile}`,
    //                 [{ text: 'OK', onPress: () => { } }],
    //                 { cancelable: false },
    //             );
    //         })
    //         .catch((err) => {
    //             // console.log(err.message);
    //         });

    //     // XLSX.writeFileAsync(excelFile, excelWorkBook, () => {
    //     //     Alert.alert(
    //     //         'Success',
    //     //         `Send to ${excelFile}`,
    //     //         [{ text: 'OK', onPress: () => { } }],
    //     //         { cancelable: false },
    //     //     );
    //     // });

    //     // const csvData = convertArrayToCSV(CsvData);

    //     // const pathToWrite = `${RNFetchBlob.fs.dirs.DownloadDir}/koodoo-report-Product-Sales${moment().format('YYYY-MM-DD-HH-mm-ss')}.csv`;
    //     // // console.log("PATH", excelFile);
    //     // RNFetchBlob.fs
    //     //     .writeFile(excelFile, excelWorkBook, 'utf8')
    //     //     .then(() => {
    //     //         // console.log(`wrote file ${excelFile}`);
    //     //         Alert.alert(
    //     //             'Success',
    //     //             `Send to ${excelFile}`,
    //     //             [{ text: 'OK', onPress: () => { } }],
    //     //             { cancelable: false },
    //     //         );
    //     //     })
    //     //     .catch(error => console.error(error));
    // };

    // const emailProductSales = () => {
    //     var body = {
    //         data: CsvData,
    //         //data: convertArrayToCSV(productSales.dataSource.data),
    //         data: convertArrayToCSV(CsvData),
    //         email: exportEmail,
    //     };
    //     //API need to change
    //     ApiClient.POST(API.emailDashboard, body, false).then((result) => {
    //         if (result !== null) {
    //             Alert.alert(
    //                 'Success',
    //                 'Email sent to your inbox',
    //                 [{ text: 'OK', onPress: () => { } }],
    //                 { cancelable: false },
    //             );
    //         }
    //     });

    //     setVisible(false);
    // };

    // Test Email
    const emailProductSales = () => {
        const excelData = convertDataToExcelFormat();

        var body = {
            // data: CsvData,
            //data: convertArrayToCSV(todaySalesChart.dataSource.data),
            data: JSON.stringify(excelData),
            //data: convertDataToExcelFormat(),
            email: exportEmail,
        };

        ApiClient.POST(API.emailDashboard, body, false).then((result) => {
            if (result !== null) {
                Alert.alert(
                    'Success',
                    'Email has been sent',
                    [{ text: 'OK', onPress: () => { } }],
                    { cancelable: false },
                );
            }
        });

        setVisible(false);
    };

    const eventsChart = {
        dataPlotClick: (e, item) => {
            // console.log('test data plot');
        },
    };

    const changeClick = () => {
        if (day == true) {
            setState({ day: false });
        } else setState({ day: true });
    };

    var leftSpacing = '0%';

    if (windowWidth >= 1280) {
        leftSpacing = '10%';
    }

    const leftSpacingScale = {
        marginLeft: leftSpacing,
    };

    const filterPressed = (filterTapped) => {
        if (filterTapped == 1) {
            setExpandSelection(true);
        }
    };

    const groupByPressed = (groupByTapped) => {
        if (groupByTapped == 1) {
            setExpandGroupBy(true);
        }
    };

    const flatListRef = useRef();

    const ScrollToTop = () => {
        flatListRef.current.scrollToOffset({ animated: true, offset: 0 });
    };

    const ScrollToBottom = () => {
        flatListRef.current.scrollToOffset({ animated: true, offset: 100 });
    };

    return (
        // <View style={styles.container}>
        //     <View style={styles.sidebar}>
        // <UserIdleWrapper disabled={!isMounted}>
        <View
            style={[
                styles.container,
                {
                    ...getTransformForScreenInsideNavigation(),
                }
            ]}>
            <View
                style={[
                    styles.sidebar,
                    {
                        width: windowWidth * 0.08,
                        flex: 0.8
                    }
                ]}>
                <SideBar
                    navigation={props.navigation}
                    selectedTab={8}
                    expandReport={true}
                />
            </View>
            <View style={{ height: windowHeight, flex: 9 }}>
                <ScrollView horizontal>
                    <Modal
                        style={{}}
                        visible={exportModalVisibility}
                        supportedOrientations={["portrait", "landscape"]}
                        transparent={true}
                        animationType={"fade"}
                    >
                        <View
                            style={{
                                flex: 1,
                                backgroundColor: Colors.modalBgColor,
                                alignItems: "center",
                                justifyContent: "center",
                            }}
                        >
                            <View
                                style={{
                                    height: Dimensions.get("screen").width * 0.08,
                                    width: Dimensions.get("screen").width * 0.18,
                                    backgroundColor: Colors.whiteColor,
                                    borderRadius: 12,
                                    padding: Dimensions.get("screen").width * 0.03,
                                    alignItems: "center",
                                    justifyContent: "center",
                                }}
                            >
                                <TouchableOpacity
                                    disabled={isLoading}
                                    style={{
                                        position: "absolute",
                                        right: Dimensions.get("screen").width * 0.015,
                                        top: Dimensions.get("screen").width * 0.01,

                                        elevation: 1000,
                                        zIndex: 1000,
                                    }}
                                    onPress={() => {
                                        setExportModalVisibility(false);
                                    }}
                                >
                                    <AntDesign
                                        name="closecircle"
                                        size={switchMerchant ? 15 : 25}
                                        color={Colors.fieldtTxtColor}
                                    />
                                </TouchableOpacity>
                                <View
                                    style={{
                                        alignItems: "center",
                                        top: "20%",
                                        position: "absolute",
                                    }}
                                >
                                    <Text
                                        style={{
                                            fontFamily: "NunitoSans-Bold",
                                            textAlign: "center",
                                            fontSize: switchMerchant ? 16 : 24,
                                        }}
                                    >
                                        Download Report
                                    </Text>
                                </View>
                                <View style={{ top: switchMerchant ? "14%" : "10%" }}>
                                    {/* <Text
                                        style={{
                                            fontSize: switchMerchant ? 10 : 20,
                                            fontFamily: 'NunitoSans-Bold',
                                        }}>
                                        Email Address:
                                    </Text>
                                    <TextInput
                                        underlineColorAndroid={Colors.fieldtBgColor}
                                        style={{
                                            backgroundColor: Colors.fieldtBgColor,
                                            width: switchMerchant ? 240 : 370,
                                            height: switchMerchant ? 35 : 50,
                                            borderRadius: 5,
                                            padding: 5,
                                            marginVertical: 5,
                                            borderWidth: 1,
                                            borderColor: '#E5E5E5',
                                            paddingLeft: 10,
                                            fontSize: switchMerchant ? 10 : 14,
                                        }}
                                        autoCapitalize='none'
                                        placeholderStyle={{ padding: 5 }}
                                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                        placeholder="Enter your email"
                                        onChangeText={(text) => {
                                            setExportEmail(text);
                                        }}
                                        value={exportEmail}
                                    />
                                    <Text
                                        style={{
                                            fontSize: switchMerchant ? 10 : 20,
                                            fontFamily: 'NunitoSans-Bold',
                                            marginTop: 15,
                                        }}>
                                        Send As:
                                    </Text> */}

                                    <View
                                        style={{
                                            alignItems: "center",
                                            justifyContent: "center",
                                            flexDirection: "row",
                                            marginTop: 30,
                                        }}
                                    >
                                        <TouchableOpacity
                                            disabled={isLoading}
                                            style={{
                                                justifyContent: "center",
                                                flexDirection: "row",
                                                borderWidth: 1,
                                                borderColor: Colors.primaryColor,
                                                backgroundColor: "#4E9F7D",
                                                borderRadius: 5,
                                                width: switchMerchant ? 100 : 100,
                                                paddingHorizontal: 10,
                                                height: switchMerchant ? 35 : 40,
                                                alignItems: "center",
                                                shadowOffset: {
                                                    width: 0,
                                                    height: 2,
                                                },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 3.22,
                                                elevation: 1,
                                                zIndex: -1,
                                                marginRight: 15,
                                            }}
                                            onPress={() => {
                                                // if (exportEmail.length > 0) {
                                                //     CommonStore.update((s) => {
                                                //         s.isLoading = true;
                                                //     });

                                                //     setIsExcel(true);

                                                //     const excelData = convertDataToExcelFormat();

                                                //     generateEmailReport(
                                                //         EMAIL_REPORT_TYPE.EXCEL,
                                                //         excelData,
                                                //         'KooDoo Transaction Sales Report',
                                                //         'KooDoo Transaction Sales Report.xlsx',
                                                //         `/merchant/${merchantId}/reports/${uuidv4()}.xlsx`,
                                                //         exportEmail,
                                                //         'KooDoo Transaction Sales Report',
                                                //         'KooDoo Transaction Sales Report',
                                                //         () => {
                                                //             CommonStore.update((s) => {
                                                //                 s.isLoading = false;
                                                //             });

                                                //             setIsExcel(false);

                                                //             window.confirm(
                                                //                 'Success',
                                                //                 'Report will be sent to the email address shortly',
                                                //             );

                                                //             setExportModalVisibility(false);
                                                //         },
                                                //     );
                                                // } else {
                                                //     window.confirm('Info', 'Invalid email address');
                                                // }
                                                handleExportExcel();
                                            }}
                                        >
                                            {isLoading && isExcel ? (
                                                <ActivityIndicator
                                                    size={"small"}
                                                    color={Colors.whiteColor}
                                                />
                                            ) : (
                                                <Text
                                                    style={{
                                                        color: Colors.whiteColor,
                                                        //marginLeft: 5,
                                                        fontSize: switchMerchant ? 10 : 16,
                                                        fontFamily: "NunitoSans-Bold",
                                                    }}
                                                >
                                                    EXCEL
                                                </Text>
                                            )}
                                        </TouchableOpacity>

                                        {/* <TouchableOpacity
                                            disabled={isLoading}
                                            style={{
                                                justifyContent: 'center',
                                                flexDirection: 'row',
                                                borderWidth: 1,
                                                borderColor: Colors.primaryColor,
                                                backgroundColor: '#4E9F7D',
                                                borderRadius: 5,
                                                width: switchMerchant ? 100 : 100,
                                                paddingHorizontal: 10,
                                                height: switchMerchant ? 35 : 40,
                                                alignItems: 'center',
                                                shadowOffset: {
                                                    width: 0,
                                                    height: 2,
                                                },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 3.22,
                                                elevation: 1,
                                                zIndex: -1,
                                            }}
                                            onPress={() => {
                                                if (exportEmail.length > 0) {
                                                    CommonStore.update((s) => {
                                                        s.isLoading = true;
                                                    });

                                                    setIsCsv(true);

                                                    //const csvData = convertArrayToCSV(transactionTypeSales);
                                                    const csvData = convertDataToCSVFormat();

                                                    generateEmailReport(
                                                        EMAIL_REPORT_TYPE.CSV,
                                                        csvData,
                                                        'KooDoo Transaction Sales Report',
                                                        'KooDoo Transaction Sales Report.csv',
                                                        `/merchant/${merchantId}/reports/${uuidv4()}.csv`,
                                                        exportEmail,
                                                        'KooDoo Transaction Sales Report',
                                                        'KooDoo Transaction Sales Report',
                                                        () => {
                                                            CommonStore.update((s) => {
                                                                s.isLoading = false;
                                                            });

                                                            setIsCsv(false);

                                                            window.confirm(
                                                                'Success',
                                                                'Report will be sent to the email address shortly',
                                                            );

                                                            setExportModalVisibility(false);
                                                        },
                                                    );
                                                } else {
                                                   window.confirm('Info', 'Invalid email address');
                                                }
                                            }}>
                                            {isLoading && isCsv ? (
                                                <ActivityIndicator
                                                    size={'small'}
                                                    color={Colors.whiteColor}
                                                />
                                            ) : (
                                                <Text
                                                    style={{
                                                        color: Colors.whiteColor,
                                                        //marginLeft: 5,
                                                        fontSize: switchMerchant ? 10 : 16,
                                                        fontFamily: 'NunitoSans-Bold',
                                                    }}>
                                                    CSV
                                                </Text>
                                            )}
                                        </TouchableOpacity> */}
                                        <CSVLink
                                            style={{
                                                justifyContent: "center",
                                                flexDirection: "row",
                                                borderWidth: 1,
                                                textDecoration: "none",
                                                borderColor: Colors.primaryColor,
                                                backgroundColor: "#4E9F7D",
                                                borderRadius: 5,
                                                width: switchMerchant ? 100 : 100,
                                                paddingHorizontal: 10,
                                                height: switchMerchant ? 35 : 40,
                                                alignItems: "center",
                                                shadowOffset: {
                                                    width: 0,
                                                    height: 2,
                                                },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 3.22,
                                                elevation: 1,
                                                zIndex: -1,
                                            }}
                                            data={convertDataToCSVFormat()}
                                            filename="ReportUpsellingRevenue.csv"
                                        >
                                            <View
                                                style={{
                                                    width: "100%",
                                                    height: "100%",
                                                    alignContent: "center",
                                                    alignItems: "center",
                                                    alignSelf: "center",
                                                    justifyContent: "center",
                                                }}
                                            >
                                                <Text
                                                    style={{
                                                        color: Colors.whiteColor,
                                                        //marginLeft: 5,
                                                        fontSize: switchMerchant ? 10 : 16,
                                                        fontFamily: "NunitoSans-Bold",
                                                    }}
                                                >
                                                    CSV
                                                </Text>
                                            </View>
                                        </CSVLink>

                                        {/* <TouchableOpacity
                                            style={[styles.modalSaveButton, {
                                                zIndex: -1
                                            }]}
                                            onPress={() => { downloadPDF() }}>
                                            <Text style={[styles.modalDescText, { color: Colors.primaryColor }]}>PDF</Text>
                                        </TouchableOpacity> */}
                                    </View>
                                </View>
                            </View>
                        </View>
                    </Modal>

                    <DateTimePickerModal
                        isVisible={showDateTimePicker}
                        mode={'date'}
                        onConfirm={(text) => {
                            // setRev_date(moment(text).startOf('day'));
                            CommonStore.update(s => {
                                s.historyStartDate = moment(text).startOf('day');
                            });
                            setShowDateTimePicker(false);
                        }}
                        onCancel={() => {
                            setShowDateTimePicker(false);
                        }}
                        maximumDate={moment(historyEndDate).toDate()}
                        date={moment(historyStartDate).toDate()}
                    />

                    <DateTimePickerModal
                        isVisible={showDateTimePicker1}
                        mode={'date'}
                        onConfirm={(text) => {
                            // setRev_date1(moment(text).endOf('day'));
                            CommonStore.update(s => {
                                s.historyEndDate = moment(text).endOf('day');
                            });
                            setShowDateTimePicker1(false);
                        }}
                        onCancel={() => {
                            setShowDateTimePicker1(false);
                        }}
                        minimumDate={moment(historyStartDate).toDate()}
                        date={moment(historyEndDate).toDate()}
                    />

                    <DateTimePickerModal
                        isVisible={showDateTimePickerFilter}
                        mode={'date'}
                        onConfirm={(text) => {
                            setSelectedChartFilterQueries([
                                {
                                    ...selectedChartFilterQueries[0],
                                    fieldDataValue: text,
                                },
                            ]);

                            setShowDateTimePickerFilter(false);
                        }}
                        onCancel={() => {
                            setShowDateTimePickerFilter(false);
                        }}
                    />

                    <Modal
                        supportedOrientations={['landscape', 'portrait']}
                        style={{ flex: 1 }}
                        visible={visible}
                        transparent
                        animationType="slide">
                        <KeyboardAvoidingView
                            behavior="padding"
                            style={{
                                backgroundColor: 'rgba(0,0,0,0.5)',
                                flex: 1,
                                justifyContent: 'center',
                                alignItems: 'center',
                                minHeight: windowHeight,
                            }}>
                            <View style={styles.confirmBox}>
                                <Text
                                    style={{
                                        fontSize: 24,
                                        justifyContent: 'center',
                                        alignSelf: 'center',
                                        marginTop: 40,
                                        fontFamily: 'NunitoSans-Bold',
                                    }}>
                                    Enter your email
                                </Text>
                                <View
                                    style={{
                                        justifyContent: 'center',
                                        alignSelf: 'center',
                                        alignContent: 'center',
                                        marginTop: 20,
                                        flexDirection: 'row',
                                        width: '80%',
                                    }}>
                                    <View style={{ justifyContent: 'center', marginHorizontal: 5 }}>
                                        <Text
                                            style={{ color: Colors.descriptionColor, fontSize: 20 }}>
                                            email:
                                        </Text>
                                    </View>
                                    <TextInput
                                        underlineColorAndroid={Colors.fieldtBgColor}
                                        style={[styles.textInput8, { paddingLeft: 5 }]}
                                        placeholder="Enter your email"
                                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                        // style={{
                                        //     // paddingLeft: 1,
                                        // }}
                                        //defaultValue={extentionCharges}
                                        onChangeText={(text) => {
                                            // setState({ exportEmail: text });
                                            setExportEmail(text);
                                        }}
                                        value={exportEmail}
                                    />
                                </View>
                                <Text
                                    style={{
                                        fontSize: 20,
                                        fontFamily: 'NunitoSans-Bold',
                                        marginTop: 25,
                                        justifyContent: 'center',
                                        alignSelf: 'center',
                                        alignContent: 'center',
                                    }}>
                                    Share As:
                                </Text>

                                {/* Share file using email */}
                                <View
                                    style={{
                                        justifyContent: 'space-between',
                                        alignSelf: 'center',
                                        marginTop: 10,
                                        flexDirection: 'row',
                                        width: '80%',
                                    }}>
                                    <TouchableOpacity
                                        style={[
                                            styles.modalSaveButton1,
                                            {
                                                zIndex: -1,
                                            },
                                        ]}
                                        onPress={() => { }}>
                                        <Text
                                            style={[
                                                styles.modalDescText,
                                                { color: Colors.primaryColor },
                                            ]}>
                                            Excel
                                        </Text>
                                    </TouchableOpacity>

                                    <TouchableOpacity
                                        style={[
                                            styles.modalSaveButton1,
                                            {
                                                zIndex: -1,
                                            },
                                        ]}
                                        onPress={() => { }}>
                                        <Text
                                            style={[
                                                styles.modalDescText,
                                                { color: Colors.primaryColor },
                                            ]}>
                                            CSV
                                        </Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        style={[
                                            styles.modalSaveButton1,
                                            {
                                                zIndex: -1,
                                            },
                                        ]}
                                        onPress={() => { }}>
                                        <Text
                                            style={[
                                                styles.modalDescText,
                                                { color: Colors.primaryColor },
                                            ]}>
                                            PDF
                                        </Text>
                                    </TouchableOpacity>
                                </View>

                                <View
                                    style={{
                                        alignSelf: 'center',
                                        marginTop: 20,
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        // width: 260,
                                        width: windowWidth * 0.2,
                                        height: 60,
                                        alignContent: 'center',
                                        flexDirection: 'row',
                                        marginTop: 40,
                                    }}>
                                    <TouchableOpacity
                                        onPress={emailProductSales}
                                        style={{
                                            backgroundColor: Colors.fieldtBgColor,
                                            width: '100%',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            alignContent: 'center',
                                            height: 60,
                                            borderBottomLeftRadius: 10,
                                            borderRightWidth: StyleSheet.hairlineWidth,
                                            borderTopWidth: StyleSheet.hairlineWidth,
                                        }}>
                                        <Text
                                            style={{
                                                fontSize: 22,
                                                color: Colors.primaryColor,
                                                fontFamily: 'NunitoSans-SemiBold',
                                            }}>
                                            Email
                                        </Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        onPress={() => {
                                            // setState({ visible: false });
                                            setVisible(false);
                                        }}
                                        style={{
                                            backgroundColor: Colors.fieldtBgColor,
                                            width: '100%',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            alignContent: 'center',
                                            height: 60,
                                            borderBottomRightRadius: 10,
                                            borderTopWidth: StyleSheet.hairlineWidth,
                                        }}>
                                        <Text
                                            style={{
                                                fontSize: 22,
                                                color: Colors.descriptionColor,
                                                fontFamily: 'NunitoSans-SemiBold',
                                            }}>
                                            Cancel
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </KeyboardAvoidingView>
                    </Modal>

                    <View
                        style={[
                            styles.content,
                            {
                                padding: 20,
                                width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
                                backgroundColor: Colors.highlightColor,
                                flexShrink: 1,
                                flexGrow: 1,
                            },
                        ]}>
                        <ScrollView
                            showsVerticalScrollIndicator={false}
                            style={{ flex: 1 }}
                            contentContainerStyle={{
                                paddingBottom: windowHeight * 0.05,
                            }}>
                            <View style={{ zindex: 2 }}>
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        alignSelf: 'center',
                                        //backgroundColor: '#ffffff',
                                        justifyContent: 'space-between',
                                        //padding: 18,
                                        marginTop: 5,
                                        width: windowWidth * 0.87,
                                        paddingLeft: 1,
                                        zindex: 2,
                                    }}>
                                    <View>
                                        <Text
                                            style={{
                                                fontSize: switchMerchant ? 20 : 26,
                                                fontFamily: 'NunitoSans-Bold',
                                            }}>
                                            {/* Sales By {name} */}
                                            {`Upselling Revenue`}
                                        </Text>

                                        {/* <View
                        style={{
                          justifyContent: 'center',
                        }}>
                        <View
                          style={{ alignItems: 'center', flexDirection: 'row' }}>
                          <Text
                            style={{
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: switchMerchant ? 20 : 26,
                            }}>
                            {!showDetails
                              ? productSales.length
                              : productSalesDetails.length}{' '}
                            Products
                          </Text>
                        </View>
                      </View> */}
                                    </View>

                                    <View
                                        style={{
                                            flexDirection: 'row', zindex: 2,
                                        }}>
                                        <View style={{ marginRight: 10, }}>
                                            <DropDownPicker
                                                style={{
                                                    backgroundColor: Colors.fieldtBgColor,
                                                    width: 210,
                                                    height: 40,
                                                    borderRadius: 10,
                                                    borderWidth: 1,
                                                    borderColor: "#E5E5E5",
                                                    flexDirection: "row",
                                                }}
                                                dropDownContainerStyle={{
                                                    width: 210,
                                                    backgroundColor: Colors.fieldtBgColor,
                                                    borderColor: "#E5E5E5",
                                                }}
                                                labelStyle={{
                                                    marginLeft: 5,
                                                    flexDirection: "row",
                                                }}
                                                textStyle={{
                                                    fontSize: 14,
                                                    fontFamily: 'NunitoSans-Regular',

                                                    marginLeft: 5,
                                                    paddingVertical: 10,
                                                    flexDirection: "row",
                                                }}
                                                selectedItemContainerStyle={{
                                                    flexDirection: "row",
                                                }}

                                                showArrowIcon={true}
                                                ArrowDownIconComponent={({ style }) => (
                                                    <Ionicon
                                                        size={25}
                                                        color={Colors.fieldtTxtColor}
                                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                                        name="chevron-down-outline"
                                                    />
                                                )}
                                                ArrowUpIconComponent={({ style }) => (
                                                    <Ionicon
                                                        size={25}
                                                        color={Colors.fieldtTxtColor}
                                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                                        name="chevron-up-outline"
                                                    />
                                                )}

                                                showTickIcon={true}
                                                TickIconComponent={({ press }) => (
                                                    <Ionicon
                                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                                        color={
                                                            press ? Colors.fieldtBgColor : Colors.primaryColor
                                                        }
                                                        name={'md-checkbox'}
                                                        size={25}
                                                    />
                                                )}

                                                placeholder="Choose Outlet"
                                                multipleText={`${selectedOutletList ? selectedOutletList.length : '0'} outlet(s) selected`}
                                                placeholderStyle={{
                                                    color: Colors.fieldtTxtColor,
                                                    // marginTop: 15,
                                                }}
                                                // multipleText={'%d outlet(s) selected'}
                                                items={outletDropdownList}
                                                value={selectedOutletList}
                                                multiple={true}
                                                open={openOS}
                                                setOpen={setOpenOS}
                                                onSelectItem={(items) => {
                                                    // setSelectedOutletList(items.map(item => item.value))
                                                    CommonStore.update((s) => {
                                                        s.reportOutletIdList = items.map(item => item.value)
                                                    })
                                                }}
                                                dropDownDirection="BOTTOM"
                                            />
                                        </View>
                                        {/* hide first 9/5/2025 */}
                                        {/* <View
                                            style={[
                                                {
                                                    flexDirection: 'row',
                                                    alignItems: 'center',
                                                    borderRadius: 10,
                                                    marginRight: 10,
                                                    marginLeft: 10,
                                                    zIndex: 5,
                                                },
                                            ]}>
                                            <DropDownPicker
                                                style={{
                                                    backgroundColor: Colors.fieldtBgColor,
                                                    width: 210,
                                                    height: 40,
                                                    borderRadius: 10,
                                                    borderWidth: 1,
                                                    borderColor: "#E5E5E5",
                                                    flexDirection: "row",
                                                }}
                                                dropDownContainerStyle={{
                                                    width: 210,
                                                    backgroundColor: Colors.fieldtBgColor,
                                                    borderColor: "#E5E5E5",
                                                }}
                                                labelStyle={{
                                                    marginLeft: 5,
                                                    flexDirection: "row",
                                                }}
                                                textStyle={{
                                                    fontSize: 14,
                                                    fontFamily: 'NunitoSans-Regular',

                                                    marginLeft: 5,
                                                    paddingVertical: 10,
                                                    flexDirection: "row",
                                                }}
                                                selectedItemContainerStyle={{
                                                    flexDirection: "row",
                                                }}

                                                showArrowIcon={true}
                                                ArrowDownIconComponent={({ style }) => (
                                                    <Ionicon
                                                        size={25}
                                                        color={Colors.fieldtTxtColor}
                                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                                        name="chevron-down-outline"
                                                    />
                                                )}
                                                ArrowUpIconComponent={({ style }) => (
                                                    <Ionicon
                                                        size={25}
                                                        color={Colors.fieldtTxtColor}
                                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                                        name="chevron-up-outline"
                                                    />
                                                )}

                                                showTickIcon={true}
                                                TickIconComponent={({ press }) => (
                                                    <Ionicon
                                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                                        color={
                                                            press ? Colors.fieldtBgColor : Colors.primaryColor
                                                        }
                                                        name={'md-checkbox'}
                                                        size={25}
                                                    />
                                                )}
                                                placeholder={'Select a Type'}
                                                placeholderStyle={{
                                                    color: Colors.fieldtTxtColor,
                                                    // marginTop: 15,
                                                }}
                                                // searchable
                                                // searchableStyle={{
                                                //   paddingHorizontal: windowWidth * 0.0079,
                                                // }}
                                                value={filterAppType}
                                                items={[
                                                    // { label: "All", value: "ALL" },
                                                    { label: "Merchant App Order", value: APP_TYPE.MERCHANT },
                                                    { label: "Waiter App Order", value: APP_TYPE.WAITER },
                                                    { label: "User App Order", value: APP_TYPE.USER },
                                                    { label: "QR Order", value: APP_TYPE.WEB_ORDER },
                                                ]}
                                                multiple={true}
                                                multipleText={`${filterAppType.length} App Type(s)`}
                                                onSelectItem={(items) => {
                                                    setFilterAppType(items.map(item => item.value))
                                                }}
                                                open={openFA}
                                                setOpen={setOpenFA}
                                                dropDownDirection="BOTTOM"
                                            />
                                        </View> */}

                                        <TouchableOpacity
                                            style={{
                                                justifyContent: 'center',
                                                flexDirection: 'row',
                                                borderWidth: 1,
                                                borderColor: Colors.primaryColor,
                                                backgroundColor: '#4E9F7D',
                                                borderRadius: 5,
                                                //width: 140,
                                                paddingHorizontal: 10,
                                                height: switchMerchant ? 35 : 40,
                                                alignItems: 'center',
                                                shadowOffset: {
                                                    width: 0,
                                                    height: 2,
                                                },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 3.22,
                                                elevation: 1,
                                                zIndex: -1,
                                                marginRight: 10,
                                            }}
                                            onPress={() => {
                                                setExportModalVisibility(true);
                                            }}>
                                            <View
                                                style={{ flexDirection: 'row', alignItems: 'center' }}>
                                                <Icon
                                                    name="download"
                                                    size={switchMerchant ? 10 : 20}
                                                    color={Colors.whiteColor}
                                                />
                                                <Text
                                                    style={{
                                                        color: Colors.whiteColor,
                                                        marginLeft: 5,
                                                        fontSize: switchMerchant ? 10 : 16,
                                                        fontFamily: 'NunitoSans-Bold',
                                                    }}>
                                                    DOWNLOAD
                                                </Text>
                                            </View>
                                        </TouchableOpacity>

                                        {/* <View
                                      style={[{
                                          // flex: 1,
                                          // alignContent: 'flex-end',
                                          // marginBottom: 10,
                                          // flexDirection: 'row',
                                          // marginRight: '-40%',
                                          // marginLeft: 310,
                                          // backgroundColor: 'red',
                                          // alignItems: 'flex-end',
                                          // right: '-50%',
                                          // width: '23%',
                                          height: 40,
                                          //marginTop: 10,
  
                                      }, !isTablet() ? {
                                          marginLeft: 0,
                                      } : {}]}> */}

                                        <View
                                            style={{
                                                width: switchMerchant ? 200 : 250,
                                                height: switchMerchant ? 35 : 40,
                                                backgroundColor: 'white',
                                                borderRadius: 5,
                                                flexDirection: 'row',
                                                alignContent: 'center',
                                                alignItems: 'center',
                                                //marginRight: windowWidth * Styles.sideBarWidth,

                                                //position: 'absolute',
                                                //right: '17%',

                                                shadowColor: '#000',
                                                shadowOffset: {
                                                    width: 0,
                                                    height: 2,
                                                },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 3.22,
                                                elevation: 3,
                                                borderWidth: 1,
                                                borderColor: '#E5E5E5',
                                            }}>
                                            <Icon
                                                name="search"
                                                size={switchMerchant ? 13 : 18}
                                                color={Colors.primaryColor}
                                                style={{ marginLeft: 15 }}
                                            />
                                            <TextInput
                                                editable={!loading}
                                                underlineColorAndroid={Colors.whiteColor}
                                                style={{
                                                    width: switchMerchant ? 180 : 250,
                                                    fontSize: switchMerchant ? 10 : 15,
                                                    fontFamily: 'NunitoSans-Regular',
                                                    paddingLeft: 5,
                                                    height: 45,
                                                }}
                                                clearButtonMode="while-editing"
                                                placeholder=" Search"
                                                onChangeText={(text) => {
                                                    setSearch(text);
                                                    // setList1(false);
                                                    // setSearchList(true);
                                                }}
                                                placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                                value={search}
                                            />
                                        </View>

                                        {/* </View> */}
                                    </View>
                                </View>
                                {/* <Text style={{ fontSize: 16, marginTop: 10, color: Colors.descriptionColor }}>Date last updated on 20 OCT 2020, 1:00PM</Text> */}
                                {/* <Text style={{ fontSize: 16, marginTop: 10, color: Colors.descriptionColor }}>Date last updated on {moment().format('LLLL')}</Text> */}
                            </View>

                            <View
                                style={{
                                    flexDirection: 'row',
                                    marginTop: 5,
                                    marginHorizontal: 1,
                                    zIndex: -1,
                                    justifyContent: 'space-between',
                                }}>
                                <View style={{ flexDirection: 'row' }}>
                                </View>

                                <View style={{ flexDirection: 'row', zIndex: -1 }}>
                                    {/* <TouchableOpacity style={{
                                  justifyContent: 'center',
                                  width: 100,
                                  marginHorizontal: 20,
                                  marginLeft: 0,
                              }}
                                  onPress={() => {
                                      groupByPressed(1); setGroupByTapped(1); setExpandGroupBy(!expandGroupBy);
                                  }}>
                                  <View style={{
                                      justifyContent: 'center',
                                      flexDirection: 'row',
                                      borderWidth: 0,
                                      borderColor: '#4E9F7D',
                                      borderRadius: 8,
                                      alignItems: 'center',
                                      backgroundColor: '#FFFFFF',
                                      position: 'relative',
                                      paddingHorizontal: 15, 
                                      backgroundColor: Colors.whiteColor, height: 40,
                                      width: 120,
                                      alignItems: 'center', borderRadius: 7, flexDirection: 'row', justifyContent: 'center',
                                      shadowColor: '#000',
                                      shadowOffset: {
                                          width: 0,
                                          height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      elevation: 1,
                                  }}>
                                      <Feather name='filter' size={18} color={Colors.primaryColor} />
                                      <Text style={{
                                          color: '#27353C',
                                          fontFamily: 'Nunitosans-Bold',
                                          fontSize: 13,
                                          marginLeft: Platform.OS === 'ios' ? 7 : 7
                                      }}>
                                          Group By
                                      </Text>
                                  </View>
                              </TouchableOpacity> */}

                                    {/* <TouchableOpacity style={{
                                  justifyContent: 'center',
                                  width: 100,
                                  marginHorizontal: 10,
                              }}
                                  onPress={() => {
                                      filterPressed(1); setFilterTapped(1); setExpandSelection(!expandSelection);
                                  }}>
                                  <View style={{
                                      justifyContent: 'center',
                                      flexDirection: 'row',
                                      borderWidth: 0,
                                      borderColor: '#4E9F7D',
                                      borderRadius: 8,
                                      alignItems: 'center',
                                      backgroundColor: '#FFFFFF',
  
                                      position: 'relative',
  
                                      paddingHorizontal: 15, backgroundColor: Colors.whiteColor, height: 40,
                                      width: 100,
                                      alignItems: 'center', borderRadius: 7, flexDirection: 'row', justifyContent: 'center',
                                      shadowColor: '#000',
                                      shadowOffset: {
                                          width: 0,
                                          height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      elevation: 1,
                                  }}>
                                      <Feather name='filter' size={18} color={Colors.primaryColor} />
                                      <Text style={{
                                          color: '#27353C',
                                          fontFamily: 'Nunitosans-Bold',
                                          fontSize: 13,
                                          marginLeft: 7
                                      }}>
                                          Filter
                                      </Text>
                                  </View>
                              </TouchableOpacity> */}

                                    {/* <View style={[{
                                  marginRight: 10,
                                  marginLeft: 10,
                                  // paddingLeft: 15,
                                  width: 230, flexDirection: 'row',
                                  alignItems: 'center', borderRadius: 10, height: 40, justifyContent: 'center',
                                  backgroundColor: Colors.whiteColor,
                                  shadowOpacity: 0,
                                  shadowColor: '#000',
                                  shadowOffset: {
                                      width: 0,
                                      height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                              }]}
                              >
                                  <View style={{ alignSelf: "center" }} onPress={() => { setState({ pickerMode: 'date', showDateTimePicker: true }) }}>
                                      <EvilIcons name="calendar" size={25} color={Colors.primaryColor} />
                                  </View>
                                  <TouchableOpacity onPress={() => {
                                      setShowDateTimePicker(true);
                                      setShowDateTimePicker1(false);
                                  }} style={{
                                      marginHorizontal: 4,
                                  }}>
                                      <Text style={{ color: '#27353C', fontFamily: 'Nunitosans-Bold', fontSize: 13, }}>{moment(rev_date).format("DD MMM yyyy")}</Text>
                                  </TouchableOpacity>
  
                                  <Text style={{ color: '#27353C', fontFamily: 'Nunitosans-Bold', }}>-</Text>
  
                                  <TouchableOpacity onPress={() => {
                                      setShowDateTimePicker(false);
                                      setShowDateTimePicker1(true);
                                  }} style={{
                                      marginHorizontal: 4,
                                  }}>
                                      <Text style={{ color: '#27353C', fontFamily: 'Nunitosans-Bold', fontSize: 13, }}>{moment(rev_date1).format("DD MMM yyyy")}</Text>
                                  </TouchableOpacity>
  
                              </View> */}

                                    {/* <TouchableOpacity
                                  style={{
                                      paddingHorizontal: 15, backgroundColor: Colors.whiteColor, height: 40, width: 120, alignItems: 'center', borderRadius: 7, flexDirection: 'row', justifyContent: 'center', shadowColor: '#000',
                                      shadowOffset: {
                                          width: 0,
                                          height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      elevation: 1,
                                  }}
                                  onPress={() => {
                                      // setState({
                                      //     visible: true
                                      // })
                                      setVisible(true);
                                  }}
                              >
                                  <Upload width={15} height={15} />
                                  <Text style={{ color: '#27353C', fontFamily: 'Nunitosans-Bold', fontSize: 13, marginLeft: 12 }}>Email</Text>
                              </TouchableOpacity> */}

                                    {/* {
                                  ((currPageStack.length > 1 && currPageStack[currPageStack.length - 2] === 'Dashboard') || currPageStack[currPageStack.length - 1] === 'Dashboard')
                                      ?
                                      <TouchableOpacity
                                          style={{
                                              marginLeft: 10,
                                              paddingHorizontal: 10, backgroundColor: Colors.whiteColor, height: 40, width: 140, alignItems: 'center', borderRadius: 7, flexDirection: 'row', justifyContent: 'center', shadowColor: '#000',
                                              shadowOffset: {
                                                  width: 0,
                                                  height: 2,
                                              },
                                              shadowOpacity: 0.22,
                                              shadowRadius: 3.22,
                                              elevation: 1,
                                          }}
                                          onPress={() => {
                                              navigation.navigate('Dashboard');
                                          }}
                                      >
                                          <Text style={{ fontFamily: "NunitoSans-Regular", fontSize: 13, }}>Dashboard</Text>
                                      </TouchableOpacity>
                                      :
                                      <></>
                              } */}
                                </View>
                            </View>

                            {/* 2024-01-23 - place to insert fusion chart related code */}

                            {/* {showDetails ? ( */}
                            <View style={{ width: windowWidth * 0.87, alignSelf: 'center', justifyContent: 'space-between', flexDirection: 'row', marginTop: 10, }}>

                                <TouchableOpacity
                                    style={{
                                        // marginLeft: switchMerchant
                                        //     ? 0
                                        //     : windowWidth <= 1823 && windowWidth >= 1820
                                        //         ? 24
                                        //         : 10,
                                        justifyContent: 'center',
                                        flexDirection: 'row',
                                        borderWidth: 1,
                                        borderColor: Colors.primaryColor,
                                        backgroundColor: '#4E9F7D',
                                        borderRadius: 5,
                                        width: switchMerchant ? 90 : 120,
                                        paddingHorizontal: 10,
                                        height: switchMerchant ? 35 : 40,
                                        alignItems: 'center',
                                        shadowOffset: {
                                            width: 0,
                                            height: 2,
                                        },
                                        shadowOpacity: 0.22,
                                        shadowRadius: 3.22,
                                        elevation: 1,
                                        zIndex: -1,
                                        opacity: !showDetails ? 0 : 100,
                                    }}
                                    onPress={() => {
                                        setShowDetails(false);
                                        setCurrentPage(pageReturn);
                                        // console.log('Returning to page');
                                        // console.log(pageReturn);
                                        setPageCount(Math.ceil(productSalesUpselling.length / perPage));
                                        setCurrReportSummarySort('');
                                        setCurrReportDetailsSort('');
                                    }}
                                    disabled={!showDetails}>
                                    <AntDesign
                                        name="arrowleft"
                                        size={switchMerchant ? 10 : 20}
                                        color={Colors.whiteColor}
                                        style={
                                            {
                                                // top: -1,
                                                //marginRight: -5,
                                            }
                                        }
                                    />
                                    <Text
                                        style={{
                                            color: Colors.whiteColor,
                                            marginLeft: 5,
                                            fontSize: switchMerchant ? 10 : 16,
                                            fontFamily: 'NunitoSans-Bold',
                                            marginBottom: Platform.OS === 'ios' ? 0 : 2,
                                        }}>
                                        Summary
                                    </Text>
                                </TouchableOpacity>

                                <View
                                    style={{
                                        flexDirection: 'row',
                                        // marginLeft: switchMerchant ? 10 : 20,
                                        //...(pageStack.length > 1 && pageStack[pageStack.length - 2] === 'Dashboard') && {
                                        //    display: 'none',
                                        // },

                                        // marginRight: 10,
                                    }}>
                                    <View
                                        style={{
                                            paddingHorizontal: 15,
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                            borderRadius: 10,
                                            paddingVertical: 10,
                                            justifyContent: 'center',
                                            backgroundColor: Colors.whiteColor,
                                            shadowOpacity: 0,
                                            shadowColor: '#000',
                                            shadowOffset: {
                                                width: 0,
                                                height: 2,
                                            },
                                            shadowOpacity: 0.22,
                                            shadowRadius: 3.22,
                                            elevation: 1,
                                            zIndex: 1,
                                        }}>
                                        <View
                                            style={{ alignSelf: 'center', marginRight: 5 }}
                                            onPress={() => {
                                                setState({
                                                    pickerMode: 'date',
                                                    showDateTimePicker: true,
                                                });
                                            }}>
                                            <GCalendar
                                                width={switchMerchant ? 15 : 20}
                                                height={switchMerchant ? 15 : 20}
                                            />
                                        </View>

                                        <DatePicker
                                            selected={moment(historyStartDate).toDate()}
                                            onChange={(date) => {
                                                // setRev_date(moment(text).startOf('day'));
                                                CommonStore.update(s => {
                                                    s.historyStartDate = moment(date).startOf('day');
                                                });
                                                (!showDetails) ?
                                                    setCurrentPage(1) :
                                                    setCurrentDetailsPage(1);
                                            }}
                                            maxDate={moment(historyEndDate).toDate()}
                                        />

                                        <Text
                                            style={
                                                switchMerchant
                                                    ? { fontSize: 10, fontFamily: "NunitoSans-Regular", marginHorizontal: 6, marginLeft: 3 }
                                                    : { fontFamily: "NunitoSans-Regular", marginHorizontal: 6, marginLeft: 3 }
                                            }
                                        >
                                            -
                                        </Text>

                                        <DatePicker
                                            selected={moment(historyEndDate).toDate()}
                                            onChange={(date) => {
                                                // setRev_date1(moment(text).endOf('day'));
                                                CommonStore.update(s => {
                                                    s.historyEndDate = moment(date).endOf('day');
                                                });
                                                (!showDetails) ?
                                                    setCurrentPage(1) :
                                                    setCurrentDetailsPage(1);
                                            }}
                                            minDate={moment(historyStartDate).toDate()}
                                        />
                                    </View>
                                </View>
                            </View>
                            {/* ) : (
                                <></>
                            )} */}

                            <View style={{ width: '100%', marginTop: 20, zIndex: -1, }}>
                                {/* /////////////////////////////////////// */}

                                {/* 2024-01-24 - Showo summary tabs here */}

                                <View
                                    style={{
                                        flexDirection: 'row',
                                        justifyContent: 'space-between',
                                        width: windowWidth * 0.87,
                                        // marginLeft: '1%',
                                        alignSelf: 'center',

                                        // backgroundColor: 'red',
                                        zIndex: -2,

                                        marginBottom: 30,
                                    }}>
                                    <TouchableOpacity
                                        style={{
                                            width: '32%',
                                            height: switchMerchant ? 80 : 100,
                                            backgroundColor: Colors.tabMint,
                                            borderRadius: 10,
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                            justifyContent: 'space-between',
                                            paddingHorizontal: switchMerchant ? 10 : 30,
                                            paddingVertical: 15,
                                            shadowColor: '#000',
                                            shadowOffset: {
                                                width: 0,
                                                height: 2,
                                            },
                                            shadowOpacity: 0.22,
                                            shadowRadius: 3.22,
                                            elevation: 3,
                                        }}
                                        onPress={() => {
                                            // navigation.navigate('ReportSalesOvertime');
                                        }}>
                                        {/* <View style={{ flex: 1 }}>
                                    <View style={{ flex: 1, justifyContent: 'center' }}>
                                        <Text style={{ fontFamily: 'NunitoSans-Bold', color: Colors.whiteColor, fontSize: 20, }}>RM {totalSales}</Text>
                                    </View>
                                    <View style={{ flex: 0.8, }}>
                                        <Text style={{ fontFamily: 'NunitoSans-Regular', color: Colors.whiteColor, fontSize: 13, paddingTop: 10 }}>Total Sales</Text>
                                    </View>
                                </View> */}
                                        <View
                                            style={{
                                                marginTop: -15,
                                            }}>
                                            <Text
                                                style={{
                                                    fontFamily: 'NunitoSans-Bold',
                                                    color: Colors.whiteColor,
                                                    fontSize: switchMerchant ? 20 : 28,
                                                }}>
                                                RM {totalUpsellingRevenue.toFixed(2)}
                                            </Text>
                                            <Text
                                                style={{
                                                    fontFamily: 'NunitoSans-Regular',
                                                    color: Colors.whiteColor,
                                                    fontSize: switchMerchant ? 10 : 13,
                                                }}>
                                                Total Revenue
                                            </Text>
                                        </View>
                                        <View>
                                            <Coins
                                                height={switchMerchant ? 40 : 60}
                                                width={switchMerchant ? 40 : 60}
                                            />
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        style={{
                                            width: '32%',
                                            height: switchMerchant ? 80 : 100,
                                            backgroundColor: Colors.tabGold,
                                            borderRadius: 10,
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                            justifyContent: 'space-between',
                                            paddingHorizontal: switchMerchant ? 10 : 30,
                                            paddingVertical: 15,
                                            shadowColor: '#000',
                                            shadowOffset: {
                                                width: 0,
                                                height: 2,
                                            },
                                            shadowOpacity: 0.22,
                                            shadowRadius: 3.22,
                                            elevation: 3,
                                        }}
                                        onPress={() => {
                                            // navigation.navigate('ReportSalesTransaction');
                                            // navigation.navigate('AllTransaction');
                                        }}>
                                        <View style={{ marginTop: -15 }}>
                                            <Text
                                                style={{
                                                    fontFamily: 'NunitoSans-Bold',
                                                    color: Colors.whiteColor,
                                                    fontSize: switchMerchant ? 20 : 28,
                                                }}>
                                                {totalUpsellingItems.toFixed(0)}
                                            </Text>
                                            <Text
                                                style={{
                                                    fontFamily: 'NunitoSans-Regular',
                                                    color: Colors.whiteColor,
                                                    fontSize: switchMerchant ? 10 : 13,
                                                }}>
                                                {switchMerchant
                                                    ? 'Total\nItems'
                                                    : 'Total Items'}
                                            </Text>
                                        </View>
                                        <View>
                                            <Hand
                                                height={switchMerchant ? 40 : 60}
                                                width={switchMerchant ? 40 : 60}
                                            />
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        style={{
                                            width: '32%',
                                            height: switchMerchant ? 80 : 100,
                                            backgroundColor: Colors.tabCyan,
                                            borderRadius: 10,
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                            justifyContent: 'space-between',
                                            paddingHorizontal: switchMerchant ? 10 : 30,
                                            paddingVertical: 15,
                                            shadowColor: '#000',
                                            shadowOffset: {
                                                width: 0,
                                                height: 2,
                                            },
                                            shadowOpacity: 0.22,
                                            shadowRadius: 3.22,
                                            elevation: 3,
                                        }}
                                        onPress={() => {
                                            // navigation.navigate('ReportSalesProduct');
                                        }}>
                                        <View style={{ marginTop: -15 }}>
                                            {/* <Text style={{ fontFamily: 'NunitoSans-Bold', color: Colors.whiteColor, fontSize: 28 }}>{allDayTotalSold}</Text> */}
                                            <Text
                                                style={{
                                                    fontFamily: 'NunitoSans-Bold',
                                                    color: Colors.whiteColor,
                                                    fontSize: switchMerchant ? 20 : 28,
                                                }}>
                                                {`RM ${totalUpsellingDiscounts.toFixed(2)}`}
                                            </Text>
                                            <Text
                                                style={{
                                                    fontFamily: 'NunitoSans-Regular',
                                                    color: Colors.whiteColor,
                                                    fontSize: switchMerchant ? 10 : 13,
                                                }}>
                                                {switchMerchant
                                                    ? 'Total\nDiscounts'
                                                    : 'Total Discounts'}
                                            </Text>
                                        </View>
                                        <View>
                                            <Dish
                                                height={switchMerchant ? 40 : 60}
                                                width={switchMerchant ? 40 : 60}
                                            />
                                        </View>
                                    </TouchableOpacity>
                                </View>

                                {/* /////////////////////////////////////// */}

                                <View
                                    style={{
                                        backgroundColor: Colors.whiteColor,
                                        width: windowWidth * 0.87,
                                        height:
                                            Platform.OS == 'android'
                                                ? windowHeight * 0.6
                                                : windowHeight * 0.6,
                                        //marginTop: 5,
                                        marginHorizontal: 30,
                                        marginBottom: 10,
                                        alignSelf: 'center',
                                        borderRadius: 5,
                                        shadowOpacity: 0,
                                        shadowColor: '#000',
                                        shadowOffset: {
                                            width: 0,
                                            height: 2,
                                        },
                                        shadowOpacity: 0.22,
                                        shadowRadius: 3.22,
                                        elevation: 3,
                                    }}>
                                    {/* Left Button */}
                                    {/* <View style={{ flexDirection: "row"}}>
                                      <View style={{ flexDirection: 'row', zIndex: 1 }}>
                                          <TouchableOpacity
                                                      style={ {
                                                          marginRight: 10,
                                                          paddingHorizontal: 15,
                                                          backgroundColor: Colors.whiteColor,
                                                          height: 40,
                                                          width: 200, 
                                                          alignItems: 'center',
                                                          borderRadius: 7,
                                                          flexDirection: 'row',
                                                          justifyContent: 'center', shadowColor: '#000',
                                                          shadowOffset: {
                                                              width: 0,
                                                              height: 2,
                                                          },
                                                          shadowOpacity: 0.22,
                                                          shadowRadius: 3.22,
                                                          elevation: 1,
  
                                                          opacity: !showDetails ? 0 : 100,
                                                      }}
                                                      onPress={() => {
                                                          setShowDetails(false);
  
                                                          setCurrentPage(1);
                                                          setPageCount(Math.ceil(productSales.length / perPage));
                                                      }}
                                                      disabled={!showDetails}
                                                  >
                                                      <AntDesign name="arrowleft" size={18} color={Colors.primaryColor} style={{
                                                          top: 1,
                                                          marginRight: -5,
                                                      }} />
                                                      <Text style={{ fontFamily: "NunitoSans-Regular", fontSize: 13, marginLeft: 12 }}>Back to Summary</Text>
                                          </TouchableOpacity>
                                      </View>
                                  </View> */}

                                    {/* Right Button */}
                                    {showDetails ? (
                                        <View style={{ flexDirection: 'row' }}>
                                            <View
                                                style={{
                                                    flexDirection: 'row',
                                                    //justifyContent: 'flex-end',
                                                    // marginBottom: 5,
                                                    // marginTop: 5,
                                                    width: '100%',
                                                    width: windowWidth * 0.875,
                                                    alignSelf: 'flex-end',
                                                }}>
                                                <Text
                                                    style={{
                                                        marginLeft: 20,
                                                        marginTop: 10,
                                                        fontFamily: 'NunitoSans-Regular',
                                                        fontSize: switchMerchant ? 14 : 22,
                                                        alignSelf: 'center',
                                                    }}>
                                                    {detailsTitle}
                                                </Text>
                                            </View>
                                            {/* <Text style={{ fontFamily: "NunitoSans-Regular", fontSize: 13, marginLeft: 12 }}>{!showDetails ? productName : productName } </Text> */}
                                        </View>
                                    ) : null}
                                    {/* <View style={{ height: '88%', position: 'absolute', justifyContent: 'space-between', zIndex: 10, marginVertical: 0, marginTop: 80, alignSelf: 'center' }}>
                                  <TouchableOpacity
                                      onPress={() => {
                                          ScrollToTop();
                                      }}
                                      style={{ alignSelf: 'center', marginTop: '8%', zIndex: 10 }}>
                                      <AntDesign name={'upcircle'} size={23} color={Colors.primaryColor} style={{ opacity: 0.4 }} />
                                  </TouchableOpacity>
                                  <TouchableOpacity
                                      onPress={() => {
                                          ScrollToBottom();
                                      }}
                                      style={{ alignSelf: 'center', marginTop: '42%', zIndex: 10 }}>
                                      <AntDesign name={'downcircle'} size={23} color={Colors.primaryColor} style={{ opacity: 0.4 }} />
                                  </TouchableOpacity>
                              </View> */}

                                    {/* /////////////////////////////////////// */}

                                    {/* 2022-02-14 - Hide flatlist first */}

                                    {!showDetails ? (
                                        <View style={{ marginTop: 10, flexDirection: 'row' }}>
                                            <View
                                                style={{
                                                    flexDirection: 'row',
                                                    width: '5%',
                                                    borderRightWidth: 1,
                                                    borderRightColor: 'lightgrey',
                                                    alignItems: 'center',
                                                    justifyContent: 'flex-start',
                                                    paddingLeft: 10,
                                                }}>
                                                <View style={{ flexDirection: 'row' }}>
                                                    <View style={{ flexDirection: 'column' }}>
                                                        <Text
                                                            numberOfLines={2}
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 13,
                                                                fontFamily: 'NunitoSans-Bold',
                                                            }}>
                                                            {'No.\n'}
                                                        </Text>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 8 : 10,
                                                                color: Colors.descriptionColor,
                                                            }} />
                                                    </View>
                                                    <View style={{ marginLeft: '3%' }}>
                                                        <Entypo
                                                            name="triangle-up"
                                                            size={switchMerchant ? 7 : 14}
                                                            color="transparent" />

                                                        <Entypo
                                                            name="triangle-down"
                                                            size={switchMerchant ? 7 : 14}
                                                            color="transparent" />
                                                        <Text
                                                            style={{
                                                                fontSize: 10,
                                                                color: Colors.descriptionColor,
                                                            }} />
                                                    </View>

                                                    {/* <Text style={{ fontSize: 10, color: Colors.descriptionColor }}></Text> */}
                                                </View>
                                                {/* <View style={{ marginLeft: '3%' }}>
                                              <TouchableOpacity onPress={() => setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.DATE_TIME_ASC)}>
                                                  <Entypo name='triangle-up' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.DATE_TIME_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                              </TouchableOpacity>
  
                                              <TouchableOpacity onPress={() => setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.DATE_TIME_DESC)}>
                                                  <Entypo name='triangle-down' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.DATE_TIME_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                              </TouchableOpacity>
                                          </View> */}
                                            </View>
                                            <View
                                                style={{
                                                    flexDirection: 'row',
                                                    width: '10%',
                                                    borderRightWidth: 1,
                                                    borderRightColor: 'lightgrey',
                                                    alignItems: 'center',
                                                    justifyContent: 'flex-start',
                                                    paddingLeft: 10,
                                                }}>
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        if (
                                                            currReportSummarySort ===
                                                            REPORT_SORT_FIELD_TYPE.CAMPAIGN_NAME_ASC
                                                        ) {
                                                            setCurrReportSummarySort(
                                                                REPORT_SORT_FIELD_TYPE.CAMPAIGN_NAME_DESC,
                                                            );
                                                        } else {
                                                            setCurrReportSummarySort(
                                                                REPORT_SORT_FIELD_TYPE.CAMPAIGN_NAME_ASC,
                                                            );
                                                        }
                                                    }}>
                                                    <View style={{ flexDirection: 'row' }}>
                                                        <View style={{ flexDirection: 'column' }}>
                                                            <Text
                                                                numberOfLines={2}
                                                                style={{
                                                                    fontSize: switchMerchant ? 10 : 13,
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                }}>
                                                                {'Campaign\n'}
                                                            </Text>
                                                            <Text
                                                                style={{
                                                                    fontSize: switchMerchant ? 8 : 10,
                                                                    color: Colors.descriptionColor,
                                                                }} />
                                                        </View>
                                                        <View style={{ marginLeft: '3%' }}>
                                                            <Entypo
                                                                name="triangle-up"
                                                                size={switchMerchant ? 7 : 14}
                                                                color={
                                                                    currReportSummarySort ===
                                                                        REPORT_SORT_FIELD_TYPE.CAMPAIGN_NAME_ASC
                                                                        ? Colors.secondaryColor
                                                                        : Colors.descriptionColor
                                                                } />

                                                            <Entypo
                                                                name="triangle-down"
                                                                size={switchMerchant ? 7 : 14}
                                                                color={
                                                                    currReportSummarySort ===
                                                                        REPORT_SORT_FIELD_TYPE.CAMPAIGN_NAME_DESC
                                                                        ? Colors.secondaryColor
                                                                        : Colors.descriptionColor
                                                                } />
                                                            <Text
                                                                style={{
                                                                    fontSize: 10,
                                                                    color: Colors.descriptionColor,
                                                                }} />
                                                        </View>
                                                    </View>
                                                </TouchableOpacity>
                                            </View>
                                            <View
                                                style={{
                                                    flexDirection: 'row',
                                                    width: '19%',
                                                    borderRightWidth: 1,
                                                    borderRightColor: 'lightgrey',
                                                    alignItems: 'center',
                                                    justifyContent: 'flex-start',
                                                    paddingLeft: 10,
                                                }}>
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        if (
                                                            currReportSummarySort ===
                                                            REPORT_SORT_FIELD_TYPE.PRODUCT_NAME_ASC
                                                        ) {
                                                            setCurrReportSummarySort(
                                                                REPORT_SORT_FIELD_TYPE.PRODUCT_NAME_DESC,
                                                            );
                                                        } else {
                                                            setCurrReportSummarySort(
                                                                REPORT_SORT_FIELD_TYPE.PRODUCT_NAME_ASC,
                                                            );
                                                        }
                                                    }}>
                                                    <View style={{ flexDirection: 'row' }}>
                                                        <View style={{ flexDirection: 'column' }}>
                                                            <Text
                                                                numberOfLines={2}
                                                                style={{
                                                                    fontSize: switchMerchant ? 10 : 13,
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                }}>
                                                                {'Product\n'}
                                                            </Text>
                                                            <Text
                                                                style={{
                                                                    fontSize: switchMerchant ? 8 : 10,
                                                                    color: Colors.descriptionColor,
                                                                }} />
                                                        </View>
                                                        <View style={{ marginLeft: '3%' }}>
                                                            <Entypo
                                                                name="triangle-up"
                                                                size={switchMerchant ? 7 : 14}
                                                                color={
                                                                    currReportSummarySort ===
                                                                        REPORT_SORT_FIELD_TYPE.PRODUCT_NAME_ASC
                                                                        ? Colors.secondaryColor
                                                                        : Colors.descriptionColor
                                                                } />

                                                            <Entypo
                                                                name="triangle-down"
                                                                size={switchMerchant ? 7 : 14}
                                                                color={
                                                                    currReportSummarySort ===
                                                                        REPORT_SORT_FIELD_TYPE.PRODUCT_NAME_DESC
                                                                        ? Colors.secondaryColor
                                                                        : Colors.descriptionColor
                                                                } />
                                                            <Text
                                                                style={{
                                                                    fontSize: 10,
                                                                    color: Colors.descriptionColor,
                                                                }} />
                                                        </View>
                                                    </View>
                                                </TouchableOpacity>
                                            </View>
                                            <View
                                                style={{
                                                    flexDirection: 'row',
                                                    width: '11%',
                                                    borderRightWidth: 1,
                                                    borderRightColor: 'lightgrey',
                                                    alignItems: 'center',
                                                    justifyContent: 'flex-start',
                                                    paddingLeft: 10,
                                                }}>
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        if (
                                                            currReportSummarySort ===
                                                            REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_ASC
                                                        ) {
                                                            setCurrReportSummarySort(
                                                                REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_DESC,
                                                            );
                                                        } else {
                                                            setCurrReportSummarySort(
                                                                REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_ASC,
                                                            );
                                                        }
                                                    }}>
                                                    <View style={{ flexDirection: 'row' }}>
                                                        <View style={{ flexDirection: 'column' }}>
                                                            <Text
                                                                numberOfLines={2}
                                                                style={{
                                                                    fontSize: switchMerchant ? 10 : 13,
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                }}>
                                                                {'Category\n'}
                                                            </Text>
                                                            <Text
                                                                style={{
                                                                    fontSize: switchMerchant ? 8 : 10,
                                                                    color: Colors.descriptionColor,
                                                                }} />
                                                        </View>
                                                        <View style={{ marginLeft: '3%' }}>
                                                            <Entypo
                                                                name="triangle-up"
                                                                size={switchMerchant ? 7 : 14}
                                                                color={
                                                                    currReportSummarySort ===
                                                                        REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_ASC
                                                                        ? Colors.secondaryColor
                                                                        : Colors.descriptionColor
                                                                } />

                                                            <Entypo
                                                                name="triangle-down"
                                                                size={switchMerchant ? 7 : 14}
                                                                color={
                                                                    currReportSummarySort ===
                                                                        REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_DESC
                                                                        ? Colors.secondaryColor
                                                                        : Colors.descriptionColor
                                                                } />
                                                            <Text
                                                                style={{
                                                                    fontSize: 10,
                                                                    color: Colors.descriptionColor,
                                                                }} />
                                                        </View>
                                                    </View>
                                                </TouchableOpacity>
                                            </View>

                                            <View
                                                style={{
                                                    flexDirection: 'row',
                                                    width: '9%',
                                                    borderRightWidth: 1,
                                                    borderRightColor: 'lightgrey',
                                                    alignItems: 'center',
                                                    justifyContent: 'flex-start',
                                                    paddingLeft: 10,
                                                }}>
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        if (
                                                            currReportSummarySort ===
                                                            REPORT_SORT_FIELD_TYPE.UPSELLING_TYPE_ASC
                                                        ) {
                                                            setCurrReportSummarySort(
                                                                REPORT_SORT_FIELD_TYPE.UPSELLING_TYPE_DESC,
                                                            );
                                                        } else {
                                                            setCurrReportSummarySort(
                                                                REPORT_SORT_FIELD_TYPE.UPSELLING_TYPE_ASC,
                                                            );
                                                        }
                                                    }}>
                                                    <View style={{ flexDirection: 'row' }}>
                                                        <View style={{ flexDirection: 'column' }}>
                                                            <Text
                                                                numberOfLines={2}
                                                                style={{
                                                                    fontSize: switchMerchant ? 10 : 13,
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                }}>
                                                                {'Type\n'}
                                                            </Text>
                                                            <Text
                                                                style={{
                                                                    fontSize: switchMerchant ? 8 : 10,
                                                                    color: Colors.descriptionColor,
                                                                }} />
                                                        </View>
                                                        <View style={{ marginLeft: '3%' }}>
                                                            <Entypo
                                                                name="triangle-up"
                                                                size={switchMerchant ? 7 : 14}
                                                                color={
                                                                    currReportSummarySort ===
                                                                        REPORT_SORT_FIELD_TYPE.UPSELLING_TYPE_ASC
                                                                        ? Colors.secondaryColor
                                                                        : Colors.descriptionColor
                                                                } />

                                                            <Entypo
                                                                name="triangle-down"
                                                                size={switchMerchant ? 7 : 14}
                                                                color={
                                                                    currReportSummarySort ===
                                                                        REPORT_SORT_FIELD_TYPE.UPSELLING_TYPE_DESC
                                                                        ? Colors.secondaryColor
                                                                        : Colors.descriptionColor
                                                                } />
                                                            <Text
                                                                style={{
                                                                    fontSize: 10,
                                                                    color: Colors.descriptionColor,
                                                                }} />
                                                        </View>
                                                    </View>
                                                </TouchableOpacity>
                                            </View>
                                            <View
                                                style={{
                                                    flexDirection: 'row',
                                                    width: '9%',
                                                    borderRightWidth: 1,
                                                    borderRightColor: 'lightgrey',
                                                    alignItems: 'center',
                                                    justifyContent: 'flex-start',
                                                    paddingLeft: 10,
                                                }}>
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        if (
                                                            currReportSummarySort ===
                                                            REPORT_SORT_FIELD_TYPE.UPSELLING_REVENUE_ASC
                                                        ) {
                                                            setCurrReportSummarySort(
                                                                REPORT_SORT_FIELD_TYPE.UPSELLING_REVENUE_DESC,
                                                            );
                                                        } else {
                                                            setCurrReportSummarySort(
                                                                REPORT_SORT_FIELD_TYPE.UPSELLING_REVENUE_ASC,
                                                            );
                                                        }
                                                    }}>
                                                    <View style={{ flexDirection: 'row' }}>
                                                        <View style={{ flexDirection: 'column' }}>
                                                            {/* <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold', textAlign: 'center' }}>{'Total\nItems'}</Text> */}
                                                            <Text
                                                                numberOfLines={2}
                                                                style={{
                                                                    fontSize: switchMerchant ? 10 : 13,
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                }}>
                                                                {'Upselling\nRevenue'}
                                                            </Text>
                                                            <Text
                                                                style={{
                                                                    fontSize: switchMerchant ? 8 : 10,
                                                                    color: Colors.descriptionColor,
                                                                }}>
                                                                RM
                                                            </Text>
                                                            {/* <Text style={{ fontSize: 10, color: Colors.descriptionColor }}></Text> */}
                                                        </View>
                                                        <View style={{ marginLeft: '3%' }}>
                                                            <Entypo
                                                                name="triangle-up"
                                                                size={switchMerchant ? 7 : 14}
                                                                color={
                                                                    currReportSummarySort ===
                                                                        REPORT_SORT_FIELD_TYPE.UPSELLING_REVENUE_ASC
                                                                        ? Colors.secondaryColor
                                                                        : Colors.descriptionColor
                                                                } />

                                                            <Entypo
                                                                name="triangle-down"
                                                                size={switchMerchant ? 7 : 14}
                                                                color={
                                                                    currReportSummarySort ===
                                                                        REPORT_SORT_FIELD_TYPE.UPSELLING_REVENUE_DESC
                                                                        ? Colors.secondaryColor
                                                                        : Colors.descriptionColor
                                                                } />
                                                            <Text
                                                                style={{
                                                                    fontSize: 10,
                                                                    color: Colors.descriptionColor,
                                                                }} />
                                                        </View>
                                                    </View>
                                                </TouchableOpacity>
                                            </View>
                                            <View
                                                style={{
                                                    flexDirection: 'row',
                                                    width: '9%',
                                                    borderRightWidth: 1,
                                                    borderRightColor: 'lightgrey',
                                                    alignItems: 'center',
                                                    justifyContent: 'flex-start',
                                                    paddingLeft: 10,
                                                }}>
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        if (
                                                            currReportSummarySort ===
                                                            REPORT_SORT_FIELD_TYPE.TOTAL_ITEMS_ASC
                                                        ) {
                                                            setCurrReportSummarySort(
                                                                REPORT_SORT_FIELD_TYPE.TOTAL_ITEMS_DESC,
                                                            );
                                                        } else {
                                                            setCurrReportSummarySort(
                                                                REPORT_SORT_FIELD_TYPE.TOTAL_ITEMS_ASC,
                                                            );
                                                        }
                                                    }}>
                                                    <View style={{ flexDirection: 'row' }}>
                                                        <View style={{ flexDirection: 'column' }}>
                                                            {/* <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold', textAlign: 'center' }}>{'Total\nSales'}</Text> */}
                                                            <Text
                                                                numberOfLines={2}
                                                                style={{
                                                                    fontSize: switchMerchant ? 10 : 13,
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                }}>
                                                                {'Items\n'}
                                                            </Text>
                                                            <Text
                                                                style={{
                                                                    fontSize: switchMerchant ? 8 : 10,
                                                                    color: Colors.descriptionColor,
                                                                }}>
                                                                Qty
                                                            </Text>
                                                        </View>
                                                        <View
                                                            style={{
                                                                marginLeft: '3%',
                                                                justifyContent: 'space-between',
                                                            }}>
                                                            <View>
                                                                <Entypo
                                                                    name="triangle-up"
                                                                    size={switchMerchant ? 7 : 14}
                                                                    color={
                                                                        currReportSummarySort ===
                                                                            REPORT_SORT_FIELD_TYPE.TOTAL_ITEMS_ASC
                                                                            ? Colors.secondaryColor
                                                                            : Colors.descriptionColor
                                                                    } />

                                                                <Entypo
                                                                    name="triangle-down"
                                                                    size={switchMerchant ? 7 : 14}
                                                                    color={
                                                                        currReportSummarySort ===
                                                                            REPORT_SORT_FIELD_TYPE.TOTAL_ITEMS_DESC
                                                                            ? Colors.secondaryColor
                                                                            : Colors.descriptionColor
                                                                    } />
                                                            </View>
                                                            {/* <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }}></Text> */}
                                                            {/* <Tooltip
                                                                isVisible={saleTip}
                                                                content={
                                                                    <Text>
                                                                        Product Amount - Discount + Tax + Service
                                                                        Charge
                                                                    </Text>
                                                                }
                                                                placement="top"
                                                                onClose={() => setSaleTip(false)}>
                                                                <TouchableOpacity
                                                                    onPress={() => setSaleTip(true)}
                                                                    style={styles.touchable}>
                                                                    <Feather
                                                                        name="help-circle"
                                                                        size={switchMerchant ? 10 : 15}
                                                                        style={{ color: Colors.primaryColor }}
                                                                    />
                                                                </TouchableOpacity>
                                                            </Tooltip> */}
                                                        </View>
                                                    </View>
                                                </TouchableOpacity>
                                            </View>
                                            {/* <View style={{ flexDirection: 'row', flex: 1.6, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                          <View style={{ flexDirection: 'column' }}>
                                              <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold', textAlign: 'center' }}>{'Total\nSales Return'}</Text>
                                              <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>RM</Text>
                                          </View>
                                          <View style={{ marginLeft: '3%' }}>
                                              <TouchableOpacity onPress={() => setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.TOTAL_SALES_RETURN_ASC)}>
                                                  <Entypo name='triangle-up' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.TOTAL_SALES_RETURN_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                              </TouchableOpacity>
  
                                              <TouchableOpacity onPress={() => setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.TOTAL_SALES_RETURN_DESC)}>
                                                  <Entypo name='triangle-down' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.TOTAL_SALES_RETURN_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                              </TouchableOpacity>
                                          </View>
                                      </View> */}
                                            <View
                                                style={{
                                                    flexDirection: 'row',
                                                    width: '8%',
                                                    borderRightWidth: 1,
                                                    borderRightColor: 'lightgrey',
                                                    alignItems: 'center',
                                                    justifyContent: 'flex-start',
                                                    paddingLeft: 10,
                                                }}>
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        if (
                                                            currReportSummarySort ===
                                                            REPORT_SORT_FIELD_TYPE.UPSELLING_DISCOUNT_ASC
                                                        ) {
                                                            setCurrReportSummarySort(
                                                                REPORT_SORT_FIELD_TYPE.UPSELLING_DISCOUNT_DESC,
                                                            );
                                                        } else {
                                                            setCurrReportSummarySort(
                                                                REPORT_SORT_FIELD_TYPE.UPSELLING_DISCOUNT_ASC,
                                                            );
                                                        }
                                                    }}>
                                                    <View style={{ flexDirection: 'row' }}>
                                                        <View style={{ flexDirection: 'column' }}>
                                                            {/* <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold', textAlign: 'center' }}>{'Total\nDiscount'}</Text> */}
                                                            <Text
                                                                numberOfLines={2}
                                                                style={{
                                                                    fontSize: switchMerchant ? 10 : 13,
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                }}>
                                                                {'Upselling\nDisc'}
                                                            </Text>
                                                            <Text
                                                                style={{
                                                                    fontSize: switchMerchant ? 8 : 10,
                                                                    color: Colors.descriptionColor,
                                                                }}>
                                                                RM
                                                            </Text>
                                                        </View>
                                                        <View style={{ marginLeft: '3%' }}>
                                                            <Entypo
                                                                name="triangle-up"
                                                                size={switchMerchant ? 7 : 14}
                                                                color={
                                                                    currReportSummarySort ===
                                                                        REPORT_SORT_FIELD_TYPE.UPSELLING_DISCOUNT_ASC
                                                                        ? Colors.secondaryColor
                                                                        : Colors.descriptionColor
                                                                } />

                                                            <Entypo
                                                                name="triangle-down"
                                                                size={switchMerchant ? 7 : 14}
                                                                color={
                                                                    currReportSummarySort ===
                                                                        REPORT_SORT_FIELD_TYPE.UPSELLING_DISCOUNT_DESC
                                                                        ? Colors.secondaryColor
                                                                        : Colors.descriptionColor
                                                                } />
                                                            <Text
                                                                style={{
                                                                    fontSize: 10,
                                                                    color: Colors.descriptionColor,
                                                                }} />
                                                        </View>
                                                    </View>
                                                </TouchableOpacity>
                                            </View>
                                            <View
                                                style={{
                                                    flexDirection: 'row',
                                                    width: '8%',
                                                    borderRightWidth: 1,
                                                    borderRightColor: 'lightgrey',
                                                    alignItems: 'center',
                                                    justifyContent: 'flex-start',
                                                    paddingLeft: 10,
                                                }}>
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        if (
                                                            currReportSummarySort ===
                                                            REPORT_SORT_FIELD_TYPE.TOTAL_ORDERS_ASC
                                                        ) {
                                                            setCurrReportSummarySort(
                                                                REPORT_SORT_FIELD_TYPE.TOTAL_ORDERS_DESC,
                                                            );
                                                        } else {
                                                            setCurrReportSummarySort(
                                                                REPORT_SORT_FIELD_TYPE.TOTAL_ORDERS_ASC,
                                                            );
                                                        }
                                                    }}>
                                                    <View style={{ flexDirection: 'row' }}>
                                                        <View style={{ flexDirection: 'column' }}>
                                                            <Text
                                                                numberOfLines={2}
                                                                style={{
                                                                    fontSize: switchMerchant ? 10 : 13,
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                }}>
                                                                {'Purchase\nRate'}
                                                            </Text>
                                                            <Text
                                                                style={{
                                                                    fontSize: switchMerchant ? 8 : 10,
                                                                    color: Colors.descriptionColor,
                                                                }}>
                                                                %
                                                            </Text>
                                                        </View>
                                                        <View style={{ marginLeft: '3%' }}>
                                                            <Entypo
                                                                name="triangle-up"
                                                                size={switchMerchant ? 7 : 14}
                                                                color={
                                                                    currReportSummarySort ===
                                                                        REPORT_SORT_FIELD_TYPE.TOTAL_ORDERS_ASC
                                                                        ? Colors.secondaryColor
                                                                        : Colors.descriptionColor
                                                                } />

                                                            <Entypo
                                                                name="triangle-down"
                                                                size={switchMerchant ? 7 : 14}
                                                                color={
                                                                    currReportSummarySort ===
                                                                        REPORT_SORT_FIELD_TYPE.TOTAL_ORDERS_DESC
                                                                        ? Colors.secondaryColor
                                                                        : Colors.descriptionColor
                                                                } />
                                                            <Text
                                                                style={{
                                                                    fontSize: 10,
                                                                    color: Colors.descriptionColor,
                                                                }} />
                                                        </View>
                                                    </View>
                                                </TouchableOpacity>
                                            </View>
                                        </View>
                                    ) : (
                                        <View style={{ marginTop: 10, flexDirection: 'row' }}>
                                            <View
                                                style={{
                                                    flexDirection: 'row',
                                                    width: '6%',
                                                    borderRightWidth: 1,
                                                    borderRightColor: 'lightgrey',
                                                    alignItems: 'center',
                                                    justifyContent: 'flex-start',
                                                    paddingLeft: 10,
                                                }}>
                                                <View style={{ flexDirection: 'row' }}>
                                                    <View style={{ flexDirection: 'column' }}>
                                                        <View
                                                            style={{
                                                                flexDirection: 'row',
                                                                alignItems: 'center',
                                                            }}>
                                                            <Text
                                                                numberOfLines={2}
                                                                style={{
                                                                    fontSize: switchMerchant ? 10 : 13,
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                }}>
                                                                {'No.\n'}
                                                            </Text>
                                                            {/* <Text style={{ fontSize: 10, color: Colors.descriptionColor }}></Text> */}
                                                        </View>
                                                        <Text
                                                            style={{
                                                                fontSize: switchMerchant ? 8 : 10,
                                                                color: Colors.descriptionColor,
                                                            }} />
                                                    </View>
                                                    <View style={{ marginLeft: '3%' }}>
                                                        <Entypo
                                                            name="triangle-up"
                                                            size={switchMerchant ? 7 : 14}
                                                            color="transparent" />

                                                        <Entypo
                                                            name="triangle-down"
                                                            size={switchMerchant ? 7 : 14}
                                                            color="transparent" />
                                                        <Text
                                                            style={{
                                                                fontSize: 10,
                                                                color: Colors.descriptionColor,
                                                            }} />
                                                    </View>
                                                </View>
                                                {/* <View style={{ marginLeft: '3%' }}>
                                              <TouchableOpacity onPress={() => setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.DATE_TIME_ASC)}>
                                                  <Entypo name='triangle-up' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.DATE_TIME_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                              </TouchableOpacity>
  
                                              <TouchableOpacity onPress={() => setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.DATE_TIME_DESC)}>
                                                  <Entypo name='triangle-down' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.DATE_TIME_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                              </TouchableOpacity>
                                          </View> */}
                                            </View>
                                            <View
                                                style={{
                                                    flexDirection: 'row',
                                                    width: '14%',
                                                    borderRightWidth: 1,
                                                    borderRightColor: 'lightgrey',
                                                    alignItems: 'center',
                                                    justifyContent: 'flex-start',
                                                    paddingLeft: 10,
                                                }}>
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        if (
                                                            currReportDetailsSort ===
                                                            REPORT_SORT_FIELD_TYPE.USER_ORDER_ID_ASC
                                                        ) {
                                                            setCurrReportDetailsSort(
                                                                REPORT_SORT_FIELD_TYPE.USER_ORDER_ID_DESC,
                                                            );
                                                        } else {
                                                            setCurrReportDetailsSort(
                                                                REPORT_SORT_FIELD_TYPE.USER_ORDER_ID_ASC,
                                                            );
                                                        }
                                                    }}>
                                                    <View style={{ flexDirection: 'row' }}>
                                                        <View style={{ flexDirection: 'column' }}>
                                                            <Text
                                                                numberOfLines={2}
                                                                style={{
                                                                    fontSize: switchMerchant ? 10 : 13,
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                }}>
                                                                {'Order ID\n'}
                                                            </Text>
                                                            <Text
                                                                style={{
                                                                    fontSize: switchMerchant ? 8 : 10,
                                                                    color: Colors.descriptionColor,
                                                                }} />
                                                        </View>
                                                        <View style={{ marginLeft: '3%' }}>
                                                            <Entypo
                                                                name="triangle-up"
                                                                size={switchMerchant ? 7 : 14}
                                                                color={
                                                                    currReportDetailsSort ===
                                                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_ID_ASC
                                                                        ? Colors.secondaryColor
                                                                        : Colors.descriptionColor
                                                                } />

                                                            <Entypo
                                                                name="triangle-down"
                                                                size={switchMerchant ? 7 : 14}
                                                                color={
                                                                    currReportDetailsSort ===
                                                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_ID_DESC
                                                                        ? Colors.secondaryColor
                                                                        : Colors.descriptionColor
                                                                } />
                                                            <Text
                                                                style={{
                                                                    fontSize: 10,
                                                                    color: Colors.descriptionColor,
                                                                }} />
                                                        </View>
                                                    </View>
                                                </TouchableOpacity>
                                            </View>
                                            <View
                                                style={{
                                                    flexDirection: 'row',
                                                    width: '18%',
                                                    borderRightWidth: 1,
                                                    borderRightColor: 'lightgrey',
                                                    alignItems: 'center',
                                                    justifyContent: 'flex-start',
                                                    paddingLeft: 10,
                                                }}>
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        if (
                                                            currReportDetailsSort ===
                                                            REPORT_SORT_FIELD_TYPE.USER_ORDER_DATE_TIME_ASC
                                                        ) {
                                                            setCurrReportDetailsSort(
                                                                REPORT_SORT_FIELD_TYPE.USER_ORDER_DATE_TIME_DESC,
                                                            );
                                                        } else {
                                                            setCurrReportDetailsSort(
                                                                REPORT_SORT_FIELD_TYPE.USER_ORDER_DATE_TIME_ASC,
                                                            );
                                                        }
                                                    }}>
                                                    <View style={{ flexDirection: 'row' }}>
                                                        <View style={{ flexDirection: 'column' }}>
                                                            <Text
                                                                numberOfLines={2}
                                                                style={{
                                                                    fontSize: switchMerchant ? 10 : 13,
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                }}>
                                                                {'Transaction Date\n'}
                                                            </Text>
                                                            <Text
                                                                style={{
                                                                    fontSize: switchMerchant ? 8 : 10,
                                                                    color: Colors.descriptionColor,
                                                                }} />
                                                        </View>
                                                        <View style={{ marginLeft: '3%' }}>
                                                            <Entypo
                                                                name="triangle-up"
                                                                size={switchMerchant ? 7 : 14}
                                                                color={
                                                                    currReportDetailsSort ===
                                                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_DATE_TIME_ASC
                                                                        ? Colors.secondaryColor
                                                                        : Colors.descriptionColor
                                                                } />

                                                            <Entypo
                                                                name="triangle-down"
                                                                size={switchMerchant ? 7 : 14}
                                                                color={
                                                                    currReportDetailsSort ===
                                                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_DATE_TIME_DESC
                                                                        ? Colors.secondaryColor
                                                                        : Colors.descriptionColor
                                                                } />
                                                            <Text
                                                                style={{
                                                                    fontSize: 10,
                                                                    color: Colors.descriptionColor,
                                                                }} />
                                                        </View>
                                                    </View>
                                                </TouchableOpacity>
                                            </View>

                                            <View
                                                style={{
                                                    flexDirection: 'row',
                                                    width: '10%',
                                                    borderRightWidth: 1,
                                                    borderRightColor: 'lightgrey',
                                                    alignItems: 'center',
                                                    justifyContent: 'flex-start',
                                                    paddingLeft: 10,
                                                }}>
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        if (
                                                            currReportDetailsSort ===
                                                            REPORT_SORT_FIELD_TYPE.UPSELLING_REVENUE_ASC
                                                        ) {
                                                            setCurrReportDetailsSort(
                                                                REPORT_SORT_FIELD_TYPE.UPSELLING_REVENUE_DESC,
                                                            );
                                                        } else {
                                                            setCurrReportDetailsSort(
                                                                REPORT_SORT_FIELD_TYPE.UPSELLING_REVENUE_ASC,
                                                            );
                                                        }
                                                    }}>
                                                    <View style={{ flexDirection: 'row' }}>
                                                        <View style={{ flexDirection: 'column' }}>
                                                            <Text
                                                                numberOfLines={2}
                                                                style={{
                                                                    fontSize: switchMerchant ? 10 : 13,
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                }}>
                                                                {'Upselling\nRevenue'}
                                                            </Text>
                                                            <Text
                                                                style={{
                                                                    fontSize: switchMerchant ? 8 : 10,
                                                                    color: Colors.descriptionColor,
                                                                }}>
                                                                RM
                                                            </Text>
                                                        </View>
                                                        <View
                                                            style={{
                                                                marginLeft: '3%',
                                                                justifyContent: 'space-between',
                                                            }}>
                                                            <View>
                                                                <Entypo
                                                                    name="triangle-up"
                                                                    size={switchMerchant ? 7 : 14}
                                                                    color={
                                                                        currReportDetailsSort ===
                                                                            REPORT_SORT_FIELD_TYPE.UPSELLING_REVENUE_ASC
                                                                            ? Colors.secondaryColor
                                                                            : Colors.descriptionColor
                                                                    } />

                                                                <Entypo
                                                                    name="triangle-down"
                                                                    size={switchMerchant ? 7 : 14}
                                                                    color={
                                                                        currReportDetailsSort ===
                                                                            REPORT_SORT_FIELD_TYPE.UPSELLING_REVENUE_DESC
                                                                            ? Colors.secondaryColor
                                                                            : Colors.descriptionColor
                                                                    } />
                                                            </View>
                                                            {/* <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }}></Text> */}
                                                            {/* <Tooltip
                                                                isVisible={saleTip}
                                                                content={
                                                                    <Text>
                                                                        Product Amount - Discount + Tax + Service
                                                                        Charge
                                                                    </Text>
                                                                }
                                                                placement="top"
                                                                onClose={() => setSaleTip(false)}>
                                                                <TouchableOpacity
                                                                    onPress={() => setSaleTip(true)}
                                                                    style={styles.touchable}>
                                                                    <Feather
                                                                        name="help-circle"
                                                                        size={switchMerchant ? 10 : 15}
                                                                        style={{ color: Colors.primaryColor }}
                                                                    />
                                                                </TouchableOpacity>
                                                            </Tooltip> */}
                                                        </View>
                                                    </View>
                                                </TouchableOpacity>
                                            </View>
                                            <View
                                                style={{
                                                    flexDirection: 'row',
                                                    width: '7%',
                                                    borderRightWidth: 1,
                                                    borderRightColor: 'lightgrey',
                                                    alignItems: 'center',
                                                    justifyContent: 'flex-start',
                                                    paddingLeft: 10,
                                                }}>
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        if (
                                                            currReportDetailsSort ===
                                                            REPORT_SORT_FIELD_TYPE.TOTAL_ITEMS_ASC
                                                        ) {
                                                            setCurrReportDetailsSort(
                                                                REPORT_SORT_FIELD_TYPE.TOTAL_ITEMS_DESC,
                                                            );
                                                        } else {
                                                            setCurrReportDetailsSort(
                                                                REPORT_SORT_FIELD_TYPE.TOTAL_ITEMS_ASC,
                                                            );
                                                        }
                                                    }}>
                                                    <View style={{ flexDirection: 'row' }}>
                                                        <View style={{ flexDirection: 'column' }}>
                                                            {/* <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold' }}>{'Total\nDiscount'}</Text> */}
                                                            <Text
                                                                numberOfLines={2}
                                                                style={{
                                                                    fontSize: switchMerchant ? 10 : 13,
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                }}>
                                                                {'Items\n'}
                                                            </Text>
                                                            <Text
                                                                style={{
                                                                    fontSize: switchMerchant ? 8 : 10,
                                                                    color: Colors.descriptionColor,
                                                                }}>
                                                                Qty
                                                            </Text>
                                                        </View>
                                                        <View style={{ marginLeft: '3%' }}>
                                                            <Entypo
                                                                name="triangle-up"
                                                                size={switchMerchant ? 7 : 14}
                                                                color={
                                                                    currReportDetailsSort ===
                                                                        REPORT_SORT_FIELD_TYPE.TOTAL_ITEMS_ASC
                                                                        ? Colors.secondaryColor
                                                                        : Colors.descriptionColor
                                                                } />

                                                            <Entypo
                                                                name="triangle-down"
                                                                size={switchMerchant ? 7 : 14}
                                                                color={
                                                                    currReportDetailsSort ===
                                                                        REPORT_SORT_FIELD_TYPE.TOTAL_ITEMS_DESC
                                                                        ? Colors.secondaryColor
                                                                        : Colors.descriptionColor
                                                                } />
                                                            <Text
                                                                style={{
                                                                    fontSize: 10,
                                                                    color: Colors.descriptionColor,
                                                                }} />
                                                        </View>
                                                    </View>
                                                </TouchableOpacity>
                                            </View>
                                            {/* <View
                          style={{
                            flexDirection: 'row',
                            width: '7%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportDetailsSort ===
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC
                              ) {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_DESC,
                                );
                              } else {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC,
                                );
                              }
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Disc\n'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  %
                                </Text>
                              </View>
                              <View style={{ marginLeft: '3%' }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  }></Entypo>
  
                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  }></Entypo>
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }}></Text>
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View> */}
                                            {/* <View style={{ flexDirection: 'row', flex: 2, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                          <View style={{ flexDirection: 'column' }}>
                                              <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold' }}>Discount</Text>
                                              <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>%</Text>
                                          </View>
                                          <View style={{ marginLeft: '3%' }}>
                                              <TouchableOpacity onPress={() => setCurrReportDetailsSort(REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC)}>
                                                  <Entypo name='triangle-up' size={14} color={currReportDetailsSort === REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                              </TouchableOpacity>
  
                                              <TouchableOpacity onPress={() => setCurrReportDetailsSort(REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_DESC)}>
                                                  <Entypo name='triangle-down' size={14} color={currReportDetailsSort === REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                              </TouchableOpacity>
                                          </View>
                                      </View> */}

                                            <View
                                                style={{
                                                    flexDirection: 'row',
                                                    width: '8%',
                                                    alignItems: 'center',
                                                    justifyContent: 'flex-start',
                                                    paddingLeft: 10,
                                                    borderRightWidth: 1,
                                                    borderRightColor: 'lightgrey',
                                                }}>
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        if (
                                                            currReportDetailsSort ===
                                                            REPORT_SORT_FIELD_TYPE.UPSELLING_DISCOUNT_ASC
                                                        ) {
                                                            setCurrReportDetailsSort(
                                                                REPORT_SORT_FIELD_TYPE.UPSELLING_DISCOUNT_DESC,
                                                            );
                                                        } else {
                                                            setCurrReportDetailsSort(
                                                                REPORT_SORT_FIELD_TYPE.UPSELLING_DISCOUNT_ASC,
                                                            );
                                                        }
                                                    }}>
                                                    <View style={{ flexDirection: 'row' }}>
                                                        <View style={{ flexDirection: 'column' }}>
                                                            <Text
                                                                numberOfLines={2}
                                                                style={{
                                                                    fontSize: switchMerchant ? 10 : 13,
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                }}>
                                                                {'Upselling\nDisc'}
                                                            </Text>
                                                            <Text
                                                                style={{
                                                                    fontSize: switchMerchant ? 8 : 10,
                                                                    color: Colors.descriptionColor,
                                                                }}>
                                                                RM
                                                            </Text>
                                                        </View>
                                                        <View style={{ marginLeft: '3%' }}>
                                                            <Entypo
                                                                name="triangle-up"
                                                                size={switchMerchant ? 7 : 14}
                                                                color={
                                                                    currReportDetailsSort ===
                                                                        REPORT_SORT_FIELD_TYPE.UPSELLING_DISCOUNT_ASC
                                                                        ? Colors.secondaryColor
                                                                        : Colors.descriptionColor
                                                                } />

                                                            <Entypo
                                                                name="triangle-down"
                                                                size={switchMerchant ? 7 : 14}
                                                                color={
                                                                    currReportDetailsSort ===
                                                                        REPORT_SORT_FIELD_TYPE.UPSELLING_DISCOUNT_DESC
                                                                        ? Colors.secondaryColor
                                                                        : Colors.descriptionColor
                                                                } />
                                                            <Text
                                                                style={{
                                                                    fontSize: 10,
                                                                    color: Colors.descriptionColor,
                                                                }} />
                                                        </View>
                                                    </View>
                                                </TouchableOpacity>
                                            </View>

                                        </View>
                                    )}

                                    {isTableApiLoading && (
                                        <View
                                            style={{
                                                position: 'absolute',
                                                top: 0,
                                                left: 0,
                                                right: 0,
                                                bottom: 0,
                                                backgroundColor: 'rgba(255, 255, 255, 0.7)',
                                                justifyContent: 'center',
                                                alignItems: 'center',
                                                zIndex: 1000,
                                            }}
                                        >
                                            <ActivityIndicator size="large" color={Colors.primaryColor} />
                                            <Text style={{ marginTop: 10, fontSize: 16 }}>Loading...</Text>
                                        </View>
                                    )}

                                    {!showDetails ? (
                                        <>
                                            {(productSalesUpselling).length > 0 ? (
                                                <FlatList
                                                    showsVerticalScrollIndicator={false}
                                                    ref={flatListRef}
                                                    data={sortReportDataList(
                                                        (productSalesUpselling).filter((item) => {
                                                            if (search !== '') {
                                                                if (
                                                                    item.productName
                                                                        .toLowerCase()
                                                                        .includes(search.toLowerCase())
                                                                ) {
                                                                    return true;
                                                                } else if (
                                                                    item.productCategory
                                                                        .toLowerCase()
                                                                        .includes(search.toLowerCase())
                                                                ) {
                                                                    return true;
                                                                } else if (
                                                                    item.productSku
                                                                        .toLowerCase()
                                                                        .includes(search.toLowerCase())
                                                                ) {
                                                                    return true;
                                                                } else if (
                                                                    item.totalItems
                                                                        .toString()
                                                                        .toLowerCase()
                                                                        .includes(search.toLowerCase())
                                                                ) {
                                                                    return true;
                                                                } else if (
                                                                    item.totalSales
                                                                        .toFixed(2)
                                                                        .toString()
                                                                        .toLowerCase()
                                                                        .includes(search.toLowerCase())
                                                                ) {
                                                                    return true;
                                                                } else if (
                                                                    item.totalDiscount
                                                                        .toFixed(2)
                                                                        .toString()
                                                                        .toLowerCase()
                                                                        .includes(search.toLowerCase())
                                                                ) {
                                                                    return true;
                                                                } else {
                                                                    return false;
                                                                }
                                                            } else {
                                                                return true;
                                                            }
                                                        }),
                                                        currReportSummarySort,
                                                    ).slice(
                                                        (currentPage - 1) * perPage,
                                                        currentPage * perPage,
                                                    )}
                                                    // extraData={productSales}
                                                    renderItem={renderItem}
                                                    keyExtractor={(item, index) => String(index)}
                                                    style={{ marginTop: 10 }}
                                                    initialNumToRender={8}
                                                />
                                            ) : (
                                                <View
                                                    style={{
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        height: '71%',
                                                    }}>
                                                    <Text style={{ color: Colors.descriptionColor }}>
                                                        - No Data Available -
                                                    </Text>
                                                </View>
                                            )}
                                        </>
                                    ) : (
                                        <>
                                            {productSalesDetails.length > 0 ? (
                                                <FlatList
                                                    showsVerticalScrollIndicator={false}
                                                    ref={flatListRef}
                                                    data={sortReportDataList(
                                                        productSalesDetails.filter((item) => {
                                                            if (search !== '') {
                                                                if (
                                                                    (`#${item.orderType !== ORDER_TYPE.DINEIN ? 'T' : ''}${item.orderId}`)
                                                                        .toLowerCase()
                                                                        .includes(search.toLowerCase())
                                                                ) {
                                                                    return true;
                                                                }
                                                                // else if (
                                                                //     item.totalPrice
                                                                //         .toFixed(2)
                                                                //         .toString()
                                                                //         .toLowerCase()
                                                                //         .includes(search.toLowerCase())
                                                                // ) {
                                                                //     return true;
                                                                // } 
                                                                // else if (
                                                                //     getOrderDiscountInfoInclOrderBased(item)
                                                                //         .toFixed(2)
                                                                //         .toString()
                                                                //         .toLowerCase()
                                                                //         .includes(search.toLowerCase())
                                                                // ) {
                                                                //     return true;
                                                                // } 
                                                                // else if (
                                                                //     item.tax
                                                                //         .toFixed(2)
                                                                //         .toString()
                                                                //         .toLowerCase()
                                                                //         .includes(search.toLowerCase())
                                                                // ) {
                                                                //     return true;
                                                                // } 
                                                                else if (
                                                                    (moment(item.createdAt).format('DD MMM YY hh:mm A'))
                                                                        .toString()
                                                                        .toLowerCase()
                                                                        .includes(search.toLowerCase())
                                                                ) {
                                                                    return true;
                                                                } else {
                                                                    return false;
                                                                }
                                                            } else {
                                                                return true;
                                                            }
                                                        }),
                                                        currReportDetailsSort,
                                                    ).slice(
                                                        (currentDetailsPage - 1) * perPage,
                                                        currentDetailsPage * perPage,
                                                    )}
                                                    // extraData={transactionTypeSales}
                                                    renderItem={renderItemDetails}
                                                    keyExtractor={(item, index) => String(index)}
                                                    style={{ marginTop: 10 }}
                                                />
                                            ) : (
                                                <View style={{ height: windowHeight * 0.4 }}>
                                                    <View
                                                        style={{
                                                            alignItems: 'center',
                                                            justifyContent: 'center',
                                                            height: '100%',
                                                        }}>
                                                        <Text style={{ color: Colors.descriptionColor }}>
                                                            - No Data Available -
                                                        </Text>
                                                    </View>
                                                </View>
                                            )}
                                        </>
                                    )}
                                </View>

                                {!showDetails ? (
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            marginTop: 10,
                                            width: windowWidth * 0.87,
                                            alignItems: 'center',
                                            alignSelf: 'center',
                                            justifyContent: 'flex-end',
                                            // shadowOffset: {
                                            //     width: 0,
                                            //     height: 1,
                                            // },
                                            // shadowOpacity: 0.22,
                                            // shadowRadius: 3.22,
                                            // elevation: 1,
                                            paddingBottom: 15,
                                        }}>
                                        <Text
                                            style={{
                                                fontSize: switchMerchant ? 10 : 14,
                                                fontFamily: 'NunitoSans-Bold',
                                                marginRight: '1%',
                                            }}>
                                            Items Showed
                                        </Text>
                                        <View
                                            style={{
                                                width: Platform.OS === 'ios' ? 65 : '13%', //65,
                                                height: switchMerchant ? 20 : 35,
                                                backgroundColor: Colors.whiteColor,
                                                borderRadius: 10,
                                                justifyContent: 'center',
                                                paddingHorizontal: Platform.OS === 'ios' ? 0 : 0,
                                                //paddingLeft:switchMerchant ? '4.5%': Platform.OS === 'ios' ? '2%' : '4%',
                                                // paddingTop: '-60%',
                                                borderWidth: 1,
                                                borderColor: '#E5E5E5',
                                                marginRight: '1%',
                                            }}>
                                            <DropDownPicker
                                                style={{
                                                    backgroundColor: Colors.fieldtBgColor,
                                                    width: '100%',
                                                    height: 40,
                                                    borderRadius: 10,
                                                    borderWidth: 1,
                                                    borderColor: "#E5E5E5",
                                                    flexDirection: "row",
                                                }}
                                                dropDownContainerStyle={{
                                                    width: '100%',
                                                    backgroundColor: Colors.fieldtBgColor,
                                                    borderColor: "#E5E5E5",
                                                }}
                                                labelStyle={{
                                                    marginLeft: 5,
                                                    flexDirection: "row",
                                                }}
                                                textStyle={{
                                                    fontSize: 14,
                                                    fontFamily: 'NunitoSans-Regular',

                                                    marginLeft: 5,
                                                    paddingVertical: 10,
                                                    flexDirection: "row",
                                                }}
                                                selectedItemContainerStyle={{
                                                    flexDirection: "row",
                                                }}

                                                showArrowIcon={true}
                                                ArrowDownIconComponent={({ style }) => (
                                                    <Ionicon
                                                        size={25}
                                                        color={Colors.fieldtTxtColor}
                                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                                        name="chevron-down-outline"
                                                    />
                                                )}
                                                ArrowUpIconComponent={({ style }) => (
                                                    <Ionicon
                                                        size={25}
                                                        color={Colors.fieldtTxtColor}
                                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                                        name="chevron-up-outline"
                                                    />
                                                )}

                                                showTickIcon={true}
                                                TickIconComponent={({ press }) => (
                                                    <Ionicon
                                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                                        color={
                                                            press ? Colors.fieldtBgColor : Colors.primaryColor
                                                        }
                                                        name={'md-checkbox'}
                                                        size={25}
                                                    />
                                                )}
                                                placeholder={'Select a Type'}
                                                placeholderStyle={{
                                                    color: Colors.fieldtTxtColor,
                                                    // marginTop: 15,
                                                }}
                                                // searchable
                                                // searchableStyle={{
                                                //   paddingHorizontal: windowWidth * 0.0079,
                                                // }}
                                                value={perPage}
                                                items={TABLE_PAGE_SIZE_DROPDOWN_LIST.concat({
                                                    label: 'All',
                                                    value: !showDetails
                                                        ? (productSalesUpselling.length + productSales.length)
                                                        : productSalesDetails.length,
                                                })}
                                                // multiple={true}
                                                // multipleText={`${item.tagIdList.length} Tag(s)`}
                                                onSelectItem={(item) => {
                                                    setPerPage(item.value);
                                                    // var currentPageTemp =
                                                    //   text.length > 0 ? parseInt(text) : 1;

                                                    // setCurrentPage(
                                                    //   currentPageTemp > pageCount
                                                    //     ? pageCount
                                                    //     : currentPageTemp < 1
                                                    //       ? 1
                                                    //       : currentPageTemp,
                                                    // );
                                                }}
                                                open={openPage}
                                                setOpen={setOpenPage}
                                                dropDownDirection="TOP"
                                            />
                                        </View>

                                        <Text
                                            style={{
                                                fontSize: switchMerchant ? 10 : 14,
                                                fontFamily: 'NunitoSans-Bold',
                                                marginRight: '1%',
                                            }}>
                                            Page
                                        </Text>
                                        <View
                                            style={{
                                                width: switchMerchant ? 65 : 70,
                                                height: switchMerchant ? 20 : 35,
                                                backgroundColor: Colors.whiteColor,
                                                borderRadius: 10,
                                                justifyContent: 'center',
                                                paddingHorizontal: 22,
                                                borderWidth: 1,
                                                borderColor: '#E5E5E5',
                                            }}>
                                            {console.log('currentPage')}
                                            {console.log(currentPage)}

                                            <TextInput
                                                onChangeText={(text) => {
                                                    var currentPageTemp =
                                                        text.length > 0 ? parseInt(text) : 1;

                                                    setCurrentPage(
                                                        currentPageTemp > pageCount
                                                            ? pageCount
                                                            : currentPageTemp < 1
                                                                ? 1
                                                                : currentPageTemp,
                                                    );
                                                }}
                                                placeholder={currentPage.toString()}
                                                placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                                style={{
                                                    color: 'black',
                                                    // fontFamily: 'NunitoSans-Regular',
                                                    fontSize: switchMerchant ? 10 : 14,
                                                    fontFamily: 'NunitoSans-Regular',
                                                    marginTop: Platform.OS === 'ios' ? 0 : -15,
                                                    marginBottom: Platform.OS === 'ios' ? 0 : -15,
                                                    textAlign: 'center',
                                                    width: '100%',
                                                }}
                                                value={currentPage.toString()}
                                                defaultValue={currentPage.toString()}
                                                keyboardType={'numeric'}
                                                onFocus={() => {
                                                    setPushPagingToTop(true);
                                                }}
                                            />
                                        </View>
                                        <Text
                                            style={{
                                                fontSize: switchMerchant ? 10 : 14,
                                                fontFamily: 'NunitoSans-Bold',
                                                marginLeft: '1%',
                                                marginRight: '1%',
                                            }}>
                                            of {pageCount}
                                        </Text>
                                        <TouchableOpacity
                                            style={{
                                                width: switchMerchant ? 30 : 45,
                                                height: switchMerchant ? 20 : 28,
                                                backgroundColor: Colors.primaryColor,
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                            }}
                                            onPress={() => {
                                                // prevPage();
                                                prevPage2();
                                            }}>
                                            <MaterialIcons
                                                name="keyboard-arrow-left"
                                                size={switchMerchant ? 20 : 25}
                                                style={{ color: Colors.whiteColor }}
                                            />
                                        </TouchableOpacity>
                                        <TouchableOpacity
                                            style={{
                                                width: switchMerchant ? 30 : 45,
                                                height: switchMerchant ? 20 : 28,
                                                backgroundColor: Colors.primaryColor,
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                            }}
                                            onPress={() => {
                                                // nextPage();
                                                nextPage2();
                                            }}>
                                            <MaterialIcons
                                                name="keyboard-arrow-right"
                                                size={switchMerchant ? 20 : 25}
                                                style={{ color: Colors.whiteColor }}
                                            />
                                        </TouchableOpacity>
                                    </View>
                                ) : (
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            marginTop: 10,
                                            width: windowWidth * 0.87,
                                            alignItems: 'center',
                                            alignSelf: 'center',
                                            justifyContent: 'flex-end',
                                            // shadowOffset: {
                                            //     width: 0,
                                            //     height: 1,
                                            // },
                                            // shadowOpacity: 0.22,
                                            // shadowRadius: 3.22,
                                            // elevation: 1,
                                            paddingBottom: 15,
                                        }}>
                                        <Text
                                            style={{
                                                fontSize: switchMerchant ? 10 : 14,
                                                fontFamily: 'NunitoSans-Bold',
                                                marginRight: '1%',
                                            }}>
                                            Items Showed
                                        </Text>
                                        <View
                                            style={{
                                                width: Platform.OS === 'ios' ? 65 : '13%', //65,
                                                height: switchMerchant ? 20 : 35,
                                                backgroundColor: Colors.whiteColor,
                                                borderRadius: 10,
                                                justifyContent: 'center',
                                                paddingHorizontal: Platform.OS === 'ios' ? 0 : 0,
                                                //paddingLeft:switchMerchant ? '4.5%': Platform.OS === 'ios' ? '2%' : '4%',
                                                // paddingTop: '-60%',
                                                borderWidth: 1,
                                                borderColor: '#E5E5E5',
                                                marginRight: '1%',
                                            }}>
                                            <DropDownPicker
                                                style={{
                                                    backgroundColor: Colors.fieldtBgColor,
                                                    width: '100%',
                                                    height: 40,
                                                    borderRadius: 10,
                                                    borderWidth: 1,
                                                    borderColor: "#E5E5E5",
                                                    flexDirection: "row",
                                                }}
                                                dropDownContainerStyle={{
                                                    width: '100%',
                                                    backgroundColor: Colors.fieldtBgColor,
                                                    borderColor: "#E5E5E5",
                                                }}
                                                labelStyle={{
                                                    marginLeft: 5,
                                                    flexDirection: "row",
                                                }}
                                                textStyle={{
                                                    fontSize: 14,
                                                    fontFamily: 'NunitoSans-Regular',

                                                    marginLeft: 5,
                                                    paddingVertical: 10,
                                                    flexDirection: "row",
                                                }}
                                                selectedItemContainerStyle={{
                                                    flexDirection: "row",
                                                }}

                                                showArrowIcon={true}
                                                ArrowDownIconComponent={({ style }) => (
                                                    <Ionicon
                                                        size={25}
                                                        color={Colors.fieldtTxtColor}
                                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                                        name="chevron-down-outline"
                                                    />
                                                )}
                                                ArrowUpIconComponent={({ style }) => (
                                                    <Ionicon
                                                        size={25}
                                                        color={Colors.fieldtTxtColor}
                                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                                        name="chevron-up-outline"
                                                    />
                                                )}

                                                showTickIcon={true}
                                                TickIconComponent={({ press }) => (
                                                    <Ionicon
                                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                                        color={
                                                            press ? Colors.fieldtBgColor : Colors.primaryColor
                                                        }
                                                        name={'md-checkbox'}
                                                        size={25}
                                                    />
                                                )}
                                                placeholder={'Select a Type'}
                                                placeholderStyle={{
                                                    color: Colors.fieldtTxtColor,
                                                    // marginTop: 15,
                                                }}
                                                // searchable
                                                // searchableStyle={{
                                                //   paddingHorizontal: windowWidth * 0.0079,
                                                // }}
                                                value={perPage}
                                                items={TABLE_PAGE_SIZE_DROPDOWN_LIST.concat({
                                                    label: 'All',
                                                    value: !showDetails
                                                        ? productSales.length
                                                        : productSalesDetails.length,
                                                })}
                                                // multiple={true}
                                                // multipleText={`${item.tagIdList.length} Tag(s)`}
                                                onSelectItem={(item) => {
                                                    setPerPage(item.value);
                                                    // var currentPageTemp =
                                                    //   text.length > 0 ? parseInt(text) : 1;

                                                    // setCurrentPage(
                                                    //   currentPageTemp > pageCount
                                                    //     ? pageCount
                                                    //     : currentPageTemp < 1
                                                    //       ? 1
                                                    //       : currentPageTemp,
                                                    // );
                                                }}
                                                open={openPage}
                                                setOpen={setOpenPage}
                                                dropDownDirection="TOP"
                                            />
                                        </View>

                                        <Text
                                            style={{
                                                fontSize: switchMerchant ? 10 : 14,
                                                fontFamily: 'NunitoSans-Bold',
                                                marginRight: '1%',
                                            }}>
                                            Page
                                        </Text>
                                        <View
                                            style={{
                                                width: switchMerchant ? 65 : 70,
                                                height: switchMerchant ? 20 : 35,
                                                backgroundColor: Colors.whiteColor,
                                                borderRadius: 10,
                                                justifyContent: 'center',
                                                paddingHorizontal: 22,
                                                borderWidth: 1,
                                                borderColor: '#E5E5E5',
                                            }}>
                                            {console.log('currentDetailsPage')}
                                            {console.log(currentDetailsPage)}

                                            <TextInput
                                                onChangeText={(text) => {
                                                    var currentPageTemp =
                                                        text.length > 0 ? parseInt(text) : 1;

                                                    setCurrentDetailsPage(
                                                        currentPageTemp > detailsPageCount
                                                            ? detailsPageCount
                                                            : currentPageTemp < 1
                                                                ? 1
                                                                : currentPageTemp,
                                                    );
                                                }}
                                                placeholder={currentDetailsPage.toString()}
                                                placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                                style={{
                                                    color: 'black',
                                                    // fontFamily: 'NunitoSans-Regular',
                                                    fontSize: switchMerchant ? 10 : 14,
                                                    fontFamily: 'NunitoSans-Regular',
                                                    marginTop: Platform.OS === 'ios' ? 0 : -15,
                                                    marginBottom: Platform.OS === 'ios' ? 0 : -15,
                                                    textAlign: 'center',
                                                    width: '100%',
                                                }}
                                                value={currentDetailsPage.toString()}
                                                defaultValue={currentDetailsPage.toString()}
                                                keyboardType={'numeric'}
                                                onFocus={() => {
                                                    setPushPagingToTop(true);
                                                }}
                                            />
                                        </View>
                                        <Text
                                            style={{
                                                fontSize: switchMerchant ? 10 : 14,
                                                fontFamily: 'NunitoSans-Bold',
                                                marginLeft: '1%',
                                                marginRight: '1%',
                                            }}>
                                            of {detailsPageCount}
                                        </Text>
                                        <TouchableOpacity
                                            style={{
                                                width: switchMerchant ? 30 : 45,
                                                height: switchMerchant ? 20 : 28,
                                                backgroundColor: Colors.primaryColor,
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                            }}
                                            onPress={() => {
                                                prevDetailsPage();
                                            }}>
                                            <MaterialIcons
                                                name="keyboard-arrow-left"
                                                size={switchMerchant ? 20 : 25}
                                                style={{ color: Colors.whiteColor }}
                                            />
                                        </TouchableOpacity>
                                        <TouchableOpacity
                                            style={{
                                                width: switchMerchant ? 30 : 45,
                                                height: switchMerchant ? 20 : 28,
                                                backgroundColor: Colors.primaryColor,
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                            }}
                                            onPress={() => {
                                                nextDetailsPage();
                                            }}>
                                            <MaterialIcons
                                                name="keyboard-arrow-right"
                                                size={switchMerchant ? 20 : 25}
                                                style={{ color: Colors.whiteColor }}
                                            />
                                        </TouchableOpacity>
                                    </View>
                                )}

                                {/* /////////////////////////////////////// */}

                                {/* </View> */}
                            </View>
                        </ScrollView>

                        {expandGroupBy ? (
                            <View style={styles.ManageFilterBox1}>
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        justifyContent: 'flex-start',
                                        alignItems: 'center',
                                        margin: 10,
                                    }}>
                                    <Text
                                        style={{
                                            fontSize: 13,
                                            color: '#27353C',
                                            fontFamily: 'Nunitosans-Bold',
                                        }}>
                                        Group By
                                    </Text>
                                </View>
                                <View
                                    style={{
                                        borderWidth: 0.5,
                                        borderColor: '#D2D2D2',
                                        width: '100%',
                                        marginTop: 5,
                                        marginBottom: 5,
                                    }} />
                                <View
                                    style={{
                                        justifyContent: 'space-between',
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        margin: 10,
                                        height: 45,
                                        elevation: 1,
                                        zIndex: 1,
                                    }}>
                                    <Text
                                        style={{
                                            fontSize: 16,
                                            Colors: '#27353C',
                                            fontFamily: 'Nunitosans-bold',
                                        }}>
                                        Select Group:{' '}
                                    </Text>
                                    <DropDownPicker
                                        arrowColor={'#BDBDBD'}
                                        arrowSize={23}
                                        arrowStyle={{ fontWeight: 'bold' }}
                                        style={{
                                            width: 300,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                        }}
                                        //items={CHART_FIELD_NAME_DROPDOWN_LIST[CHART_TYPE.REPORT_PRODUCT_SALES]}
                                        dropDownStyle={{
                                            width: 300,
                                        }}
                                        itemStyle={{
                                            justifyContent: 'flex-start',
                                        }}
                                        globalTextStyle={{
                                            fontFamily: 'NunitoSans-SemiBold',
                                            color: Colors.fontDark,
                                            marginLeft: 5,
                                        }}
                                        items={[
                                            { label: 'Product Name', value: 1 },
                                            { label: 'Category', value: 2 },
                                            { label: 'Tag', value: 3 },
                                            { label: 'Sku', value: 4 },
                                        ]}
                                        placeholder={'select a group'}
                                        //onChangeItem={item => {
                                        //    setSelectedChartFilterQueries([
                                        //        {
                                        //            ...selectedChartFilterQueries[0],
                                        //            fieldNameKey: item.value,
                                        //            fieldNameType: item.fieldType,
                                        //        },
                                        //    ]);
                                        //}}
                                        //defaultValue={selectedChartFilterQueries[0].fieldNameKey}
                                        listMode={'SCROLLVIEW'}
                                        scrollViewProps={{
                                            nestedScrollEnabled: true,
                                        }}
                                    />
                                </View>
                                <View
                                    style={{
                                        borderWidth: 0.5,
                                        borderColor: '#D2D2D2',
                                        width: '100%',
                                        marginTop: 5,
                                        marginBottom: 5,
                                    }} />
                                <View
                                    style={{
                                        justifyContent: 'space-between',
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        margin: 10,
                                        height: 45,
                                        elevation: 1,
                                        zIndex: -1,
                                    }}>
                                    <Text
                                        style={{
                                            fontSize: 16,
                                            Colors: '#27353C',
                                            fontFamily: 'Nunitosans-bold',
                                        }}>
                                        Select SubItems:{' '}
                                    </Text>
                                    <DropDownPicker
                                        arrowColor={'#BDBDBD'}
                                        arrowSize={23}
                                        arrowStyle={{ fontWeight: 'bold' }}
                                        style={{
                                            width: 300,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                        }}
                                        //items={CHART_FIELD_NAME_DROPDOWN_LIST[CHART_TYPE.REPORT_PRODUCT_SALES]}
                                        dropDownStyle={{
                                            width: 300,
                                        }}
                                        itemStyle={{
                                            justifyContent: 'flex-start',
                                        }}
                                        globalTextStyle={{
                                            fontFamily: 'NunitoSans-SemiBold',
                                            color: Colors.fontDark,
                                            marginLeft: 5,
                                        }}
                                        items={[
                                            { label: 'Product Name', value: 1 },
                                            { label: 'Category', value: 2 },
                                            { label: 'Tag', value: 3 },
                                            { label: 'Sku', value: 4 },
                                        ]}
                                        placeholder={'select a group'}
                                        //onChangeItem={item => {
                                        //    setSelectedChartFilterQueries([
                                        //        {
                                        //            ...selectedChartFilterQueries[0],
                                        //            fieldNameKey: item.value,
                                        //            fieldNameType: item.fieldType,
                                        //        },
                                        //    ]);
                                        //}}
                                        //defaultValue={selectedChartFilterQueries[0].fieldNameKey}
                                        listMode={'SCROLLVIEW'}
                                        scrollViewProps={{
                                            nestedScrollEnabled: true,
                                        }}
                                    />
                                </View>
                            </View>
                        ) : null}

                        {expandSelection ? (
                            <View style={styles.ManageFilterBox}>
                                {/* <ScrollViewGH showsVerticalScrollIndicator={false}> */}
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        justifyContent: 'flex-start',
                                        alignItems: 'center',
                                        margin: 10,
                                    }}>
                                    <Text
                                        style={{
                                            fontSize: 16,
                                            Colors: '#27353C',
                                            fontFamily: 'Nunitosans-bold',
                                        }}>
                                        Manage Filter
                                    </Text>
                                </View>
                                <View
                                    style={{
                                        borderWidth: 0.5,
                                        borderColor: '#D2D2D2',
                                        width: '100%',
                                        marginTop: 5,
                                        marginBottom: 5,
                                    }} />
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        margin: 10,
                                        height: 35,
                                        elevation: 1,
                                        zIndex: 1,
                                    }}>
                                    <DropDownPicker
                                        arrowColor={'#BDBDBD'}
                                        arrowSize={23}
                                        arrowStyle={{ fontWeight: 'bold' }}
                                        style={{ width: 250, height: 35 }}
                                        // items={[
                                        //     { label: 'Daily', value: 1 },
                                        //     { label: 'Weekly', value: 2 },
                                        //     { label: 'Monthly', value: 3 },
                                        //     //{ label: 'Hour of day', value: 4 },
                                        //     //{ label: 'Day of week', value: 5 }
                                        // ]}
                                        items={
                                            CHART_FIELD_NAME_DROPDOWN_LIST[
                                            CHART_TYPE.REPORT_PRODUCT_SALES
                                            ]
                                        }
                                        // placeholder={'Field name'}
                                        // placeholderStyle={{ color: 'black' }}
                                        dropDownStyle={{
                                            width: 250,
                                        }}
                                        itemStyle={{
                                            justifyContent: 'flex-start',
                                        }}
                                        globalTextStyle={{
                                            fontFamily: 'NunitoSans-SemiBold',
                                            // fontSize: 12,
                                            color: Colors.fontDark,
                                            marginLeft: 5,
                                        }}
                                        onChangeItem={(item) => {
                                            // setState({ sort: selectedSort }),
                                            //sortingFunc(selectedSort);

                                            // console.log('test queries');
                                            // console.log([
                                            //   {
                                            //     ...selectedChartFilterQueries[0],
                                            //     fieldNameKey: item.value,
                                            //     fieldNameType: item.fieldType,
                                            //     fieldDataValue: null,
                                            //     fieldSpecial: item.special ? item.special : null,
                                            //   },
                                            // ]);

                                            setSelectedChartFilterQueries([
                                                {
                                                    ...selectedChartFilterQueries[0],
                                                    fieldNameKey: item.value,
                                                    fieldNameType: item.fieldType,
                                                    fieldDataValue: null,
                                                    fieldSpecial: item.special ? item.special : null,
                                                },
                                            ]);
                                        }}
                                        defaultValue={selectedChartFilterQueries[0].fieldNameKey}
                                        listMode={'SCROLLVIEW'}
                                        scrollViewProps={{
                                            nestedScrollEnabled: true,
                                        }}
                                    />

                                    <DropDownPicker
                                        arrowColor={'#BDBDBD'}
                                        arrowSize={23}
                                        arrowStyle={{ fontWeight: 'bold' }}
                                        style={{ marginLeft: '5%', width: 150 }}
                                        // items={[
                                        //     { label: 'Daily', value: 1 },
                                        //     { label: 'Weekly', value: 2 },
                                        //     { label: 'Monthly', value: 3 },
                                        //     //{ label: 'Hour of day', value: 4 },
                                        //     //{ label: 'Day of week', value: 5 }
                                        // ]}
                                        items={
                                            CHART_FIELD_COMPARE_DROPDOWN_LIST[
                                            CHART_TYPE.REPORT_PRODUCT_SALES
                                            ]
                                        }
                                        // placeholder={'Field name'}
                                        // placeholderStyle={{ color: 'black' }}
                                        dropDownStyle={{
                                            width: 150,
                                            marginLeft: '5%',
                                        }}
                                        itemStyle={{
                                            justifyContent: 'flex-start',
                                        }}
                                        globalTextStyle={{
                                            fontFamily: 'NunitoSans-SemiBold',
                                            // fontSize: 12,
                                            color: Colors.fontDark,
                                            marginLeft: 5,
                                        }}
                                        onChangeItem={(item) => {
                                            // setState({ sort: selectedSort }),
                                            //sortingFunc(selectedSort);
                                            setSelectedChartFilterQueries([
                                                {
                                                    ...selectedChartFilterQueries[0],
                                                    fieldCompare: item.value,
                                                    fieldDataValue: null,
                                                },
                                            ]);
                                        }}
                                        defaultValue={selectedChartFilterQueries[0].fieldCompare}
                                        listMode={'SCROLLVIEW'}
                                        scrollViewProps={{
                                            nestedScrollEnabled: true,
                                        }}
                                    />
                                </View>
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        margin: 10,
                                        marginVertical: 5,
                                        // height: 35,
                                    }}>
                                    <TextInput
                                        {...(selectedChartFilterQueries[0].fieldNameType ===
                                            CHART_FIELD_TYPE.DATETIME && {
                                            onPressIn: () => {
                                                setShowDateTimePickerFilter(true);
                                            },
                                        })}
                                        editable={
                                            selectedChartFilterQueries[0].fieldNameType ===
                                                CHART_FIELD_TYPE.DATETIME
                                                ? false
                                                : true
                                        }
                                        style={{
                                            width: 410,
                                            borderWidth: 1,
                                            borderColor: '#D2D2D2',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            color: 'black',
                                            fontFamily: 'NunitoSans-Regular',
                                            borderRadius: 8,
                                            paddingVertical: 3,
                                            paddingLeft: 15,
                                            height: 35,
                                        }}
                                        placeholder="Field Value"
                                        placeholderStyle={{
                                            color: 'black',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            fontFamily: 'NunitoSans-Regular',
                                        }}
                                        defaultValue={
                                            selectedChartFilterQueries[0].fieldNameType ===
                                                CHART_FIELD_TYPE.DATETIME
                                                ? moment(
                                                    selectedChartFilterQueries[0].fieldDataValue,
                                                ).format('DD/MM/YYYY')
                                                : selectedChartFilterQueries[0].fieldDataValue
                                                    ? selectedChartFilterQueries[0].fieldDataValue
                                                    : ''
                                        }
                                        onChangeText={(text) => {
                                            setSelectedChartFilterQueries([
                                                {
                                                    ...selectedChartFilterQueries[0],
                                                    fieldDataValue: text,
                                                },
                                            ]);
                                        }}
                                        keyboardType={
                                            selectedChartFilterQueries[0].fieldNameType ===
                                                CHART_FIELD_TYPE.STRING
                                                ? 'default'
                                                : 'numeric'
                                        }
                                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                    />
                                </View>

                                <View
                                    style={{
                                        flexDirection: 'row',
                                        justifyContent: 'flex-start',
                                        alignItems: 'center',
                                        margin: 10,
                                        height: 40,
                                        zIndex: -1,
                                    }} />
                                <View
                                    style={{
                                        borderWidth: 0.5,
                                        borderColor: '#D2D2D2',
                                        width: '100%',
                                        marginTop: 5,
                                        marginBottom: 5,
                                    }} />
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        justifyContent: 'flex-end',
                                        alignItems: 'center',
                                        margin: 10,
                                    }}>
                                    <TouchableOpacity
                                        style={{
                                            justifyContent: 'center',
                                            width: '30%',
                                        }}
                                        onPress={() => {
                                            setExpandSelection(false);

                                            setAppliedChartFilterQueries([]);
                                        }}>
                                        <View
                                            style={{
                                                justifyContent: 'center',
                                                width: '100%',
                                                flexDirection: 'row',
                                                borderWidth: 1,
                                                borderColor: '#BDBDBD',
                                                borderRadius: 5,
                                                height: 35,
                                                alignItems: 'center',
                                                backgroundColor: '#FFFFFF',
                                                height: windowHeight * 0.05,
                                            }}>
                                            <Text
                                                style={{
                                                    color: '#BDBDBD',
                                                    fontSize: 15,
                                                    fontFamily: 'Nunitosans-bold',
                                                    marginLeft: 7,
                                                }}>
                                                CANCEL
                                            </Text>
                                        </View>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        style={{
                                            justifyContent: 'center',
                                            width: '30%',
                                            marginLeft: '3%',
                                        }}
                                        onPress={() => {
                                            setExpandSelection(false);

                                            setAppliedChartFilterQueries(selectedChartFilterQueries);
                                        }}>
                                        <View
                                            style={{
                                                justifyContent: 'center',
                                                width: '100%',
                                                flexDirection: 'row',
                                                backgroundColor: '#4E9F7D',
                                                borderRadius: 5,
                                                height: 35,
                                                alignItems: 'center',
                                                height: windowHeight * 0.05,
                                            }}>
                                            <Text
                                                style={{
                                                    color: '#FFFFFF',
                                                    fontSize: 15,
                                                    fontFamily: 'Nunitosans-bold',
                                                    marginLeft: 7,
                                                }}>
                                                APPLY
                                            </Text>
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                {/* </ScrollViewGH> */}
                            </View>
                        ) : null}

                        {/* <Modal style={{ flex: 1 }} visible={visible} transparent={true} animationType="slide">
                      <View
                          style={{
                              backgroundColor: 'rgba(0,0,0,0.5)',
                              flex: 1,
                              justifyContent: 'center',
                              alignItems: 'center',
                              minHeight: windowHeight,
                          }}>
                          <View style={styles.confirmBox}>
                              <View style={{ flex: 3, borderBottomWidth: StyleSheet.hairlineWidth, justifyContent: 'center', alignItems: 'center' }}>
  
                              </View>
                              <View style={{ flex: 1, flexDirection: 'row' }}>
                                  <TouchableOpacity style={{ flex: 1, borderRightWidth: StyleSheet.hairlineWidth, justifyContent: 'center' }} onPress={() => { download() }}>
                                      <Text style={{ color: Colors.primaryColor, fontSize: 24, fontFamily: 'NunitoSans-SemiBold', textAlign: 'center' }}>Download</Text>
                                  </TouchableOpacity>
                                  <TouchableOpacity style={{ flex: 1, justifyContent: 'center' }} onPress={() => { setState({ visible: !visible }); }}>
                                      <Text style={{ color: Colors.descriptionColor, fontSize: 24, fontFamily: 'NunitoSans-SemiBold', textAlign: 'center' }}>Cancel</Text>
                                  </TouchableOpacity>
                              </View>
                          </View>
                      </View>
                  </Modal> */}
                        {/* <Modal style={{ flex: 1 }} visible={visible1} transparent={true} animationType="slide">
                      <View
                          style={{
                              backgroundColor: 'rgba(0,0,0,0.5)',
                              flex: 1,
                              justifyContent: 'center',
                              alignItems: 'center',
                              minHeight: windowHeight,
                          }}>
                          <View style={styles.confirmBox}>
                              <View style={{ flex: 3, borderBottomWidth: StyleSheet.hairlineWidth, justifyContent: 'center', alignItems: 'center' }}>
  
                              </View>
                              <View style={{ flex: 1, flexDirection: 'row' }}>
                                  <TouchableOpacity style={{ flex: 1, borderRightWidth: StyleSheet.hairlineWidth, justifyContent: 'center' }} onPress={() => { email() }}>
                                      <Text style={{ color: Colors.primaryColor, fontSize: 24, fontWeight: '400', textAlign: 'center' }}>Email</Text>
                                  </TouchableOpacity>
                                  <TouchableOpacity style={{ flex: 1, justifyContent: 'center' }} onPress={() => { setState({ visible1: !visible1 }); }}>
                                      <Text style={{ color: Colors.descriptionColor, fontSize: 24, fontWeight: '400', textAlign: 'center' }}>Cancel</Text>
                                  </TouchableOpacity>
                              </View>
                          </View>
                      </View>
                  </Modal> */}
                    </View>
                </ScrollView>
            </View>
        </View>
        // </UserIdleWrapper>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.highlightColor,
        flexDirection: 'row',
    },
    sidebar: {
        width: Dimensions.get('window').width * Styles.sideBarWidth,
        // shadowColor: '#000',
        // shadowOffset: {
        //   width: 0,
        //   height: 8,
        // },
        // shadowOpacity: 0.44,
        // shadowRadius: 10.32,

        // elevation: 16,
    },
    content: {
        padding: 20,
        width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
        backgroundColor: Colors.highlightColor,
    },
    headerLogo: {
        width: 112,
        height: 25,
        marginLeft: 10,
    },
    confirmBox: {
        // width: '30%',
        // height: '30%',
        // borderRadius: 30,
        // backgroundColor: Colors.whiteColor,
        width: Dimensions.get('window').width * 0.4,
        height: Dimensions.get('window').height * 0.4,
        borderRadius: 12,
        backgroundColor: Colors.whiteColor,
        justifyContent: 'space-between',
    },
    modalContainer: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: 'center',
        justifyContent: 'center',
    },
    modalView: {
        height: Dimensions.get('window').width * 0.2,
        width: Dimensions.get('window').width * 0.3,
        backgroundColor: Colors.whiteColor,
        borderRadius: 12,
        padding: Dimensions.get('window').width * 0.03,
        alignItems: 'center',
        justifyContent: 'center',
    },
    closeButton: {
        position: 'absolute',
        right: Dimensions.get('window').width * 0.02,
        top: Dimensions.get('window').width * 0.02,

        elevation: 1000,
        zIndex: 1000,
    },
    modalTitle: {
        alignItems: 'center',
        top: '20%',
        position: 'absolute',
    },
    modalBody: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    modalTitleText: {
        fontFamily: 'NunitoSans-Bold',
        textAlign: 'center',
        fontSize: 20,
    },
    modalDescText: {
        fontFamily: 'NunitoSans-SemiBold',
        fontSize: 18,
        color: Colors.fieldtTxtColor,
    },
    modalBodyText: {
        flex: 1,
        fontFamily: 'NunitoSans-SemiBold',
        fontSize: 25,
        width: '20%',
    },
    modalSaveButton: {
        width: Dimensions.get('window').width * 0.15,
        backgroundColor: Colors.fieldtBgColor,
        height: 40,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 8,

        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 1,

        marginVertical: 10,
    },
    modalSaveButton1: {
        width: Dimensions.get('window').width * 0.1,
        backgroundColor: Colors.fieldtBgColor,
        height: 40,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 8,

        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 1,

        marginVertical: 10,
    },
    ManageFilterBox: {
        //width: windowWidth * 0.33,
        height: Dimensions.get('window').height * 0.25,
        width:
            Platform.OS === 'ios'
                ? Dimensions.get('window').width * 0.42
                : Dimensions.get('window').width * 0.33,
        position: 'absolute',
        marginTop: '18.5%',
        marginLeft: '2%',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 8,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 10,
        zIndex: 1000,
        borderRadius: 10,
        borderTopLeftRadius: 0,
        borderWidth: 1,
        borderColor: '#E5E5E5',
        backgroundColor: Colors.whiteColor,
        //justifyContent: 'space-between'
    },
    ManageFilterBox1: {
        //width: windowWidth * 0.33,
        //height: windowHeight * 0.25,
        width:
            Platform.OS === 'ios'
                ? Dimensions.get('window').width * 0.42
                : Dimensions.get('window').width * 0.33,
        position: 'absolute',
        marginTop: '18.5%',
        marginLeft: '2%',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 8,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 10,
        zIndex: 1000,
        borderRadius: 10,
        borderTopLeftRadius: 0,
        borderWidth: 1,
        borderColor: '#E5E5E5',
        backgroundColor: Colors.whiteColor,
        //justifyContent: 'space-between'
    },
    submitText: {
        height:
            Platform.OS == 'ios'
                ? Dimensions.get('window').height * 0.06
                : Dimensions.get('window').height * 0.07,
        paddingVertical: 5,
        paddingHorizontal: 20,
        flexDirection: 'row',
        color: '#4cd964',
        textAlign: 'center',
        borderRadius: 10,
        borderWidth: 1,
        borderColor: Colors.primaryColor,
        justifyContent: 'center',
        alignContent: 'center',
        alignItems: 'center',
        marginRight: 10,
    },
    headerLeftStyle: {
        width: Dimensions.get('screen').width * 0.17,
        justifyContent: 'center',
        alignItems: 'center',
    },
});

export default ReportSalesUpsellingRevenue;
