import React, {
  Component,
  useReducer,
  useState,
  useEffect,
  useRef,
  createRef,
  useCallback,
  useMemo,
} from 'react';
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  Alert,
  TouchableOpacity,
  Dimensions,
  Platform,
  Switch,
  Modal,
  KeyboardAvoidingView,
  TextInput,
  ActivityIndicator,
  Picker,
  useWindowDimensions,
} from 'react-native';
//import firestore from '@react-native-firebase/firestore';
import firebase from "firebase"
import { Collections } from '../constant/firebase';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Feather';
import Ionicon from 'react-native-vector-icons/Ionicons';
import AntDesign from 'react-native-vector-icons/AntDesign';
import { ReactComponent as GCalendar } from "../assets/svg/GCalendar.svg";
import Entypo from 'react-native-vector-icons/Entypo';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { ReactComponent as ArrowLeft } from "../assets/svg/ArrowLeft.svg";
import { ReactComponent as ArrowRight } from "../assets/svg/ArrowRight.svg";
import { ReactComponent as FastFoodOutLine } from "../assets/svg/FastFoodOutLine.svg";
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { FlatList } from 'react-native-gesture-handler';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import Styles from '../constant/Styles';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage';
//import CheckBox from 'react-native-check-box';
import moment from 'moment';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { getOrderDiscountInfo, getOrderDiscountInfoInclOrderBased, getTransformForModalInsideNavigation, getTransformForScreenInsideNavigation, getCartItemPriceWithoutAddOn, getAddOnChoiceQuantity, getAddOnChoicePrice, } from '../util/common';
import { CommonStore } from '../store/commonStore';
import { OutletStore } from '../store/outletStore';
import { MerchantStore } from '../store/merchantStore';
import { UserStore } from '../store/userStore';
//import Upload from '../assets/svg/Upload';
//import Download from '../assets/svg/Download';
import {
  convertArrayToCSV,
  generateEmailReport,
  sortReportDataList,
} from '../util/common';
import {
  EMAIL_REPORT_TYPE,
  REPORT_SORT_FIELD_TYPE,
  TABLE_PAGE_SIZE_DROPDOWN_LIST,
  ORDER_TYPE,
  EXPAND_TAB_TYPE,
  PRODUCT_PRICE_TYPE,
  UNIT_TYPE_SHORT,
  APP_TYPE,
  SERVER_REPORT_FILTER_TYPE
} from '../constant/common';
//import RNFetchBlob from 'rn-fetch-blob';
//import { useKeyboard } from '../hooks';
import XLSX from 'xlsx';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
//import RNPickerSelect from 'react-native-picker-select';
import AsyncImage from '../components/asyncImage';
import Feather from 'react-native-vector-icons/Feather';
//import Tooltip from 'react-native-walkthrough-tooltip';
//import { useFocusEffect } from '@react-navigation/native';
//import UserIdleWrapper from '../components/userIdleWrapper';
import DropDownPicker from 'react-native-dropdown-picker';
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import "../constant/datePicker.css";
import { CSVLink } from "react-csv";
import personicon from "../assets/image/default-profile.png";
import headerLogo from "../assets/image/logo.png";
import "../constant/styles.css";
import Select from "react-select";
import { sliceUnicodeStringV2WithDots } from "../util/common";
import { TempStore } from '../store/tempStore';
import TextTicker from 'react-native-text-ticker';
import ApiClientReporting from '../util/ApiClientReporting';

const { nanoid } = require('nanoid');
//const RNFS = require('react-native-fs');

const ReportSalesVariant = (props) => {
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  /*const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, []),
  );*/

  ///////////////////////////////////////////////////////////

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  //const [keyboardHeight] = useKeyboard();
  const [list, setList] = useState([]);
  const [page, setPage] = useState(0);
  const [name, setName] = useState('Variants');
  const [visible, setVisible] = useState(false);
  const [visible1, setVisible1] = useState(false);
  const [isChecked, setIsChecked] = useState(false);
  const [isChecked1, setIsChecked1] = useState(false);
  const [endDate, setEndDate] = useState(new Date());
  const [startDate, setStartDate] = useState(new Date());
  const [oriList, setOriList] = useState([]);
  const [offset, setOffset] = useState(0);
  const [perPage, setPerPage] = useState(10);
  const [pageCount, setPageCount] = useState(0);
  const [detailsPageCount, setDetailsPageCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [currentDetailsPage, setCurrentDetailsPage] = useState(1);
  const [pageReturn, setPageReturn] = useState(1);
  const [day, setDay] = useState(false);
  const [pick, setPick] = useState(null);
  const [pick1, setPick1] = useState(null);
  const [search, setSearch] = useState('');
  const [list1, setList1] = useState(true);
  const [searchList, setSearchList] = useState(false);
  const [lists, setLists] = useState([]);

  const [loading, setLoading] = useState(false);

  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  const [showDateTimePicker1, setShowDateTimePicker1] = useState(false);
  const [pickerMode, setPickerMode] = useState('datetime');
  const [rev_date, setRev_date] = useState(
    moment().subtract(6, 'days').startOf('day'),
  );
  const [rev_date1, setRev_date1] = useState(
    moment().endOf(Date.now()).endOf('day'),
  );

  const reportOutletShifts = OutletStore.useState((s) => s.reportOutletShifts);
  const reportDisplayType = OutletStore.useState((s) => s.reportDisplayType);

  const historyStartDate = CommonStore.useState(s => s.historyStartDate);
  const historyEndDate = CommonStore.useState(s => s.historyEndDate);

  const [addOnSales, setAddOnSales] = useState([]);

  const [selectedItemSummary, setSelectedItemSummary] = useState({});

  const [expandDetailsDict, setExpandDetailsDict] = useState({});
  const crmUsers = OutletStore.useState((s) => s.crmUsers);

  // const allOutletsItemAddOn = CommonStore.useState(
  //   (s) => s.allOutletsItemAddOn,
  // );

  // 2023-04-14
  const payoutTransactions = OutletStore.useState(s => s.payoutTransactions.filter(p => p.v >= '3')); // only check for v3
  const payoutTransactionsExtend = OutletStore.useState(s => s.payoutTransactionsExtend.filter(p => p.v >= '3')); // only check for v3

  const ptTimestamp = OutletStore.useState(s => s.ptTimestamp);
  const pteTimestamp = OutletStore.useState(s => s.pteTimestamp);

  const allOutletsUserOrdersDone = TempStore.useState((s) => s.allOutletUserOrderDoneProcessed);
  const allOutletsUserOrdersDoneRaw = OutletStore.useState((s) => s.allOutletsUserOrdersDone,);
  // const [allOutletsUserOrdersDone, setAllOutletsUserOrdersDone] = useState([]);

  // const allOutletsUserOrdersDone = OutletStore.useState(
  //   (s) => s.allOutletsUserOrdersDone,
  // );
  const [switchMerchant, setSwitchMerchant] = useState(false);
  const outletItems = OutletStore.useState((s) => s.outletItems);
  const outletItemsDict = OutletStore.useState((s) => s.outletItemsDict);

  const outletCategories = OutletStore.useState((s) => s.outletCategories);
  const outletCategoriesDict = OutletStore.useState(
    (s) => s.outletCategoriesDict,
  );

  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const currOutlet = MerchantStore.useState(s => s.currOutlet);
  const allOutletsRaw = MerchantStore.useState((s) => s.allOutlets);

  const userName = UserStore.useState((s) => s.name);
  const merchantName = MerchantStore.useState((s) => s.name);

  const [exportEmail, setExportEmail] = useState('');
  const [CsvData, setCsvData] = useState([]);

  const [pushPagingToTop, setPushPagingToTop] = useState(false);

  const [showDetails, setShowDetails] = useState(false);
  const [addOnSalesDetails, setAddOnSalesDetails] = useState([]);

  const [exportModalVisibility, setExportModalVisibility] = useState(false);

  const [currReportSummarySort, setCurrReportSummarySort] = useState('');
  const [currReportDetailsSort, setCurrReportDetailsSort] = useState('');

  const merchantId = UserStore.useState((s) => s.merchantId);
  const isLoading = CommonStore.useState((s) => s.isLoading);
  const [isCsv, setIsCsv] = useState(false);
  const [isExcel, setIsExcel] = useState(false);
  const [detailsTitle, setDetailsTitle] = useState('');

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );

  const [saleTip, setSaleTip] = useState(false);
  const [netSaleTip, setNetSaleTip] = useState(false);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  const [filterAppType, setFilterAppType] = useState([APP_TYPE.WEB_ORDER, APP_TYPE.MERCHANT, APP_TYPE.USER, APP_TYPE.WAITER]);


  const [openFA, setOpenFA] = useState(false);
  const [openPage, setOpenPage] = useState(false);
  const [openOS, setOpenOS] = useState(false);
  // const [selectedOutletList, setSelectedOutletList] = useState([]); // multi-outlets
  const selectedOutletList = CommonStore.useState((s) => s.reportOutletIdList);
  const isMasterAccount = UserStore.useState((s) => s.isMasterAccount);
  const [outletDropdownList, setOutletDropdownList] = useState([]);
  const [selectedOutletId, setSelectedOutletId] = useState("");
  const [allOutlets, setAllOutlets] = useState([]);

  const [allOutletsItemAddOn, setAllOutletsItemAddOn] = useState([]);

  const [isTableApiLoading, setIsTableApiLoading] = useState(true);
  const reportOutletIdList = CommonStore.useState((s) => s.reportOutletIdList);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Get outlet items from firestore
        const addOnsSnapshot = await firebase
          .firestore()
          .collection(Collections.OutletItemAddOn)
          .where('outletId', 'in', selectedOutletList)
          .get();

        const items = addOnsSnapshot.docs.map(doc => ({
          ...doc.data(),
          uniqueId: doc.id
        }));
        setAllOutletsItemAddOn(items);
      } 
      catch (error) {
        console.error('Error fetching addons data:', error);
      }
    };

    if (selectedOutletList.length > 0) {
      fetchData();
    }
  }, [selectedOutletList]);

  useEffect(() => {
    if (global.reportTableTimerId) {
      clearTimeout(global.reportTableTimerId);
    }

    global.reportTableTimerId = setTimeout(() => {
      setIsTableApiLoading(true);

      if (
        currOutletId !== '' &&
        allOutlets.length > 0 &&
        allOutletsItemAddOn.length > 0 &&
        outletItems.length > 0
      ) {
        //   setHistoryOrders(allOutletsUserOrdersDone.filter(order => order.outletId === currOutletId));

        var addOnSalesDict = {};

        var processedCartItemDict = {};

        if (allOutletsUserOrdersDone.length > 0) {
          for (var index = 0; index < allOutletsItemAddOn.length; index++) {
            if (
              allOutletsItemAddOn[index].minSelect !== undefined &&
              allOutletsItemAddOn[index].maxSelect !== undefined
            ) {
              var addOnSaleRecord = {
                summaryId: nanoid(),
                addOnName: allOutletsItemAddOn[index].name || 'N/A',
                // addOnChoices: 'N/A',
                addOnChoices: [],
                totalItemsSold: 0,
                totalSales: 0,
                totalSalesReturn: 0,
                totalDiscount: 0,
                discount: 0,
                averageCost: 0,
                itemNetSales: 0,
                averageNetSales: 0,
                gp: 0,
                detailsList: [],
              };

              if (
                addOnSalesDict[allOutletsItemAddOn[index].name] === undefined
              ) {
                addOnSalesDict[allOutletsItemAddOn[index].name] =
                  addOnSaleRecord;
              }

              var includedOrderIdDict = {};

              // will optimize this flow in future....
              for (var i = 0; i < allOutletsUserOrdersDone.length; i++) {
                if (
                  // allOutletsUserOrdersDone[i].outletId === currOutletId && // no need first
                  // isMasterAccount && (selectedOutletList.includes(allOutletsUserOrdersDone[i].outletId))
                  // ||
                  // (!isMasterAccount && allOutletsUserOrdersDone[i].outletId === currOutletId)
                  // &&
                  moment(historyStartDate).isSameOrBefore(
                    allOutletsUserOrdersDone[i].createdAt,
                  ) &&
                  moment(historyEndDate).isAfter(
                    allOutletsUserOrdersDone[i].createdAt,
                  )
                  // && (filterAppType.includes(allOutletsUserOrdersDone[i].appType) || filterAppType.length === 0)
                ) {
                  for (
                    var j = 0;
                    j < allOutletsUserOrdersDone[i].cartItems.length;
                    j++
                  ) {
                    // const choicesList = Object.entries(allOutletsUserOrdersDone[i].cartItems[j].choices).map(([key, value]) => ({ key: key, value: value }));

                    // if (allOutletsUserOrdersDone[i].cartItems[j].choices[allOutletsItemAddOn[index].uniqueId]) {

                    // }

                    for (
                      var k = 0;
                      k <
                      allOutletsUserOrdersDone[i].cartItems[j].addOns.length;
                      k++
                    ) {
                      // find it by name

                      if (
                        allOutletsUserOrdersDone[i].cartItems[j].addOns[k]
                          .name === allOutletsItemAddOn[index].name
                      ) {
                        // addOnSalesDict[allOutletsItemAddOn[index].uniqueId].detailsList.push(allOutletsUserOrdersDone[i]);

                        if (
                          // // !addOnSalesDict[
                          // //   allOutletsItemAddOn[index].name
                          // // ].detailsList.find(
                          // //   (order) =>
                          // //     order.uniqueId ===
                          // //     allOutletsUserOrdersDone[i].uniqueId,
                          // // )
                          // !processedCartItemDict[
                          // allOutletsUserOrdersDone[i].cartItems[j].itemId +
                          // allOutletsUserOrdersDone[i].cartItems[j]
                          //   .cartItemDate +
                          // JSON.stringify(
                          //   allOutletsUserOrdersDone[i].cartItems[j].addOns[
                          //     k
                          //   ].name,
                          // ) +
                          // JSON.stringify(
                          //   allOutletsUserOrdersDone[i].cartItems[j].addOns[
                          //     k
                          //   ].choiceNames,
                          // )
                          // ]
                          true
                        ) {
                          // add if not existed

                          // processedCartItemDict[
                          //   allOutletsUserOrdersDone[i].cartItems[j].itemId +
                          //   allOutletsUserOrdersDone[i].cartItems[j]
                          //     .cartItemDate +
                          //   JSON.stringify(
                          //     allOutletsUserOrdersDone[i].cartItems[j].addOns[
                          //       k
                          //     ].name,
                          //   ) +
                          //   JSON.stringify(
                          //     allOutletsUserOrdersDone[i].cartItems[j].addOns[
                          //       k
                          //     ].choiceNames,
                          //   )
                          // ] = true;

                          // in future need include uniqueid in addOns, in case of clashing of same name
                          const cartItem =
                            allOutletsUserOrdersDone[i].cartItems[j];
                          // const categoryId = outletItemsDict[cartItem.itemId].categoryId;

                          // addOnSalesDict[allOutletsItemAddOn[i].uniqueId].totalItemsSold += cartItem.quantity;
                          // addOnSalesDict[allOutletsItemAddOn[i].uniqueId].totalSales += cartItem.price * cartItem.quantity;
                          // addOnSalesDict[allOutletsItemAddOn[i].uniqueId].itemNetSales += cartItem.price * cartItem.quantity;

                          addOnSalesDict[
                            allOutletsItemAddOn[index].name
                          ].totalItemsSold += cartItem.quantity;
                          addOnSalesDict[
                            allOutletsItemAddOn[index].name
                          ].totalSales += getAddOnChoicePrice(cartItem.addOns[k], cartItem);

                          //10/18 add discount price but sales is 0 discount precentage will be infinity
                          //addOnSalesDict[allOutletsItemAddOn[index].name].totalDiscount += allOutletsUserOrdersDone[i].discount + getOrderDiscountInfo(allOutletsUserOrdersDone[i])
                          addOnSalesDict[
                            allOutletsItemAddOn[index].name
                          ].itemNetSales += getAddOnChoicePrice(cartItem.addOns[k], cartItem);
                          var variantPrice = addOnSaleRecord.totalSales;

                          // for (var l = 0; l < allOutletsUserOrdersDone[i].cartItems[j].addOns[k].choiceNames.length; l++) {
                          //     if (!addOnSalesDict[allOutletsItemAddOn[index].uniqueId].addOnChoices.toLowerCase()
                          //         .includes(allOutletsUserOrdersDone[i].cartItems[j].addOns[k].choiceNames[l].toLowerCase())) {
                          //         addOnSalesDict[allOutletsItemAddOn[index].uniqueId].addOnChoices += `${addOnSalesDict[allOutletsItemAddOn[index].uniqueId].addOnChoices.length > 0 ? '\n' : ''} ${allOutletsUserOrdersDone[i].cartItems[j].addOns[k].choiceNames[l]}`;
                          //     }
                          // }

                          /////////////////////////////////////

                          for (var m = 0; m < allOutletsUserOrdersDone[i].cartItems[j].addOns[k].choiceNames.length; m++) {
                            var choiceName = allOutletsUserOrdersDone[i].cartItems[j].addOns[k].choiceNames[m];
                            var addOnName = allOutletsItemAddOn[index].name;

                            // Ensure addOnSalesDict[addOnName] is initialized
                            if (!addOnSalesDict[addOnName]) {
                              addOnSalesDict[addOnName] = {
                                addOnChoices: [],
                                choiceSales: {},
                              };
                            }

                            // Ensure addOnChoices array exists
                            if (!addOnSalesDict[addOnName].addOnChoices) {
                              addOnSalesDict[addOnName].addOnChoices = [];
                            }

                            // Ensure choiceSales object exists
                            if (!addOnSalesDict[addOnName].choiceSales) {
                              addOnSalesDict[addOnName].choiceSales = {};
                            }

                            // Add choiceName to addOnChoices if not already included
                            if (!addOnSalesDict[addOnName].addOnChoices.includes(choiceName)) {
                              addOnSalesDict[addOnName].addOnChoices.push(choiceName);
                            }

                            // Ensure choiceSales[choiceName] is initialized
                            if (!addOnSalesDict[addOnName].choiceSales[choiceName]) {
                              addOnSalesDict[addOnName].choiceSales[choiceName] = {
                                totalSales: 0,
                                totalDiscount: 0,
                                totalItemsSold: 0,
                                tax: 0,
                                sc: 0,
                                salesReturn: 0,
                                netSales: 0,
                              };
                            }

                            // Accumulate sales and discount per choice
                            let choicePrice = getAddOnChoicePrice(cartItem.addOns[k], cartItem);
                            addOnSalesDict[addOnName].choiceSales[choiceName].totalSales += choicePrice;
                            addOnSalesDict[addOnName].choiceSales[choiceName].totalItemsSold += cartItem.quantity;

                            const calculatedDiscount = getOrderDiscountInfoInclOrderBased(allOutletsUserOrdersDone[i]);
                            addOnSalesDict[addOnName].choiceSales[choiceName].totalDiscount += calculatedDiscount;

                            // ✅ Add SC & Tax from detailsList
                            let orderDetails = allOutletsUserOrdersDone[i]; // detailsList[i]

                            addOnSalesDict[addOnName].choiceSales[choiceName].sc += orderDetails.sc || 0;
                            addOnSalesDict[addOnName].choiceSales[choiceName].tax += orderDetails.tax || 0;
                            addOnSalesDict[addOnName].choiceSales[choiceName].salesReturn += orderDetails.salesReturn || 0;
                            addOnSalesDict[addOnName].choiceSales[choiceName].netSales += choicePrice;
                          }

                          // addOnSalesDict[
                          //   allOutletsItemAddOn[index].name
                          // ].addOnChoices =
                          //   allOutletsUserOrdersDone[i].cartItems[j].addOns[
                          //     k
                          //   ].choiceNames.join(', ');

                          /////////////////////////////////////

                          if (
                            !includedOrderIdDict[
                            allOutletsUserOrdersDone[i].uniqueId
                            ]
                          ) {
                            includedOrderIdDict[
                              allOutletsUserOrdersDone[i].uniqueId
                            ] = true;

                            const calculatedDiscount = getOrderDiscountInfoInclOrderBased(allOutletsUserOrdersDone[i]);

                            addOnSalesDict[
                              allOutletsItemAddOn[index].name
                            ].detailsList.push({
                              // ...allOutletsUserOrdersDone[i],
                              id: allOutletsUserOrdersDone[i].uniqueId,
                              discountPercentage: parseFloat(
                                isFinite(
                                  calculatedDiscount /
                                  (allOutletsUserOrdersDone[i].finalPrice +
                                    calculatedDiscount),
                                )
                                  ? (calculatedDiscount /
                                    (allOutletsUserOrdersDone[i].finalPrice +
                                      calculatedDiscount)) *
                                  100
                                  : 0,
                              ),
                              itemPrice: cartItem.price,
                            });
                          }
                        }
                      }
                    }
                  }

                  if (allOutletsUserOrdersDone[i].cartItemsCancelled) {
                    for (
                      var j = 0;
                      j < allOutletsUserOrdersDone[i].cartItemsCancelled.length;
                      j++
                    ) {
                      // const choicesList = Object.entries(allOutletsUserOrdersDone[i].cartItems[j].choices).map(([key, value]) => ({ key: key, value: value }));

                      // if (allOutletsUserOrdersDone[i].cartItems[j].choices[allOutletsItemAddOn[index].uniqueId]) {

                      // }

                      for (
                        var k = 0;
                        k <
                        allOutletsUserOrdersDone[i].cartItemsCancelled[j].addOns
                          .length;
                        k++
                      ) {
                        // find it by name

                        if (
                          allOutletsUserOrdersDone[i].cartItemsCancelled[j]
                            .addOns[k].name === allOutletsItemAddOn[index].name
                        ) {
                          // in future need include uniqueid in addOns, in case of clashing of same name
                          const cartItem =
                            allOutletsUserOrdersDone[i].cartItemsCancelled[j];

                          addOnSalesDict[
                            allOutletsItemAddOn[index].name
                          ].totalSalesReturn += getAddOnChoicePrice(cartItem.addOns[k], cartItem);

                          // addOnSalesDict[allOutletsItemAddOn[index].uniqueId].totalItemsSold += 1;
                          // addOnSalesDict[allOutletsItemAddOn[index].uniqueId].totalSales += cartItem.addOns[k].prices.reduce((accum, price) => accum + price, 0);
                          // addOnSalesDict[allOutletsItemAddOn[index].uniqueId].itemNetSales += cartItem.addOns[k].prices.reduce((accum, price) => accum + price, 0);

                          // for (var l = 0; l < allOutletsUserOrdersDone[i].cartItemsCancelled[j].addOns[k].choiceNames.length; l++) {
                          //     if (!addOnSalesDict[allOutletsItemAddOn[index].uniqueId].addOnChoices.toLowerCase()
                          //         .includes(allOutletsUserOrdersDone[i].cartItemsCancelled[j].addOns[k].choiceNames[l].toLowerCase())) {
                          //         addOnSalesDict[allOutletsItemAddOn[index].uniqueId].addOnChoices += `${addOnSalesDict[allOutletsItemAddOn[index].uniqueId].addOnChoices.length > 0 ? '\n' : ''} ${allOutletsUserOrdersDone[i].cartItems[j].cartItemsCancelled[k].choiceNames[l]}`;
                          //     }
                          // }

                          // addOnSalesDict[allOutletsItemAddOn[index].uniqueId].detailsList.push({
                          //     ...allOutletsUserOrdersDone[i],
                          //     discountPercentage: parseFloat(isFinite(allOutletsUserOrdersDone[i].finalPrice / allOutletsUserOrdersDone[i].discount) ? allOutletsUserOrdersDone[i].finalPrice / allOutletsUserOrdersDone[i].discount * 100 : 0),
                          // });
                        }
                      }
                    }
                  }
                }
              }
              if (addOnSaleRecord.totalItemsSold > 0) {
                addOnSaleRecord.itemNetSales =
                  Math.round(addOnSaleRecord.itemNetSales * 20) / 20;
                addOnSaleRecord.averageNetSales =
                  addOnSaleRecord.itemNetSales / addOnSaleRecord.totalItemsSold;
              }
            }
          }
        }

        const addOnSalesTemp = Object.entries(addOnSalesDict).map(
          ([key, value]) => ({
            ...value,
            addOnChoices: value.addOnChoices.join(', '),
            addOnChoiceSplit: value.addOnChoices.join('\n'),
          }),
        );

        const dummyData = ['Download Test'];

        setState({ CsvData: dummyData });

        setAddOnSales(addOnSalesTemp);

        //setCurrentPage(1);
        setPageCount(Math.ceil(addOnSalesTemp.length / perPage));

        setShowDetails(false);
      }

      setIsTableApiLoading(false);
    }, 2000);
  }, [
    allOutletsUserOrdersDone,
    outletItems,
    outletItemsDict,
    allOutletsItemAddOn,
    currOutletId,
    allOutlets,
    historyStartDate,
    historyEndDate,
    perPage,
    // filterAppType,

    selectedOutletList,
    isMasterAccount,
  ]);

  // useEffect(() => {
  //   const fetchData = async () => {
  //     OutletStore.update(s => {
  //       s.reportingApiLoading = true;
  //     });
      
  //     setIsTableApiLoading(true);

  //     // Calculate pagination for categories instead of days
  //     const startIndex = (currentPage - 1) * perPage;
  //     const endIndex = Math.min(startIndex + perPage, addOnSales.length);
  //     const filterIdList = addOnSales.slice(startIndex, endIndex).map(item => item.name);

  //     // Calculate the start and end dates for the API request
  //     let startReportDate = moment(historyStartDate).startOf('day');
  //     let endReportDate = moment(historyEndDate).endOf('day');

  //     try {
  //       const requestBody = {
  //         isMasterAccount,
  //         merchantId,
  //         outletId: currOutletId,
  //         reportOutletIdList,
  //         startDate: startReportDate,
  //         endDate: endReportDate,
  //         filterType: SERVER_REPORT_FILTER_TYPE.VARIANT_SALES,
  //         filterIdList: filterIdList,
  //       };

  //       console.log('API Request Body:', requestBody);

  //       const data = await ApiClientReporting.POST(API.getOutletUserOrderDoneProcessed, requestBody);

  //       console.log('data returned', data);

  //       TempStore.update(s => {
  //         s.allOutletUserOrderDoneProcessed = data.allOutletUserOrderDoneProcessed || [];
  //       });
  //     } 
  //     catch (error) {
  //       console.error('Error fetching sales variant report:', error);
  //     }

  //     OutletStore.update(s => {
  //       s.reportingApiLoading = false;
  //     });

  //     setTimeout(() => {
  //       setIsTableApiLoading(false);
  //     }, 1000);
  //   };

  //   if (global.getRazerPayoutTransactionsParsedTimer) {
  //     clearTimeout(global.getRazerPayoutTransactionsParsedTimer);
  //   }

  //   global.getRazerPayoutTransactionsParsedTimer = setTimeout(() => {
  //     fetchData();
  //   }, 1000);
  // }, [
  //   reportOutletIdList,
  //   historyStartDate,
  //   historyEndDate,
  //   perPage,
  //   currentPage,
  //   isMasterAccount,
  // ]);

  useEffect(() => {
    setAllOutlets(allOutletsRaw.filter(outlet => {
      if (outlet.uniqueId === currOutletId || isMasterAccount) {
        return true;
      }
      else {
        return false;
      }
    }));
  }, [allOutletsRaw, currOutletId, isMasterAccount]);

  useEffect(() => {
    setOutletDropdownList(
      allOutlets.map((item) => ({
        label: item.name,
        value: item.uniqueId,
      })),
    );
    if (selectedOutletId === "" && allOutlets.length > 0 && currOutletId) {
      setSelectedOutletId(currOutletId);

      // setSelectedOutletList([currOutletId]);
      // CommonStore.update((s) => {
      //   s.reportOutletIdList = [currOutletId];
      // })
    }
  }, [allOutlets, currOutletId]);

  useEffect(() => {
    if (showDetails && selectedItemSummary.detailsList) {
      setAddOnSalesDetails(selectedItemSummary.detailsList.map(details => {
        const findOrder = allOutletsUserOrdersDone.find(order => order.uniqueId === details.id);

        return {
          ...findOrder,
          ...details,
        };
      }));

      setPageReturn(currentPage);
      // console.log('currentPage value is');
      // console.log(currentPage);
      setCurrentDetailsPage(1);

      setDetailsPageCount(
        Math.ceil(selectedItemSummary.detailsList.length / perPage),
      );
    }
  }, [showDetails, selectedItemSummary, perPage, filterAppType,]);

  useEffect(() => {
    var allOutletsUserOrdersDoneTemp = [];

    var currDateTime = moment().valueOf();

    if (
      // global.payoutTransactions.length > 0
      true
    ) {
      for (var j = 0; j < global.payoutTransactions.length; j++) {
        allOutletsUserOrdersDoneTemp = allOutletsUserOrdersDoneTemp.concat(
          (filterAppType && filterAppType.length > 0)
            ?
            (global.payoutTransactions[j].userOrdersFigures ? global.payoutTransactions[j].userOrdersFigures : [])
            :
            (global.payoutTransactions[j].userOrdersFigures ? global.payoutTransactions[j].userOrdersFigures : []).filter((item) =>
              //filterChartItems(item, appliedChartFilterQueriesLineChart),
              (filterAppType.includes(item.appType))
            )
        );
      }

      for (var j = 0; j < global.payoutTransactionsExtend.length; j++) {
        allOutletsUserOrdersDoneTemp = allOutletsUserOrdersDoneTemp.concat(
          (filterAppType && filterAppType.length > 0)
            ?
            (global.payoutTransactionsExtend[j].userOrdersFigures ? global.payoutTransactionsExtend[j].userOrdersFigures : [])
            :
            (global.payoutTransactionsExtend[j].userOrdersFigures ? global.payoutTransactionsExtend[j].userOrdersFigures : []).filter((item) =>
              //filterChartItems(item, appliedChartFilterQueriesLineChart),
              (filterAppType.includes(item.appType))
            )
        );
      }

      const startTime = moment().set({ hour: 0, minute: 0, second: 0 }); // Set the start time to 12:00am
      const endTime = moment().set({ hour: 5, minute: 55, second: 0 }); // Set the end time to 05:55am

      for (var i = 0; i < allOutletsUserOrdersDoneRaw.length; i++) {
        if (
          moment(allOutletsUserOrdersDoneRaw[i].createdAt).isSame(currDateTime, 'day')
          ||
          (
            moment(currDateTime).isBetween(startTime, endTime)
            &&
            moment(currDateTime).add(-1, 'day').isSame(allOutletsUserOrdersDoneRaw[i].createdAt, 'day')
          )
        ) {
          if (filterAppType.includes(allOutletsUserOrdersDoneRaw[i].appType)) {
            if (!allOutletsUserOrdersDoneTemp.find(order => order.uniqueId === allOutletsUserOrdersDoneRaw[i].uniqueId)) {
              allOutletsUserOrdersDoneTemp.push(allOutletsUserOrdersDoneRaw[i]);
            }
          }
        }
      }
    }
    else {
      allOutletsUserOrdersDoneTemp = allOutletsUserOrdersDoneRaw.filter((item) =>
        (filterAppType.includes(item.appType))
      );
    }

    // 2025-05-09 - comment first
    // setAllOutletsUserOrdersDone(allOutletsUserOrdersDoneTemp);
  }, [
    allOutletsUserOrdersDoneRaw,

    // payoutTransactions,
    // payoutTransactionsExtend,

    ptTimestamp,
    pteTimestamp,

    //isMounted,
    reportOutletShifts,
    reportDisplayType,

    filterAppType,
  ]);

  const setState = () => { };

  // navigation.dangerouslyGetParent().setOptions({
  //     tabBarVisible: false,
  // });

  const currOutletShiftStatus = OutletStore.useState(
    (s) => s.currOutletShiftStatus
  );
  // const [outletDropdownList, setOutletDropdownList] = useState([]);
  // const [selectedOutletList, setSelectedOutletList] = useState([]); // multi-outlets

  // var outletNames = [];

  // for (var i = 0; i < allOutlets.length; i++) {
  //     for (var j = 0; j < selectedOutletList.length; j++) {
  //         if (selectedOutletList.includes(allOutlets[i].uniqueId)) {
  //             outletNames.push(allOutlets[i].name);
  //             break;
  //         }
  //     }
  // }

  // useEffect(() => {
  //     setOutletDropdownList(
  //         allOutlets.map((item) => {
  //             return { label: item.name, value: item.uniqueId };
  //         })
  //     );
  // }, [allOutlets]);

  var targetOutletDropdownListTemp = allOutlets.map((outlet) => ({
    label: sliceUnicodeStringV2WithDots(outlet.name, 20),
    value: outlet.uniqueId,
  }));

  // useEffect(() => {
  //   CommonStore.update((s) => {
  //     s.outletSelectDropdownView = () => {
  //       return (
  //         <View
  //           style={{
  //             flexDirection: "row",
  //             alignItems: "center",
  //             borderRadius: 8,
  //             width: 200,
  //             backgroundColor: "white",
  //           }}
  //         >
  //           {currOutletId.length > 0 &&
  //             allOutlets.find((item) => item.uniqueId === currOutletId) ? (
  //             <MultiSelect
  //               clearable={false}
  //               singleSelect={true}
  //               defaultValue={currOutletId}
  //               placeholder={"Choose Outlet"}
  //               onChange={(value) => {
  //                 if (value) { // if choose the same option again, value = ''
  //                   MerchantStore.update((s) => {
  //                     s.currOutletId = value;
  //                     s.currOutlet =
  //                       allOutlets.find(
  //                         (outlet) => outlet.uniqueId === value
  //                       ) || {};
  //                   });
  //                 }

  //                 CommonStore.update((s) => {
  //                   s.shiftClosedModal = false;
  //                 });
  //               }}
  //               options={targetOutletDropdownListTemp}
  //               className="msl-varsHEADER"
  //             />
  //           ) : (
  //             <ActivityIndicator size={"small"} color={Colors.whiteColor} />
  //           )}

  //           {/* <Select

  //                           placeholder={"Choose Outlet"}
  //                           onChange={(items) => {
  //                               setSelectedOutletList(items);
  //                           }}
  //                           options={outletDropdownList}
  //                           isMulti
  //                       /> */}
  //         </View>
  //       );
  //     };
  //   });
  // }, [allOutlets, currOutletId, isLoading, currOutletShiftStatus]);

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <img src={headerLogo} width={124} height={26} />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            justifyContent: "center",
            alignItems: "center",
          },
          windowWidth >= 768 && switchMerchant
            ? { right: windowWidth * 0.1 }
            : {},
          windowWidth <= 768
            ? { right: 20 }
            : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'center',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Variant Sales Report
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView && outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }} />
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >
            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <img
              src={personicon}
              width={windowHeight * 0.035}
              height={windowHeight * 0.035}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  /* const email = () => {
        var body = {
            email: '<EMAIL>',
            data: list
        }
        ApiClient.POST(API.emailReportPdf, body, false).then((result) => {
            try {
                if (result != null) {
                    Alert.alert(
                        'Congratulation!',
                        'You Have Successfull',
                        [
                            {
                                text: 'OK',
                                onPress: () => { setState({ visible1: false }) },
                            },
                        ],
                        { cancelable: false },
                    );
                }
            } catch (error) {
                Alert.alert('Failed', [{ text: 'OK', onPress: () => { } }], {
                    cancelable: false,
                });
            }
        })
    }

    const download = () => {
        var body = {
            data: list
        }
        ApiClient.POST(API.generateReportPDF, body, false).then((result) => {
            try {
                if (result != null) {
                    Alert.alert(
                        'Congratulation!',
                        'You Have Successfull',
                        [
                            {
                                text: 'OK',
                                onPress: () => { setState({ visible: false }) },
                            },
                        ],
                        { cancelable: false },
                    );
                }
            } catch (error) {
                Alert.alert('Failed', [{ text: 'OK', onPress: () => { } }], {
                    cancelable: false,
                });
            }
        })
    } */

  const searchBarItem = () => {
    ApiClient.GET(
      `${API.getSalesByVariantsSearchBar + 1}&queryName=${search}`,
    ).then((result) => {
      setState({ lists: result });
    });
  };

  const add = async () => {
    if (page + 1 < pageCount) {
      await setState({ page: page + 1, currentPage: currentPage + 1 });
      // console.log(page);
      var e = page;
      next(e);
    }
  };

  const next = (e) => {
    const offset = e * perPage;
    setState({ offset });
    loadMoreData();
  };

  const less = async () => {
    if (page > 0) {
      await setState({ page: page - 1, currentPage: currentPage - 1 });
      // console.log(page);
      var y = page;
      pre(y);
    }
  };

  const pre = (y) => {
    const offset = y * perPage;
    setState({ offset });
    loadMoreData();
  };

  const nextPage = () => {
    setCurrentPage(currentPage + 1 > pageCount ? currentPage : currentPage + 1);
  };

  const prevPage = () => {
    setCurrentPage(currentPage - 1 < 1 ? currentPage : currentPage - 1);
  };

  const nextDetailsPage = () => {
    setCurrentDetailsPage(
      currentDetailsPage + 1 > detailsPageCount
        ? currentDetailsPage
        : currentDetailsPage + 1,
    );
    //setCurrentDetailsPage(currentDetailsPage + 1);
  };

  const prevDetailsPage = () => {
    setCurrentDetailsPage(
      currentDetailsPage - 1 < 1 ? currentDetailsPage : currentDetailsPage - 1,
    );
  };

  const prevPage2 = async () => {
    if (currentPage > 1) {
      OutletStore.update(s => {
        s.reportingApiLoading = true;
      });
      setIsTableApiLoading(true);

      // Calculate pagination for categories in the previous page
      const prevPage = currentPage - 1;
      const startIndex = (prevPage - 1) * perPage;
      const endIndex = Math.min(startIndex + perPage, addOnSales.length);
      const filterList = addOnSales.slice(startIndex, endIndex).map(item => item.uniqueId);

      try {
        const requestBody = {
          isMasterAccount,
          merchantId,
          outletId: currOutletId,
          reportOutletIdList,
          startDate: moment(historyStartDate).startOf('day'),
          endDate: moment(historyEndDate).endOf('day'),
          filterType: SERVER_REPORT_FILTER_TYPE.VARIANT_SALES,
          filterIdList: filterList,
        };

        console.log('Prev Page API Request Body:', requestBody);

        const data = await ApiClientReporting.POST(API.getOutletUserOrderDoneProcessed, requestBody);

        console.log('data returned', data);

        OutletStore.update(s => {
          s.reportingApiLoading = false;
        });

        setTimeout(() => {
          setIsTableApiLoading(false);
        }, 1000);

        TempStore.update(s => {
          s.allOutletUserOrderDoneProcessed = data.allOutletUserOrderDoneProcessed;
        });

        setCurrentPage(currentPage - 1);
      } catch (error) {
        console.error('Error fetching previous page data:', error);

        OutletStore.update(s => {
          s.reportingApiLoading = false;
        });

        setTimeout(() => {
          setIsTableApiLoading(false);
        }, 1000);
      }
    }
  };

  const nextPage2 = async () => {
    if (currentPage < pageCount) {
      OutletStore.update(s => {
        s.reportingApiLoading = true;
      });
      setIsTableApiLoading(true);

      // Calculate pagination for categories in the next page
      const nextPage = currentPage + 1;
      const startIndex = (nextPage - 1) * perPage;
      const endIndex = Math.min(startIndex + perPage, addOnSales.length);
      const filterList = addOnSales.slice(startIndex, endIndex).map(item => item.uniqueId);

      try {
        const requestBody = {
          isMasterAccount,
          merchantId,
          outletId: currOutletId,
          reportOutletIdList,
          startDate: moment(historyStartDate).startOf('day'),
          endDate: moment(historyEndDate).endOf('day'),
          filterType: SERVER_REPORT_FILTER_TYPE.VARIANT_SALES,
          filterIdList: filterList,
        };

        console.log('Next Page API Request Body:', requestBody);

        const data = await ApiClientReporting.POST(API.getOutletUserOrderDoneProcessed, requestBody);

        console.log('data returned', data);

        OutletStore.update(s => {
          s.reportingApiLoading = false;
        });

        setTimeout(() => {
          setIsTableApiLoading(false);
        }, 1000);

        TempStore.update(s => {
          s.allOutletUserOrderDoneProcessed = data.allOutletUserOrderDoneProcessed;
        });

        setCurrentPage(currentPage + 1);
      } catch (error) {
        console.error('Error fetching next page data:', error);

        OutletStore.update(s => {
          s.reportingApiLoading = false;
        });

        setTimeout(() => {
          setIsTableApiLoading(false);
        }, 1000);
      }
    }
  };

  const loadMoreData = () => {
    const data = oriList;
    const slice = data.slice(offset, offset + perPage);
    setState({ list: slice, pageCount: Math.ceil(data.length / perPage) });
  };

  // moment = async () => {
  //     const today = new Date();
  //     const day = new Date(today.getTime() - (7 * 24 * 60 * 60 * 1000));
  //     await setState({ startDate: moment(day).format('YYYY-MM-DD'), endDate: moment(today).format('YYYY-MM-DD') })
  //     getDetail()
  // }

  const getDetail = () => {
    ApiClient.GET(
      `${API.getSalesByVariants +
      1
      }&startDate=${startDate
      }&endDate=${endDate}`,
    ).then((result) => {
      var data = result;
      var slice = data.slice(offset, offset + perPage);
      setState({
        list: slice,
        oriList: data,
        pageCount: Math.ceil(data.length / perPage),
      });
    });
  };

  const decimal = (value) => {
    return value.toFixed(2);
  };

  const renderSearchItem = ({ item, index }) =>
    (index + 1) % 2 == 0 ? (
      <View style={{ backgroundColor: Colors.whiteColor, padding: 12 }}>
        <View style={{ flexDirection: 'row' }}>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.categoryName}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.itemName}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.quantity}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.totalPrice}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            0
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.totalDiscount}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            -25.00
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.netSale}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            0.00
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.netSale}
          </Text>
          <Text
            style={{
              flex: 1,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            0.00
          </Text>
        </View>
      </View>
    ) : (
      <View style={{ backgroundColor: Colors.fieldtBgColor, padding: 12 }}>
        <View style={{ flexDirection: 'row' }}>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.categoryName}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.itemName}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.quantity}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.totalPrice}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            0
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.totalDiscount}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            -25.00
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.netSale}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            0.00
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            {item.netSale}
          </Text>
          <Text
            style={{
              flex: 1,
              fontSize: 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
            }}>
            0.00
          </Text>
        </View>
      </View>
    );

  const onItemSummaryClicked = (item) => {
    // setAddOnSalesDetails(item.detailsList);
    setSelectedItemSummary(item);
    setShowDetails(true);
    setDetailsTitle(
      `${item.addOnName})\n(${item.addOnChoices}) (Total Sold: ${item.totalItemsSold}`,
    );

    // setCurrentPage(1);
    // setPageCount(Math.ceil(item.detailsList.length / perPage));

    // console.log('item.detailsList');
    // console.log(item.detailsList);
  };

  const renderItem = ({ item, index }) => {
    let tempSc = 0;
    let tempTax = 0;
    let tempSalesReturn = 0;
    for (let i = 0; i < item.detailsList.length; i++) {
      tempSc += item.detailsList[i].sc;
      tempTax += item.detailsList[i].tax;
      tempSalesReturn += item.detailsList[i].salesReturn;
    }
    return (
      <TouchableOpacity
        onPress={() => onItemSummaryClicked(item)}
        style={{
          backgroundColor:
            (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
          paddingVertical: 10,
          paddingHorizontal: 3,
          paddingLeft: 1,
          borderColor: '#BDBDBD',
          borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
          borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
        }}>
        <View style={{ flexDirection: 'row' }}>
          <Text
            style={{
              width: '5%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {((currentPage - 1) * perPage) + index + 1}
          </Text>
          <Text
            style={{
              width: '11%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {item.addOnName}
          </Text>
          {/* <Text
          <Text
            style={{
              width: '13%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {item.addOnChoices}{item.addOnChoiceSplit ? `\n\n${item.addOnChoiceSplit}` : ''}
          </Text> */}
          <View style={{
            width: '13%',
            paddingLeft: 10,
          }}>
            <TextTicker
              style={{
                fontSize: switchMerchant ? 10 : 13,
                fontFamily: 'NunitoSans-Regular',
                textAlign: 'left',
              }}
              duration={8000} // Speed of the scrolling
              loop
              bounce={false} // Remove bouncing effect
              repeatSpacer={50} // Space after text before looping
              marqueeDelay={1000} // Delay before scrolling starts
            >
              {item.addOnChoices}
            </TextTicker>
            {item.choiceSales ? <Text></Text> : null}
            {item.addOnChoiceSplit && item.choiceSales ?
              item.addOnChoiceSplit.split('\n').map((line, index) => (
                <Text
                  key={index}
                  numberOfLines={1}
                  style={{
                    fontSize: switchMerchant ? 10 : 13,
                    fontFamily: 'NunitoSans-Regular',
                    textAlign: 'left',
                  }}>
                  {line}
                </Text>
              ))
              : null
            }
          </View>

          <View style={{
            width: '8%',
            paddingLeft: 10,
          }}>
            <Text
              style={{
                fontSize: switchMerchant ? 10 : 13,
                fontFamily: 'NunitoSans-Regular',
                textAlign: 'left',
              }}>
              {item.totalItemsSold}
            </Text>
            {item.choiceSales ? <Text></Text> : null}
            {item.choiceSales ?
              <Text
                style={{
                  fontSize: switchMerchant ? 10 : 13,
                  fontFamily: 'NunitoSans-Regular',
                  textAlign: 'left',
                }}>
                {Object.entries(item.choiceSales || {}).map(([choice, data]) =>
                  `${data.totalItemsSold}`).join('\n')}
              </Text> : null}
          </View>

          <View style={{
            width: '9%',
            paddingLeft: 10,
          }}>
            <Text
              style={{
                fontSize: switchMerchant ? 10 : 13,
                fontFamily: 'NunitoSans-Regular',
                textAlign: 'left',
              }}>
              {item.totalSales
                .toFixed(2)
                .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
            </Text>
            {item.choiceSales ? <Text></Text> : null}
            {item.choiceSales ?
              <Text
                style={{
                  fontSize: switchMerchant ? 10 : 13,
                  fontFamily: 'NunitoSans-Regular',
                  textAlign: 'left',
                }}>
                {Object.entries(item.choiceSales || {}).map(([choice, data]) =>
                  `${data.totalSales.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`).join('\n')}
              </Text> : null}
          </View>

          <View style={{
            width: '7%',
            paddingLeft: 12,
          }}>
            <Text
              style={{
                fontSize: switchMerchant ? 10 : 13,
                fontFamily: 'NunitoSans-Regular',
                textAlign: 'left',
              }}>
              {item.totalDiscount
                .toFixed(2)
                .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
            </Text>
            {item.choiceSales ? <Text></Text> : null}
            {item.choiceSales ?
              <Text
                style={{
                  fontSize: switchMerchant ? 10 : 13,
                  fontFamily: 'NunitoSans-Regular',
                  textAlign: 'left',
                }}>
                {Object.entries(item.choiceSales || {}).map(([choice, data]) =>
                  `${(item.totalDiscount != 0 ? data.totalDiscount : 0).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`).join('\n')}
              </Text> : null}
          </View>

          <View style={{
            width: '7%',
            paddingLeft: 10,
          }}>
            <Text
              style={{
                fontSize: switchMerchant ? 10 : 13,
                fontFamily: 'NunitoSans-Regular',
                textAlign: 'left',
              }}>
              {(item.totalDiscount != 0
                ? (item.totalDiscount / item.totalSales) * 100
                : 0
              ).toFixed(2)}
            </Text>
            {item.choiceSales ? <Text></Text> : null}
            {item.choiceSales ?
              <Text
                style={{
                  fontSize: switchMerchant ? 10 : 13,
                  fontFamily: 'NunitoSans-Regular',
                  textAlign: 'left',
                }}>
                {Object.entries(item.choiceSales || {}).map(([choice, data]) =>
                  `${(item.totalDiscount != 0 && data.totalDiscount != 0 && data.totalSales != 0 ? ((data.totalDiscount / data.totalSales) * 100) : 0).toFixed(2)}`).join('\n')}
              </Text> : null}
          </View>

          <View style={{
            width: '8%',
            paddingLeft: 10,
          }}>
            <Text
              style={{
                fontSize: switchMerchant ? 10 : 13,
                fontFamily: 'NunitoSans-Regular',
                textAlign: 'left',
              }}>
              {(tempTax || 0)
                .toFixed(2)
                .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
            </Text>
            {item.choiceSales ? <Text></Text> : null}
            {item.choiceSales ?
              <Text
                style={{
                  fontSize: switchMerchant ? 10 : 13,
                  fontFamily: 'NunitoSans-Regular',
                  textAlign: 'left',
                }}>
                {Object.entries(item.choiceSales || {}).map(([choice, data]) =>
                  `${((data.tax) || 0)
                    .toFixed(2)
                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
                ).join('\n')}
              </Text> : null}
          </View>

          <View style={{
            width: '10%',
            paddingLeft: 10,
          }}>
            <Text
              style={{
                fontSize: switchMerchant ? 10 : 13,
                fontFamily: 'NunitoSans-Regular',
                textAlign: 'left',
              }}>
              {(tempSc || 0).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
            </Text>
            {item.choiceSales ? <Text></Text> : null}
            {item.choiceSales ?
              <Text
                style={{
                  fontSize: switchMerchant ? 10 : 13,
                  fontFamily: 'NunitoSans-Regular',
                  textAlign: 'left',
                }}>
                {Object.entries(item.choiceSales || {}).map(([choice, data]) =>
                  `${((data.sc) || 0)
                    .toFixed(2)
                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
                ).join('\n')}
              </Text> : null}
          </View>

          <View style={{
            width: '10%',
            paddingLeft: 12,
          }}>
            <Text
              style={{
                fontSize: switchMerchant ? 10 : 13,
                fontFamily: 'NunitoSans-Regular',
                textAlign: 'left',
              }}>
              {(tempSalesReturn || 0)
                .toFixed(2)
                .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
            </Text>
            {item.choiceSales ? <Text></Text> : null}
            {item.choiceSales ?
              <Text
                style={{
                  fontSize: switchMerchant ? 10 : 13,
                  fontFamily: 'NunitoSans-Regular',
                  textAlign: 'left',
                }}>
                {Object.entries(item.choiceSales || {}).map(([choice, data]) =>
                  `${((data.salesReturn) || 0)
                    .toFixed(2)
                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
                ).join('\n')}
              </Text> : null}
          </View>

          <View style={{
            width: '12%',
            paddingRight: 20,
          }}>
            <Text
              style={{
                fontSize: switchMerchant ? 10 : 13,
                fontFamily: 'NunitoSans-Regular',
                textAlign: 'right',
              }}>
              {item.itemNetSales
                .toFixed(2)
                .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
            </Text>
            {item.choiceSales ? <Text></Text> : null}
            {item.choiceSales ?
              <Text
                style={{
                  fontSize: switchMerchant ? 10 : 13,
                  fontFamily: 'NunitoSans-Regular',
                  textAlign: 'right',
                }}>
                {Object.entries(item.choiceSales || {}).map(([choice, data]) =>
                  `${((data.netSales) || 0)
                    .toFixed(2)
                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
                ).join('\n')}
              </Text> : null}
          </View>

          {/* <Text
          style={{
            width: '10%',
            fontSize: switchMerchant ? 10 : 13,
            fontFamily: 'NunitoSans-Regular',
            textAlign: 'left',
            paddingLeft: 12,
          }}>
          {item.averageCost
            .toFixed(2)
            .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
        </Text>
        <Text
          style={{
            width: '11%',
            fontSize: switchMerchant ? 10 : 13,
            fontFamily: 'NunitoSans-Regular',
            textAlign: 'right',
            paddingRight: 20,
          }}>
          {item.averageNetSales
            .toFixed(2)
            .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
        </Text> */}
        </View>
      </TouchableOpacity>
    );
  };

  const onClickItemDetails = async (item) => {
    const userOrderSnapshot = await firebase.firestore()
      .collection(Collections.UserOrder)
      .where('uniqueId', '==', item.uniqueId)
      .limit(1)
      .get();

    var userOrder = null;
    if (!userOrderSnapshot.empty) {
      userOrder = userOrderSnapshot.docs[0].data();
    }

    if (userOrder) {
      setExpandDetailsDict({
        ...expandDetailsDict,
        [item.uniqueId]: expandDetailsDict[item.uniqueId] ? false : userOrder,
      });
    }
  };

  const renderItemDetails = ({ item, index }) => {
    var record = null;
    if (item && expandDetailsDict[item.uniqueId] && expandDetailsDict[item.uniqueId].uniqueId) {
      record = expandDetailsDict[item.uniqueId];
    }

    ///////////////////////////

    // console.log('order id');
    // console.log(item.orderId);

    // calculate longest

    var longestStr = 5;

    for (var i = 0; i < item.cartItems.length; i++) {
      const cartItemPriceWIthoutAddOn = getCartItemPriceWithoutAddOn(item.cartItems[i]);

      if (cartItemPriceWIthoutAddOn.toFixed(0).length > longestStr) {
        longestStr = cartItemPriceWIthoutAddOn.toFixed(0).length;
      }

      for (var j = 0; j < item.cartItems[i].addOns.length; j++) {
        if (
          item.cartItems[i].addOns[j].prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(0).length > longestStr
        ) {
          longestStr = item.cartItems[i].addOns[j].prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(0).length;
        }
      }
    }

    if (item.totalPrice.toFixed(0).length > longestStr) {
      longestStr = item.totalPrice.toFixed(0).length;
    }

    if (item.discount.toFixed(0).length > longestStr) {
      longestStr = item.discount.toFixed(0).length;
    }

    if (item.tax.toFixed(0).length > longestStr) {
      longestStr = item.tax.toFixed(0).length;
    }

    if (item.finalPrice.toFixed(0).length > longestStr) {
      longestStr = item.finalPrice.toFixed(0).length;
    }

    // console.log(longestStr);

    ///////////////////////////

    // calculate spacing

    var cartItemPriceWIthoutAddOnSpacingList = [];
    var addOnsSpacingList = [];

    // for (var i = 0; i < item.cartItems.length; i++) {
    //   const cartItemPriceWIthoutAddOn =
    //     item.cartItems[i].price -
    //     item.cartItems[i].addOns.reduce(
    //       (accum, addOn) => accum + addOn.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0),
    //       0,
    //     );

    //   cartItemPriceWIthoutAddOnSpacingList.push(
    //     Math.max(longestStr - cartItemPriceWIthoutAddOn.toFixed(0).length, 0) +
    //     1,
    //   );

    //   for (var j = 0; j < item.cartItems[i].addOns.length; j++) {
    //     addOnsSpacingList.push(
    //       Math.max(
    //         longestStr -
    //         item.cartItems[i].addOns[j].prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(0).length,
    //         0,
    //       ) + 1,
    //     );
    //   }
    // }

    var totalPriceSpacing =
      Math.max(longestStr - item.totalPrice.toFixed(0).length, 0) + 1;
    var discountSpacing =
      Math.max(longestStr - item.discount.toFixed(0).length, 0) + 1;
    var taxSpacing = Math.max(longestStr - item.tax.toFixed(0).length, 0) + 1;
    var finalPriceSpacing =
      Math.max(longestStr - item.finalPrice.toFixed(0).length, 0) + 1;

    ///////////////////////////
    return (
      <TouchableOpacity
        onPress={() => {
          {
            onClickItemDetails(item);
          }
        }}
        style={{
          backgroundColor:
            (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
          paddingVertical: 10,
          //paddingHorizontal: 3,
          //paddingLeft: 1,
          borderColor: '#BDBDBD',
          borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
          borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
        }}>
        <View style={{ flexDirection: 'row' }}>
          <Text
            style={{
              width: '6%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {((currentDetailsPage - 1) * perPage) + index + 1}
          </Text>
          <Text
            style={{
              width: '12%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {item.orderId ? `#${item.orderId}` : 'N/A'}
          </Text>

          <View style={{ width: '18%', paddingLeft: 10 }}>
            <Text
              style={{
                fontSize: switchMerchant ? 10 : 13,
                fontFamily: 'NunitoSans-Regular',
                textAlign: 'left',
                paddingLeft: 0,
              }}>
              {moment(item.createdAt).format('DD MMM YY hh:mm A')}
            </Text>
          </View>
          <Text
            style={{
              width: '10%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {parseFloat((item.itemPrice) + getOrderDiscountInfo(item))
              //(item.finalPriceBefore ? item.finalPriceBefore : item.finalPrice)
              .toFixed(2)
              .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
          </Text>
          <Text
            style={{
              width: '8%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {parseFloat(getOrderDiscountInfoInclOrderBased(item))
              .toFixed(2)
              .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
          </Text>
          <Text
            style={{
              width: '8%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {parseFloat(item.discountPercentage).toFixed(2)}
          </Text>
          <Text
            style={{
              width: '8%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {parseFloat(item.tax)
              .toFixed(2)
              .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
          </Text>
          <Text
            style={{
              width: '9%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {parseFloat(item.sc || 0).toFixed(2)}
          </Text>
          <Text
            style={{
              width: '9%',
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 10,
            }}>
            {parseFloat(item.salesReturn || 0).toFixed(2)}
          </Text>
          {/* <Text style={{ flex: 1, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>{parseFloat(0).toFixed(2)}</Text> */}
          {/* <Text style={{ flex: 2, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>{parseFloat(item.finalPrice).toFixed(2)}</Text> */}
          <View
            style={{
              width: '12%',
              flexDirection: 'row',
              justifyContent: 'space-between',
              paddingLeft: 10,
              paddingRight: switchMerchant ? '2.1%' : '1.78%',
            }}>
            <Text style={{}} />
            <Text
              style={{
                fontSize: switchMerchant ? 10 : 13,
                fontFamily: 'NunitoSans-Regular',
              }}>
              {/* <Text style={{
                            opacity: 0,
                            ...Platform.OS === 'android' && {
                                color: 'transparent',
                            },
                        }}>
                            {'0'.repeat((finalPriceSpacing * 0.6) + (item.finalPrice.toFixed(0).length === 1 ? 1 : 0))}
                        </Text> */}
              {(item.finalPriceBefore != 0 || item.finalPrice != 0
                ? (item.finalPriceBefore
                  ? item.finalPriceBefore
                  : item.finalPrice) -
                item.tax -
                (item.sc || 0)
                : 0
              )
                .toFixed(2)
                .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
            </Text>
          </View>
          {/* <Text style={{ flex: 3, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'center' }}>{parseFloat(item.totalPrice).toFixed(2)}</Text> */}
        </View>

        {expandDetailsDict[item.uniqueId] ? (
          <View
            style={{
              minheight: windowHeight * 0.35,
              marginTop: 30,
              paddingBottom: 20,
            }}>
            {record.cartItems.map((cartItem, index) => {
              const cartItemPriceWIthoutAddOn = getCartItemPriceWithoutAddOn(cartItem);

              return (
                <View
                  style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <View
                    style={{
                      width: '100%',
                      alignItems: 'flex-start',
                      flexDirection: 'row',
                      marginBottom: Platform.OS == 'ios' ? 10 : 10,
                      minHeight: 80,
                      //backgroundColor: 'yellow',
                    }}>
                    <View
                      style={{
                        flexDirection: 'row',
                        width: '100%',
                        //backgroundColor: 'blue',
                      }}>
                      {index == 0 ? (
                        <View
                          style={{
                            marginHorizontal: 1,
                            width: Platform.OS == 'ios' ? '8%' : '8%',
                            //justifyContent: 'center',
                            alignItems: 'center',
                            //backgroundColor: 'blue',
                          }}>
                          <TouchableOpacity
                            style={{
                              alignItems: 'center',
                              marginTop: 0,
                            }}
                            onPress={() => {
                              var crmUser = null;

                              if (record.crmUserId !== undefined) {
                                for (var i = 0; i < crmUsers.length; i++) {
                                  if (record.crmUserId === crmUsers[i].uniqueId) {
                                    crmUser = crmUsers[i];
                                    break;
                                  }
                                }
                              }

                              if (!crmUser) {
                                for (var i = 0; i < crmUsers.length; i++) {
                                  if (record.userId === crmUsers[i].firebaseUid) {
                                    crmUser = crmUsers[i];
                                    break;
                                  }
                                }
                              }

                              if (crmUser) {
                                CommonStore.update(
                                  (s) => {
                                    s.selectedCustomerEdit = crmUser;
                                    // s.selectedCustomerEdit = userReservations[item.userId] && crmUsers[item.userId] ? crmUsers[item.userId] : null ;

                                    s.routeParams = {
                                      pageFrom: 'Reservation',
                                    };
                                  },
                                  () => {
                                    navigation.navigate('NewCustomer');
                                  },
                                );
                              }
                            }}>
                            <img src={personicon} width={60} height={60} />

                            <View
                              style={{
                                alignItems: 'center',
                                justifyContent: 'center',
                              }}>
                              <Text
                                style={[
                                  {
                                    fontFamily: 'NunitoSans-Bold',
                                    marginTop: 0,
                                    fontSize: 13,
                                    textAlign: 'center',
                                  },
                                  switchMerchant
                                    ? {
                                      fontFamily: 'NunitoSans-Bold',
                                      marginTop: 0,
                                      fontSize: 10,
                                      textAlign: 'center',
                                    }
                                    : {},
                                ]}
                                numberOfLines={1}>
                                {record.userName ? record.userName : 'Guest'}
                              </Text>
                            </View>
                          </TouchableOpacity>
                        </View>
                      ) : (
                        <View
                          style={{
                            marginHorizontal: 1,
                            width: Platform.OS == 'ios' ? '8%' : '8%',
                            justifyContent: 'center',
                            alignItems: 'center',
                          }}
                        />
                      )}

                      <View
                        style={{
                          // flex: 0.3,
                          width: '5%',
                          //justifyContent: 'center',
                          alignItems: 'center',
                          //backgroundColor: 'red',
                          //paddingLeft: '1.2%',
                        }}>
                        <Text
                          style={[
                            {
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: 13,
                            },
                            switchMerchant
                              ? {
                                fontFamily: 'NunitoSans-Bold',
                                fontSize: 10,
                              }
                              : {},
                          ]}>
                          {index + 1}.
                        </Text>
                      </View>

                      <View
                        style={{
                          //flex: 0.5,
                          width: '10%',
                          //backgroundColor: 'green',
                          alignItems: 'center',
                        }}>
                        {cartItem.image ? (
                          <AsyncImage
                            source={{ uri: cartItem.image }}
                            // item={cartItem}
                            style={{
                              width: switchMerchant ? 30 : 60,
                              height: switchMerchant ? 30 : 60,
                              borderWidth: 1,
                              borderColor: '#E5E5E5',
                              borderRadius: 5,
                            }}
                          />
                        ) : (
                          <View
                            style={{
                              justifyContent: 'center',
                              alignItems: 'center',
                              width: switchMerchant ? 30 : 60,
                              height: switchMerchant ? 30 : 60,
                              borderWidth: 1,
                              borderColor: '#E5E5E5',
                              borderRadius: 5,
                            }}>
                            <Ionicon
                              name="fast-food-outline"
                              size={switchMerchant ? 25 : 35}
                            />
                          </View>
                        )}
                      </View>
                      <View style={{ width: '75%' }}>
                        <View
                          style={{
                            marginLeft: Platform.OS == 'ios' ? 14 : 14,
                            marginBottom: 10,
                            //backgroundColor: 'blue',
                            width: '100%',
                            flexDirection: 'row',
                          }}>
                          <View style={{ width: '69%' }}>
                            <Text
                              style={[
                                {
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: 13,
                                },
                                switchMerchant
                                  ? {
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}>
                              {cartItem.name}{cartItem.priceType === PRODUCT_PRICE_TYPE.UNIT ? ` (${UNIT_TYPE_SHORT[cartItem.unitType]})` : ''}
                            </Text>
                          </View>

                          <View
                            style={{
                              width: '13%',
                            }}>
                            <View
                              style={{
                                alignItems: 'center',
                                //backgroundColor: 'yellow',
                              }}>
                              <Text
                                style={[
                                  {
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: 13,
                                  },
                                  // Platform.OS === 'android'
                                  //   ? {
                                  //       width: '200%',
                                  //     }
                                  //   : {},
                                  switchMerchant
                                    ? {
                                      fontFamily: 'NunitoSans-Bold',
                                      fontSize: 10,
                                    }
                                    : {},
                                ]}>
                                x{cartItem.quantity}
                              </Text>
                            </View>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                              justifyContent: 'space-between',
                              width: '18.8%',
                            }}>
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : { fontSize: 13 }
                              }>
                              RM
                            </Text>
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    paddingRight: 20,
                                    fontFamily: 'NunitoSans-Regular',
                                  }
                                  : {
                                    fontSize: 13,
                                    paddingRight: 20,
                                    fontFamily: 'NunitoSans-Regular',
                                  }
                              }>
                              {cartItemPriceWIthoutAddOn
                                .toFixed(2)
                                .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                            </Text>
                          </View>
                        </View>

                        {cartItem.remarks && cartItem.remarks.length > 0 ? (
                          <View
                            style={{
                              alignItems: 'center',
                              flexDirection: 'row',
                              marginLeft: Platform.OS == 'ios' ? 14 : 14,
                            }}>
                            <View style={{ justifyContent: 'center' }}>
                              <Text
                                style={[
                                  {
                                    fontFamily: 'NunitoSans-SemiBold',
                                    fontSize: 13,
                                  },
                                  switchMerchant
                                    ? {
                                      fontFamily: 'NunitoSans-SemiBold',
                                      fontSize: 10,
                                    }
                                    : {},
                                ]}>
                                {cartItem.remarks}
                              </Text>
                            </View>
                          </View>
                        ) : (
                          <></>
                        )}

                        {cartItem.addOns.map((addOnChoice, i) => {
                          return (
                            <View
                              style={{
                                flexDirection: 'row',
                                // marginLeft: -5,
                                width: '100%',
                              }}>
                              <View
                                style={{
                                  width: '69%',
                                  flexDirection: 'row',
                                  marginLeft: Platform.OS == 'ios' ? 14 : 14,
                                }}>
                                <Text
                                  style={[
                                    {
                                      fontFamily: 'NunitoSans-Bold',
                                      fontSize: 13,
                                      color: Colors.descriptionColor,
                                      width: '25%',
                                      // marginLeft: 5,
                                    },
                                    switchMerchant
                                      ? {
                                        fontFamily: 'NunitoSans-Bold',
                                        fontSize: 10,
                                        color: Colors.descriptionColor,
                                        width: '25%',
                                      }
                                      : {},
                                  ]}>
                                  {`${addOnChoice.name}:`}
                                </Text>
                                <Text
                                  style={[
                                    {
                                      fontFamily: 'NunitoSans-Bold',
                                      fontSize: 13,
                                      color: Colors.descriptionColor,
                                      width: '75%',
                                      // marginLeft: 5,
                                    },
                                    switchMerchant
                                      ? {
                                        fontFamily: 'NunitoSans-Bold',
                                        fontSize: 10,
                                        color: Colors.descriptionColor,
                                        width: '75%',
                                      }
                                      : {},
                                  ]}>
                                  {`${addOnChoice.choiceNames[0]}`}
                                </Text>
                              </View>

                              <View
                                style={[
                                  {
                                    width: '13%',
                                    flexDirection: 'row',
                                    justifyContent: 'center',
                                    //backgroundColor: 'blue',
                                  },
                                ]}>
                                <Text
                                  style={[
                                    {
                                      fontFamily: 'NunitoSans-Bold',
                                      fontSize: 13,
                                      color: Colors.descriptionColor,
                                      width: '28%',
                                      // right: 38,
                                      //backgroundColor: 'green',
                                      textAlign: 'center',
                                    },
                                    switchMerchant
                                      ? {
                                        fontFamily: 'NunitoSans-Bold',
                                        fontSize: 10,
                                        color: Colors.descriptionColor,
                                        width: '28%',
                                        textAlign: 'center',
                                      }
                                      : {},
                                  ]}>
                                  {`${addOnChoice.quantities
                                    ? `x${getAddOnChoiceQuantity(addOnChoice, cartItem)}`
                                    : ''
                                    }`}
                                </Text>
                              </View>

                              <View
                                style={{
                                  flexDirection: 'row',
                                  justifyContent: 'space-between',
                                  width: '18.8%',
                                  alignItems: 'center',
                                }}>
                                <Text
                                  style={[
                                    switchMerchant
                                      ? {
                                        color: Colors.descriptionColor,
                                        fontSize: 10,
                                      }
                                      : {
                                        color: Colors.descriptionColor,
                                        fontSize: 13,
                                      },
                                  ]}>
                                  RM
                                </Text>
                                <Text
                                  style={
                                    switchMerchant
                                      ? {
                                        color: Colors.descriptionColor,
                                        paddingRight: 20,
                                        fontSize: 10,
                                        fontFamily: 'NunitoSans-Regular',
                                      }
                                      : {
                                        color: Colors.descriptionColor,
                                        paddingRight: 20,
                                        fontSize: 13,
                                        fontFamily: 'NunitoSans-Regular',
                                      }
                                  }>
                                  {(getAddOnChoicePrice(addOnChoice, cartItem))
                                    .toFixed(2)
                                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                                </Text>
                              </View>
                            </View>
                          );
                        })}
                      </View>
                    </View>
                  </View>
                  <View style={{ flexDirection: 'row', width: '100%' }}>
                    <View style={{ width: '70%' }} />
                    <View style={{ width: 15 }} />
                    {index === record.cartItems.length - 1 ? (
                      <View
                        style={{
                          flexDirection: 'row',
                          //backgroundColor: 'yellow',
                          width: '28.65%',
                        }}>
                        <View
                          style={{
                            justifyContent: 'center',
                            width: '100%',
                          }}>
                          <View
                            style={{
                              flexDirection: 'row',
                            }}>
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    width: '50.9%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                                  : {
                                    fontSize: 13,
                                    width: '50.9%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                              }>
                              Subtotal:
                            </Text>
                            <View
                              style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                width: '49.2%',
                              }}>
                              <Text
                                style={
                                  switchMerchant
                                    ? { fontSize: 10 }
                                    : { fontSize: 13 }
                                }>
                                RM
                              </Text>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                    : {
                                      fontSize: 13,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                }>
                                {((record.isRefundOrder && record.finalPrice <= 0)
                                  ? 0
                                  : record.totalPrice +
                                  getOrderDiscountInfo(record)
                                )
                                  .toFixed(2)
                                  .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                              </Text>
                            </View>
                          </View>
                          {cartItem.orderType === ORDER_TYPE.DELIVERY ? (
                            <View
                              style={{
                                flexDirection: 'row',
                              }}>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      width: '50.9%',
                                      fontFamily: 'Nunitosans-Bold',
                                    }
                                    : {
                                      fontSize: 13,
                                      width: '50.9%',
                                      fontFamily: 'Nunitosans-Bold',
                                    }
                                }>
                                Delivery Fee:
                              </Text>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  justifyContent: 'space-between',
                                  width: '49.2%',
                                }}>
                                <Text
                                  style={
                                    switchMerchant
                                      ? { fontSize: 10 }
                                      : { fontSize: 13 }
                                  }>
                                  RM
                                </Text>
                                <Text
                                  style={
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                        paddingRight: 20,
                                        fontFamily: 'NunitoSans-Regular',
                                      }
                                      : {
                                        fontSize: 13,
                                        paddingRight: 20,
                                        fontFamily: 'NunitoSans-Regular',
                                      }
                                  }>
                                  {record.deliveryFee
                                    .toFixed(2)
                                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                                </Text>
                              </View>
                            </View>
                          ) : (
                            <></>
                          )}

                          <View
                            style={{
                              flexDirection: 'row',
                            }}>
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    width: '50.9%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                                  : {
                                    fontSize: 13,
                                    width: '50.9%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                              }>
                              Discount:
                            </Text>
                            <View
                              style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                width: '49.2%',
                              }}>
                              <Text
                                style={
                                  switchMerchant
                                    ? { fontSize: 10 }
                                    : { fontSize: 13 }
                                }>
                                RM
                              </Text>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                    : {
                                      fontSize: 13,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                }>
                                {' '}
                                {((record.isRefundOrder && record.finalPrice <= 0)
                                  ? 0
                                  :
                                  // record.discount +
                                  getOrderDiscountInfoInclOrderBased(record)
                                )
                                  .toFixed(2)
                                  .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                              </Text>
                            </View>
                          </View>

                          <View
                            style={{
                              flexDirection: 'row',
                            }}>
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    width: '50.9%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                                  : {
                                    fontSize: 13,
                                    width: '50.9%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                              }>
                              Tax:
                            </Text>
                            <View
                              style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                width: '49.2%',
                              }}>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      paddingRight: 20,
                                    }
                                    : { fontSize: 13, paddingRight: 20 }
                                }>
                                RM
                              </Text>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                    : {
                                      fontSize: 13,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                }>
                                {record.tax
                                  .toFixed(2)
                                  .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                              </Text>
                            </View>
                          </View>

                          <View
                            style={{
                              flexDirection: 'row',
                            }}>
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    width: '50.85%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                                  : {
                                    fontSize: 13,
                                    width: '50.85%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                              }>
                              Service Charge:
                            </Text>
                            <View
                              style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                width: switchMerchant ? '49.15%' : '49.1%',
                              }}>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                    }
                                    : { fontSize: 13 }
                                }>
                                RM
                              </Text>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                    : {
                                      fontSize: 13,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                }>
                                {(record.sc || 0)
                                  .toFixed(2)
                                  .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                              </Text>
                            </View>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                            }}>
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    width: '50.9%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                                  : {
                                    fontSize: 13,
                                    width: '50.9%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                              }>
                              Rounding:
                            </Text>
                            <View
                              style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                width: '49.2%',
                              }}>
                              <Text
                                style={
                                  switchMerchant
                                    ? { fontSize: 10 }
                                    : { fontSize: 13 }
                                }>
                                RM
                              </Text>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                    : {
                                      fontSize: 13,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                }>
                                {(record.finalPrice
                                  ? record.finalPrice - record.finalPriceBefore
                                  : 0
                                )
                                  .toFixed(2)
                                  .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                              </Text>
                            </View>
                          </View>

                          <View
                            style={{
                              flexDirection: 'row',
                            }}>
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    width: '50.9%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                                  : {
                                    fontSize: 13,
                                    width: '50.9%',
                                    fontFamily: 'Nunitosans-Bold',
                                  }
                              }>
                              Total:
                            </Text>
                            <View
                              style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                width: '49.2%',
                              }}>
                              <Text
                                style={
                                  switchMerchant
                                    ? { fontSize: 10 }
                                    : { fontSize: 13 }
                                }>
                                RM
                              </Text>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                    : {
                                      fontSize: 13,
                                      paddingRight: 20,
                                      fontFamily: 'NunitoSans-Regular',
                                    }
                                }>
                                {record.finalPrice
                                  .toFixed(2)
                                  .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                              </Text>
                            </View>
                          </View>
                        </View>
                      </View>
                    ) : (
                      <></>
                    )}
                  </View>

                  {/* <View style={{alignItems:'flex-end'}}>
                        <View style={{ flexDirection: 'row' }}>
                          <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 16 }}>Subtotal: {(Math.ceil(cartItem.price * 20-0.05) /20).toFixed(2)}</Text>
                        </View>
                      </View> */}
                  {/* {(cartItem.remarks && cartItem.remarks.length > 0) ?
                      <View style={{ alignItems: 'center', flexDirection: 'row' }}>
                        
                        <View style={{ flex: 1, justifyContent: 'center', }}>
                          <Text style={{ fontFamily: 'NunitoSans-SemiBold', fontSize: 15 }}>{cartItem.remarks}</Text>
                        </View>
                        
                      </View>
                      : <></>
                    } */}
                </View>
              );
            })}
          </View>
        ) : null}
      </TouchableOpacity>
    );
  };

  /*const downloadCsv = () => {
    //if (productSales && productSales.dataSource && productSales.dataSource.data) {
    //const csvData = convertArrayToCSV(productSales.dataSource.data);
    const csvData = convertArrayToCSV(CsvData);

    const pathToWrite = `${RNFetchBlob.fs.dirs.DownloadDir
      }/koodoo-report-Product-Sales${moment().format('YYYY-MM-DD-HH-mm-ss')}.csv`;
    // console.log('PATH', pathToWrite);
    RNFetchBlob.fs
      .writeFile(pathToWrite, csvData, 'utf8')
      .then(() => {
        // console.log(`wrote file ${pathToWrite}`);
        // wrote file /storage/emulated/0/Download/data.csv
        Alert.alert(
          'Success',
          `Send to ${pathToWrite}`,
          [{ text: 'OK', onPress: () => { } }],
          { cancelable: false },
        );
      })
      .catch((error) => console.error(error));
    //}
  };*/

  const convertDataToExcelFormat = () => {
    var excelData = [];

    if (!showDetails) {
      for (var i = 0; i < addOnSales.length; i++) {
        var excelRow = {
          'Variant Name': addOnSales[i].addOnName,
          'Variant Options': addOnSales[i].addOnChoices,
          'Item Sold': addOnSales[i].totalItemsSold,
          'Sales (RM)': +parseFloat(addOnSales[i].totalSales).toFixed(2),
          'Disc (RM)': +parseFloat(
            addOnSales[i].totalDiscount,
          ).toFixed(2),
          'Disc (%)': +parseFloat(addOnSales[i].totalDiscount != 0
            ? (addOnSales[i].totalDiscount / addOnSales[i].totalSales) * 100
            : 0
          ).toFixed(2),
          'Tax (RM)': +parseFloat(addOnSales[i].detailsList
            .reduce((accum, order) => accum + (order.tax || 0), 0)).toFixed(2),
          'Service Charge (RM)': (addOnSales[i].detailsList
            .reduce((accum, order) => accum + (order.sc || 0), 0)).toFixed(2),
          'Sales Return (RM)': +parseFloat(addOnSales[i].detailsList
            .reduce((accum, order) => accum + (order.salesReturn || 0), 0)).toFixed(2),
          'Net Sales (RM)': +parseFloat(
            addOnSales[i].itemNetSales,
          ).toFixed(2),
          //'GP(%)': parseFloat(addOnSales[i].gp).toFixed(2),
        };

        excelData.push(excelRow);
      }
    } else {
      for (var i = 0; i < addOnSalesDetails.length; i++) {
        var excelRow = {
          // 'Transaction Category': ORDER_TYPE_PARSED[addOnSalesDetails[i].orderType],
          // 'Sales (RM)': parseFloat(addOnSalesDetails[i].finalPrice).toFixed(2),
          // 'Transaction Date': moment(addOnSalesDetails[i].createdAt).format('DD MMM hh:mma'),
          // 'Total Discount (RM)': parseFloat(addOnSalesDetails[i].discount).toFixed(2),
          // 'Discount (%)': parseFloat(isFinite(addOnSalesDetails[i].finalPrice / addOnSalesDetails[i].discount) ? addOnSalesDetails[i].finalPrice / addOnSalesDetails[i].discount * 100 : 0).toFixed(2),
          // 'Tax (RM)': parseFloat(addOnSalesDetails[i].tax).toFixed(2),
          // 'Tax (RM)': parseFloat(0).toFixed(2),
          // 'GP (%)': parseFloat(0).toFixed(2),
          // 'Net Sales (RM)': parseFloat(addOnSalesDetails[i].totalPrice).toFixed(2),
          'Order ID': addOnSalesDetails[i].orderId
            ? `#${addOnSalesDetails[i].orderId}`
            : 'N/A',
          'Transaction Date': moment(addOnSalesDetails[i].createdAt).format(
            'DD MMM YY hh:mm A',
          ),
          'Sales (RM)': +parseFloat((addOnSalesDetails[i].itemPrice) + getOrderDiscountInfo(addOnSalesDetails[i])).toFixed(2),
          'Disc (RM)': +parseFloat(
            getOrderDiscountInfoInclOrderBased(addOnSalesDetails[i]),
          ).toFixed(2),
          'Disc (%)': +parseFloat(
            addOnSalesDetails[i].discountPercentage,
          ).toFixed(2),
          'Tax (RM)': +parseFloat(addOnSalesDetails[i].tax).toFixed(2),
          'Service Charge (RM)': +parseFloat(
            addOnSalesDetails[i].sc || 0,
          ).toFixed(2),
          'Sales Return (RM)': parseFloat(addOnSalesDetails[i].salesReturn || 0).toFixed(2),
          //'GP (%)': parseFloat(0).toFixed(2),
          'Net Sales (RM)': +parseFloat(addOnSalesDetails[i].finalPriceBefore != 0 || addOnSalesDetails[i].finalPrice != 0
            ? (addOnSalesDetails[i].finalPriceBefore
              ? addOnSalesDetails[i].finalPriceBefore
              : addOnSalesDetails[i].finalPrice) -
            addOnSalesDetails[i].tax -
            (addOnSalesDetails[i].sc || 0)
            : 0
          ).toFixed(2),
        };

        excelData.push(excelRow);
      }
    }

    // console.log('excelData');
    // console.log(excelData);

    return excelData;
  };
  const handleExportExcel = () => {
    var wb = XLSX.utils.book_new(),
      ws = XLSX.utils.json_to_sheet(convertDataToExcelFormat());

    XLSX.utils.book_append_sheet(wb, ws, "ReportSalesVariant");
    XLSX.writeFile(wb, "ReportSalesVariant.xlsx");
  };

  const convertDataToCSVFormat = () => {
    var csvData = [];

    if (!showDetails) {
      csvData.push(
        `Variant Name,Variant Options,Item Sold,Sales (RM),Disc (RM),Disc (%),Tax (RM),Service Charge (RM),Sales Return (RM),Net Sales (RM)`,
      );

      for (var i = 0; i < addOnSales.length; i++) {
        var csvRow = `${addOnSales[i].addOnName},${addOnSales[i].addOnChoices
          .split(', ')
          .join(' | ')},${addOnSales[i].totalItemsSold},${+parseFloat(
            addOnSales[i].totalSales,
          ).toFixed(2)},${+parseFloat(addOnSales[i].totalDiscount).toFixed(
            2,
          )},${(addOnSales[i].totalDiscount != 0
            ? (addOnSales[i].totalDiscount / addOnSales[i].totalSales) * 100
            : 0
          ).toFixed(2)},${(addOnSales[i].detailsList
            .reduce((accum, order) => accum + (order.tax || 0), 0))
            .toFixed(2)},${(addOnSales[i].detailsList
              .reduce((accum, order) => accum + (order.sc || 0), 0)).toFixed(2)},${(addOnSales[i].detailsList
                .reduce((accum, order) => accum + (order.salesReturn || 0), 0))
                .toFixed(2)
          },${addOnSales[i].itemNetSales
            .toFixed(2)}`;

        csvData.push(csvRow);
      }
    } else {
      csvData.push(
        `Order ID,Transaction Date,Sales (RM),Disc (RM),Disc (%),Tax (RM),Service Charge (RM),Sales Return (RM),Net Sales (RM)`,
      );

      for (var i = 0; i < addOnSalesDetails.length; i++) {
        var csvRow = `${addOnSalesDetails[i].orderId
          ? `#${addOnSalesDetails[i].orderId.split(', ').join(' | ')}`
          : 'N/A'
          },${moment(addOnSalesDetails[i].createdAt).format(
            'DD MMM YY hh:mm A',
          )},${+parseFloat((addOnSalesDetails[i].itemPrice) + getOrderDiscountInfo(addOnSalesDetails[i])).toFixed(2)},${+parseFloat(getOrderDiscountInfoInclOrderBased(addOnSalesDetails[i])).toFixed(
            2,
          )},${+parseFloat(addOnSalesDetails[i].discountPercentage).toFixed(
            2,
          )},${(addOnSalesDetails[i].tax || 0).toFixed(2)},${(
            addOnSalesDetails[i].sc || 0
          ).toFixed(2)},${(addOnSalesDetails[i].salesReturn || 0).toFixed(
            2,
          )},${(addOnSalesDetails[i].finalPriceBefore != 0 ||
            addOnSalesDetails[i].finalPrice != 0
            ? (addOnSalesDetails[i].finalPriceBefore
              ? addOnSalesDetails[i].finalPriceBefore
              : addOnSalesDetails[i].finalPrice) -
            addOnSalesDetails[i].tax -
            (addOnSalesDetails[i].sc || 0)
            : 0
          ).toFixed(2)}`;

        csvData.push(csvRow);
      }
    }

    // console.log('excelData');
    // console.log(excelData);

    return csvData.join('\r\n');
  };

  /*const downloadExcel = () => {
    const excelData = convertDataToExcelFormat();

    var excelFile = `${Platform.OS === 'ios'
      ? RNFS.DocumentDirectoryPath
      : RNFS.DownloadDirectoryPath
      }/koodoo-report-Product-Sales${moment().format(
        'YYYY-MM-DD-HH-mm-ss',
      )}.xlsx`;
    var excelWorkSheet = XLSX.utils.json_to_sheet(excelData);
    var excelWorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(
      excelWorkBook,
      excelWorkSheet,
      'Product Sales Report',
    );

    const workBookData = XLSX.write(excelWorkBook, {
      type: 'binary',
      bookType: 'xlsx',
    });

    RNFS.writeFile(excelFile, workBookData, 'ascii')
      .then((success) => {
        // console.log(`wrote file ${excelFile}`);

        Alert.alert(
          'Success',
          `Send to ${excelFile}`,
          [{ text: 'OK', onPress: () => { } }],
          { cancelable: false },
        );
      })
      .catch((err) => {
        // console.log(err.message);
      });

    // XLSX.writeFileAsync(excelFile, excelWorkBook, () => {
    //     Alert.alert(
    //         'Success',
    //         `Send to ${excelFile}`,
    //         [{ text: 'OK', onPress: () => { } }],
    //         { cancelable: false },
    //     );
    // });

    // const csvData = convertArrayToCSV(CsvData);

    // const pathToWrite = `${RNFetchBlob.fs.dirs.DownloadDir}/koodoo-report-Product-Sales${moment().format('YYYY-MM-DD-HH-mm-ss')}.csv`;
    // // console.log("PATH", excelFile);
    // RNFetchBlob.fs
    //     .writeFile(excelFile, excelWorkBook, 'utf8')
    //     .then(() => {
    //         // console.log(`wrote file ${excelFile}`);
    //         Alert.alert(
    //             'Success',
    //             `Send to ${excelFile}`,
    //             [{ text: 'OK', onPress: () => { } }],
    //             { cancelable: false },
    //         );
    //     })
    //     .catch(error => console.error(error));
  };*/

  // const emailVariant = () => {
  //     var body = {
  //         data: CsvData,
  //         //data: convertArrayToCSV(productSales.dataSource.data),
  //         data: convertArrayToCSV(CsvData),
  //         email: exportEmail,
  //     };

  //     ApiClient.POST(API.emailDashboard, body, false).then((result) => {
  //         if (result !== null) {
  //             Alert.alert(
  //                 'Success',
  //                 'Email sent to your inbox',
  //                 [{ text: 'OK', onPress: () => { } }],
  //                 { cancelable: false },
  //             );
  //         }
  //     });

  //     setVisible(false);
  // };

  const emailVariant = () => {
    const excelData = convertDataToExcelFormat();

    var body = {
      // data: CsvData,
      //data: convertArrayToCSV(todaySalesChart.dataSource.data),
      data: JSON.stringify(excelData),
      //data: convertDataToExcelFormat(),
      email: exportEmail,
    };

    ApiClient.POST(API.emailDashboard, body, false).then((result) => {
      if (result !== null) {
        alert(
          'Success: Email has been sent',
          [{ text: 'OK', onPress: () => { } }],
          { cancelable: false },
        );
      }
    });

    setVisible(false);
  };

  var leftSpacing = '0%';

  if (windowWidth >= 1280) {
    leftSpacing = '10%';
  }

  const leftSpacingScale = {
    marginLeft: leftSpacing,
  };

  const flatListRef = useRef();

  const ScrollToTop = () => {
    flatListRef.current.scrollToOffset({ animated: true, offset: 0 });
  };

  const ScrollToBottom = () => {
    flatListRef.current.scrollToEnd({ animated: true });
  };

  return (
    // <View style={styles.container}>
    //     <View style={styles.sidebar}>
    <View
      style={[
        styles.container,
        {
          ...getTransformForScreenInsideNavigation(),
        }
      ]}>
      <View
        style={[
          styles.sidebar,
          switchMerchant
            ? {
              // width: '10%'
            }
            : {},
          {
            width: windowWidth * 0.08,
            flex: 0.8
          }
        ]}>
        <SideBar
          navigation={props.navigation}
          selectedTab={8}
          expandReport={true}
        />
      </View>
      <View style={{ height: windowHeight, flex: 9 }}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{}}
          contentContainerStyle={{
            paddingBottom: windowHeight * 0.1,
            backgroundColor: Colors.highlightColor,
          }}>
          <ScrollView horizontal={true}>
            <Modal
              style={{}}
              visible={exportModalVisibility}
              supportedOrientations={["portrait", "landscape"]}
              transparent={true}
              animationType={"fade"}
            >
              <View
                style={{
                  flex: 1,
                  backgroundColor: Colors.modalBgColor,
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <View
                  style={{
                    height: Dimensions.get("screen").width * 0.08,
                    width: Dimensions.get("screen").width * 0.18,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 12,
                    padding: Dimensions.get("screen").width * 0.03,
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <TouchableOpacity
                    disabled={isLoading}
                    style={{
                      position: "absolute",
                      right: Dimensions.get("screen").width * 0.015,
                      top: Dimensions.get("screen").width * 0.01,

                      elevation: 1000,
                      zIndex: 1000,
                    }}
                    onPress={() => {
                      setExportModalVisibility(false);
                    }}
                  >
                    <AntDesign
                      name="closecircle"
                      size={switchMerchant ? 15 : 25}
                      color={Colors.fieldtTxtColor}
                    />
                  </TouchableOpacity>
                  <View
                    style={{
                      alignItems: "center",
                      top: "20%",
                      position: "absolute",
                    }}
                  >
                    <Text
                      style={{
                        fontFamily: "NunitoSans-Bold",
                        textAlign: "center",
                        fontSize: switchMerchant ? 16 : 24,
                      }}
                    >
                      Download Report
                    </Text>
                  </View>
                  <View style={{ top: switchMerchant ? "14%" : "10%" }}>
                    {/* <Text
                                        style={{
                                            fontSize: switchMerchant ? 10 : 20,
                                            fontFamily: 'NunitoSans-Bold',
                                        }}>
                                        Email Address:
                                    </Text>
                                    <TextInput
                                        underlineColorAndroid={Colors.fieldtBgColor}
                                        style={{
                                            backgroundColor: Colors.fieldtBgColor,
                                            width: switchMerchant ? 240 : 370,
                                            height: switchMerchant ? 35 : 50,
                                            borderRadius: 5,
                                            padding: 5,
                                            marginVertical: 5,
                                            borderWidth: 1,
                                            borderColor: '#E5E5E5',
                                            paddingLeft: 10,
                                            fontSize: switchMerchant ? 10 : 14,
                                        }}
                                        autoCapitalize='none'
                                        placeholderStyle={{ padding: 5 }}
                                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                        placeholder="Enter your email"
                                        onChangeText={(text) => {
                                            setExportEmail(text);
                                        }}
                                        value={exportEmail}
                                    />
                                    <Text
                                        style={{
                                            fontSize: switchMerchant ? 10 : 20,
                                            fontFamily: 'NunitoSans-Bold',
                                            marginTop: 15,
                                        }}>
                                        Send As:
                                    </Text> */}

                    <View
                      style={{
                        alignItems: "center",
                        justifyContent: "center",
                        flexDirection: "row",
                        marginTop: 30,
                      }}
                    >
                      <TouchableOpacity
                        disabled={isLoading}
                        style={{
                          justifyContent: "center",
                          flexDirection: "row",
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: "#4E9F7D",
                          borderRadius: 5,
                          width: switchMerchant ? 100 : 100,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 35 : 40,
                          alignItems: "center",
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                          marginRight: 15,
                        }}
                        onPress={() => {
                          // if (exportEmail.length > 0) {
                          //     CommonStore.update((s) => {
                          //         s.isLoading = true;
                          //     });

                          //     setIsExcel(true);

                          //     const excelData = convertDataToExcelFormat();

                          //     generateEmailReport(
                          //         EMAIL_REPORT_TYPE.EXCEL,
                          //         excelData,
                          //         'KooDoo Transaction Sales Report',
                          //         'KooDoo Transaction Sales Report.xlsx',
                          //         `/merchant/${merchantId}/reports/${uuidv4()}.xlsx`,
                          //         exportEmail,
                          //         'KooDoo Transaction Sales Report',
                          //         'KooDoo Transaction Sales Report',
                          //         () => {
                          //             CommonStore.update((s) => {
                          //                 s.isLoading = false;
                          //             });

                          //             setIsExcel(false);

                          //             window.confirm(
                          //                 'Success',
                          //                 'Report will be sent to the email address shortly',
                          //             );

                          //             setExportModalVisibility(false);
                          //         },
                          //     );
                          // } else {
                          //     window.confirm('Info', 'Invalid email address');
                          // }
                          handleExportExcel();
                        }}
                      >
                        {isLoading && isExcel ? (
                          <ActivityIndicator
                            size={"small"}
                            color={Colors.whiteColor}
                          />
                        ) : (
                          <Text
                            style={{
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: "NunitoSans-Bold",
                            }}
                          >
                            EXCEL
                          </Text>
                        )}
                      </TouchableOpacity>

                      {/* <TouchableOpacity
                                            disabled={isLoading}
                                            style={{
                                                justifyContent: 'center',
                                                flexDirection: 'row',
                                                borderWidth: 1,
                                                borderColor: Colors.primaryColor,
                                                backgroundColor: '#4E9F7D',
                                                borderRadius: 5,
                                                width: switchMerchant ? 100 : 100,
                                                paddingHorizontal: 10,
                                                height: switchMerchant ? 35 : 40,
                                                alignItems: 'center',
                                                shadowOffset: {
                                                    width: 0,
                                                    height: 2,
                                                },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 3.22,
                                                elevation: 1,
                                                zIndex: -1,
                                            }}
                                            onPress={() => {
                                                if (exportEmail.length > 0) {
                                                    CommonStore.update((s) => {
                                                        s.isLoading = true;
                                                    });

                                                    setIsCsv(true);

                                                    //const csvData = convertArrayToCSV(transactionTypeSales);
                                                    const csvData = convertDataToCSVFormat();

                                                    generateEmailReport(
                                                        EMAIL_REPORT_TYPE.CSV,
                                                        csvData,
                                                        'KooDoo Transaction Sales Report',
                                                        'KooDoo Transaction Sales Report.csv',
                                                        `/merchant/${merchantId}/reports/${uuidv4()}.csv`,
                                                        exportEmail,
                                                        'KooDoo Transaction Sales Report',
                                                        'KooDoo Transaction Sales Report',
                                                        () => {
                                                            CommonStore.update((s) => {
                                                                s.isLoading = false;
                                                            });

                                                            setIsCsv(false);

                                                            window.confirm(
                                                                'Success',
                                                                'Report will be sent to the email address shortly',
                                                            );

                                                            setExportModalVisibility(false);
                                                        },
                                                    );
                                                } else {
                                                   window.confirm('Info', 'Invalid email address');
                                                }
                                            }}>
                                            {isLoading && isCsv ? (
                                                <ActivityIndicator
                                                    size={'small'}
                                                    color={Colors.whiteColor}
                                                />
                                            ) : (
                                                <Text
                                                    style={{
                                                        color: Colors.whiteColor,
                                                        //marginLeft: 5,
                                                        fontSize: switchMerchant ? 10 : 16,
                                                        fontFamily: 'NunitoSans-Bold',
                                                    }}>
                                                    CSV
                                                </Text>
                                            )}
                                        </TouchableOpacity> */}
                      <CSVLink
                        style={{
                          justifyContent: "center",
                          flexDirection: "row",
                          borderWidth: 1,
                          textDecoration: "none",
                          borderColor: Colors.primaryColor,
                          backgroundColor: "#4E9F7D",
                          borderRadius: 5,
                          width: switchMerchant ? 100 : 100,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 35 : 40,
                          alignItems: "center",
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        }}
                        data={convertDataToCSVFormat()}
                        filename="ReportSalesVariant.csv"
                      >
                        <View
                          style={{
                            width: "100%",
                            height: "100%",
                            alignContent: "center",
                            alignItems: "center",
                            alignSelf: "center",
                            justifyContent: "center",
                          }}
                        >
                          <Text
                            style={{
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: "NunitoSans-Bold",
                            }}
                          >
                            CSV
                          </Text>
                        </View>
                      </CSVLink>

                      {/* <TouchableOpacity
                                            style={[styles.modalSaveButton, {
                                                zIndex: -1
                                            }]}
                                            onPress={() => { downloadPDF() }}>
                                            <Text style={[styles.modalDescText, { color: Colors.primaryColor }]}>PDF</Text>
                                        </TouchableOpacity> */}
                    </View>
                  </View>
                </View>
              </View>
            </Modal>

            <DateTimePickerModal
              isVisible={showDateTimePicker}
              mode={'date'}
              onConfirm={(text) => {
                // setRev_date(moment(text).startOf('day'));
                CommonStore.update(s => {
                  s.historyStartDate = moment(text).startOf('day');
                });
                setShowDateTimePicker(false);
              }}
              onCancel={() => {
                setShowDateTimePicker(false);
              }}
              maximumDate={moment(historyEndDate).toDate()}
              date={moment(historyStartDate).toDate()}
            />

            <DateTimePickerModal
              isVisible={showDateTimePicker1}
              mode={'date'}
              onConfirm={(text) => {
                // setRev_date1(moment(text).endOf('day'));
                CommonStore.update(s => {
                  s.historyEndDate = moment(text).endOf('day');
                });
                setShowDateTimePicker1(false);
              }}
              onCancel={() => {
                setShowDateTimePicker1(false);
              }}
              minimumDate={moment(historyStartDate).toDate()}
              date={moment(historyEndDate).toDate()}
            />

            <Modal
              supportedOrientations={['landscape', 'portrait']}
              style={{ flex: 1 }}
              visible={visible}
              transparent
              animationType="slide">
              <View
                //behavior="padding"
                style={{
                  backgroundColor: 'rgba(0,0,0,0.5)',
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  minHeight: windowHeight,
                }}>
                <View style={[styles.confirmBox, { ...getTransformForModalInsideNavigation(), }]}>
                  <Text
                    style={{
                      fontSize: 24,
                      justifyContent: 'center',
                      alignSelf: 'center',
                      marginTop: 40,
                      fontFamily: 'NunitoSans-Bold',
                    }}>
                    Enter your email
                  </Text>
                  <View
                    style={{
                      justifyContent: 'center',
                      alignSelf: 'center',
                      alignContent: 'center',
                      marginTop: 20,
                      flexDirection: 'row',
                      width: '80%',
                    }}>
                    <View
                      style={{ justifyContent: 'center', marginHorizontal: 5 }}>
                      <Text
                        style={{ color: Colors.descriptionColor, fontSize: 20 }}>
                        email:
                      </Text>
                    </View>
                    <TextInput
                      underlineColorAndroid={Colors.fieldtBgColor}
                      style={styles.textInput8}
                      placeholder="Enter your email"
                      // style={{
                      //     // paddingLeft: 1,
                      // }}
                      //defaultValue={extentionCharges}
                      onChangeText={(text) => {
                        // setState({ exportEmail: text });
                        setExportEmail(text);
                      }}
                      value={exportEmail}
                      placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                    />
                  </View>
                  <View
                    style={{
                      alignSelf: 'center',
                      marginTop: 20,
                      justifyContent: 'center',
                      alignItems: 'center',
                      // width: 260,
                      width: windowWidth * 0.2,
                      height: 60,
                      alignContent: 'center',
                      flexDirection: 'row',
                      marginTop: 40,
                    }}>
                    <TouchableOpacity
                      onPress={emailVariant}
                      style={{
                        backgroundColor: Colors.fieldtBgColor,
                        width: '100%',
                        justifyContent: 'center',
                        alignItems: 'center',
                        alignContent: 'center',
                        height: 60,
                        borderBottomLeftRadius: 10,
                        borderRightWidth: StyleSheet.hairlineWidth,
                        borderTopWidth: StyleSheet.hairlineWidth,
                      }}>
                      <Text
                        style={{
                          fontSize: 22,
                          color: Colors.primaryColor,
                          fontFamily: 'NunitoSans-SemiBold',
                        }}>
                        Email
                      </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => {
                        // setState({ visible: false });
                        setVisible(false);
                      }}
                      style={{
                        backgroundColor: Colors.fieldtBgColor,
                        width: '100%',
                        justifyContent: 'center',
                        alignItems: 'center',
                        alignContent: 'center',
                        height: 60,
                        borderBottomRightRadius: 10,
                        borderTopWidth: StyleSheet.hairlineWidth,
                      }}>
                      <Text
                        style={{
                          fontSize: 22,
                          color: Colors.descriptionColor,
                          fontFamily: 'NunitoSans-SemiBold',
                        }}>
                        Cancel
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </Modal>

            <View
              style={[
                styles.content,
                {
                  padding: 20,
                  width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
                  backgroundColor: Colors.highlightColor,
                  //top: Platform.OS === 'ios' && keyboardHeight > 0 ? -keyboardHeight * 1 : 0,
                },
              ]}>
              <View style={{ flex: 1 }}>
                <View
                  style={{
                    flexDirection: 'row',
                    alignSelf: 'center',
                    alignItems: 'center',
                    //backgroundColor: '#ffffff',
                    justifyContent: 'space-between',
                    //padding: 18,
                    marginTop: 5,
                    width: windowWidth * 0.87,
                  }}>
                  <View style={{ width: windowWidth * 0.25 }}>
                    <Text
                      numberOfLines={3}
                      style={{
                        fontSize: switchMerchant ? 20 : 26,
                        fontFamily: 'NunitoSans-Bold',
                      }}>
                      {!showDetails
                        ? `Sales By ${name}`
                        : `Sales By ${name}\n(${detailsTitle})`}
                    </Text>
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                    }}>
                    <View style={{ marginRight: 10, }}>
                      <DropDownPicker
                        style={{
                          backgroundColor: Colors.fieldtBgColor,
                          width: 210,
                          height: 40,
                          borderRadius: 10,
                          borderWidth: 1,
                          borderColor: "#E5E5E5",
                          flexDirection: "row",
                        }}
                        dropDownContainerStyle={{
                          width: 210,
                          backgroundColor: Colors.fieldtBgColor,
                          borderColor: "#E5E5E5",
                        }}
                        labelStyle={{
                          marginLeft: 5,
                          flexDirection: "row",
                        }}
                        textStyle={{
                          fontSize: 14,
                          fontFamily: 'NunitoSans-Regular',

                          marginLeft: 5,
                          paddingVertical: 10,
                          flexDirection: "row",
                        }}
                        selectedItemContainerStyle={{
                          flexDirection: "row",
                        }}

                        showArrowIcon={true}
                        ArrowDownIconComponent={({ style }) => (
                          <Ionicon
                            size={25}
                            color={Colors.fieldtTxtColor}
                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                            name="chevron-down-outline"
                          />
                        )}
                        ArrowUpIconComponent={({ style }) => (
                          <Ionicon
                            size={25}
                            color={Colors.fieldtTxtColor}
                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                            name="chevron-up-outline"
                          />
                        )}

                        showTickIcon={true}
                        TickIconComponent={({ press }) => (
                          <Ionicon
                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                            color={
                              press ? Colors.fieldtBgColor : Colors.primaryColor
                            }
                            name={'md-checkbox'}
                            size={25}
                          />
                        )}

                        placeholder="Choose Outlet"
                        multipleText={`${selectedOutletList ? selectedOutletList.length : '0'} outlet(s) selected`}
                        placeholderStyle={{
                          color: Colors.fieldtTxtColor,
                          // marginTop: 15,
                        }}
                        // multipleText={'%d outlet(s) selected'}
                        items={outletDropdownList}
                        value={selectedOutletList}
                        multiple={true}
                        open={openOS}
                        setOpen={setOpenOS}
                        onSelectItem={(items) => {
                          // setSelectedOutletList(items.map(item => item.value))
                          CommonStore.update((s) => {
                            s.reportOutletIdList = items.map(item => item.value)
                          })
                        }}
                        dropDownDirection="BOTTOM"
                      />
                    </View>
                    {/* hide first 9/5/2025 */}
                    {/* <View
                      style={[
                        {
                          flexDirection: 'row',
                          alignItems: 'center',
                          borderRadius: 10,
                          marginRight: 10,
                          marginLeft: 10,
                          zIndex: 5,
                        },
                      ]}>
                      <DropDownPicker
                        style={{
                          backgroundColor: Colors.fieldtBgColor,
                          width: 210,
                          height: 40,
                          borderRadius: 10,
                          borderWidth: 1,
                          borderColor: "#E5E5E5",
                          flexDirection: "row",
                        }}
                        dropDownContainerStyle={{
                          width: 210,
                          backgroundColor: Colors.fieldtBgColor,
                          borderColor: "#E5E5E5",
                        }}
                        labelStyle={{
                          marginLeft: 5,
                          flexDirection: "row",
                        }}
                        textStyle={{
                          fontSize: 14,
                          fontFamily: 'NunitoSans-Regular',

                          marginLeft: 5,
                          paddingVertical: 10,
                          flexDirection: "row",
                        }}
                        selectedItemContainerStyle={{
                          flexDirection: "row",
                        }}

                        showArrowIcon={true}
                        ArrowDownIconComponent={({ style }) => (
                          <Ionicon
                            size={25}
                            color={Colors.fieldtTxtColor}
                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                            name="chevron-down-outline"
                          />
                        )}
                        ArrowUpIconComponent={({ style }) => (
                          <Ionicon
                            size={25}
                            color={Colors.fieldtTxtColor}
                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                            name="chevron-up-outline"
                          />
                        )}

                        showTickIcon={true}
                        TickIconComponent={({ press }) => (
                          <Ionicon
                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                            color={
                              press ? Colors.fieldtBgColor : Colors.primaryColor
                            }
                            name={'md-checkbox'}
                            size={25}
                          />
                        )}
                        placeholder={'Select a Type'}
                        placeholderStyle={{
                          color: Colors.fieldtTxtColor,
                          // marginTop: 15,
                        }}
                        // searchable
                        // searchableStyle={{
                        //   paddingHorizontal: windowWidth * 0.0079,
                        // }}
                        value={filterAppType}
                        items={[
                          // { label: "All", value: "ALL" },
                          { label: "Merchant App Order", value: APP_TYPE.MERCHANT },
                          { label: "Waiter App Order", value: APP_TYPE.WAITER },
                          { label: "User App Order", value: APP_TYPE.USER },
                          { label: "QR Order", value: APP_TYPE.WEB_ORDER },
                        ]}
                        multiple={true}
                        multipleText={`${filterAppType.length} App Type(s)`}
                        onSelectItem={(items) => {
                          setFilterAppType(items.map(item => item.value))
                        }}
                        open={openFA}
                        setOpen={setOpenFA}
                        dropDownDirection="BOTTOM"
                      />
                    </View> */}
                    <TouchableOpacity
                      style={{
                        justifyContent: 'center',
                        flexDirection: 'row',
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        backgroundColor: '#4E9F7D',
                        borderRadius: 5,
                        //width: 140,
                        paddingHorizontal: 10,
                        height: switchMerchant ? 35 : 40,
                        alignItems: 'center',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,
                        marginRight: 10,
                      }}
                      onPress={() => {
                        setExportModalVisibility(true);
                      }}>
                      <View
                        style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <Icon
                          name="download"
                          size={switchMerchant ? 10 : 20}
                          color={Colors.whiteColor}
                        />
                        <Text
                          style={{
                            color: Colors.whiteColor,
                            marginLeft: 5,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          DOWNLOAD
                        </Text>
                      </View>
                    </TouchableOpacity>

                    <View
                      style={[
                        {
                          // flex: 1,
                          // alignContent: 'flex-end',
                          // marginBottom: 10,
                          // flexDirection: 'row',
                          // backgroundColor: 'red',
                          // alignItems: 'flex-end',
                          height: switchMerchant ? 35 : 40,
                          //marginTop: 10,
                        }
                      ]}>
                      <View
                        style={{
                          width: switchMerchant ? 200 : 250,
                          height: switchMerchant ? 35 : 40,
                          backgroundColor: 'white',
                          borderRadius: 5,
                          flexDirection: 'row',
                          alignContent: 'center',
                          alignItems: 'center',

                          //marginRight: windowWidth * Styles.sideBarWidth,

                          // position: 'absolute',
                          //right: '17%',

                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                        }}>
                        <Icon
                          name="search"
                          size={switchMerchant ? 13 : 18}
                          color={Colors.primaryColor}
                          style={{ marginLeft: 15 }}
                        />
                        <TextInput
                          editable={!loading}
                          underlineColorAndroid={Colors.whiteColor}
                          style={{
                            width: switchMerchant ? 180 : 220,
                            fontSize: switchMerchant ? 10 : 15,
                            fontFamily: 'NunitoSans-Regular',
                            paddingLeft: 5,
                            height: 45,
                          }}
                          clearButtonMode="while-editing"
                          placeholder=" Search"
                          placeholderTextColor={Platform.select({
                            ios: '#a9a9a9',
                          })}
                          onChangeText={(text) => {
                            setSearch(text);
                            // setList1(false);
                            // setSearchList(true);
                          }}
                          value={search}
                        />
                      </View>
                    </View>
                  </View>
                </View>
                {/* <Text style={{ fontSize: 16, marginTop: 10, color: Colors.descriptionColor }}>Date last updated on 20 OCT 2020, 1:00PM</Text> */}
                {/* <Text style={{ fontSize: 16, marginTop: 10, color: Colors.descriptionColor }}>Date last updated on {moment().format('LLLL')}</Text> */}
                <View
                  style={{
                    flexDirection: 'row',
                    //backgroundColor: '#ffffff',
                    justifyContent: 'space-between',
                    //padding: 18,
                    marginTop: 20,
                    width: windowWidth * 0.87,
                    alignSelf: 'center',
                    zIndex: -1,
                    // paddingLeft: switchMerchant
                    //   ? 0
                    //   : windowWidth <= 1823 && windowWidth >= 1820
                    //     ? '1.5%'
                    //     : '1%',
                    // paddingRight: switchMerchant
                    //   ? 0
                    //   : windowWidth <= 1823 && windowWidth >= 1820
                    //     ? '1.5%'
                    //     : '1%',
                  }}>
                  {/* <View
                              style={[{
                                  // flex: 1,
                                  // alignContent: 'flex-end',
                                  // marginBottom: 10,
                                  // flexDirection: 'row',
                                  // marginRight: '-40%',
                                  // marginLeft: 310,
                                  // backgroundColor: 'red',
                                  // alignItems: 'flex-end',
                                  // right: '-50%',
                                  width: '45%',
                                  height: 40,

                              }, !isTablet() ? {
                                  marginLeft: 0,
                              } : {}]}>
                              <View style={{
                                  width: 350,
                                  height: 40,
                                  backgroundColor: 'white',
                                  borderRadius: 10,
                                  // marginLeft: '53%',
                                  flexDirection: 'row',
                                  alignContent: 'center',
                                  alignItems: 'center',

                                  //marginRight: windowWidth * Styles.sideBarWidth,

                                  position: 'absolute',
                                  //right: '17%',

                                  shadowColor: '#000',
                                  shadowOffset: {
                                      width: 0,
                                      height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 3,
                              }}>
                                  <Icon name="search" size={18} color={Colors.fieldtTxtColor} style={{ marginLeft: 15 }} />
                                  <TextInput
                                      editable={!loading}
                                      underlineColorAndroid={Colors.whiteColor}
                                      style={{
                                          width: 250,
                                          fontSize: 15,
                                          fontFamily: 'NunitoSans-Regular',
                                      }}
                                      clearButtonMode="while-editing"
                                      placeholder=" Search"
                                      onChangeText={(text) => {
                                          setSearch(text);
                                          // setList1(false);
                                          // setSearchList(true);
                                      }}
                                      value={search}
                                  />
                              </View>
                          </View> */}

                  <TouchableOpacity
                    style={[
                      {
                        justifyContent: 'center',
                        flexDirection: 'row',
                        // borderWidth: 1,
                        // borderColor: Colors.primaryColor,
                        backgroundColor: '#4E9F7D',
                        borderRadius: 5,
                        //width: 160,
                        paddingHorizontal: 10,
                        height: switchMerchant ? 30 : 40,
                        alignItems: 'center',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,

                        opacity: !showDetails ? 0 : 100,
                      },
                    ]}
                    onPress={() => {
                      setShowDetails(false);
                      setCurrentPage(pageReturn);
                      // console.log('Returning to page');
                      // console.log(pageReturn);
                      setPageCount(Math.ceil(addOnSales.length / perPage));
                      setCurrReportSummarySort('');
                      setCurrReportDetailsSort('');
                    }}
                    disabled={!showDetails}>
                    <AntDesign
                      name="arrowleft"
                      size={switchMerchant ? 10 : 20}
                      color={Colors.whiteColor}
                      style={
                        {
                          // top: -1,
                          //marginRight: -5,
                        }
                      }
                    />
                    <Text
                      style={{
                        color: Colors.whiteColor,
                        marginLeft: 5,
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-Bold',
                        // marginBottom: Platform.OS === 'ios' ? 0 : 2
                      }}>
                      Summary
                    </Text>
                  </TouchableOpacity>

                  <View style={{ flexDirection: 'row' }}>
                    <View
                      style={[
                        {
                          //marginRight: Platform.OS === 'ios' ? 0 : 10,
                          // paddingLeft: 15,
                          paddingHorizontal: 15,
                          flexDirection: 'row',
                          alignItems: 'center',
                          borderRadius: 10,
                          paddingVertical: 10,
                          justifyContent: 'center',
                          backgroundColor: Colors.whiteColor,
                          shadowOpacity: 0,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                        },
                      ]}>
                      <View
                        style={{ alignSelf: 'center', marginRight: 5 }}
                        onPress={() => {
                          setState({
                            pickerMode: 'date',
                            showDateTimePicker: true,
                          });
                        }}>
                        {/* <EvilIcons name="calendar" size={25} color={Colors.primaryColor} /> */}
                        <GCalendar
                          width={switchMerchant ? 15 : 20}
                          height={switchMerchant ? 15 : 20}
                        />
                      </View>

                      <DatePicker
                        selected={moment(historyStartDate).toDate()}
                        onChange={(date) => {
                          // setRev_date(moment(text).startOf('day'));
                          CommonStore.update(s => {
                            s.historyStartDate = moment(date).startOf('day');
                          });
                        }}
                        maxDate={moment(historyEndDate).toDate()}
                      />

                      <Text
                        style={
                          switchMerchant
                            ? { fontSize: 10, fontFamily: "NunitoSans-Regular", marginHorizontal: 10 }
                            : { fontFamily: "NunitoSans-Regular", marginHorizontal: 10 }
                        }
                      >
                        -
                      </Text>

                      <DatePicker
                        selected={moment(historyEndDate).toDate()}
                        onChange={(date) => {
                          // setRev_date1(moment(text).endOf('day'));
                          CommonStore.update(s => {
                            s.historyEndDate = moment(date).endOf('day');
                          });
                        }}
                        minDate={moment(historyStartDate).toDate()}
                      />
                    </View>

                    {/* <TouchableOpacity
                                  style={{
                                      paddingHorizontal: 15, backgroundColor: Colors.whiteColor, height: 40, width: 120, alignItems: 'center', borderRadius: 7, flexDirection: 'row', justifyContent: 'center', shadowColor: '#000',
                                      shadowOffset: {
                                          width: 0,
                                          height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      elevation: 1,
                                  }}
                                  onPress={() => {
                                      // setState({
                                      //     visible: true
                                      // })
                                      setVisible(true);
                                  }}
                              >
                                  <Upload width={15} height={15} />
                                  <Text style={{ fontFamily: "NunitoSans-Regular", fontSize: 13, marginLeft: 12 }}>Email</Text>
                              </TouchableOpacity> */}
                  </View>

                  {/* <View style={{ flex: 4 }}>
                              <TouchableOpacity>
                                  <View style={{ width: '92%', height: 50, backgroundColor: Colors.whiteColor, borderRadius: 10, elevation: 2, alignItems: 'center', flexDirection: 'row' }}>
                                      <AntDesign name='search1' size={25} color={Colors.primaryColor} style={{ marginLeft: '5%' }} />
                                      <TextInput
                                          editable={!loading}
                                          underlineColorAndroid={Colors.whiteColor}
                                          style={{ width: '82%' }}
                                          clearButtonMode="while-editing"
                                          placeholder=" Search"
                                          onChangeText={(text) => {
                                              setState({
                                                  search: text.trim(),
                                                  list1: false,
                                                  searchList: true,
                                              });
                                          }}
                                          value={search}
                                      //onSubmitEditing={searchBarItem()}
                                      />
                                  </View>
                              </TouchableOpacity>
                          </View>
                          <View style={{ flex: 6, flexDirection: 'row', justifyContent: 'flex-end' }}>
                              <View style={{ width: '40%' }}>
                                  <TouchableOpacity style={{ width: '100%' }} onPress={() => { setState({ day: !day }) }}>
                                      <View style={{ width: '100%', height: 50, backgroundColor: Colors.whiteColor, borderRadius: 10, elevation: 2, alignItems: 'center', flexDirection: 'row' }}>
                                          <EvilIcons name='calendar' size={25} color={Colors.primaryColor} style={{ marginLeft: '5%' }} />
                                          <View style={{ justifyContent: 'center', flex: 2 }}>
                                              <Text style={{ color: Colors.descriptionColor, marginLeft: '2%', fontSize: 12 }}>{moment(startDate).format('DD MMM YYYY')} - {moment(endDate).format('DD MMM YYYY')} </Text>
                                          </View>
                                      </View>
                                  </TouchableOpacity>
                                  <DateTimePickerModal
                                      isVisible={showDateTimePicker}
                                      mode={pickerMode}
                                      onConfirm={(text) => {
                                          if (pick == 1) {
                                              var date_ob = new Date(text);
                                              let date = ("0" + date_ob.getDate()).slice(-2);
                                              let month = ("0" + (date_ob.getMonth() + 1)).slice(-2);
                                              let year = date_ob.getFullYear();
                                              setState({ startDate: year + "-" + month + "-" + date })
                                          } else {
                                              var date_ob = new Date(text);
                                              let date = ("0" + date_ob.getDate()).slice(-2);
                                              let month = ("0" + (date_ob.getMonth() + 1)).slice(-2);
                                              let year = date_ob.getFullYear();
                                              setState({ endDate: year + "-" + month + "-" + date })
                                          }

                                          setState({ showDateTimePicker: false })
                                      }}
                                      onCancel={() => {
                                          setState({ showDateTimePicker: false })
                                      }}
                                  />
                                  {day ?
                                      <View style={{ position: 'absolute', width: "100%", backgroundColor: Colors.whiteColor, marginTop: '20%', zIndex: 6000 }}>
                                          <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.primaryColor }} onPress={() => { moment() }}>
                                              <Text style={{ color: Colors.whiteColor }}>Today</Text>
                                          </TouchableOpacity>
                                          <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { setState({ startDate: moment(moment(new Date()).subtract(1, 'days')).format('YYYY-MM-DD'), endDate: moment(new Date()).format('YYYY-MM-DD') }) }}>
                                              <Text style={{ color: "#828282" }}>Yesterday</Text>
                                          </TouchableOpacity>
                                          <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { setState({ startDate: moment(moment(new Date()).subtract(7, 'days')).format('YYYY-MM-DD'), endDate: moment(new Date()).format('YYYY-MM-DD') }) }}>
                                              <Text style={{ color: "#828282" }}>Last 7 days</Text>
                                          </TouchableOpacity>
                                          <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { setState({ startDate: moment(moment(new Date()).subtract(30, 'days')).format('YYYY-MM-DD'), endDate: moment(new Date()).format('YYYY-MM-DD') }) }}>
                                              <Text style={{ color: "#828282" }}>Last 30 days</Text>
                                          </TouchableOpacity>
                                          <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { setState({ startDate: moment(moment(new Date()).startOf("month")).format('YYYY-MM-DD'), endDate: moment(moment(new Date()).endOf("month")).format('YYYY-MM-DD') }) }}>
                                              <Text style={{ color: "#828282" }}>This month</Text>
                                          </TouchableOpacity>
                                          <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { setState({ startDate: moment(moment(moment(new Date()).startOf("month")).subtract(1, 'month')).format('YYYY-MM-DD'), endDate: moment(moment(moment(new Date()).endOf("month")).subtract(1, 'month')).format('YYYY-MM-DD') }) }}>
                                              <Text style={{ color: "#828282" }}>Last month</Text>
                                          </TouchableOpacity>
                                          <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }}>
                                              <Text style={{ color: "#828282" }}>Custom range</Text>
                                          </TouchableOpacity>
                                          <View style={{ flexDirection: 'row' }}>
                                              <View style={{ flex: 1, marginLeft: 25 }}>
                                                  <Text style={{ color: "#828282" }}>From</Text>
                                              </View>
                                              <View style={{ flex: 1 }}>
                                                  <Text style={{ color: "#828282" }}>To</Text>
                                              </View>
                                          </View>
                                          <View style={{ flexDirection: 'row' }}>
                                              <TouchableOpacity style={{ width: "38%", marginLeft: 25, borderWidth: 1, alignItems: "center", borderColor: "#919191", backgroundColor: Colors.fieldtBgColor }}
                                                  onPress={() => { setState({ pick: 1, pick1: 0, pickerMode: 'date', showDateTimePicker: true }) }}>
                                                  <Text style={{ fontSize: 12 }}>{moment(startDate).format("DD MMM yyyy")}</Text>
                                              </TouchableOpacity>
                                              <View style={{ width: "8%" }}>
                                              </View>
                                              <TouchableOpacity style={{ width: "38%", borderWidth: 1, alignItems: "center", borderColor: "#919191", backgroundColor: Colors.fieldtBgColor }}
                                                  onPress={() => { setState({ pick: 0, pick1: 1, pickerMode: 'date', showDateTimePicker: true }) }}>
                                                  <Text style={{ fontSize: 12 }}>{moment(endDate).format("DD MMM yyyy")}</Text>
                                              </TouchableOpacity>
                                          </View>
                                          <View style={{ flexDirection: 'row', marginTop: 20 }}>
                                              <TouchableOpacity style={{ width: "38%", marginLeft: 15, borderWidth: 1, alignItems: "center", borderColor: "#919191", backgroundColor: Colors.whiteColor, height: 30, borderRadius: 5, justifyContent: "center", alignItems: 'center' }}
                                                  onPress={() => { setState({ day: false }) }}>
                                                  <Text style={{ fontSize: 15, color: "#919191" }}>Cancel</Text>
                                              </TouchableOpacity>
                                              <View style={{ width: "8%" }}>
                                              </View>
                                              <TouchableOpacity style={{ width: "38%", borderWidth: 1, alignItems: "center", borderColor: Colors.primaryColor, backgroundColor: Colors.primaryColor, height: 30, borderRadius: 5, justifyContent: "center", alignItems: 'center' }}
                                                  onPress={() => { setState({ day: false }), getDetail() }}>
                                                  <Text style={{ fontSize: 15, color: Colors.whiteColor }}>Apply</Text>
                                              </TouchableOpacity>
                                          </View>
                                          <View style={{ height: 20 }}>
                                          </View>
                                      </View>
                                      : null}
                              </View>
                              <View style={{ width: '4%' }}></View>
                              <TouchableOpacity style={{ width: '20%' }} onPress={() => { setState({ visible: true }); }}>
                                  <View style={{ width: '100%', height: 50, backgroundColor: Colors.whiteColor, borderRadius: 10, elevation: 2, alignItems: 'center', flexDirection: 'row' }}>
                                      <AntDesign name='download' size={20} color={Colors.primaryColor} style={{ marginLeft: '5%' }} />
                                      <View style={{ justifyContent: 'center', flex: 2 }}>
                                          <Text style={{ color: Colors.descriptionColor, marginLeft: '5%', fontSize: 15 }}>Download</Text>
                                      </View>
                                  </View>
                              </TouchableOpacity>
                              <View style={{ width: '4%' }}></View>
                              <TouchableOpacity style={{ width: '20%' }} onPress={() => { setState({ visible1: true }); }}>
                                  <View style={{ width: '100%', height: 50, backgroundColor: Colors.whiteColor, borderRadius: 10, elevation: 2, alignItems: 'center', flexDirection: 'row' }}>
                                      <AntDesign name='upload' size={20} color={Colors.primaryColor} style={{ marginLeft: '5%', flex: 1 }} />
                                      <View style={{ justifyContent: 'center', flex: 2 }}>
                                          <Text style={{ color: Colors.descriptionColor, marginLeft: '2%', fontSize: 15 }}>Email</Text>
                                      </View>
                                  </View>
                              </TouchableOpacity>
                          </View> */}
                </View>
                <View style={{ width: '100%', marginTop: 10, zIndex: -2 }}>
                  <View
                    style={{
                      backgroundColor: Colors.whiteColor,
                      width: windowWidth * 0.87,
                      height:
                        Platform.OS == 'android'
                          ? windowHeight * 0.6
                          : windowHeight * 0.65,
                      marginTop: 10,
                      marginHorizontal: 30,
                      marginBottom: 10,
                      alignSelf: 'center',
                      borderRadius: 5,
                      shadowOpacity: 0,
                      shadowColor: '#000',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 3,
                    }}>
                    {/* <View style={{ height: '88%', position: 'absolute', justifyContent: 'space-between', zIndex: 10, marginVertical: 0, marginTop: 80, alignSelf: 'center' }}>
                                  <TouchableOpacity
                                      onPress={() => {
                                          ScrollToTop();
                                      }}
                                      style={{ alignSelf: 'center', marginTop: '8%', zIndex: 10 }}>
                                      <AntDesign name={'upcircle'} size={23} color={Colors.primaryColor} style={{ opacity: 0.4 }} />
                                  </TouchableOpacity>
                                  <TouchableOpacity
                                      onPress={() => {
                                          ScrollToBottom();
                                      }}
                                      style={{ alignSelf: 'center', marginTop: '42%', zIndex: 10 }}>
                                      <AntDesign name={'downcircle'} size={23} color={Colors.primaryColor} style={{ opacity: 0.4 }} />
                                  </TouchableOpacity>
                              </View> */}

                    {!showDetails ? (
                      <View style={{ marginTop: 10, flexDirection: 'row' }}>
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '5%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={1}
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                  fontFamily: 'NunitoSans-Bold',
                                  textAlign: 'left',
                                }}>
                                {'No.\n'}
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 8 : 10,
                                  color: Colors.descriptionColor,
                                }} />
                            </View>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 7 : 14}
                                color="transparent" />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 7 : 14}
                                color="transparent" />
                              <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }} />
                            </View>
                          </View>
                          {/* <View style={{ marginLeft: '3%' }}>
                                              <TouchableOpacity onPress={() => setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_ASC)}>
                                                  <Entypo name='triangle-up' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                              </TouchableOpacity>

                                              <TouchableOpacity onPress={() => setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_DESC)}>
                                                  <Entypo name='triangle-down' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                              </TouchableOpacity>
                                          </View> */}
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '11%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportSummarySort ===
                                REPORT_SORT_FIELD_TYPE.ADD_ON_NAME_ASC
                              ) {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.ADD_ON_NAME_DESC,
                                );
                              } else {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.ADD_ON_NAME_ASC,
                                );
                              }
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Variant\nName'}
                                </Text>
                                {/* <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold', textAlign: 'center' }}>{'Variant\nGroup Name'}</Text> */}
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                              <View style={{ marginLeft: '3%' }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.ADD_ON_NAME_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.ADD_ON_NAME_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '13%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportSummarySort ===
                                REPORT_SORT_FIELD_TYPE.ADD_ON_CHOICES_ASC
                              ) {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.ADD_ON_CHOICES_DESC,
                                );
                              } else {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.ADD_ON_CHOICES_ASC,
                                );
                              }
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Variant\nOptions'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                              <View style={{ marginLeft: '3%' }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.ADD_ON_CHOICES_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.ADD_ON_CHOICES_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '8%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportSummarySort ===
                                REPORT_SORT_FIELD_TYPE.TOTAL_ITEMS_SOLD_ASC
                              ) {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.TOTAL_ITEMS_SOLD_DESC,
                                );
                              } else {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.TOTAL_ITEMS_SOLD_ASC,
                                );
                              }
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                {/* <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold', textAlign: 'center' }}>{'Total\nItem Sold'}</Text> */}
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Item\nSold'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                              <View style={{ marginLeft: '3%' }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.TOTAL_ITEMS_SOLD_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.TOTAL_ITEMS_SOLD_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '9%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportSummarySort ===
                                REPORT_SORT_FIELD_TYPE.TOTAL_SALES_ASC
                              ) {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.TOTAL_SALES_DESC,
                                );
                              } else {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.TOTAL_SALES_ASC,
                                );
                              }
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Sales\n'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  RM
                                </Text>
                              </View>
                              <View
                                style={{
                                  marginLeft: '3%',
                                  justifyContent: 'space-between',
                                }}>
                                <View>
                                  <Entypo
                                    name="triangle-up"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportSummarySort ===
                                        REPORT_SORT_FIELD_TYPE.TOTAL_SALES_ASC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    } />

                                  <Entypo
                                    name="triangle-down"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportSummarySort ===
                                        REPORT_SORT_FIELD_TYPE.TOTAL_SALES_DESC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    } />
                                </View>
                                {/* <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }}></Text> */}
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>

                        <View
                          style={{
                            flexDirection: 'row',
                            width: '7%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportSummarySort ===
                                REPORT_SORT_FIELD_TYPE.TOTAL_DISCOUNT_ASC
                              ) {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.TOTAL_DISCOUNT_DESC,
                                );
                              } else {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.TOTAL_DISCOUNT_ASC,
                                );
                              }
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Disc\n'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  RM
                                </Text>
                              </View>
                              <View
                                style={{
                                  marginLeft: '3%',
                                  left: Platform.OS === 'ios' ? 0 : '-10%',
                                }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.TOTAL_DISCOUNT_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.TOTAL_DISCOUNT_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '7%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportDetailsSort ===
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC
                              ) {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_DESC,
                                );
                              } else {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC,
                                );
                              }
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Disc\n'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  %
                                </Text>
                              </View>
                              <View
                                style={{
                                  marginLeft: '3%',
                                  left: Platform.OS === 'ios' ? 0 : '-10%',
                                }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        {/* <View style={{ flexDirection: 'row', width: '6%', borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                          <View style={{ flexDirection: 'column' }}>
                                              <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold' }}>Disc</Text>
                                              <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>%</Text>
                                          </View>
                                          <View style={{ marginLeft: '3%' }}>
                                              <TouchableOpacity onPress={() => setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.DISCOUNT_ASC)}>
                                                  <Entypo name='triangle-up' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.DISCOUNT_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                              </TouchableOpacity>

                                              <TouchableOpacity onPress={() => setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.DISCOUNT_DESC)}>
                                                  <Entypo name='triangle-down' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.DISCOUNT_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                              </TouchableOpacity>
                                          </View>
                                      </View> */}
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '8%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportSummarySort ===
                                REPORT_SORT_FIELD_TYPE.TAX_ASC
                              ) {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.TAX_DESC,
                                );
                              } else {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.TAX_ASC,
                                );
                              }
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Tax\n'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  RM
                                </Text>
                              </View>
                              <View style={{ marginLeft: '3%' }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.TAX_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.TAX_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '10%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportSummarySort ===
                                REPORT_SORT_FIELD_TYPE.SERVICE_CHARGE_ASC
                              ) {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.SERVICE_CHARGE_DESC,
                                );
                              } else {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.SERVICE_CHARGE_ASC,
                                );
                              }
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                    textAlign: 'left',
                                  }}>
                                  {'Service\nCharge'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  RM
                                </Text>
                              </View>
                              <View style={{ marginLeft: '3%' }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.SERVICE_CHARGE_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.SERVICE_CHARGE_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '10%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportSummarySort ===
                                REPORT_SORT_FIELD_TYPE.TOTAL_SALES_RETURN_ASC
                              ) {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.TOTAL_SALES_RETURN_DESC,
                                );
                              } else {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.TOTAL_SALES_RETURN_ASC,
                                );
                              }
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                {/* <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold', textAlign: 'center' }}>{'Total\nSales Return'}</Text> */}
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                    textAlign: 'left',
                                  }}>
                                  {'Sales\nReturn'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  RM
                                </Text>
                              </View>
                              <View style={{ marginLeft: '3%' }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.TOTAL_SALES_RETURN_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.TOTAL_SALES_RETURN_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>

                        <View
                          style={{
                            flexDirection: 'row',
                            width: '12%',
                            //borderRightWidth: 1,
                            //borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportSummarySort ===
                                REPORT_SORT_FIELD_TYPE.ITEM_NET_SALES_ASC
                              ) {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.ITEM_NET_SALES_DESC,
                                );
                              } else {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.ITEM_NET_SALES_ASC,
                                );
                              }
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Net Sales\n'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  RM
                                </Text>
                              </View>
                              <View
                                style={{
                                  marginLeft: '3%',
                                  justifyContent: 'space-between',
                                }}>
                                <View>
                                  <Entypo
                                    name="triangle-up"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportSummarySort ===
                                        REPORT_SORT_FIELD_TYPE.ITEM_NET_SALES_ASC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    } />

                                  <Entypo
                                    name="triangle-down"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportSummarySort ===
                                        REPORT_SORT_FIELD_TYPE.ITEM_NET_SALES_DESC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    } />
                                </View>
                                {/* <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }}></Text> */}
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        {/* <View
                          style={{
                            flexDirection: 'row',
                            width: '10%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportSummarySort ===
                                REPORT_SORT_FIELD_TYPE.AVERAGE_COST_ASC
                              ) {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.AVERAGE_COST_DESC,
                                );
                              } else {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.AVERAGE_COST_ASC,
                                );
                              }
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                    textAlign: 'left',
                                  }}>
                                  {'Avg\nCost'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  RM
                                </Text>
                              </View>
                              <View style={{ marginLeft: '3%' }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.AVERAGE_COST_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  }></Entypo>

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.AVERAGE_COST_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  }></Entypo>
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }}></Text>
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '11%',
                            borderRightWidth: 0,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportSummarySort ===
                                REPORT_SORT_FIELD_TYPE.AVERAGE_NET_SALES_ASC
                              ) {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.AVERAGE_NET_SALES_DESC,
                                );
                              } else {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.AVERAGE_NET_SALES_ASC,
                                );
                              }
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                    textAlign: 'left',
                                  }}>
                                  {'Avg\nNet Sales'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  RM
                                </Text>
                              </View>
                              <View style={{ marginLeft: '3%' }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.AVERAGE_NET_SALES_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  }></Entypo>

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.AVERAGE_NET_SALES_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  }></Entypo>
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }}></Text>
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View> */}
                        {/* <View style={{ flexDirection: 'row', width: '5%', borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                          <View style={{ flexDirection: 'column' }}>
                                              <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold' }}>GP</Text>
                                              <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>%</Text>
                                          </View>
                                          <View style={{ marginLeft: '3%' }}>
                                              <TouchableOpacity onPress={() => setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.GP_ASC)}>
                                                  <Entypo name='triangle-up' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.GP_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                              </TouchableOpacity>

                                              <TouchableOpacity onPress={() => setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.GP_DESC)}>
                                                  <Entypo name='triangle-down' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.GP_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                              </TouchableOpacity>
                                          </View>
                                      </View> */}
                      </View>
                    ) : (
                      <View style={{ marginTop: 10, flexDirection: 'row' }}>
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '6%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                  fontFamily: 'NunitoSans-Bold',
                                  textAlign: 'left',
                                }}>
                                {'No.\n'}
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 8 : 10,
                                  color: Colors.descriptionColor,
                                }} />
                            </View>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 7 : 14}
                                color="transparent" />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 7 : 14}
                                color="transparent" />
                              <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }} />
                            </View>
                          </View>
                          {/* <View style={{ marginLeft: '3%' }}>
                                              <TouchableOpacity onPress={() => setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_ASC)}>
                                                  <Entypo name='triangle-up' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                              </TouchableOpacity>

                                              <TouchableOpacity onPress={() => setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_DESC)}>
                                                  <Entypo name='triangle-down' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                              </TouchableOpacity>
                                          </View> */}
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '12%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportDetailsSort ===
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_ID_ASC
                              ) {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_ID_DESC,
                                );
                              } else {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_ID_ASC,
                                );
                              }
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Order ID\n'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                              <View style={{ marginLeft: '3%' }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_ID_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_ID_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>

                        <View
                          style={{
                            flexDirection: 'row',
                            width: '18%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportDetailsSort ===
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_DATE_TIME_ASC
                              ) {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_DATE_TIME_DESC,
                                );
                              } else {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_DATE_TIME_ASC,
                                );
                              }
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Transaction\nDate'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                              <View style={{ marginLeft: '3%' }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_DATE_TIME_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_DATE_TIME_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '10%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportDetailsSort ===
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_ASC
                              ) {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_DESC,
                                );
                              } else {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_ASC,
                                );
                              }
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Sales\n'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  RM
                                </Text>
                              </View>
                              <View
                                style={{
                                  marginLeft: '3%',
                                  justifyContent: 'space-between',
                                }}>
                                <View>
                                  <Entypo
                                    name="triangle-up"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportDetailsSort ===
                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_ASC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    } />

                                  <Entypo
                                    name="triangle-down"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportDetailsSort ===
                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_DESC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    } />
                                </View>
                                {/* <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }}></Text> */}
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '8%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportDetailsSort ===
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_TOTAL_DISCOUNT_ASC
                              ) {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_TOTAL_DISCOUNT_DESC,
                                );
                              } else {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_TOTAL_DISCOUNT_ASC,
                                );
                              }
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Disc\n'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  RM
                                </Text>
                              </View>
                              <View style={{ marginLeft: '3%' }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_TOTAL_DISCOUNT_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_TOTAL_DISCOUNT_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '8%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportDetailsSort ===
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC
                              ) {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_DESC,
                                );
                              } else {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC,
                                );
                              }
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Disc\n'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  %
                                </Text>
                              </View>
                              <View style={{ marginLeft: '3%' }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>

                        <View
                          style={{
                            flexDirection: 'row',
                            width: '8%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportDetailsSort ===
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_TAX_ASC
                              ) {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_TAX_DESC,
                                );
                              } else {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_TAX_ASC,
                                );
                              }
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Tax\n'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  RM
                                </Text>
                              </View>
                              <View style={{ marginLeft: '3%' }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_TAX_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_TAX_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '9%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportDetailsSort ===
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_ASC
                              ) {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_DESC,
                                );
                              } else {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_ASC,
                                );
                              }
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Service\nCharge'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  RM
                                </Text>
                              </View>

                              <View style={{ marginLeft: '3%' }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '9%',
                            borderRightWidth: 1,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportDetailsSort ===
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_ASC
                              ) {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_DESC,
                                );
                              } else {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_ASC,
                                );
                              }
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Sales\nReturn'}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}>
                                  RM
                                </Text>
                              </View>
                              <View style={{ marginLeft: '3%' }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  } />
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }} />
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        {/* <View style={{ flexDirection: 'row', flex: 1, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                          <View style={{ flexDirection: 'column' }}>
                                              <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold' }}>GP</Text>
                                              <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>%</Text>
                                          </View>
                                          <View style={{ marginLeft: '3%' }}>
                                              <TouchableOpacity onPress={() => setCurrReportDetailsSort(REPORT_SORT_FIELD_TYPE.USER_ORDER_GP_ASC)}>
                                                  <Entypo name='triangle-up' size={14} color={currReportDetailsSort === REPORT_SORT_FIELD_TYPE.USER_ORDER_GP_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                              </TouchableOpacity>

                                              <TouchableOpacity onPress={() => setCurrReportDetailsSort(REPORT_SORT_FIELD_TYPE.USER_ORDER_GP_DESC)}>
                                                  <Entypo name='triangle-down' size={14} color={currReportDetailsSort === REPORT_SORT_FIELD_TYPE.USER_ORDER_GP_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                              </TouchableOpacity>
                                          </View>
                                      </View> */}
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '12%',
                            borderRightWidth: 0,
                            borderRightColor: 'lightgrey',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            paddingLeft: 10,
                          }}>
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportDetailsSort ===
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_NET_SALES_ASC
                              ) {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_NET_SALES_DESC,
                                );
                              } else {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_NET_SALES_ASC,
                                );
                              }
                            }}>
                            <View style={{ flexDirection: 'row' }}>
                              <View style={{ flexDirection: 'column' }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                  {'Net Sales\n'}
                                </Text>
                                <View style={{ flexDirection: 'row' }}>
                                  <Text
                                    style={{
                                      fontSize: switchMerchant ? 8 : 10,
                                      color: Colors.descriptionColor,
                                    }}>
                                    RM
                                  </Text>
                                  <Text
                                    style={{
                                      fontSize: switchMerchant ? 8 : 10,
                                      color: Colors.primaryColor,
                                    }}>
                                    {' '}
                                    *incl tax
                                  </Text>
                                </View>
                              </View>
                              <View
                                style={{
                                  marginLeft: '3%',
                                  justifyContent: 'space-between',
                                }}>
                                <View>
                                  <Entypo
                                    name="triangle-up"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportDetailsSort ===
                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_NET_SALES_ASC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    } />

                                  <Entypo
                                    name="triangle-down"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportDetailsSort ===
                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_NET_SALES_DESC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    } />
                                </View>
                                {/* <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }}></Text> */}
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        {/* <View style={{ flexDirection: 'row', flex: 3, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                  <View>
                                      <Text style={{ fontSize: 13, fontFamily: 'NunitoSans-Regular' }}>Average Net Sales</Text>
                                      <Text style={{ fontSize: 10, color: Colors.descriptionColor }}></Text>
                                  </View>
                              </View> */}
                      </View>
                    )}

                    {isTableApiLoading && (
                      <View
                        style={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          backgroundColor: 'rgba(255, 255, 255, 0.7)',
                          justifyContent: 'center',
                          alignItems: 'center',
                          zIndex: 1000,
                        }}
                      >
                        <ActivityIndicator size="large" color={Colors.primaryColor} />
                        <Text style={{ marginTop: 10, fontSize: 16 }}>Loading...</Text>
                      </View>
                    )}

                    {!showDetails ? (
                      <>
                        {addOnSales.length > 0 ? (
                          <FlatList
                            showsVerticalScrollIndicator={false}
                            ref={flatListRef}
                            data={sortReportDataList(
                              addOnSales.filter((item) => {
                                if (search !== '') {
                                  if (
                                    item.addOnName
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.addOnChoices
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.totalItemsSold
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.totalSales
                                      .toFixed(2)
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.totalSalesReturn
                                      .toFixed(2)
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.totalDiscount
                                      .toFixed(2)
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.averageCost
                                      .toFixed(2)
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.itemNetSales
                                      .toFixed(2)
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.averageNetSales
                                      .toFixed(2)
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else {
                                    return false;
                                  }
                                } else {
                                  return true;
                                }
                              }),
                              currReportSummarySort,
                            ).slice(
                              (currentPage - 1) * perPage,
                              currentPage * perPage,
                            )}
                            // extraData={addOnSales}
                            renderItem={renderItem}
                            keyExtractor={(item, index) => String(index)}
                            style={{ marginTop: 10 }}
                          />
                        ) : (
                          <View
                            style={{
                              height: windowHeight * 0.4,
                            }}>
                            <View
                              style={{
                                alignItems: 'center',
                                justifyContent: 'center',
                                height: '100%',
                              }}>
                              <Text style={{ color: Colors.descriptionColor }}>
                                - No Data Available -
                              </Text>
                            </View>
                          </View>
                        )}
                      </>
                    ) : (
                      <>
                        {addOnSalesDetails.length > 0 ? (
                          <FlatList
                            showsVerticalScrollIndicator={false}
                            ref={flatListRef}
                            data={sortReportDataList(
                              addOnSalesDetails.filter((item) => {
                                if (search !== '') {
                                  if (
                                    item.orderId
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.totalPrice
                                      .toFixed(2)
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    getOrderDiscountInfoInclOrderBased(item)
                                      .toFixed(2)
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.discountPercentage
                                      .toFixed(2)
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.tax
                                      .toFixed(2)
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    moment(item.createdAt)
                                      .format('DD MMM YYY hh:mma')
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else {
                                    return false;
                                  }
                                } else {
                                  return true;
                                }
                              }),
                              currReportDetailsSort,
                            ).slice(
                              (currentDetailsPage - 1) * perPage,
                              currentDetailsPage * perPage,
                            )}
                            // extraData={transactionTypeSales}
                            renderItem={renderItemDetails}
                            keyExtractor={(item, index) => String(index)}
                            style={{ marginTop: 10 }}
                          />
                        ) : (
                          <View
                            style={{
                              height: windowHeight * 0.4,
                            }}>
                            <View
                              style={{
                                alignItems: 'center',
                                justifyContent: 'center',
                                height: '100%',
                              }}>
                              <Text style={{ color: Colors.descriptionColor }}>
                                - No Data Available -
                              </Text>
                            </View>
                          </View>
                        )}
                      </>
                    )}
                  </View>

                  {!showDetails ? (
                    <View
                      style={{
                        flexDirection: 'row',
                        marginTop: 10,
                        width: windowWidth * 0.87,
                        alignItems: 'center',
                        alignSelf: 'center',
                        justifyContent: 'flex-end',
                        // backgroundColor: pushPagingToTop && keyboardHeight > 0 ? Colors.highlightColor : null,
                        // borderWidth: pushPagingToTop && keyboardHeight > 0 ? 1 : 0,
                        // borderColor: pushPagingToTop && keyboardHeight > 0 ? '#E5E5E5' : null,
                        // shadowOffset: {
                        //     width: 0,
                        //     height: 1,
                        // },
                        // shadowOpacity: 0.22,
                        // shadowRadius: 3.22,
                        // elevation: 1,
                      }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Bold',
                          marginRight: '1%',
                        }}>
                        Items Showed
                      </Text>
                      <View
                        style={{
                          width: Platform.OS === 'ios' ? 65 : '13%', //65,
                          height: switchMerchant ? 20 : 35,
                          backgroundColor: Colors.whiteColor,
                          borderRadius: 10,
                          justifyContent: 'center',
                          paddingHorizontal: Platform.OS === 'ios' ? 0 : 0,
                          //paddingLeft:switchMerchant ? '4.5%': Platform.OS === 'ios' ? '2%' : '4%',
                          // paddingTop: '-60%',
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                          marginRight: '1%',
                        }}>
                        <DropDownPicker
                          style={{
                            backgroundColor: Colors.fieldtBgColor,
                            width: '100%',
                            height: 40,
                            borderRadius: 10,
                            borderWidth: 1,
                            borderColor: "#E5E5E5",
                            flexDirection: "row",
                          }}
                          dropDownContainerStyle={{
                            width: '100%',
                            backgroundColor: Colors.fieldtBgColor,
                            borderColor: "#E5E5E5",
                          }}
                          labelStyle={{
                            marginLeft: 5,
                            flexDirection: "row",
                          }}
                          textStyle={{
                            fontSize: 14,
                            fontFamily: 'NunitoSans-Regular',

                            marginLeft: 5,
                            paddingVertical: 10,
                            flexDirection: "row",
                          }}
                          selectedItemContainerStyle={{
                            flexDirection: "row",
                          }}

                          showArrowIcon={true}
                          ArrowDownIconComponent={({ style }) => (
                            <Ionicon
                              size={25}
                              color={Colors.fieldtTxtColor}
                              style={{ paddingHorizontal: 5, marginTop: 5 }}
                              name="chevron-down-outline"
                            />
                          )}
                          ArrowUpIconComponent={({ style }) => (
                            <Ionicon
                              size={25}
                              color={Colors.fieldtTxtColor}
                              style={{ paddingHorizontal: 5, marginTop: 5 }}
                              name="chevron-up-outline"
                            />
                          )}

                          showTickIcon={true}
                          TickIconComponent={({ press }) => (
                            <Ionicon
                              style={{ paddingHorizontal: 5, marginTop: 5 }}
                              color={
                                press ? Colors.fieldtBgColor : Colors.primaryColor
                              }
                              name={'md-checkbox'}
                              size={25}
                            />
                          )}
                          placeholder={'Select a Type'}
                          placeholderStyle={{
                            color: Colors.fieldtTxtColor,
                            // marginTop: 15,
                          }}
                          // searchable
                          // searchableStyle={{
                          //   paddingHorizontal: windowWidth * 0.0079,
                          // }}
                          value={perPage}
                          items={TABLE_PAGE_SIZE_DROPDOWN_LIST.concat({
                            label: 'All',
                            value: !showDetails
                              ? addOnSales.length
                              : addOnSalesDetails.length,
                          })}
                          // multiple={true}
                          // multipleText={`${item.tagIdList.length} Tag(s)`}
                          onSelectItem={(item) => {
                            setPerPage(item.value);
                            // var currentPageTemp =
                            //   text.length > 0 ? parseInt(text) : 1;

                            // setCurrentPage(
                            //   currentPageTemp > pageCount
                            //     ? pageCount
                            //     : currentPageTemp < 1
                            //       ? 1
                            //       : currentPageTemp,
                            // );
                          }}
                          open={openPage}
                          setOpen={setOpenPage}
                          dropDownDirection="TOP"
                        />
                      </View>

                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Bold',
                          marginRight: '1%',
                        }}>
                        Page
                      </Text>
                      <View
                        style={{
                          width: switchMerchant ? 65 : 70,
                          height: switchMerchant ? 20 : 35,
                          backgroundColor: Colors.whiteColor,
                          borderRadius: 10,
                          justifyContent: 'center',
                          paddingHorizontal: 22,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                        }}>
                        {console.log('currentPage')}
                        {console.log(currentPage)}

                        <TextInput
                          onChangeText={(text) => {
                            var currentPageTemp =
                              text.length > 0 ? parseInt(text) : 1;
                            // console.log('currentPage pending');
                            // console.log(
                            //   currentPageTemp > pageCount
                            //     ? pageCount
                            //     : currentPageTemp < 1
                            //       ? 1
                            //       : currentPageTemp,
                            // );
                            setCurrentPage(
                              currentPageTemp > pageCount
                                ? pageCount
                                : currentPageTemp < 1
                                  ? 1
                                  : currentPageTemp,
                            );
                          }}
                          placeholder={
                            pageCount !== 0 ? currentPage.toString() : '0'
                          }
                          placeholderTextColor={Platform.select({
                            ios: '#a9a9a9',
                          })}
                          style={{
                            color: 'black',
                            // fontFamily: 'NunitoSans-Regular',
                            fontSize: switchMerchant ? 10 : 14,
                            fontFamily: 'NunitoSans-Regular',
                            marginTop: Platform.OS === 'ios' ? 0 : -15,
                            marginBottom: Platform.OS === 'ios' ? 0 : -15,
                            textAlign: 'center',
                            width: '100%',
                          }}
                          value={pageCount !== 0 ? currentPage.toString() : '0'}
                          defaultValue={
                            pageCount !== 0 ? currentPage.toString() : '0'
                          }
                          keyboardType={'numeric'}
                          onFocus={() => {
                            setPushPagingToTop(true);
                          }}
                        />
                      </View>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Bold',
                          marginLeft: '1%',
                          marginRight: '1%',
                        }}>
                        of {pageCount}
                      </Text>
                      <TouchableOpacity
                        style={{
                          width: switchMerchant ? 30 : 45,
                          height: switchMerchant ? 20 : 28,
                          backgroundColor: Colors.primaryColor,
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                        onPress={() => {
                          prevPage();
                        }}>
                        <ArrowLeft color={Colors.whiteColor} />
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={{
                          width: switchMerchant ? 30 : 45,
                          height: switchMerchant ? 20 : 28,
                          backgroundColor: Colors.primaryColor,
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                        onPress={() => {
                          nextPage();
                        }}>
                        <ArrowRight color={Colors.whiteColor} />
                      </TouchableOpacity>
                    </View>
                  ) : (
                    <View
                      style={{
                        flexDirection: 'row',
                        marginTop: 10,
                        width: windowWidth * 0.87,
                        alignItems: 'center',
                        alignSelf: 'center',
                        justifyContent: 'flex-end',
                        // backgroundColor: pushPagingToTop && keyboardHeight > 0 ? Colors.highlightColor : null,
                        // borderWidth: pushPagingToTop && keyboardHeight > 0 ? 1 : 0,
                        // borderColor: pushPagingToTop && keyboardHeight > 0 ? '#E5E5E5' : null,
                        // shadowOffset: {
                        //     width: 0,
                        //     height: 1,
                        // },
                        // shadowOpacity: 0.22,
                        // shadowRadius: 3.22,
                        // elevation: 1,
                      }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Bold',
                          marginRight: '1%',
                        }}>
                        Items Showed
                      </Text>
                      <View
                        style={{
                          width: Platform.OS === 'ios' ? 65 : '13%', //65,
                          height: switchMerchant ? 20 : 35,
                          backgroundColor: Colors.whiteColor,
                          borderRadius: 10,
                          justifyContent: 'center',
                          paddingHorizontal: Platform.OS === 'ios' ? 0 : 0,
                          //paddingLeft:switchMerchant ? '4.5%': Platform.OS === 'ios' ? '2%' : '4%',
                          // paddingTop: '-60%',
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                          marginRight: '1%',
                        }}>
                        <DropDownPicker
                          style={{
                            backgroundColor: Colors.fieldtBgColor,
                            width: '100%',
                            height: 40,
                            borderRadius: 10,
                            borderWidth: 1,
                            borderColor: "#E5E5E5",
                            flexDirection: "row",
                          }}
                          dropDownContainerStyle={{
                            width: '100%',
                            backgroundColor: Colors.fieldtBgColor,
                            borderColor: "#E5E5E5",
                          }}
                          labelStyle={{
                            marginLeft: 5,
                            flexDirection: "row",
                          }}
                          textStyle={{
                            fontSize: 14,
                            fontFamily: 'NunitoSans-Regular',

                            marginLeft: 5,
                            paddingVertical: 10,
                            flexDirection: "row",
                          }}
                          selectedItemContainerStyle={{
                            flexDirection: "row",
                          }}

                          showArrowIcon={true}
                          ArrowDownIconComponent={({ style }) => (
                            <Ionicon
                              size={25}
                              color={Colors.fieldtTxtColor}
                              style={{ paddingHorizontal: 5, marginTop: 5 }}
                              name="chevron-down-outline"
                            />
                          )}
                          ArrowUpIconComponent={({ style }) => (
                            <Ionicon
                              size={25}
                              color={Colors.fieldtTxtColor}
                              style={{ paddingHorizontal: 5, marginTop: 5 }}
                              name="chevron-up-outline"
                            />
                          )}

                          showTickIcon={true}
                          TickIconComponent={({ press }) => (
                            <Ionicon
                              style={{ paddingHorizontal: 5, marginTop: 5 }}
                              color={
                                press ? Colors.fieldtBgColor : Colors.primaryColor
                              }
                              name={'md-checkbox'}
                              size={25}
                            />
                          )}
                          placeholder={'Select a Type'}
                          placeholderStyle={{
                            color: Colors.fieldtTxtColor,
                            // marginTop: 15,
                          }}
                          // searchable
                          // searchableStyle={{
                          //   paddingHorizontal: windowWidth * 0.0079,
                          // }}
                          value={perPage}
                          items={TABLE_PAGE_SIZE_DROPDOWN_LIST.concat({
                            label: 'All',
                            value: !showDetails
                              ? addOnSales.length
                              : addOnSalesDetails.length,
                          })}
                          // multiple={true}
                          // multipleText={`${item.tagIdList.length} Tag(s)`}
                          onSelectItem={(item) => {
                            setPerPage(item.value);
                            // var currentPageTemp =
                            //   text.length > 0 ? parseInt(text) : 1;

                            // setCurrentPage(
                            //   currentPageTemp > pageCount
                            //     ? pageCount
                            //     : currentPageTemp < 1
                            //       ? 1
                            //       : currentPageTemp,
                            // );
                          }}
                          open={openPage}
                          setOpen={setOpenPage}
                          dropDownDirection="TOP"
                        />
                      </View>

                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Bold',
                          marginRight: '1%',
                        }}>
                        Page
                      </Text>
                      <View
                        style={{
                          width: switchMerchant ? 65 : 70,
                          height: switchMerchant ? 20 : 35,
                          backgroundColor: Colors.whiteColor,
                          borderRadius: 10,
                          justifyContent: 'center',
                          paddingHorizontal: 22,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                        }}>
                        {console.log('currentDetailsPage')}
                        {console.log(currentDetailsPage)}

                        <TextInput
                          onChangeText={(text) => {
                            var currentPageTemp =
                              text.length > 0 ? parseInt(text) : 1;
                            // console.log('currentPage pending');
                            // console.log(
                            //   currentPageTemp > detailsPageCount
                            //     ? detailsPageCount
                            //     : currentPageTemp < 1
                            //       ? 1
                            //       : currentPageTemp,
                            // );
                            setCurrentDetailsPage(
                              currentPageTemp > detailsPageCount
                                ? detailsPageCount
                                : currentPageTemp < 1
                                  ? 1
                                  : currentPageTemp,
                            );
                          }}
                          placeholder={
                            pageCount !== 0
                              ? currentDetailsPage.toString()
                              : '0'
                          }
                          placeholderTextColor={Platform.select({
                            ios: '#a9a9a9',
                          })}
                          style={{
                            color: 'black',
                            // fontFamily: 'NunitoSans-Regular',
                            fontSize: switchMerchant ? 10 : 14,
                            fontFamily: 'NunitoSans-Regular',
                            marginTop: Platform.OS === 'ios' ? 0 : -15,
                            marginBottom: Platform.OS === 'ios' ? 0 : -15,
                            textAlign: 'center',
                            width: '100%',
                          }}
                          value={
                            pageCount !== 0
                              ? currentDetailsPage.toString()
                              : '0'
                          }
                          defaultValue={
                            pageCount !== 0
                              ? currentDetailsPage.toString()
                              : '0'
                          }
                          keyboardType={'numeric'}
                          onFocus={() => {
                            setPushPagingToTop(true);
                          }}
                        />
                      </View>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Bold',
                          marginLeft: '1%',
                          marginRight: '1%',
                        }}>
                        of {detailsPageCount}
                      </Text>
                      <TouchableOpacity
                        style={{
                          width: switchMerchant ? 30 : 45,
                          height: switchMerchant ? 20 : 28,
                          backgroundColor: Colors.primaryColor,
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                        onPress={() => {
                          prevDetailsPage();
                        }}>
                        <ArrowLeft color={Colors.whiteColor} />
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={{
                          width: switchMerchant ? 30 : 45,
                          height: switchMerchant ? 20 : 28,
                          backgroundColor: Colors.primaryColor,
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                        onPress={() => {
                          nextDetailsPage();
                        }}>
                        <ArrowRight color={Colors.whiteColor} />
                      </TouchableOpacity>
                    </View>
                  )}
                </View>
              </View>
              {/* <Modal style={{ flex: 1 }} visible={visible} transparent={true} animationType="slide">
                      <View
                          style={{
                              backgroundColor: 'rgba(0,0,0,0.5)',
                              flex: 1,
                              justifyContent: 'center',
                              alignItems: 'center',
                              minHeight: windowHeight,
                          }}>
                          <View style={styles.confirmBox}>
                              <View style={{ flex: 3, borderBottomWidth: StyleSheet.hairlineWidth, justifyContent: 'center', alignItems: 'center' }}>

                              </View>
                              <View style={{ flex: 1, flexDirection: 'row' }}>
                                  <TouchableOpacity style={{ flex: 1, borderRightWidth: StyleSheet.hairlineWidth, justifyContent: 'center' }} onPress={() => { download() }}>
                                      <Text style={{ color: Colors.primaryColor, fontSize: 24, fontWeight: '400', textAlign: 'center' }}>Download</Text>
                                  </TouchableOpacity>
                                  <TouchableOpacity style={{ flex: 1, justifyContent: 'center' }} onPress={() => { setState({ visible: !visible }); }}>
                                      <Text style={{ color: Colors.descriptionColor, fontSize: 24, fontWeight: '400', textAlign: 'center' }}>Cancel</Text>
                                  </TouchableOpacity>
                              </View>
                          </View>
                      </View>
                  </Modal> */}
              <Modal
                supportedOrientations={['landscape', 'portrait']}
                style={{ flex: 1 }}
                visible={visible1}
                transparent
                animationType="slide">
                <View
                  style={{
                    backgroundColor: 'rgba(0,0,0,0.5)',
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center',
                    minHeight: windowHeight,
                  }}>
                  <View style={[styles.confirmBox, { ...getTransformForModalInsideNavigation(), }]}>
                    <View
                      style={{
                        flex: 3,
                        borderBottomWidth: StyleSheet.hairlineWidth,
                        justifyContent: 'center',
                        alignItems: 'center',
                      }} />
                    <View style={{ flex: 1, flexDirection: 'row' }}>
                      <TouchableOpacity
                        style={{
                          flex: 1,
                          borderRightWidth: StyleSheet.hairlineWidth,
                          justifyContent: 'center',
                        }}
                        onPress={() => {
                          //email();
                        }}>
                        <Text
                          style={{
                            color: Colors.primaryColor,
                            fontSize: 24,
                            fontWeight: '400',
                            textAlign: 'center',
                          }}>
                          Email
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={{ flex: 1, justifyContent: 'center' }}
                        onPress={() => {
                          setState({ visible1: !visible1 });
                        }}>
                        <Text
                          style={{
                            color: Colors.descriptionColor,
                            fontSize: 24,
                            fontWeight: '400',
                            textAlign: 'center',
                          }}>
                          Cancel
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              </Modal>
            </View>
          </ScrollView>
        </ScrollView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    flexDirection: 'row',
  },
  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    padding: 20,
    width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
    backgroundColor: Colors.highlightColor,
    height: '100%',
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  confirmBox: {
    // width: '30%',
    // height: '30%',
    // borderRadius: 30,
    // backgroundColor: Colors.whiteColor,
    width: Dimensions.get('window').width * 0.4,
    height: Dimensions.get('window').height * 0.3,
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
    justifyContent: 'space-between',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView: {
    height: Dimensions.get('window').width * 0.2,
    width: Dimensions.get('window').width * 0.3,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get('window').width * 0.03,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButton: {
    position: 'absolute',
    right: Dimensions.get('window').width * 0.02,
    top: Dimensions.get('window').width * 0.02,

    elevation: 1000,
    zIndex: 1000,
  },
  modalTitle: {
    alignItems: 'center',
    top: '20%',
    position: 'absolute',
  },
  modalBody: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalTitleText: {
    fontFamily: 'NunitoSans-Bold',
    textAlign: 'center',
    fontSize: 20,
  },
  modalDescText: {
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 18,
    color: Colors.fieldtTxtColor,
  },
  modalBodyText: {
    flex: 1,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
    width: '20%',
  },
  modalSaveButton: {
    width: Dimensions.get('window').width * 0.15,
    backgroundColor: Colors.fieldtBgColor,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,

    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,

    marginVertical: 10,
  },
  submitText: {
    height:
      Platform.OS == 'ios'
        ? Dimensions.get('window').height * 0.06
        : Dimensions.get('window').height * 0.07,
    paddingVertical: 5,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  headerLeftStyle: {
    width: Dimensions.get('screen').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
export default ReportSalesVariant;
