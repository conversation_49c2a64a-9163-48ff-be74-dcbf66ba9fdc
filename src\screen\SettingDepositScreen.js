import React, { useState, useEffect, useCallback } from 'react';
import {
    StyleSheet,
    Image,
    View,
    Text,
    TouchableOpacity,
    Dimensions,
    Alert,
    TextInput,
    ActivityIndicator,
    useWindowDimensions,
    Modal,
    Button,
    ScrollView,
} from 'react-native';
import { Platform } from 'react-native';
import Colors from '../constant/Colors';
// import Switch from 'react-native-switch-pro';
import Switch from 'react-switch';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import Styles from '../constant/Styles';
import {
    getTransformForScreenInsideNavigation,
    // isTablet
} from '../util/common';
import { MerchantStore } from '../store/merchantStore';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import { parseValidPriceText, } from '../util/common';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Swipeable from 'react-native-gesture-handler/Swipeable';
import {
    DELAY_LONG_PRESS_TIME,
    MODE_ADD_CART,
    OFFLINE_BILL_TYPE,
    OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST,
    OFFLINE_PAYMENT_METHOD_TYPE,
    ORDER_TYPE,
    USER_ORDER_PRIORITY,
    USER_ORDER_STATUS,
    EXPAND_TAB_TYPE,
} from '../constant/common';
// import { useKeyboard } from '../hooks';
// import RNPickerSelect from 'react-native-picker-select';
import {
    getObjectDiff,
    isObjectEqual,
    listenToCurrOutletIdChangesWaiter,
    naturalCompare,
} from '../util/common';
import { qrUrl } from '../constant/env';
import { printUserOrder } from '../util/printer';
import { Collections } from '../constant/firebase';
// import firestore from '@react-native-firebase/firestore';
import firebase from "firebase";
// import CheckBox from 'react-native-check-box';
import AsyncImage from '../components/asyncImage';
import 'react-native-get-random-values';
import SideBar from './SideBar';
// import { ScrollView } from 'react-native-gesture-handler';
import { useFocusEffect } from '@react-navigation/native';
// import UserIdleWrapper from '../components/userIdleWrapper';
import APILocal from '../util/apiLocalReplacers';
import personicon from "../assets/image/default-profile.png";
import headerLogo from "../assets/image/logo.png";

const SettingDepositScreen = (props) => {
    const { navigation } = props;

    ///////////////////////////////////////////////////////////

    const [isMounted, setIsMounted] = useState(true);

    useFocusEffect(
        useCallback(() => {
            setIsMounted(true);
            return () => {
                setIsMounted(false);
            };
        }, [])
    );

    ///////////////////////////////////////////////////////////

    const { width: windowWidth, height: windowHeight } = useWindowDimensions();

    const [switchMerchant, setSwitchMerchant] = useState(false);

    ///////////////////////////////////////////////////////////////

    const [temp, setTemp] = useState('');

    const [reservationDepositAmount, setReservationDepositAmount] = useState('0.00');

    ///////////////////////////////////////////////////////////////

    const currOutletId = MerchantStore.useState(s => s.currOutletId);
    const currOutlet = MerchantStore.useState(s => s.currOutlet);

    const isLoading = CommonStore.useState(s => s.isLoading);

    const outletSelectDropdownView = CommonStore.useState(
        (s) => s.outletSelectDropdownView,
    );
    const userName = UserStore.useState((s) => s.name);

    const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
    const expandTab = CommonStore.useState((s) => s.expandTab);
    const currPageStack = CommonStore.useState((s) => s.currPageStack);

    ///////////////////////////////////////////////////////////////////////////////

    useEffect(() => {
        if (currOutlet && currOutlet.reservationDepositAmount) {
            setReservationDepositAmount(currOutlet.reservationDepositAmount.toFixed(2));
        }
    }, [currOutlet]);

    ///////////////////////////////////////////////////////////////////////////////

    const switchReservationStatus = (value) => {
        // var body = {
        //   outletId: User.getOutletId(),
        // };

        var body = {
            // outletId: User.getOutletId()
            outletId: currOutlet.uniqueId,
            reservationStatus: value,
        };

        CommonStore.update((s) => {
            s.isLoading = true;
        });

        // ApiClient.POST(API.switchReservationStatus, body)
        APILocal.switchReservationStatus({ body: body })
            .then((result) => {
                if (result.status) {
                    setTimeout(() => {
                        CommonStore.update((s) => {
                            s.isLoading = false;
                        });
                    }, 2000);
                }
                // if (result.reservationStatus == true) {
                //   Alert.alert(
                //     'Success',
                //     'Open Rservation',
                //     [{ text: 'OK', onPress: () => { } }],
                //     { cancelable: false },
                //   );
                // }

                // if (result.reservationStatus == false) {
                //   Alert.alert(
                //     'Success',
                //     'Close Rservation',
                //     [{ text: 'OK', onPress: () => { } }],
                //     { cancelable: false },
                //   );
                // }
            })
            .catch((err) => {
                // console.log(err);
            });
    };

    const updateOutletReservationSettings = async (param) => {
        if (reservationDepositAmount.length <= 0 ||
            isNaN(parseFloat(reservationDepositAmount))) {
            Alert.alert('Info', 'Invalid reservation deposit amount.');
            return;
        }

        var body = {
            reservationDepositAmount: parseFloat(reservationDepositAmount),
            outletId: currOutletId,
        };

        // console.log('body', body);

        // CommonStore.update(s => {
        //     s.isLoading = true;
        // });

        ApiClient.POST(API.updateOutletReservationSettings, body, false).then((result) => {

        });

        Alert.alert(
            'Success',
            'Reservation settings has been updated, and will be synced to devices across the outlet.',
            [
                {
                    text: 'OK',
                    onPress: () => {
                        // CommonStore.update(s => {
                        //     s.isLoading = false;
                        // });
                    },
                },
            ],
            { cancelable: false },
        );
    };

    ///////////////////////////////////////////////////////////////////////////////

    // Navigation bar
    navigation.setOptions({
        headerLeft: () => (
            <View
                style={[
                    styles.headerLeftStyle,
                    {
                        width: windowWidth * 0.17,
                    },
                ]}
            >
                <img src={headerLogo} width={124} height={26} />
                {/* <Image
              style={{
                width: 124,
                height: 26,
              }}
              resizeMode="contain"
              source={require('../assets/image/logo.png')}
            /> */}
            </View>
        ),
        headerTitle: () => (
            <View
                style={[
                    {
                        justifyContent: "center",
                        alignItems: "center",
                        // marginRight: Platform.OS === 'ios' ? "27%" : 0,
                        // bottom: switchMerchant ? '2%' : 0,
                        //width:  "55%",
                    },
                    Dimensions.get("screen").width <= 768
                        ? { right: Dimensions.get("screen").width * 0.12 }
                        : {},
                ]}
            >
                <Text
                    style={{
                        fontSize: 24,
                        // lineHeight: 25,
                        textAlign: "center",
                        fontFamily: "NunitoSans-Bold",
                        color: Colors.whiteColor,
                        opacity: 1,
                    }}
                >
                    Setting Deposit
                </Text>
            </View>
        ),
        headerRight: () => (
            <View
                style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between",
                }}
            >
                {/* {console.log('edward test')} */}
                {/* {console.log(outletSelectDropdownView)} */}
                {outletSelectDropdownView && outletSelectDropdownView()}
                <View
                    style={{
                        backgroundColor: "white",
                        width: 0.5,
                        height: Dimensions.get("screen").height * 0.025,
                        opacity: 0.8,
                        marginHorizontal: 15,
                        bottom: -1,
                        // borderWidth: 1
                    }}
                ></View>
                <TouchableOpacity
                    onPress={() => {
                        if (global.currUserRole === 'admin') {
                            navigation.navigate("General Settings - KooDoo BackOffice")
                        }
                    }}
                    style={{ flexDirection: "row", alignItems: "center" }}
                >
                    <Text
                        style={{
                            fontFamily: "NunitoSans-SemiBold",
                            fontSize: 16,
                            color: Colors.secondaryColor,
                            marginRight: 15,
                        }}
                    >
                        {userName}
                    </Text>
                    <View
                        style={{
                            //backgroundColor: 'red',
                            marginRight: 30,
                            width: windowHeight * 0.05,
                            height: windowHeight * 0.05,
                            borderRadius: windowHeight * 0.05 * 0.5,
                            alignItems: "center",
                            justifyContent: "center",
                            backgroundColor: "white",
                        }}
                    >
                        <img
                            src={personicon}
                            width={windowHeight * 0.035}
                            height={windowHeight * 0.035}
                        />
                        {/* <Image
                  style={{
                    width: windowHeight * 0.05,
                  height: windowHeight * 0.05,
                    alignSelf: 'center',
                  }}
                  source={require('../assets/image/profile-pic.jpg')}
                /> */}
                    </View>
                </TouchableOpacity>
            </View>
        ),
    });

    return (
        // <UserIdleWrapper disabled={!isMounted}>
        <View
            style={[
                styles.container,
                // !isTablet()
                //     ? {
                //         transform: [{ scaleX: 1 }, { scaleY: 1 }],
                //     }
                //     : {},
                // {
                //     ...getTransformForScreenInsideNavigation(),
                // }
            ]}>
            {/* Sidebar */}
            <View
                style={[
                    styles.sidebar,
                    // !isTablet()
                    //     ? {
                    //         width: windowWidth * 0.08,
                    //     }
                    //     : {},
                    // switchMerchant
                    //     ? {
                    //         width: '10%',
                    //     }
                    //     : {},
                    // {
                    //     width: windowWidth * 0.08,
                    // }
                ]}>
                <SideBar
                    navigation={props.navigation}
                    selectedTab={1}
                    expandOperation={true}
                />
            </View>
            <View
                style={[
                    switchMerchant
                        ? { width: windowWidth * 0.89 }
                        : {
                            //height: windowHeight * 0.07,
                            backgroundColor: Colors.highlightColor,
                            width: windowWidth * 0.92,
                        },
                ]}>

                <View style={{
                    marginTop: '3%',
                    width: '100%',

                    alignItems: 'flex-end',

                    paddingHorizontal: '2%',
                }}>
                    <TouchableOpacity
                        style={[{
                            justifyContent: 'center',
                            flexDirection: 'row',
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: '#4E9F7D',
                            borderRadius: 5,
                            width: 130,
                            paddingHorizontal: 10,
                            height: 40,
                            alignItems: 'center',
                            shadowOffset: {
                                width: 0,
                                height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                            marginBottom: 10,
                        }, switchMerchant ? {
                            height: 35,
                            width: 120,
                        } : {}]}
                        disabled={isLoading}
                        onPress={() => updateOutletReservationSettings()}
                    >
                        <Text
                            style={[{
                                color: Colors.whiteColor,
                                fontSize: 16,
                                fontFamily: 'NunitoSans-Bold',
                            }, switchMerchant ? {
                                fontSize: 10,
                            } : {}]}>
                            {isLoading ? 'LOADING...' : 'SAVE'}
                        </Text>

                        {isLoading ?
                            <ActivityIndicator color={Colors.whiteColor} size={'small'} />
                            : <></>
                        }
                    </TouchableOpacity>
                </View>

                <View
                    style={{
                        paddingTop: windowHeight * 0.027,
                        paddingHorizontal: 20,
                    }}>
                    {/* <View>
                    <TouchableOpacity
                        onPress={() => {
                            //props.navigation.navigate('');
                        }}
                        style={{
                            paddingHorizontal: windowWidth * 0.01,
                            borderBottomWidth: 1,
                            borderBottomColor: '#EBEDEF',
                            borderTopLeftRadius: 10,
                            borderTopRightRadius: 10,
                            width: '100%',
                            backgroundColor: '#ffffff',
                            paddingVertical: windowHeight * 0.02,
                        }}>
                        <View
                            style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                            }}>
                            <Text
                                style={{
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                }}>
                                Online Reservations
                            </Text>
                            <Plus
                                name="chevron-right"
                                size={20}
                                color={Colors.darkBgColor}
                                style={{}}
                            />
                        </View>
                    </TouchableOpacity>
                </View>
                <View>
                    <TouchableOpacity
                        onPress={() => {
                            //props.navigation.navigate('');
                        }}
                        style={{
                            paddingHorizontal: windowWidth * 0.01,
                            borderBottomWidth: 1,
                            borderBottomColor: '#EBEDEF',
                            width: '100%',
                            backgroundColor: '#ffffff',
                            paddingVertical: windowHeight * 0.02,
                        }}>
                        <View
                            style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                            }}>
                            <Text
                                style={{
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                }}>
                                Notes and policies
                            </Text>
                            <Plus
                                name="chevron-right"
                                size={20}
                                color={Colors.darkBgColor}
                                style={{}}
                            />
                        </View>
                    </TouchableOpacity>
                </View>
                <View>
                    <View
                        style={{
                            paddingHorizontal: windowWidth * 0.01,
                            borderBottomWidth: 1,
                            borderBottomColor: '#EBEDEF',
                            width: '100%',
                            backgroundColor: '#ffffff',
                            paddingVertical: windowHeight * 0.02,
                            zIndex: 1000,
                        }}>
                        <View
                            style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                            }}>
                            <Text
                                style={{
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                }}>
                                Record spend upon finish?
                            </Text>
                            <DropDownPicker
                                arrowColor={'black'}
                                arrowSize={20}
                                arrowStyle={{ fontWeight: 'bold' }}
                                style={[
                                    {
                                        height: switchMerchant ? 35 : 40,
                                        width: 200,
                                        paddingVertical: 0,
                                        backgroundColor: Colors.fieldtBgColor,
                                        borderRadius: 10,
                                        zIndex: 1000,
                                    },
                                    switchMerchant
                                        ? {
                                            width:
                                                windowWidth * 0.195,
                                        }
                                        : {},
                                ]}
                                placeholderStyle={{
                                    color: Colors.blackColor,
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                }}
                                items={[
                                    {
                                        label: 'Yes (Optional)',
                                        value: 'Yes_Optional',
                                    },
                                    {
                                        label: 'Yes (Mandatory)',
                                        value: 'Yes_Mandatory',
                                    },
                                    {
                                        label: 'No',
                                        value: 'No',
                                    },
                                ]}
                                itemStyle={{
                                    justifyContent: 'flex-start',
                                    zIndex: 1000,
                                }}
                                placeholder={'Yes (Optional)'}
                                onChangeItem={(item) => {
                                    setReord(item.value);
                                }}
                                defaultValue={record}
                                value={record}
                                dropDownMaxHeight={150}
                                dropDownStyle={[
                                    {
                                        //width: 250,
                                        width: 200,
                                        height: 120,
                                        backgroundColor: Colors.fieldtBgColor,
                                        borderRadius: 10,
                                        borderWidth: 1,
                                        textAlign: 'left',
                                        zIndex: 1000,
                                    },
                                    switchMerchant
                                        ? {
                                            width:
                                                windowWidth * 0.195,
                                        }
                                        : {},
                                ]}
                                globalTextStyle={{
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                    color: Colors.fontDark,
                                    marginLeft: 5,
                                }}
                            />
                        </View>
                    </View>
                </View>
                <View>
                    <View
                        style={{
                            paddingHorizontal: windowWidth * 0.01,
                            borderBottomWidth: 1,
                            borderBottomColor: '#EBEDEF',
                            borderBottomLeftRadius: 10,
                            borderBottomRightRadius: 10,
                            width: '100%',
                            backgroundColor: '#ffffff',
                            paddingVertical: windowHeight * 0.02,
                        }}>
                        <View
                            style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                            }}>
                            <Text
                                style={{
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                }}>
                                Select venue when adding new reservation
                            </Text>
                            <Switch
                                width={42}
                                style={{}}
                                value={selectVenue}
                                onSyncPress={(statusTemp) =>
                                    setSelectVenue(statusTemp)
                                }
                                circleColorActive={Colors.primaryColor}
                                circleColorInactive={Colors.fieldtTxtColor}
                                backgroundActive="#dddddd"
                            />
                        </View>
                    </View>
                </View> */}

                    {/* <ScrollView> */}
                    <View>
                        <View
                            style={{
                                paddingHorizontal: windowWidth * 0.01,
                                borderBottomWidth: 1,
                                borderBottomColor: '#EBEDEF',
                                borderBottomLeftRadius: 10,
                                borderBottomRightRadius: 10,
                                width: switchMerchant ? windowWidth * 0.85 : '100%',
                                backgroundColor: '#ffffff',
                                paddingVertical: windowHeight * 0.02,
                            }}>
                            <View
                                style={[{
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                }, switchMerchant ? { width: '50%' } : {}]}>
                                <Text
                                    style={{
                                        fontFamily: 'NunitoSans-Bold',
                                        fontSize: switchMerchant ? 10 : 14,
                                    }}>
                                    Deposit for online reservation (Amount below RM 1.01 will not take place)
                                </Text>

                            </View>
                            <View
                                style={{
                                    flexDirection: 'row',
                                    marginTop: 15,
                                    // paddingLeft: '10%',
                                    // borderWidth: 1
                                    //left: Platform.OS == 'ios' ? 130 : 0
                                    justifyContent: 'space-between',
                                }}>
                                <View style={{ flexDirection: "row", alignItems: 'center', marginTop: 15, }}>
                                    <Text
                                        style={{
                                            fontFamily: 'NunitoSans-Bold',
                                            fontSize: switchMerchant ? 10 : 14,
                                        }}>
                                        Accepting Reservation:
                                    </Text>
                                    <View
                                        style={[
                                            {
                                                flexDirection: 'row',
                                                //right: Platform.OS == 'ios' ? 40 : 0,
                                                top: Platform.OS == 'ios' ? 3 : 0,
                                                marginLeft: '10%',
                                                // height: '15%',
                                                alignItems: 'center',
                                            },
                                            switchMerchant
                                                ? {
                                                    // borderWidth: 1,
                                                    // top: windowHeight * -0.045,
                                                }
                                                : {},
                                        ]}>
                                        {isLoading ? (
                                            <View
                                                style={
                                                    {
                                                        // paddingTop: Platform.OS == 'ios' ? 10 : 0,
                                                    }
                                                }>
                                                {switchMerchant ? (
                                                    <ActivityIndicator
                                                        size={'small'}
                                                        color={Colors.secondaryColor}
                                                    />
                                                ) : (
                                                    <ActivityIndicator
                                                        size={'large'}
                                                        color={Colors.secondaryColor}
                                                    />
                                                )}
                                            </View>
                                        ) : (
                                            <>
                                                {switchMerchant ? (
                                                    <Switch
                                                        value={currOutlet.reservationStatus}
                                                        onSyncPress={(value) => {
                                                            // setState({ newReservationStatus: item }, () => {
                                                            //   switchReservationStatus();
                                                            // });
                                                            switchReservationStatus(value);
                                                            //// console.log(userReservations.length)
                                                        }}
                                                        width={20}
                                                        height={10}
                                                        circleColorActive={Colors.primaryColor}
                                                        circleColorInactive={Colors.fieldtTxtColor}
                                                        backgroundActive="#dddddd"
                                                    />
                                                ) : (
                                                    // <Switch
                                                    //     value={currOutlet.reservationStatus}
                                                    //     onSyncPress={(value) => {
                                                    //         // setState({ newReservationStatus: item }, () => {
                                                    //         //   switchReservationStatus();
                                                    //         // });
                                                    //         switchReservationStatus(value);
                                                    //         //// console.log(userReservations.length)
                                                    //     }}
                                                    //     width={42}
                                                    //     circleColorActive={Colors.primaryColor}
                                                    //     circleColorInactive={Colors.fieldtTxtColor}
                                                    //     backgroundActive="#dddddd"
                                                    // />
                                                    <Switch
                                                        checked={currOutlet.reservationStatus}
                                                        onChange={(statusTemp) => {
                                                            // setState({ status: status })
                                                            switchReservationStatus(statusTemp);
                                                        }}
                                                        width={35}
                                                        height={20}
                                                        handleDiameter={30}
                                                        uncheckedIcon={false}
                                                        checkedIcon={false}
                                                        boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                                        activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                                                    />
                                                )}

                                                <Text
                                                    style={[
                                                        {
                                                            fontSize: 18,
                                                            marginTop: 0,
                                                            color: currOutlet.reservationStatus
                                                                ? Colors.primaryColor
                                                                : Colors.fieldtTxtColor,
                                                            textAlign: 'center',
                                                            right: Platform.OS == 'ios' ? 1 : 0,

                                                            marginLeft: 5,
                                                        },
                                                        switchMerchant
                                                            ? {
                                                                fontSize: 10,
                                                            }
                                                            : {},
                                                    ]}>
                                                    {currOutlet.reservationStatus ? 'ON' : 'OFF'}
                                                </Text>
                                            </>
                                        )}
                                    </View>
                                </View>
                                <View style={{ marginTop: 15, alignItems: 'center' }}>
                                    <TextInput style={[{
                                        // marginTop: '2%',
                                        padding: 5,
                                        backgroundColor: Colors.fieldtBgColor,
                                        width: 100,
                                        // height: Platform.OS == 'ios' ? 100 : 117,
                                        borderRadius: 5,
                                        borderWidth: 1,
                                        borderColor: '#E5E5E5',
                                        paddingTop: Platform.OS == 'ios' ? 10 : 10,
                                        paddingLeft: 10,
                                        fontFamily: 'NunitoSans-Regular',
                                        fontSize: 14
                                    }, switchMerchant ? {
                                        fontSize: 10,
                                        width: '70%',
                                        height: 35,
                                        marginLeft: 10,
                                    } : {}]}
                                        textAlignVertical={'top'}
                                        placeholderStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: 14 }}
                                        placeholder='Amount'
                                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                        defaultValue={reservationDepositAmount}
                                        multiline={true}
                                        //iOS
                                        // clearTextOnFocus={true}
                                        //////////////////////////////////////////////
                                        //Android
                                        // onFocus={() => {
                                        //     setTemp(reservationDepositAmount)
                                        //     setReservationDepositAmount('');
                                        // }}
                                        // ///////////////////////////////////////////////
                                        // //When textinput is not selected
                                        // onEndEditing={() => {
                                        //     if (reservationDepositAmount == '') {
                                        //         setReservationDepositAmount(temp);
                                        //     }
                                        // }}
                                        onChangeText={text => {
                                            setReservationDepositAmount(parseValidPriceText(text));
                                        }}
                                        keyboardType='numeric'
                                    />
                                </View>
                            </View>
                        </View>
                    </View>
                    {/* </ScrollView> */}

                </View>
            </View>
        </View>
        // </UserIdleWrapper >
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#EBEDEF',
        flexDirection: 'row',
    },
    headerLogo: {
        width: 112,
        height: 25,
        marginLeft: 10,
    },
    list: {
        paddingVertical: 20,
        paddingHorizontal: 30,
        flexDirection: 'row',
        alignItems: 'center',
    },

    listItem: {
        backgroundColor: Colors.whiteColor,
        borderWidth: StyleSheet.hairlineWidth,
        borderColor: '#c4c4c4',
        borderRadius: 20,
        paddingVertical: 20,
        paddingHorizontal: 20,
        marginRight: 10,
        marginBottom: 10,
        width: (Dimensions.get('window').width - 150) / 2,
    },

    tablebox: {
        backgroundColor: Colors.whiteColor,
        shadowColor: '#c4c4c4',
        shadowOffset: {
            width: 8,
            height: 8,
        },
        shadowOpacity: 0.55,
        shadowRadius: 10.32,
        width: 100,
        height: 100,
        marginRight: 25,
        borderRadius: 10,
        marginBottom: 30,
        marginTop: 10,
        marginHorizontal: 20,
    },

    sidebar: {
        width: Dimensions.get('window').width * Styles.sideBarWidth,
        // shadowColor: '#000',
        // shadowOffset: {
        //   width: 0,
        //   height: 8,
        // },
        // shadowOpacity: 0.44,
        // shadowRadius: 10.32,

        // elevation: 16,
    },
    circleIcon: {
        width: 30,
        height: 30,
        // resizeMode: 'contain',
        marginRight: 10,
        alignSelf: 'center',
    },
    headerLeftStyle: {
        width: Dimensions.get('window').width * 0.17,
        justifyContent: 'center',
        alignItems: 'center',
    },
});

export default SettingDepositScreen;
