import React, {
  Component,
  useReducer,
  useState,
  useEffect,
  useMemo,
  useCallback,
} from 'react';
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Modal,
  Text,
  Alert,
  Dimensions,
  TouchableOpacity,
  FlatList,
  Linking,
  ActivityIndicator,
  Platform,
  useWindowDimensions,
} from 'react-native';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import Entypo from 'react-native-vector-icons/Entypo';
import Icon from 'react-native-vector-icons/Feather';
import Icon3 from 'react-native-vector-icons/EvilIcons';
//import Swipeout from 'react-native-swipeout';
import Icon1 from 'react-native-vector-icons/AntDesign';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Ionicon from 'react-native-vector-icons/Ionicons';
import SimpleLineIcons from 'react-native-vector-icons/SimpleLineIcons';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import AsyncStorage from '@react-native-async-storage/async-storage';
import DropDownPicker from 'react-native-dropdown-picker';
import * as User from '../util/User';
import Styles from '../constant/Styles';
import moment from 'moment';
import MIcon from 'react-native-vector-icons/MaterialCommunityIcons';
//import Switch from 'react-native-switch-pro';
import Swipeable from 'react-native-gesture-handler/Swipeable';
import Select from "react-select";
import {
  ipV6ToIpV4,
  getTransformForScreenInsideNavigation,
  getTransformForModalInsideNavigation,
  sliceUnicodeStringV2WithDots,
} from '../util/common';
import { OutletStore } from '../store/outletStore';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import { TextInput } from 'react-native-gesture-handler';
//import RNPickerSelect from 'react-native-picker-select';
import { CommonStore } from '../store/commonStore';
import AsyncImage from '../components/asyncImage';
import Feather from 'react-native-vector-icons/Feather';
import { KD_PRINT_VARIATION, KD_PRINT_VARIATION_DROPDOWN_LIST, PRINTER_USER_PRIORITY, PRINTER_USER_PRIORITY_DROPDOWN_LIST, USER_QUEUE_STATUS, USER_QUEUE_STATUS_PARSED } from '../constant/common';
import {
  QUEUE_SORT_FIELD_TYPE,
  REPORT_SORT_FIELD_TYPE_COMPARE,
  QUEUE_SORT_FIELD_TYPE_VALUE,
  REPORT_SORT_COMPARE_OPERATOR,
  EXPAND_TAB_TYPE,
} from '../constant/common';
import {
  // USBPrinter,
  NetPrinter,
  BLEPrinter,
} from 'react-native-thermal-receipt-printer-image-qr';
import {
  CODEPAGE,
  ESCPOS_CMD,
  KD_OPTIONS_DELIVER_REJECT,
  KD_OPTIONS_DELIVER_REJECT_DROPDOWN_LIST,
  PRINTER_COMMAND_TYPE,
  PRINTER_COMMAND_TYPE_DROPDOWN_LIST,
  PRINTER_PAPER_WIDTH,
  PRINTER_PAPER_WIDTH_DROPDOWN_LIST,
  PRINTER_USAGE_TYPE,
  PRINTER_USAGE_TYPE_DROPDOWN_LIST,
  PW_CONFIG,
  PW_CONFIG_TYPE
} from '../constant/printer';
import {
  connectToPrinter,
  connectToPrinterFast,
  encodeGB18030,
  encodeGBK,
  isPortOpen,
  padEndUnicodeStringV2,
  printShiftReport,
  sliceUnicodeStringV2,
  openCashDrawer,
  isUsbPrinter,
  preparePrintImageCommand,
  printToUsbDevice,
} from '../util/printer';
import NetInfo, { useNetInfo } from '@react-native-community/netinfo';
//import { useKeyboard } from '../hooks';
import APILocal from '../util/apiLocalReplacers';
import { useFocusEffect } from '@react-navigation/native';
//import UserIdleWrapper from '../components/userIdleWrapper';
import personicon from "../assets/image/default-profile.png";
import MultiSelect from "react-multiple-select-dropdown-lite";
import headerLogo from "../assets/image/logo.png";
import { PRINTER_DEVICE_TYPE } from '../constant/printer';

const SettingPrinterScreen = (props) => {
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const netInfo = useNetInfo();

  //const [keyboardHeight] = useKeyboard();

  const [visible, setVisible] = useState(false);

  const [addQueueModal, setAddQueueModal] = useState(false);
  const [confirmQueueModal, setConfirmQueueModal] = useState(false);
  const [switchMerchant, setSwitchMerchant] = useState();
  const [selectedQueue, setSelectedQueue] = useState(null);
  const [queueCustomerName, setQueueCustomerName] = useState('');
  const [queuePhone, setQueuePhone] = useState('');
  const [queuePax, setQueuePax] = useState(0);

  const [printerName, setPrinterName] = useState('');
  const [printerIP, setPrinterIP] = useState('');
  const [printerArea, setPrinterArea] = useState('');
  const [printerTypes, setPrinterTypes] = useState([]);

  const [printerIdDict, setPrinterIdDict] = useState({});

  const [table, setTable] = useState([]);
  const [queue, setQueue] = useState([]);
  const [newReservationStatus, setNewReservationStatus] = useState(false);

  const [filterType, setFilterType] = useState(0);

  const [isScanning, setIsScanning] = useState(false);
  const [scanningPercentage, setScanningPercentage] = useState(0);

  //////////////////////////////////////////////////////////////////

  const [outletCategoryDropdownList, setOutletCategoryDropdownList] = useState([]);

  //////////////////////////////////////////////////////////////////

  const [printerIdCategoryDropdownListDict, setPrinterIdCategoryDropdownListDict] = useState({});

  //////////////////////////////////////////////////////////////////

  // const [userQueues, setUserQueues] = useState([]);

  // const userQueuesRaw = OutletStore.useState(s => s.userQueues);

  const allOutletShifts = OutletStore.useState(s => s.allOutletShifts);

  const outletPrinters = OutletStore.useState((s) => s.outletPrinters);

  const outletCategories = OutletStore.useState(s => s.outletCategories);

  const allOutlets = MerchantStore.useState((s) => s.allOutlets);
  const currOutletId = MerchantStore.useState((s) => s.currOutletId);

  const currOutlet = MerchantStore.useState((s) => s.currOutlet);

  const userName = UserStore.useState((s) => s.name);
  const userId = UserStore.useState((s) => s.firebaseUid);
  const userEmail = UserStore.useState((s) => s.email);
  const merchantName = MerchantStore.useState((s) => s.name);
  const merchantLogo = MerchantStore.useState((s) => s.logo);

  const isLoading = CommonStore.useState((s) => s.isLoading);

  const [temp, setTemp] = useState('');

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );

  // Track Expanded dropdown
  const [activeDropdown, setActiveDropdown] = useState(null);
  //New UX
  const [controller1, setController1] = useState({});

  useEffect(() => {
    var printerIdDictTemp = {};

    // var printerIdCategoryDropdownListDictTemp = {};

    const outletCategoriesIdList = outletCategories.map(category => category.uniqueId);

    for (var i = 0; i < outletPrinters.length; i++) {
      var blockKDCategoryIdListTemp = outletPrinters[i].blockKDCategoryIdList !== undefined ? outletPrinters[i].blockKDCategoryIdList : [];
      var blockOSCategoryIdListTemp = outletPrinters[i].blockOSCategoryIdList !== undefined ? outletPrinters[i].blockOSCategoryIdList : [];
      var blockReceiptCategoryIdListTemp = outletPrinters[i].blockReceiptCategoryIdList !== undefined ? outletPrinters[i].blockReceiptCategoryIdList : [];
      var blockBDCategoryIdListTemp = outletPrinters[i].blockBDCategoryIdList !== undefined ? outletPrinters[i].blockBDCategoryIdList : [];

      if (blockKDCategoryIdListTemp.length > 0) {
        var blockKDCategoryIdListTempNew = [];

        for (var j = 0; j < blockKDCategoryIdListTemp.length; j++) {
          if (outletCategoriesIdList.includes(blockKDCategoryIdListTemp[j])) {
            blockKDCategoryIdListTempNew.push(blockKDCategoryIdListTemp[j]);
          }
        }

        blockKDCategoryIdListTemp = blockKDCategoryIdListTempNew;
      }

      if (blockOSCategoryIdListTemp.length > 0) {
        var blockOSCategoryIdListTempNew = [];

        for (var j = 0; j < blockOSCategoryIdListTemp.length; j++) {
          if (outletCategoriesIdList.includes(blockOSCategoryIdListTemp[j])) {
            blockOSCategoryIdListTempNew.push(blockOSCategoryIdListTemp[j]);
          }
        }

        blockOSCategoryIdListTemp = blockOSCategoryIdListTempNew;
      }

      if (blockReceiptCategoryIdListTemp.length > 0) {
        var blockReceiptCategoryIdListTempNew = [];

        for (var j = 0; j < blockReceiptCategoryIdListTemp.length; j++) {
          if (outletCategoriesIdList.includes(blockReceiptCategoryIdListTemp[j])) {
            blockReceiptCategoryIdListTempNew.push(blockReceiptCategoryIdListTemp[j]);
          }
        }

        blockReceiptCategoryIdListTemp = blockReceiptCategoryIdListTempNew;
      }

      if (blockBDCategoryIdListTemp.length > 0) {
        var blockBDCategoryIdListTempNew = [];

        for (var j = 0; j < blockBDCategoryIdListTemp.length; j++) {
          if (outletCategoriesIdList.includes(blockBDCategoryIdListTemp[j])) {
            blockBDCategoryIdListTempNew.push(blockBDCategoryIdListTemp[j]);
          }
        }

        blockBDCategoryIdListTemp = blockBDCategoryIdListTempNew;
      }

      /////////////////////////////////////////////////////////////////////

      var groupListTemp = outletPrinters[i].groupList !== undefined ? outletPrinters[i].groupList.map(group => group.values) : [];

      console.log('groupListTemp');
      console.log(groupListTemp);

      var groupListNew = [];

      if (groupListTemp.length > 0) {
        for (var j = 0; j < groupListTemp.length; j++) {
          var groupTemp = groupListTemp[j];
          var groupNew = [];

          for (var k = 0; k < groupTemp.length; k++) {
            if (outletCategoriesIdList.includes(groupTemp[k])) {
              groupNew.push(groupTemp[k]);
            }
          }

          groupListNew.push(groupNew);
        }
      }

      /////////////////////////////////////////////////////////////////////

      // determine the category dropdown list options for each printer

      // var outletCategoriesNew = outletCategories;

      // for (var j = 0; j < groupListNew.length; j++) {
      //   var groupNew = groupListNew[j];

      //   for (var k = 0; k < groupNew.length; k++) {
      //     if (outletCategoriesNew.find(category => category.uniqueId === groupNew[k])) {
      //       // found, remove it from option
      //       outletCategoriesNew = outletCategoriesNew.filter(category => category.uniqueId !== groupNew[k]);
      //     }
      //   }
      // }

      // var printerIdCategoryDropdownList = outletCategoriesNew.map(category => ({ label: category.name, value: category.uniqueId }));

      // printerIdCategoryDropdownListDictTemp[outletPrinters[i].uniqueId] = printerIdCategoryDropdownList;

      /////////////////////////////////////////////////////////////////////

      printerIdDictTemp[outletPrinters[i].uniqueId] = {
        name: outletPrinters[i].name,
        ip: outletPrinters[i].ip,
        area: outletPrinters[i].area || '',
        types: outletPrinters[i].types || [],
        uniqueId: outletPrinters[i].uniqueId || '',

        deviceType: outletPrinters[i].deviceType ? outletPrinters[i].deviceType : PRINTER_DEVICE_TYPE.LAN,

        commandType: outletPrinters[i].commandType !== undefined ? outletPrinters[i].commandType : PRINTER_COMMAND_TYPE.ESCPOS,
        labelWidth: outletPrinters[i].labelWidth !== undefined ? outletPrinters[i].labelWidth : 70,
        labelHeight: outletPrinters[i].labelHeight !== undefined ? outletPrinters[i].labelHeight : 40,
        labelGap: outletPrinters[i].labelGap !== undefined ? outletPrinters[i].labelGap : 2,

        blockKDCategoryIdList: blockKDCategoryIdListTemp,
        blockOSCategoryIdList: blockOSCategoryIdListTemp,
        blockReceiptCategoryIdList: blockReceiptCategoryIdListTemp,
        blockBDCategoryIdList: blockBDCategoryIdListTemp,

        paperWidth: outletPrinters[i].paperWidth ? outletPrinters[i].paperWidth : PRINTER_PAPER_WIDTH._80MM,

        kdVariation: outletPrinters[i].kdVariation ? outletPrinters[i].kdVariation : KD_PRINT_VARIATION.SUMMARY,

        userPriority: outletPrinters[i].userPriority ? outletPrinters[i].userPriority : PRINTER_USER_PRIORITY.NORMAL,

        kdOptionsDeliverReject: outletPrinters[i].kdOptionsDeliverReject ? outletPrinters[i].kdOptionsDeliverReject : KD_OPTIONS_DELIVER_REJECT.DELIVERED_REJECTED_ITEMS,

        groupList: groupListNew,
        groupName: outletPrinters[i].groupName ? outletPrinters[i].groupName : '',

        osPrintTimes: outletPrinters[i].osPrintTimes ? outletPrinters[i].osPrintTimes : '1',

        usbPort: outletPrinters[i].usbPort ? outletPrinters[i].usbPort : -1,
      };
    }

    setPrinterIdDict(printerIdDictTemp);

    // setPrinterIdCategoryDropdownListDict(printerIdCategoryDropdownListDictTemp);

    ////////////////////////////////////////////////////////////////
  }, [outletPrinters, outletCategories]);

  useEffect(() => {
    var outletCategoryDropdownListTemp = outletCategories.map(category => ({ label: category.name, value: category.uniqueId }));

    setOutletCategoryDropdownList(outletCategoryDropdownListTemp);
  }, [outletCategories]);

  ///////////////////////////////////////////////////////////

  // 2022-11-09 - Printer group list option

  useEffect(() => {
    // var printerIdDictTemp = {};

    var printerIdCategoryDropdownListDictTemp = {};

    // const outletCategoriesIdList = outletCategories.map(category => category.uniqueId);

    for (var i = 0; i < outletPrinters.length; i++) {
      var groupListNew = [];

      if (printerIdDict[outletPrinters[i].uniqueId] && printerIdDict[outletPrinters[i].uniqueId].groupList) {
        groupListNew = printerIdDict[outletPrinters[i].uniqueId].groupList;
      }

      // console.log('================================');
      // console.log('groupListNew');
      // console.log(groupListNew);

      //////////////////////////////////////////////////////////////////////      

      var assignedCategoryIdList = [];

      for (var j = 0; j < groupListNew.length; j++) {
        var groupNew = groupListNew[j];

        assignedCategoryIdList = assignedCategoryIdList.concat(groupNew);
      }

      var assignedCategoryIdListUnique = [...new Set(assignedCategoryIdList)];

      //////////////////////////////////////////////////////////////////////

      // determine the category dropdown list options for each printer     

      for (var j = 0; j < groupListNew.length; j++) {
        var groupNew = groupListNew[j];

        // console.log('groupNew');
        // console.log(groupNew);

        var outletCategoriesNew = [];

        for (var k = 0; k < outletCategories.length; k++) {
          if (assignedCategoryIdListUnique.includes(outletCategories[k].uniqueId)
            // ||
            // !groupNew.includes(outletCategories[k].uniqueId)
          ) {
            if (groupNew.includes(outletCategories[k].uniqueId)) {
              // this group itself included, can add

              outletCategoriesNew.push(outletCategories[k]);
            }
          }
          else {
            // not included, can add

            outletCategoriesNew.push(outletCategories[k]);
          }
        }

        //////////////////////////////////

        var printerIdCategoryDropdownList = outletCategoriesNew.map(category => ({ label: category.name, value: category.uniqueId }));

        // console.log('printerIdCategoryDropdownList');
        // console.log(printerIdCategoryDropdownList);

        printerIdCategoryDropdownListDictTemp[outletPrinters[i].uniqueId + j] = printerIdCategoryDropdownList;
      }

      /////////////////////////////////////////////////////////////////////
    }

    // console.log('printerIdCategoryDropdownListDictTemp');
    // console.log(printerIdCategoryDropdownListDictTemp);

    setPrinterIdCategoryDropdownListDict(printerIdCategoryDropdownListDictTemp);

    ////////////////////////////////////////////////////////////////
  }, [printerIdDict, outletCategories]);

  ///////////////////////////////////////////////////////////

  // useEffect(() => {
  //   var userQueuesTemp = [];

  //   if (filterType == 0) { //Prioritize
  //     userQueuesTemp = userQueuesRaw;
  //   }
  //   if (filterType == 1) { //Prioritize
  //     userQueuesTemp = userQueuesRaw.filter(order => order.status === USER_QUEUE_STATUS.PENDING);
  //   }
  //   if (filterType == 2) { //orderid
  //     userQueuesTemp = userQueuesRaw.filter(order => order.status === USER_QUEUE_STATUS.ACCEPTED);
  //   }
  //   if (filterType == 3) { //date time
  //     userQueuesTemp = userQueuesRaw.filter(order => order.status === USER_QUEUE_STATUS.SEATED);
  //   }
  //   if (filterType == 4) { //Name
  //     userQueuesTemp = userQueuesRaw.filter(order => order.status === USER_QUEUE_STATUS.SERVED);
  //   }
  //   if (filterType == 5) { //Waiting Time
  //     userQueuesTemp = userQueuesRaw.filter(order => order.status === USER_QUEUE_STATUS.CANCELED);
  //   }
  //   if (filterType == 6) { //Waiting Time
  //     userQueuesTemp = userQueuesRaw.filter(order => order.status === USER_QUEUE_STATUS.NO_SHOW);
  //   }

  //   setUserQueues(userQueuesTemp);
  // }, [filterType, userQueuesRaw]);

  const getOutlet = () => {
    // ApiClient.GET(API.outlet + User.getOutletId())
    //   .then((result) => {
    //     setState({ switchState: result[0].queueStatus });
    //   })
    //   .catch((err) => {
    //     // console.log(err);
    //   });
  };

  const switchQueueStatus = (value) => {
    // self.onButtonClickHandler();

    var body = {
      // outletId: User.getOutletId()
      outletId: currOutlet.uniqueId,
      queueStatus: value,
    };

    ApiClient.POST(API.switchQueueStatus, body)
      .then((result) => {
        // if (result.queueStatus === true) {
        //     // Alert.alert("Queue is closed now")
        //     return self.setState({ switchState: false })
        // } else if (result.queueStatus === false) {
        //     //   Alert.alert("Queue is open now")
        //     return self.setState({ switchState: true })
        // }
      })
      .catch((err) => {
        // console.log(err);
      });
  };

  /*const getOutletQueue = () => {
    ApiClient.GET(API.getAllOutletQueue + User.getOutletId())
      .then((result) => {
        setState({ outletQueue: result });
      })
      .catch((err) => {
        // console.log(err);
      });
  };*/

  const closeAllSwipeable = () => { };

  const servedForQueue = (param) => {
    var body = {
      queueId: param,
      // seated: 1,
    };

    ApiClient.POST(API.servedQueue, body, false)
      .then((result) => {
        if (result.status) {
          // getOutletQueue()
          alert('Success: Queue has been served');
          closeAllSwipeable();
        }
      })
      .catch((err) => {
        // console.log(err);
      });
  };

  const seatedForQueue = (param) => {
    var body = {
      queueId: param,
      seated: 1,
    };

    ApiClient.POST(API.seatedQueue, body, false)
      .then((result) => {
        if (result.status) {
          // getOutletQueue()
          alert('Success: Queue has been seated');
          closeAllSwipeable();
        }
      })
      .catch((err) => {
        // console.log(err);
      });
  };

  const noShowQueue = (param) => {
    var body = {
      queueId: param,
    };

    ApiClient.POST(API.noShowQueue, body, false)
      .then((result) => {
        if (result.status) {
          // getOutletQueue()
          alert('Success: Queue has been updated');
          closeAllSwipeable();
        }
      })
      .catch((err) => {
        // console.log(err);
      });
  };

  const cancelQueue = (param) => {
    var body = {
      queueId: param,
    };

    ApiClient.POST(API.cancelQueue, body, false)
      .then((result) => {
        if (result.status) {
          // getOutletQueue()
          alert('Success: Queue has been cancelled');
          closeAllSwipeable();
        }
      })
      .catch((err) => {
        // console.log(err);
      });
  };

  const notifyForQueue = (param, userId) => {
    var body = {
      queueId: param,
      // seated: 1,
      userId,
    };

    // ApiClient.POST(API.notifyQueueMember, body, false).then(result => {
    ApiClient.POST(API.notifyQueue, body, false)
      .then((result) => {
        if (result && result.status === 'success') {
          // getOutletQueue()
          alert('Success: Queue has been notified');
        }
      })
      .catch((err) => {
        // console.log(err);
      });
  };

  const createUserQueueByMerchant = () => {
    if (
      queueCustomerName.length <= 0 ||
      queuePhone.length <= 0 ||
      queuePax <= 0
    ) {
      alert('Info: Please fill in the following fields:\n\nCustomer Name\nPhone No\nCapacity');

    } else {
      CommonStore.update((s) => {
        s.isLoading = true;
      });

      var body = {
        outletId: currOutlet.uniqueId,
        pax: queuePax,
        userId,

        merchantId: currOutlet.merchantId,
        outletCover: currOutlet.cover,
        merchantLogo,
        outletName: currOutlet.name,
        merchantName,

        userName: queueCustomerName,
        userPhone: queuePhone,
        userEmail,

        isMerchant: true,
      };

      ApiClient.POST(API.createUserQueueByMerchant, body, false)
        .then((result) => {
          if (result.status) {
            // getOutletQueue()
            // Alert.alert('Success', "Queue created successfully.");

            setSelectedQueue(result.userQueue);
            setConfirmQueueModal(true);
            setAddQueueModal(false);

            closeAllSwipeable();

            CommonStore.update((s) => {
              s.isLoading = false;
            });
          }
        })
        .catch((err) => {
          // console.log(err);

          CommonStore.update((s) => {
            s.isLoading = false;
          });
        });
    }
  };

  const updateUserQueueByMerchant = () => {
    if (
      queueCustomerName.length <= 0 ||
      queuePhone.length <= 0 ||
      queuePax <= 0
    ) {
      alert('Info: Please fill in the following fields:\n\nCustomer Name\nPhone No\nCapacity');

    } else {
      CommonStore.update((s) => {
        s.isLoading = true;
      });

      var body = {
        // outletId: currOutlet.uniqueId,
        pax: queuePax,
        // userId: userId,

        // merchantId: currOutlet.merchantId,
        // outletCover: currOutlet.cover,
        // merchantLogo: merchantLogo,
        // outletName: currOutlet.name,
        // merchantName: merchantName,

        userName: queueCustomerName,
        userPhone: queuePhone,
        // userEmail: userEmail,

        isMerchant: true,
        userQueueId: selectedQueue.uniqueId,
      };

      ApiClient.POST(API.updateUserQueueByMerchant, body, false)
        .then((result) => {
          if (result.status) {
            // getOutletQueue()
            // Alert.alert('Success', "Queue created successfully.");

            // setSelectedQueue(result.userQueue);
            // setConfirmQueueModal(true);

            alert('Succes: Queue has been updated');

            setAddQueueModal(false);

            closeAllSwipeable();

            CommonStore.update((s) => {
              s.isLoading = false;
            });
          }
        })
        .catch((err) => {
          // console.log(err);

          CommonStore.update((s) => {
            s.isLoading = false;
          });
        });
    }
  };

  const [queueOrders, setQueueOrders] = useState([]);

  // useEffect(() => {

  //   // console.log('USER_QUEUE_STATUS_PARSED.SEATED')
  //   // console.log(userQueues)

  //   userQueues.filter(order => order.status === USER_QUEUE_STATUS_PARSED.SEATED);
  // }, [userQueues]);

  // const filterOrders = (param) => {
  //   if (param.value == 0) { // Pending
  //     userQueues.filter(order => order.status === USER_QUEUE_STATUS.PENDING);
  //   }
  //   // if (param.value == 1) { //Accepted
  //   //   setQueueOrders(userQueues.filter(order => order.status === STATUS.ACCEPTED));
  //   // }

  //   // if (param.value == 2) { //Seated
  //   //   setDineInOrders(userOrders.filter(order => order.orderType === STATUS.SEATED));
  //   // }

  //   // if (param.value == 3) { //Served
  //   //   setDineInOrders(userOrders.filter(order => order.orderType === STATUS.SERVED));
  //   // }

  //   // if (param.value == 4) { //Rejected
  //   //   setDineInOrders(userOrders.filter(order => order.orderType === STATUS.REJECTED));
  //   // }
  //   // {USER_QUEUE_STATUS_PARSED[item.status]}
  //   // if (param.value == 5) { //No Show
  //   //   setSelectedQueue(selectedQueue.filter(order => order.orderType === STATUS.REJECTED));
  //   // }
  // }

  const [sort, setSort] = useState('');
  const [search, setSearch] = useState('');

  //Start Here Sorting

  const sortOperationQueue = (dataList, queueSortFieldType) => {
    var dataListTemp = [...dataList];
    // console.log('dataList');
    // console.log(dataList);

    // console.log('queueSortFieldType');
    // console.log(queueSortFieldType);

    const queueSortFieldTypeValue =
      QUEUE_SORT_FIELD_TYPE_VALUE[queueSortFieldType];

    const queueSortFieldTypeCompare =
      REPORT_SORT_FIELD_TYPE_COMPARE[queueSortFieldType];

    //QUEUE_ID
    if (queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.QUEUE_ID_ASC) {
        dataListTemp.sort(
          (a, b) =>
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : '') -
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : ''),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : '') -
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : ''),
        );
      }
    } else if (
      queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC
    ) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.QUEUE_ID_DESC) {
        dataListTemp.sort(
          (a, b) =>
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : '') -
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : ''),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : '') -
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : ''),
        );
      }
    }

    //NAME
    if (queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.NAME_ASC) {
        dataListTemp.sort((a, b) =>
          (a[queueSortFieldTypeValue]
            ? a[queueSortFieldTypeValue]
            : ''
          ).localeCompare(
            b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : '') -
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : ''),
        );
      }
    } else if (
      queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC
    ) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.NAME_DESC) {
        dataListTemp.sort((a, b) =>
          (b[queueSortFieldTypeValue]
            ? b[queueSortFieldTypeValue]
            : ''
          ).localeCompare(
            a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : '') -
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : ''),
        );
      }
    }

    //DATE_TIME
    if (queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.DATE_TIME_ASC) {
        dataListTemp.sort(
          (a, b) =>
            (moment(a[queueSortFieldTypeValue]).valueOf()
              ? moment(a[queueSortFieldTypeValue]).valueOf()
              : '') -
            (moment(b[queueSortFieldTypeValue]).valueOf()
              ? moment(b[queueSortFieldTypeValue]).valueOf()
              : ''),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : '') -
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : ''),
        );
      }
    } else if (
      queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC
    ) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.DATE_TIME_DESC) {
        dataListTemp.sort(
          (a, b) =>
            (moment(b[queueSortFieldTypeValue]).valueOf()
              ? moment(b[queueSortFieldTypeValue]).valueOf()
              : '') -
            (moment(a[queueSortFieldTypeValue]).valueOf()
              ? moment(a[queueSortFieldTypeValue]).valueOf()
              : ''),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : '') -
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : ''),
        );
      }
    }

    //CAPACITY
    if (queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.CAPACITY_ASC) {
        dataListTemp.sort(
          (a, b) =>
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : '') -
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : ''),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : '') -
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : ''),
        );
      }
    } else if (
      queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC
    ) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.CAPACITY_DESC) {
        dataListTemp.sort(
          (a, b) =>
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : '') -
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : ''),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : '') -
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : ''),
        );
      }
    }

    //WAITING_TIME
    if (queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.WAITING_TIME_ASC) {
        dataListTemp.sort(
          (a, b) =>
            (moment(a[queueSortFieldTypeValue]).valueOf()
              ? moment(a[queueSortFieldTypeValue]).valueOf()
              : '') -
            (moment(b[queueSortFieldTypeValue]).valueOf()
              ? moment(b[queueSortFieldTypeValue]).valueOf()
              : ''),
        );
      }
    } else if (
      queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC
    ) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.WAITING_TIME_DESC) {
        dataListTemp.sort(
          (a, b) =>
            (moment(b[queueSortFieldTypeValue]).valueOf()
              ? moment(b[queueSortFieldTypeValue]).valueOf()
              : '') -
            (moment(a[queueSortFieldTypeValue]).valueOf()
              ? moment(a[queueSortFieldTypeValue]).valueOf()
              : ''),
        );
      }
    }

    //STATUS
    if (queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.STATUS_ASC) {
        dataListTemp.sort((a, b) =>
          (a[queueSortFieldTypeValue]
            ? a[queueSortFieldTypeValue]
            : ''
          ).localeCompare(
            b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : '') -
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : ''),
        );
      }
    } else if (
      queueSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC
    ) {
      if (queueSortFieldType === QUEUE_SORT_FIELD_TYPE.STATUS_DESC) {
        dataListTemp.sort((a, b) =>
          (b[queueSortFieldTypeValue]
            ? b[queueSortFieldTypeValue]
            : ''
          ).localeCompare(
            a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (b[queueSortFieldTypeValue] ? b[queueSortFieldTypeValue] : '') -
            (a[queueSortFieldTypeValue] ? a[queueSortFieldTypeValue] : ''),
        );
      }
    }

    return dataListTemp;
  };

  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });

  const currOutletShiftStatus = OutletStore.useState(
    (s) => s.currOutletShiftStatus
  );
  // const [outletDropdownList, setOutletDropdownList] = useState([]);
  // const [selectedOutletList, setSelectedOutletList] = useState([]); // multi-outlets

  const [openPT, setOpenPT] = useState([]);
  const [openBKD, setOpenBKD] = useState([]);
  const [openBOS, setOpenBOS] = useState([]);
  const [openBR, setOpenBR] = useState([]);
  const [openBBD, setOpenBBD] = useState([]);
  const [openPW, setOpenPW] = useState([]);
  const [openKDV, setOpenKDV] = useState([]);
  const [openGL, setOpenGL] = useState([]);
  const [openP, setOpenP] = useState([]);
  const [openOPT, setOpenOPT] = useState([]);
  const [openKDO, setOpenKDO] = useState([]);

  const [openPCT, setOpenPCT] = useState([]);

  // var outletNames = [];

  // for (var i = 0; i < allOutlets.length; i++) {
  //   for (var j = 0; j < selectedOutletList.length; j++) {
  //     if (selectedOutletList.includes(allOutlets[i].uniqueId)) {
  //       outletNames.push(allOutlets[i].name);
  //       break;
  //     }
  //   }
  // }

  // useEffect(() => {
  //   setOutletDropdownList(
  //     allOutlets.map((item) => {
  //       return { label: item.name, value: item.uniqueId };
  //     })
  //   );
  // }, [allOutlets]);

  var targetOutletDropdownListTemp = allOutlets.map((outlet) => ({
    label: sliceUnicodeStringV2WithDots(outlet.name, 20),
    value: outlet.uniqueId,
  }));

  // useEffect(() => {
  //   CommonStore.update((s) => {
  //     s.outletSelectDropdownView = () => {
  //       return (
  //         <View
  //           style={{
  //             flexDirection: "row",
  //             alignItems: "center",
  //             borderRadius: 8,
  //             width: 200,
  //             backgroundColor: "white",
  //           }}
  //         >
  //           {currOutletId.length > 0 &&
  //             allOutlets.find((item) => item.uniqueId === currOutletId) ? (
  //             <MultiSelect
  //               clearable={false}
  //               singleSelect={true}
  //               defaultValue={currOutletId}
  //               placeholder={"Choose Outlet"}
  //               onChange={(value) => {
  //                 if (value) {
  //                   // if choose the same option again, value = ''
  //                   MerchantStore.update((s) => {
  //                     s.currOutletId = value;
  //                     s.currOutlet =
  //                       allOutlets.find(
  //                         (outlet) => outlet.uniqueId === value
  //                       ) || {};
  //                   });
  //                 }

  //                 CommonStore.update((s) => {
  //                   s.shiftClosedModal = false;
  //                 });
  //               }}
  //               options={targetOutletDropdownListTemp}
  //               className="msl-varsHEADER"
  //             />
  //           ) : (
  //             <ActivityIndicator size={"small"} color={Colors.whiteColor} />
  //           )}
  //           {/* <Select

  //             placeholder={"Choose Outlet"}
  //             onChange={(items) => {
  //               setSelectedOutletList(items);
  //             }}
  //             options={outletDropdownList}
  //             isMulti
  //           /> */}
  //         </View>
  //       );
  //     };
  //   });
  // }, [allOutlets, currOutletId, isLoading, currOutletShiftStatus]);

  navigation.setOptions({
    headerLeft: () => (
      <View
        style={[
          styles.headerLeftStyle,
          {
            width: windowWidth * 0.17,
          },
        ]}
      >
        <img src={headerLogo} width={124} height={26} />
        {/* <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        /> */}
      </View>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            justifyContent: "center",
            alignItems: "center",
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            //width:  "55%",
          },
          Dimensions.get("screen").width <= 768
            ? { right: Dimensions.get("screen").width * 0.12 }
            : {},
        ]}
      >
        <Text
          style={{
            fontSize: 24,
            // lineHeight: 25,
            textAlign: "center",
            fontFamily: "NunitoSans-Bold",
            color: Colors.whiteColor,
            opacity: 1,
          }}
        >
          Printer Settings
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        {/* {console.log('edward test')} */}
        {/* {console.log(outletSelectDropdownView)} */}
        {outletSelectDropdownView && outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: "white",
            width: 0.5,
            height: Dimensions.get("screen").height * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
            // borderWidth: 1
          }}
        ></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate("General Settings - KooDoo BackOffice")
            }
          }}
          style={{ flexDirection: "row", alignItems: "center" }}
        >
          <Text
            style={{
              fontFamily: "NunitoSans-SemiBold",
              fontSize: 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }}
          >
            {userName}
          </Text>
          <View
            style={{
              //backgroundColor: 'red',
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "white",
            }}
          >
            <img
              src={personicon}
              width={windowHeight * 0.035}
              height={windowHeight * 0.035}
            />
            {/* <Image
              style={{
                width: windowHeight * 0.05,
              height: windowHeight * 0.05,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            /> */}
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  const rightAction = (item, index) => {
    return (
      <View
        style={{
          justifyContent: 'center',
          alignItems: 'center',
          flexDirection: 'row',
          //width: '32%',
        }}>
        <TouchableOpacity
          onPress={() => {
            // removeFromCartItems(item);

            // noShowQueue(item.uniqueId);

            notifyForQueue(item.uniqueId, item.userId);
          }}
          style={{
            height: '100%',
            justifyContent: 'center',
            alignItems: 'center',
            // alignContent: 'center',
            // alignSelf: "center",
            backgroundColor: Colors.primaryColor,
            underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
            // paddingBottom: 6,
            width: 75,
          }}>
          <MIcon
            name={'bell-ring-outline'}
            size={40}
            color={Colors.whiteColor}
          />

          <Text
            style={{
              color: Colors.whiteColor,
              fontSize: 12,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
              width: '80%',
            }}>
            Notify
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            // removeFromCartItems(item);

            // noShowQueue(item.uniqueId);

            seatedForQueue(item.uniqueId);
          }}
          style={{
            height: '100%',
            justifyContent: 'center',
            alignItems: 'center',
            // alignContent: 'center',
            // alignSelf: "center",
            backgroundColor: Colors.secondaryColor,
            underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
            // paddingBottom: 6,
            width: 75,
          }}>
          <MIcon name="seat-outline" size={40} color={Colors.whiteColor} />

          <Text
            style={{
              color: Colors.whiteColor,
              fontSize: 12,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
              width: '80%',
            }}>
            Seated
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            // removeFromCartItems(item);

            // noShowQueue(item.uniqueId);

            servedForQueue(item.uniqueId);
          }}
          style={{
            height: '100%',
            justifyContent: 'center',
            alignItems: 'center',
            // alignContent: 'center',
            // alignSelf: "center",
            backgroundColor: '#00B1E1',
            underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
            // paddingBottom: 6,
            width: 75,
          }}>
          <MIcon
            name="room-service-outline"
            size={40}
            color={Colors.whiteColor}
          />

          <Text
            style={{
              color: Colors.whiteColor,
              fontSize: 12,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
              width: '80%',
            }}>
            Served
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            // removeFromCartItems(item);

            noShowQueue(item.uniqueId);
          }}
          style={{
            height: '100%',
            justifyContent: 'center',
            alignItems: 'center',
            // alignContent: 'center',
            // alignSelf: "center",
            backgroundColor: Colors.tabGrey,
            underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
            // paddingBottom: 6,
            width: 75,
          }}>
          <Feather name="alert-triangle" size={40} color={Colors.whiteColor} />

          <Text
            style={{
              color: Colors.whiteColor,
              fontSize: 12,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
              width: '80%',
            }}>
            No-Show
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            // removeFromCartItems(item);

            cancelQueue(item.uniqueId);
          }}
          style={{
            height: '100%',
            justifyContent: 'center',
            alignItems: 'center',
            // alignContent: 'center',
            // alignSelf: "center",
            backgroundColor: Colors.tabRed,
            underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
            // paddingBottom: 6,
            width: 75,
          }}>
          {/* <MaterialCommunityIcons
            name="message-alert-outline"
            size={40}
            color={Colors.whiteColor}
            style={{ marginTop: 10 }}
          /> */}

          <FontAwesome name="trash-o" size={40} color={Colors.whiteColor} />

          <Text
            style={{
              color: Colors.whiteColor,
              fontSize: 12,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'center',
              width: '80%',
            }}>
            Remove
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  // const renderPrinter = ({ item, index }) => {
  //   // var dys = Math.floor(moment().diff(item.createdAt, 'hours') / 24);
  //   // var hrs = Math.floor((moment().diff(item.createdAt, 'minutes') / 60) % 24);
  //   // var mins = Math.floor(moment().diff(item.createdAt, 'minutes') % 60);

  //   return (
  //     <View
  //       style={{
  //         paddingVertical: 5,
  //         shadowOffset: {
  //           width: 0,
  //           height: 2,
  //         },
  //         shadowOpacity: 0.22,
  //         shadowRadius: 3.22,
  //         elevation: 1,
  //       }}>
  //       {/* <Swipeable
  //         renderRightActions={() => rightAction(item)}
  //         // ref={refArray[index]}
  //         onSwipeableWillOpen={() => {
  //         }}> */}
  //       <View
  //         style={{
  //           // elevation: 1,
  //           borderRadius: 5,
  //           // backgroundColor: 'white',
  //           // height: 300,
  //           paddingHorizontal: 5,
  //           paddingTop: switchMerchant ? 5 : 0,
  //         }}>
  //         <View
  //           style={{
  //             flexDirection: 'row',
  //             height: switchMerchant
  //               ? windowHeight * 0.1
  //               : windowHeight * 0.06,
  //             alignItems: 'center',
  //             borderBottomColor: Colors.fieldtBgColor,
  //             width: switchMerchant
  //               ? windowWidth * 0.8
  //               : '100%',
  //             alignSelf: 'center',
  //           }}>
  //           <View
  //             style={{
  //               width: switchMerchant ? '12%' : '14%',
  //               marginHorizontal: switchMerchant ? 0 : 4,
  //             }}>
  //             <TextInput
  //               style={{
  //                 color: Colors.fontDark,
  //                 fontSize: switchMerchant ? 10 : 16,
  //                 fontFamily: 'NunitoSans-Bold',
  //                 borderWidth: 1,
  //                 borderRadius: 5,
  //                 borderColor: '#E5E5E5',
  //                 paddingLeft: switchMerchant ? 5 : 5,
  //                 height: 40,
  //                 width: switchMerchant ? 98 : Platform.OS === 'ios' ? windowWidth * 0.1 : 150,
  //               }}
  //               placeholder={'Printer Name'}
  //               placeholderTextColor={Colors.descriptionColor}
  //               defaultValue={
  //                 printerIdDict[item.uniqueId]
  //                   ? printerIdDict[item.uniqueId].name
  //                   : ''
  //               }
  //               onChangeText={(text) => {
  //                 setPrinterIdDict({
  //                   ...printerIdDict,
  //                   [item.uniqueId]: {
  //                     ...printerIdDict[item.uniqueId],
  //                     name: text,
  //                   },
  //                 });
  //               }}></TextInput>
  //           </View>
  //           <View style={{ width: '18%', marginHorizontal: 4 }}>
  //             {/* <Text style={{ color: Colors.fontDark, fontSize: 16, fontFamily: 'NunitoSans-Bold' }}>
  //                 168.88.8888.88
  //               </Text> */}

  //             <TextInput
  //               style={{
  //                 color: Colors.fontDark,
  //                 fontSize: switchMerchant ? 10 : 16,
  //                 fontFamily: 'NunitoSans-Bold',
  //                 borderWidth: 1,
  //                 borderRadius: 5,
  //                 borderColor: '#E5E5E5',
  //                 paddingLeft: 5,
  //                 height: 40,
  //                 width: switchMerchant ? 127 : 140,
  //               }}
  //               placeholder={'Printer IP'}
  //               placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
  //               keyboardType={'decimal-pad'}
  //               defaultValue={
  //                 printerIdDict[item.uniqueId]
  //                   ? printerIdDict[item.uniqueId].ip
  //                   : ''
  //               }
  //               onChangeText={(text) => {
  //                 setPrinterIdDict({
  //                   ...printerIdDict,
  //                   [item.uniqueId]: {
  //                     ...printerIdDict[item.uniqueId],
  //                     ip: text,
  //                   },
  //                 });
  //               }}></TextInput>
  //           </View>
  //           <View
  //             style={{
  //               width: switchMerchant ? '8%' : '8%',
  //               marginHorizontal: switchMerchant ? 0 : 4,
  //             }}>
  //             <TextInput
  //               style={{
  //                 color: Colors.fontDark,
  //                 fontSize: switchMerchant ? 10 : 16,
  //                 fontFamily: 'NunitoSans-Bold',
  //                 borderWidth: 1,
  //                 borderRadius: 5,
  //                 borderColor: '#E5E5E5',
  //                 paddingLeft: switchMerchant ? 5 : 5,
  //                 height: 40,
  //                 width: switchMerchant ? 58 : Platform.OS === 'ios' ? windowWidth * 0.06 : 80,
  //               }}
  //               placeholder={'Kitchen'}
  //               placeholderTextColor={Colors.descriptionColor}
  //               defaultValue={
  //                 printerIdDict[item.uniqueId]
  //                   ? printerIdDict[item.uniqueId].area
  //                   : ''
  //               }
  //               onChangeText={(text) => {
  //                 setPrinterIdDict({
  //                   ...printerIdDict,
  //                   [item.uniqueId]: {
  //                     ...printerIdDict[item.uniqueId],
  //                     area: text,
  //                   },
  //                 });
  //               }}></TextInput>
  //           </View>

  //           <View style={{
  //             width: switchMerchant ? '14%' : '14%',
  //             marginHorizontal: switchMerchant ? 4 : 4,
  //             zIndex: 10001 - index,
  //           }}>
  //             <DropDownPicker
  //               containerStyle={[{ height: 40 }, switchMerchant ? { height: 35, } : {}]}
  //               arrowColor={'black'}
  //               arrowSize={20}
  //               arrowStyle={{ fontWeight: 'bold' }}
  //               labelStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: 14 }}
  //               style={[{ width: '90%', paddingVertical: 0, backgroundColor: Colors.fieldtBgColor, borderRadius: 10 }, switchMerchant ? {
  //                 width: '90%',
  //               } : {}]}
  //               placeholderStyle={{ color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Regular', fontSize: 14 }}
  //               //items={[{ label: 'Dine In', value: 1 }, { label: 'Takeaway', value: 2 }, { label: 'Delivery', value: 3 } ]}
  //               items={PRINTER_USAGE_TYPE_DROPDOWN_LIST}
  //               itemStyle={{ justifyContent: 'flex-start', marginLeft: 5, zIndex: 2, fontFamily: 'NunitoSans-Regular', fontSize: 14 }}
  //               defaultValue={printerIdDict[item.uniqueId]
  //                 ? printerIdDict[item.uniqueId].types
  //                 : []}
  //               placeholder={"Type"}
  //               multipleText={'%d type(s) selected'}
  //               multiple={true}
  //               customTickIcon={(press) => <Ionicon name={"md-checkbox"} color={press ? Colors.fieldtBgColor : Colors.primaryColor} size={25} />}
  //               onChangeItem={(items) => {
  //                 setPrinterIdDict({
  //                   ...printerIdDict,
  //                   [item.uniqueId]: {
  //                     ...printerIdDict[item.uniqueId],
  //                     types: items,
  //                   },
  //                 });
  //               }}
  //               dropDownMaxHeight={100}
  //               dropDownStyle={[{ width: '90%', height: 100, backgroundColor: Colors.fieldtBgColor, borderRadius: 10, borderWidth: 1, }, switchMerchant ? {
  //                 width: '90%',
  //               } : {}]}
  //               globalTextStyle={{
  //                 fontSize: switchMerchant ? 10 : 14,
  //               }}
  //             />
  //           </View>

  //           <View style={{ width: '14%', marginHorizontal: 4 }}>
  //             <TouchableOpacity
  //               style={{
  //                 height: 35,
  //                 width: 100,
  //                 borderColor: '#E5E5E5',
  //                 borderWidth: 1,
  //                 borderRadius: 5,
  //                 alignItems: 'center',
  //                 justifyContent: 'center',
  //                 borderColor: Colors.primaryColor,
  //                 backgroundColor: Colors.primaryColor,
  //               }}
  //               onPress={() => {
  //                 updateOutletPrinter(item.uniqueId);

  //                 // bindPrinter(
  //                 //   printerIdDict[item.uniqueId]
  //                 //     ? printerIdDict[item.uniqueId].ip
  //                 //     : '',

  //                 bindPrinter(
  //                   printerIdDict[item.uniqueId]
  //                 );
  //               }}>
  //               <Text
  //                 style={{
  //                   color: Colors.whiteColor,
  //                   fontSize: switchMerchant ? 10 : 16,
  //                   fontFamily: 'NunitoSans-Bold',
  //                 }}>
  //                 BIND
  //               </Text>
  //             </TouchableOpacity>
  //           </View>
  //           {/* <View style={{ width: '14%', marginHorizontal: 4 }}>
  //             <TouchableOpacity
  //               style={{
  //                 height: 35,
  //                 width: 100,
  //                 borderColor: '#E5E5E5',
  //                 borderWidth: 1,
  //                 borderRadius: 5,
  //                 alignItems: 'center',
  //                 justifyContent: 'center',
  //                 borderColor: Colors.tabCyan,
  //                 backgroundColor: Colors.tabCyan,
  //               }}
  //               onPress={() => {
  //                 unbindPrinter();
  //               }}>
  //               <Text
  //                 style={{
  //                   color: Colors.whiteColor,
  //                   fontSize: switchMerchant ? 10 : 16,
  //                   fontFamily: 'NunitoSans-Bold',
  //                 }}>
  //                 UNBIND
  //               </Text>
  //             </TouchableOpacity>
  //           </View> */}
  //           <View style={{ width: '14%', marginHorizontal: 4 }}>
  //             <TouchableOpacity
  //               style={{
  //                 height: 35,
  //                 width: 100,
  //                 borderColor: '#E5E5E5',
  //                 borderWidth: 1,
  //                 borderRadius: 5,
  //                 alignItems: 'center',
  //                 justifyContent: 'center',
  //                 borderColor: Colors.tabGold,
  //                 backgroundColor: Colors.tabGold,
  //               }}
  //               onPress={() => {
  //                 testPrinter();
  //               }}>
  //               <Text
  //                 style={{
  //                   color: Colors.whiteColor,
  //                   fontSize: switchMerchant ? 10 : 16,
  //                   fontFamily: 'NunitoSans-Bold',
  //                 }}>
  //                 TEST
  //               </Text>
  //             </TouchableOpacity>
  //           </View>
  //           <View style={{ width: '14%', marginHorizontal: 4 }}>
  //             <TouchableOpacity
  //               style={{
  //                 height: 35,
  //                 width: 100,
  //                 borderColor: '#E5E5E5',
  //                 borderWidth: 1,
  //                 borderRadius: 5,
  //                 alignItems: 'center',
  //                 justifyContent: 'center',
  //                 borderColor: Colors.tabRed,
  //                 backgroundColor: Colors.tabRed,
  //               }}
  //               onPress={() => {
  //                 Alert.alert(
  //                   'Info',
  //                   'Are you sure you want to remove this printer?',
  //                   [
  //                     {
  //                       text: 'Yes',
  //                       onPress: () => {
  //                         deleteOutletPrinter(item.uniqueId);
  //                       },
  //                     },
  //                   ],
  //                   { cancelable: false },
  //                 );
  //               }}>
  //               <Text
  //                 style={{
  //                   color: Colors.whiteColor,
  //                   fontSize: switchMerchant ? 10 : 16,
  //                   fontFamily: 'NunitoSans-Bold',
  //                 }}>
  //                 REMOVE
  //               </Text>
  //             </TouchableOpacity>
  //           </View>
  //         </View>
  //       </View>
  //       {/* </Swipeable> */}
  //     </View >
  //   );
  // };

  /*const viewNote = (rowData) => {
    props.navigator.push({
      title: 'The Note',
      component: ViewNote,
      passProps: {
        noteText: rowData,
        noteId: noteId(rowData),
      },
    });
  };*/

  const createOutletPrinter = () => {
    if (printerIP.length === 0 || printerName.length === 0) {
      alert('Info: Printer name and ip address cannot be empty');
      return;
    }

    if (printerArea.length === 0) {
      alert('Info: Printer area cannot be empty');
      return;
    }

    // if (printerTypes.length === 0) {
    //   Alert.alert('Info', 'Printer type(s) cannot be empty');
    //   return;
    // }

    var isExisted = false;

    for (var i = 0; i < outletPrinters.length; i++) {
      if (
        outletPrinters[i].ip === printerIP &&
        outletPrinters[i].name === printerName
        // &&
        // outletPrinters[i].uniqueId !== printerId
      ) {
        isExisted = true;
        break;
      }
    }

    if (isExisted) {
      alert('Info: Printer with the same name/ip exists');

    } else {
      CommonStore.update((s) => {
        s.isLoading = true;
      });

      var body = {
        outletId: currOutlet.uniqueId,
        merchantId: currOutlet.merchantId,
        printerName,
        printerIP,
        printerArea,

        printerTypes,

        blockKDCategoryIdList: [],
        blockOSCategoryIdList: [],
        blockReceiptCategoryIdList: [],
        blockBDCategoryIdList: [],

        blockKDOutletSectionIdList: [],
        blockOSOutletSectionIdList: [],
        blockReceiptOutletSectionIdList: [],
        blockBDOutletSectionIdList: [],

        paperWidth: PRINTER_PAPER_WIDTH._80MM,

        kdVariation: KD_PRINT_VARIATION.SUMMARY,

        userPriority: PRINTER_USER_PRIORITY.NORMAL,

        kdOptionsDeliverReject: KD_OPTIONS_DELIVER_REJECT.DELIVERED_REJECTED_ITEMS,

        groupList: [],
        groupName: '',

        osPrintTimes: '1',

        commandType: PRINTER_COMMAND_TYPE.ESCPOS,
        labelWidth: 70,
        labelHeight: 40,
        labelGap: 2,
      };

      // ApiClient.POST(API.createOutletPrinter, body, false)
      APILocal.createOutletPrinter({ body, uid: userId })
        .then((result) => {
          if (result.status) {
            // getOutletQueue()
            // Alert.alert('Success', "Queue created successfully.");

            // setSelectedQueue(result.userQueue);
            // setConfirmQueueModal(true);
            setAddQueueModal(false);

            closeAllSwipeable();

            alert('Success: Printer has been added');

            CommonStore.update((s) => {
              s.isLoading = false;
            });
          }
        })
        .catch((err) => {
          // console.log(err);

          CommonStore.update((s) => {
            s.isLoading = false;
          });
        });
    }
  };

  const updateOutletPrinter = (printerId) => {
    var printerIPTemp = printerIdDict[printerId]
      ? printerIdDict[printerId].ip
      : '';
    var printerNameTemp = printerIdDict[printerId]
      ? printerIdDict[printerId].name
      : '';
    var printerAreaTemp = printerIdDict[printerId]
      ? printerIdDict[printerId].area
      : '';
    var printerTypesTemp = printerIdDict[printerId]
      ? printerIdDict[printerId].types
      : [];

    var blockKDCategoryIdListTemp = printerIdDict[printerId]
      ? printerIdDict[printerId].blockKDCategoryIdList
      : [];
    var blockOSCategoryIdListTemp = printerIdDict[printerId]
      ? printerIdDict[printerId].blockOSCategoryIdList
      : [];
    var blockReceiptCategoryIdListTemp = printerIdDict[printerId]
      ? printerIdDict[printerId].blockReceiptCategoryIdList
      : [];
    var blockBDCategoryIdListTemp = printerIdDict[printerId]
      ? printerIdDict[printerId].blockBDCategoryIdList
      : [];

    var printerPaperWidthTemp = (printerIdDict[printerId] && printerIdDict[printerId].paperWidth)
      ? printerIdDict[printerId].paperWidth
      : PRINTER_PAPER_WIDTH._80MM;

    var printerKdVariationTemp = (printerIdDict[printerId] && printerIdDict[printerId].kdVariation)
      ? printerIdDict[printerId].kdVariation
      : KD_PRINT_VARIATION.SUMMARY;

    var printerUserPriorityTemp = (printerIdDict[printerId] && printerIdDict[printerId].userPriority)
      ? printerIdDict[printerId].userPriority
      : PRINTER_USER_PRIORITY.NORMAL;

    var groupListTemp = printerIdDict[printerId]
      ? printerIdDict[printerId].groupList
      : [];

    var groupNameTemp = printerIdDict[printerId]
      ? printerIdDict[printerId].groupName
      : '';

    var osPrintTimesTemp = printerIdDict[printerId]
      ? printerIdDict[printerId].osPrintTimes
      : '1';

    var kdOptionsDeliverRejectTemp = (printerIdDict[printerId] && printerIdDict[printerId].kdOptionsDeliverReject)
      ? printerIdDict[printerId].kdOptionsDeliverReject
      : KD_OPTIONS_DELIVER_REJECT.DELIVERED_REJECTED_ITEMS;

    var commandTypeTemp = printerIdDict[printerId]
      ? printerIdDict[printerId].commandType
      : PRINTER_COMMAND_TYPE.ESCPOS;
    var labelWidthTemp = printerIdDict[printerId]
      ? printerIdDict[printerId].labelWidth
      : 70;
    var labelHeightTemp = printerIdDict[printerId]
      ? printerIdDict[printerId].labelHeight
      : 40;
    var labelGapTemp = printerIdDict[printerId]
      ? printerIdDict[printerId].labelGap
      : 2;

    if (printerIPTemp.length === 0 || printerNameTemp.length === 0) {
      alert('Info: Printer name and ip address cannot be empty');
      return;
    }

    if (printerAreaTemp.length === 0) {
      alert('Info: Printer area cannot be empty');
      return;
    }

    if (printerTypesTemp.length === 0) {
      alert('Info: Printer types(s) cannot be empty');
      return;
    }

    var isExisted = false;

    // for (var i = 0; i < outletPrinters.length; i++) {
    //   if (
    //     outletPrinters[i].ip === printerIPTemp &&
    //     outletPrinters[i].name === printerNameTemp &&
    //     outletPrinters[i].uniqueId !== printerId
    //   ) {
    //     isExisted = true;
    //     break;
    //   }
    // }

    if (isExisted) {
      alert('Info: Printer with the same name and ip exists');

    } else {
      CommonStore.update((s) => {
        s.isLoading = true;
      });

      var body = {
        outletId: currOutlet.uniqueId,
        merchantId: currOutlet.merchantId,
        printerName: printerNameTemp,
        printerIP: printerIPTemp,
        printerId,
        printerArea: printerAreaTemp,
        printerTypes: printerTypesTemp,

        blockKDCategoryIdList: blockKDCategoryIdListTemp,
        blockOSCategoryIdList: blockOSCategoryIdListTemp,
        blockReceiptCategoryIdList: blockReceiptCategoryIdListTemp,
        blockBDCategoryIdList: blockBDCategoryIdListTemp,

        blockKDOutletSectionIdList: [],
        blockOSOutletSectionIdList: [],
        blockReceiptOutletSectionIdList: [],
        blockBDOutletSectionIdList: [],

        paperWidth: printerPaperWidthTemp,

        kdVariation: printerKdVariationTemp,

        userPriority: printerUserPriorityTemp,

        kdOptionsDeliverReject: kdOptionsDeliverRejectTemp,

        groupList: groupListTemp.map(group => {
          return {
            values: group,
          };
        }),
        groupName: groupNameTemp,

        osPrintTimes: osPrintTimesTemp,

        commandType: commandTypeTemp,
        labelWidth: labelWidthTemp,
        labelHeight: labelHeightTemp,
        labelGap: labelGapTemp,
      };

      console.log('body');
      console.log(body);

      // ApiClient.POST(API.updateOutletPrinter, body, false)
      APILocal.updateOutletPrinter({ body, uid: userId })
        .then((result) => {
          if (result.status) {
            // getOutletQueue()
            // Alert.alert('Success', "Queue created successfully.");

            // setSelectedQueue(result.userQueue);
            // setConfirmQueueModal(true);
            // setAddQueueModal(false);

            alert('Success: Printer has been updated');

            closeAllSwipeable();

            CommonStore.update((s) => {
              s.isLoading = false;
            });
          }
        })
        .catch((err) => {
          // console.log(err);

          CommonStore.update((s) => {
            s.isLoading = false;
          });
        });
    }
  };

  const deleteOutletPrinter = (printerId) => {
    CommonStore.update((s) => {
      s.isLoading = true;
    });

    var body = {
      printerId,
    };

    // ApiClient.POST(API.deleteOutletPrinter, body, false)
    APILocal.deleteOutletPrinter({ body, uid: userId })
      .then(async (result) => {
        if (result.status) {
          // getOutletQueue()
          // Alert.alert('Success', "Queue created successfully.");

          // setSelectedQueue(result.userQueue);
          // setConfirmQueueModal(true);
          // setAddQueueModal(false);

          //////////////////////////////////////////

          // remove from printer list, add/update obj as well

          // const printerListRaw = await AsyncStorage.getItem('printerList');

          // var printerList = [];
          // if (printerListRaw) {
          //   printerList = JSON.parse(printerListRaw);
          // }

          var printerList = global.printerList ? global.printerList : [];

          printerList = printerList.filter(
            (printerIdTemp) => printerIdTemp !== printerId,
          );

          // await AsyncStorage.removeItem(printerId);

          global.printerId = null;

          // await AsyncStorage.setItem(
          //   'printerList',
          //   JSON.stringify(printerList),
          // );

          global.printerList = printerList;

          alert('Success: Printer has been removed');

          closeAllSwipeable();

          CommonStore.update((s) => {
            s.isLoading = false;
          });
        }
      })
      .catch((err) => {
        // console.log(err);

        CommonStore.update((s) => {
          s.isLoading = false;
        });
      });
  };

  const bindPrinter = async (printerTemp) => {
    if (
      printerTemp &&
      printerTemp.ip &&
      printerTemp.ip.length > 0 &&
      printerTemp.area &&
      printerTemp.types.length > 0
    ) {
      try {
        // await AsyncStorage.setItem('printerIP', printerTemp.ip);

        // await AsyncStorage.setItem(printerTemp.area, printerTemp.ip);

        global.printerIP = printerTemp.ip;

        global[printerTemp.area] = printerTemp.ip;

        //////////////////////////////////////////

        // add to printer list, add/update obj as well

        // const printerListRaw = await AsyncStorage.getItem('printerList');

        // var printerList = [];
        // if (printerListRaw) {
        //   printerList = JSON.parse(printerListRaw);
        // }

        var printerList = global.printerList ? global.printerList : [];

        if (printerList.includes(printerTemp.uniqueId)) {
          // await AsyncStorage.setItem(
          //   printerTemp.uniqueId,
          //   JSON.stringify({
          //     ip: printerTemp.ip,
          //     area: printerTemp.area,
          //     types: printerTemp.types,
          //   }),
          // );

          global[printerTemp.uniqueId] = {
            ip: printerTemp.ip || '',
            area: printerTemp.area || '',
            types: printerTemp.types || [],

            blockKDCategoryIdList: printerTemp.blockKDCategoryIdList || [],
            blockOSCategoryIdList: printerTemp.blockOSCategoryIdList || [],
            blockReceiptCategoryIdList: printerTemp.blockReceiptCategoryIdList || [],
            blockBDCategoryIdList: printerTemp.blockBDCategoryIdList || [],

            paperWidth: printerTemp.paperWidth ? printerTemp.paperWidth : PRINTER_PAPER_WIDTH._80MM,

            kdVariation: printerTemp.kdVariation ? printerTemp.kdVariation : KD_PRINT_VARIATION.SUMMARY,

            userPriority: printerTemp.userPriority ? printerTemp.userPriority : PRINTER_USER_PRIORITY.NORMAL,

            kdOptionsDeliverReject: printerTemp.kdOptionsDeliverReject ? printerTemp.kdOptionsDeliverReject : KD_OPTIONS_DELIVER_REJECT.DELIVERED_REJECTED_ITEMS,

            groupList: printerTemp.groupList ? printerTemp.groupList.map(group => group.values) : [],
            groupName: printerTemp.groupName ? printerTemp.groupName : '',

            osPrintTimes: printerTemp.osPrintTimes ? printerTemp.osPrintTimes : '1',

            usbPort: printerTemp.usbPort ? printerTemp.usbPort : -1,

            commandType: printerTemp.commandType !== undefined ? printerTemp.commandType : PRINTER_COMMAND_TYPE.ESCPOS,
            labelWidth: printerTemp.labelWidth !== undefined ? printerTemp.labelWidth : 70,
            labelHeight: printerTemp.labelHeight !== undefined ? printerTemp.labelHeight : 40,
            labelGap: printerTemp.labelGap !== undefined ? printerTemp.labelGap : 2,
          };
        } else {
          // await AsyncStorage.setItem(
          //   printerTemp.uniqueId,
          //   JSON.stringify({
          //     ip: printerTemp.ip,
          //     area: printerTemp.area,
          //     types: printerTemp.types,
          //   }),
          // );

          global[printerTemp.uniqueId] = {
            ip: printerTemp.ip || '',
            area: printerTemp.area || '',
            types: printerTemp.types || [],

            blockKDCategoryIdList: printerTemp.blockKDCategoryIdList || [],
            blockOSCategoryIdList: printerTemp.blockOSCategoryIdList || [],
            blockReceiptCategoryIdList: printerTemp.blockReceiptCategoryIdList || [],
            blockBDCategoryIdList: printerTemp.blockBDCategoryIdList || [],

            paperWidth: printerTemp.paperWidth ? printerTemp.paperWidth : PRINTER_PAPER_WIDTH._80MM,

            kdVariation: printerTemp.kdVariation ? printerTemp.kdVariation : KD_PRINT_VARIATION.SUMMARY,

            userPriority: printerTemp.userPriority ? printerTemp.userPriority : PRINTER_USER_PRIORITY.NORMAL,

            kdOptionsDeliverReject: printerTemp.kdOptionsDeliverReject ? printerTemp.kdOptionsDeliverReject : KD_OPTIONS_DELIVER_REJECT.DELIVERED_REJECTED_ITEMS,

            groupList: printerTemp.groupList ? printerTemp.groupList.map(group => group.values) : [],
            groupName: printerTemp.groupName ? printerTemp.groupName : '',

            osPrintTimes: printerTemp.osPrintTimes ? printerTemp.osPrintTimes : '1',

            usbPort: printerTemp.usbPort ? printerTemp.usbPort : -1,

            commandType: printerTemp.commandType !== undefined ? printerTemp.commandType : PRINTER_COMMAND_TYPE.ESCPOS,
            labelWidth: printerTemp.labelWidth !== undefined ? printerTemp.labelWidth : 70,
            labelHeight: printerTemp.labelHeight !== undefined ? printerTemp.labelHeight : 40,
            labelGap: printerTemp.labelGap !== undefined ? printerTemp.labelGap : 2,
          };

          printerList.push(printerTemp.uniqueId);

          // await AsyncStorage.setItem(
          //   'printerList',
          //   JSON.stringify(printerList),
          // );

          global.printerList = printerList;
        }

        //////////////////////////////////////////

        const result = await connectToPrinter();

        if (result) {
          // Alert.alert('Success', 'IP has been binded');
        } else {
          // Alert.alert('Error', 'Unable to bind the IP');
        }
      } catch (ex) {
        // console.log(ex);
      }
    } else {
      alert('Error: Invalid IP address, area name and type(s)');
    }
  };

  const unbindPrinter = async () => {
    try {
      // await AsyncStorage.removeItem('printerIP');

      global[printerIP] = null;

      alert('Succes: IP has been unbinded');
    } catch (ex) {
      // console.log(ex);

      alert('Error: Failed to unbind the IP');
    }
  };

  const testPrinter = async (printerTemp) => {
    // console.log('test printer');
    CommonStore.update((s) => { s.isLoading = true });

    if (printerTemp.deviceType === PRINTER_DEVICE_TYPE.USB) {

    }
    else {
      await NetPrinter.closeConn();

      // await connectToPrinter();
      await connectToPrinter(printerTemp.ip || '');
    }

    var testItems = [
      {
        // name: 'Coconut Coffee',
        name: '椰子口味咖啡 Coconut Flavored Coffee',
        remarks: '少糖 Less Sugar',
        price: 8,
        discount: 0,
        quantity: 1,
        subtotal: 8,
        tax: 0,
        sc: 0,
      },
      {
        name: 'Yogurt Coffee',
        remarks: 'LESS ICE',
        price: 8,
        discount: 0,
        quantity: 1,
        subtotal: 8,
        tax: 0,
        sc: 0,
      },
    ];

    try {
      for (var index = 0; index < printerTemp.types.length; index++) {
        if (printerTemp.types[index] === PRINTER_USAGE_TYPE.RECEIPT) {
          let pwc = PW_CONFIG[printerTemp.paperWidth].PUO_RECEIPT;

          // const koodooLogoUrl = await AsyncStorage.getItem('koodooLogoUrl');

          const { koodooLogoUrl } = global;

          // const merchantLogoUrl = await AsyncStorage.getItem('merchantLogoUrl');

          const { merchantLogoUrl } = global;

          if (merchantLogoUrl && merchantLogoUrl.startsWith('http') && netInfo.isInternetReachable && netInfo.isConnected) {
            await new Promise(async (resolve, reject) => {
              if (isUsbPrinter(printerTemp)) {
                try {
                  const command = await preparePrintImageCommand(merchantLogoUrl);
                  await printToUsbDevice(printerTemp, command, false);
                  resolve();
                }
                catch (error) {
                  console.error(error);
                  reject(error);
                }
              }
              else {
                await NetPrinter.printImage(merchantLogoUrl);
              }
            });
          }

          // const currOutletLocalRaw = await AsyncStorage.getItem('currOutlet');
          // var currOutletLocal = null;
          // if (currOutletLocalRaw) {
          //   currOutletLocal = JSON.parse(currOutletLocalRaw);
          // }

          const currOutletLocal = global.currOutlet ? global.currOutlet : null;

          var result = `${ESCPOS_CMD.SIZE_NORMAL}${ESCPOS_CMD.UNDERLINE_OFF}${ESCPOS_CMD.BOLD_OFF}`;
          result += `${ESCPOS_CMD.CENTER}${currOutlet.name}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
          result += `${ESCPOS_CMD.CENTER}${currOutlet.address}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
          result += `${ESCPOS_CMD.CENTER}${currOutlet.phone}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
          result += `${ESCPOS_CMD.CENTER}${merchantName}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.CENTER}SST ID No. 0012612771${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
          result += `${ESCPOS_CMD.LEFT}${ESCPOS_CMD.SIZE_2X}ORDER #38${ESCPOS_CMD.NEWLINE}`;
          result += `${ESCPOS_CMD.CENTER}${ESCPOS_CMD.SIZE_NORMAL}${[
            ...Array(pwc.FULL_W),
          ]
            .map((i) => '-')
            .join('')}${ESCPOS_CMD.NEWLINE}`;
          result += `${ESCPOS_CMD.LEFT}Mark${ESCPOS_CMD.NEWLINE}`;
          result += `${ESCPOS_CMD.LEFT}0127148876${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
          result += `${ESCPOS_CMD.LEFT}${'Receipt Date'.padEnd(
            12,
            ' ',
          )} : ${moment().format('ddd, MMM D, YYYY')}${ESCPOS_CMD.NEWLINE}`;
          result += `${ESCPOS_CMD.LEFT}${'Receipt #'.padEnd(
            12,
            ' ',
          )} : ${'38'}${ESCPOS_CMD.NEWLINE}`;
          result += `${ESCPOS_CMD.LEFT}${'Cashier'.padEnd(
            12,
            ' ',
          )} : ${'Sophie Kim'}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
          result += `${ESCPOS_CMD.LEFT}${'ITEM'.padEnd(
            pwc.ITEM_W,
            ' ',
          )}${'PRICE'.padEnd(8, ' ')}${'DISC'.padEnd(8, ' ')}${'QTY'.padEnd(
            4,
            ' ',
          )}${'SUB'.padStart(8, ' ')}${ESCPOS_CMD.NEWLINE}`;
          result += `${ESCPOS_CMD.CENTER}${[...Array(pwc.FULL_W)]
            .map((i) => '-')
            .join('')}${ESCPOS_CMD.NEWLINE}`;

          for (var i = 0; i < testItems.length; i++) {
            const testItem = testItems[i];

            // result += `${ESCPOS_CMD.LEFT}${
            //   // encodeGB18030(testItem.name)
            //   // testItem.name
            //   //   .slice(0, 20)
            //   padEndUnicodeStringV2(sliceUnicodeStringV2(testItem.name, 0, 19), 20, ' ')
            //   // .padEnd(20, ' ')
            //   }${(
            //     '' +
            //     testItem.price.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
            //   ).padEnd(8, ' ')}${(
            //     '' +
            //     testItem.discount.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
            //   ).padEnd(8, ' ')}${testItem.quantity.toString().padEnd(4, ' ')}${(
            //     '' +
            //     testItem.subtotal.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
            //   ).padStart(8, ' ')}${ESCPOS_CMD.NEWLINE}`;

            result += `${ESCPOS_CMD.LEFT}${
              // encodeGB18030(testItem.name)
              // testItem.name
              //   .slice(0, 20)
              padEndUnicodeStringV2(
                sliceUnicodeStringV2(testItem.name, pwc.ITEM_NAME_SLICE_W),
                pwc.ITEM_NAME_SLICE_MAX_W,
                ' ',
              )
              // .padEnd(20, ' ')
              }${ESCPOS_CMD.NEWLINE}`;

            if (testItem.remarks) {
              result += `${ESCPOS_CMD.LEFT}${
                // testItem.remarks
                //   .slice(0, 20)
                padEndUnicodeStringV2(
                  sliceUnicodeStringV2(testItem.remarks, pwc.ITEM_NAME_SLICE_W),
                  pwc.ITEM_NAME_SLICE_MAX_W,
                  ' ',
                )
                // .padEnd(20, ' ')
                }${ESCPOS_CMD.NEWLINE}`;
            }

            result += `${ESCPOS_CMD.LEFT}${
              // encodeGB18030(testItem.name)
              // testItem.name
              //   .slice(0, 20)
              ''.padEnd(pwc.ITEM_W, ' ')
              // .padEnd(20, ' ')
              }${(
                `${testItem.price
                  .toFixed(2)
                  .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
              ).padEnd(8, ' ')}${(
                `${testItem.discount
                  .toFixed(2)
                  .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
              ).padEnd(8, ' ')}${testItem.quantity.toString().padEnd(4, ' ')}${(
                `${testItem.subtotal
                  .toFixed(2)
                  .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
              ).padStart(8, ' ')}${ESCPOS_CMD.NEWLINE}`;

            if (i !== testItems.length - 1) {
              result += `${ESCPOS_CMD.NEWLINE}`;
            }
          }

          result += `${ESCPOS_CMD.CENTER}${[...Array(pwc.FULL_W)]
            .map((i) => '-')
            .join('')}${ESCPOS_CMD.NEWLINE}`;
          result += `${ESCPOS_CMD.LEFT}${'Subtotal'.padEnd(pwc.HALF_W, ' ')}${(
            `RM ${testItems
              .reduce((accum, item) => accum + item.subtotal, 0)
              .toFixed(2)
              .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
          ).padStart(24, ' ')}`;
          result += `${ESCPOS_CMD.LEFT}${'Discount'.padEnd(pwc.HALF_W, ' ')}${(
            `RM ${testItems
              .reduce((accum, item) => accum + item.discount, 0)
              .toFixed(2)
              .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
          ).padStart(24, ' ')}`;

          if (currOutletLocal && currOutletLocal.taxActive) {
            result += `${ESCPOS_CMD.LEFT}${`Tax (${(currOutletLocal.taxRate * 100).toFixed(0)}%)`.padEnd(pwc.HALF_W, ' ')}${(
              `RM ${testItems
                .reduce((accum, item) => accum + (item.subtotal * currOutletLocal.taxRate), 0)
                .toFixed(2)
                .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
            ).padStart(24, ' ')}${ESCPOS_CMD.NEWLINE}`;
          }

          if (currOutletLocal && currOutletLocal.scActive) {
            result += `${ESCPOS_CMD.LEFT}${`Service Charge (${(currOutletLocal.scRate * 100).toFixed(0)}%)`.padEnd(pwc.HALF_W, ' ')}${(
              `RM ${testItems
                .reduce((accum, item) => accum + (item.subtotal * currOutlet.scRate), 0)
                .toFixed(2)
                .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
            ).padStart(24, ' ')}${ESCPOS_CMD.NEWLINE}`;
          }

          result += `${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.LEFT}${ESCPOS_CMD.SIZE_2X}${'Total'.padEnd(
            pwc.TOTAL_W,
            ' ',
          )}${(
            `RM ${(Math.ceil(testItems
              .reduce((accum, item) => accum + item.subtotal +
                (currOutlet.taxActive ? (item.subtotal * currOutlet.taxRate) : 0) +
                (currOutlet.scActive ? (item.subtotal * currOutlet.scRate) : 0)
                , 0) * 20) / 20)
              .toFixed(2)
              .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
          ).padStart(12, ' ')}${ESCPOS_CMD.NEWLINE}`;
          result += `${ESCPOS_CMD.CENTER}${ESCPOS_CMD.SIZE_NORMAL}${[
            ...Array(pwc.FULL_W),
          ]
            .map((i) => '-')
            .join('')}${ESCPOS_CMD.NEWLINE}`;
          result += `${ESCPOS_CMD.LEFT}${'Received'.padEnd(pwc.HALF_W, ' ')}${(
            `RM ${(Math.ceil(testItems
              .reduce((accum, item) => accum + item.subtotal +
                (currOutlet.taxActive ? (item.subtotal * currOutlet.taxRate) : 0) +
                (currOutlet.scActive ? (item.subtotal * currOutlet.scRate) : 0)
                , 0) * 20) / 20)
              .toFixed(2)
              .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
          ).padStart(24, ' ')}${ESCPOS_CMD.NEWLINE}`;
          result += `${ESCPOS_CMD.LEFT}${'Balance'.padEnd(pwc.HALF_W, ' ')}${(
            `RM ${(0).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
          ).padStart(24, ' ')}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;

          result += `${ESCPOS_CMD.LEFT}${'Notes:'}${ESCPOS_CMD.NEWLINE}`;
          result += `${ESCPOS_CMD.LEFT}${'1.'}${ESCPOS_CMD.NEWLINE}`;
          result += `${ESCPOS_CMD.LEFT}${'2.'}${ESCPOS_CMD.NEWLINE}`;
          result += `${ESCPOS_CMD.LEFT}${'3.'}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE
            }${ESCPOS_CMD.NEWLINE}`;

          result += `${ESCPOS_CMD.CENTER}${ESCPOS_CMD.SIZE_2H
            }${'Thank you for your order'}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE
            }`;

          // result += `${ESCPOS_CMD.CENTER}${ESCPOS_CMD.SIZE_NORMAL}${ESCPOS_CMD.BARCODE_HEIGHT}${ESCPOS_CMD.BARCODE_WIDTH}${ESCPOS_CMD.BARCODE_FONT_A}${ESCPOS_CMD.BARCODE_TXT_OFF}${ESCPOS_CMD.BARCODE_EAN13}978020137962${ESCPOS_CMD.NEWLINE}`;

          // result += `${ESCPOS_CMD.CENTER}${printBarcode({
          //   data: 'TEST12345',
          //   type: 'ean',
          // }, 'cp936')}${ESCPOS_CMD.NEWLINE}`;

          // console.log('on print receipt');

          if (isUsbPrinter(printerTemp)) {
            try {
              await printToUsbDevice(printerTemp, result, true);
            }
            catch (error) {
              console.error(error);
            }
          }
          else {
            NetPrinter.printText(result, {
              encoding: 'GB18030',
              beep: true,
            });
          }

          ////////////////////////////////////////////////////////////

          result = '';
          if (koodooLogoUrl && koodooLogoUrl.startsWith('http') && netInfo.isInternetReachable && netInfo.isConnected) {
            // console.log('on print koodoo logo');

            await new Promise(async (resolve, reject) => {
              if (isUsbPrinter(printerTemp)) {
                try {
                  const command = await preparePrintImageCommand(merchantLogoUrl);
                  await printToUsbDevice(printerTemp, command, false);
                  resolve();
                }
                catch (error) {
                  console.error(error);
                  reject(error);
                }
              }
              else {
                await NetPrinter.printImage(merchantLogoUrl);
              }
            });

            result += `${ESCPOS_CMD.CENTER}Powered by KooDoo${ESCPOS_CMD.NEWLINE}`;
          }

          result += `${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;

          result += `${ESCPOS_CMD.CUT_PARTIAL}`;

          if (isUsbPrinter(printerTemp)) {
            try {
              await printToUsbDevice(printerTemp, result, true);
            }
            catch (error) {
              console.error(error);
            }
          }
          else {
            NetPrinter.printText(result, {
              encoding: 'GB18030',
              // beep: true,
            });
          }

          await openCashDrawer();

          // result = '';
          // result += `${ESCPOS_CMD.CD_KICK_2}${ESCPOS_CMD.CD_KICK_5}`;
          // NetPrinter.printText(result, {
          //   encoding: 'GB18030',
          //   // beep: true,
          // });
        } else if (
          printerTemp.types[index] === PRINTER_USAGE_TYPE.KITCHEN_DOCKET
        ) {
          let pwc = PW_CONFIG[printerTemp.paperWidth].PUO_KITCHEN_DOCKET;

          var result = `${ESCPOS_CMD.SIZE_NORMAL}${ESCPOS_CMD.UNDERLINE_OFF}${ESCPOS_CMD.BOLD_OFF}`;
          // result += `${ESCPOS_CMD.CENTER}${currOutlet.name}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.CENTER}${currOutlet.address}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.CENTER}${currOutlet.phone}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.CENTER}${merchantName}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.CENTER}SST ID No. 0012612771${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
          result += `${ESCPOS_CMD.LEFT}${ESCPOS_CMD.SIZE_2X}KITCHEN DOCKET${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
          result += `${ESCPOS_CMD.LEFT}${ESCPOS_CMD.SIZE_2X}TABLE: D7${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
          result += `${ESCPOS_CMD.LEFT}${ESCPOS_CMD.SIZE_2X}ORDER #38${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.CENTER}${ESCPOS_CMD.SIZE_NORMAL}${[...Array(48)]
          //   .map((i) => '-')
          //   .join('')}${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.LEFT}Mark${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.LEFT}0127148876${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
          result += `${ESCPOS_CMD.LEFT}${ESCPOS_CMD.SIZE_NORMAL
            }${moment().format('YYYY.MM.DD')}  ${moment().format('HH:mm:ss')}${ESCPOS_CMD.NEWLINE
            }${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.LEFT}${'Receipt #'.padEnd(12, ' ')} : ${'38'}${ESCPOS_CMD.NEWLINE
          //   }`;
          result += `${ESCPOS_CMD.LEFT}${'Cashier'.padEnd(
            12,
            ' ',
          )} : ${'Sophie Kim'}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
          result += `${ESCPOS_CMD.LEFT}${'ITEM'.padEnd(pwc.ITEM_W, ' ')}${''.padEnd(
            8,
            ' ',
          )}${''.padEnd(8, ' ')}${''.padEnd(4, ' ')}${'QTY'.padStart(8, ' ')}${ESCPOS_CMD.NEWLINE
            }`;
          result += `${ESCPOS_CMD.CENTER}${[...Array(pwc.FULL_W)]
            .map((i) => '-')
            .join('')}${ESCPOS_CMD.NEWLINE}`;

          for (var i = 0; i < testItems.length; i++) {
            const testItem = testItems[i];

            result += `${ESCPOS_CMD.LEFT}${
              // encodeGB18030(testItem.name)
              // testItem.name
              //   .slice(0, 20)
              padEndUnicodeStringV2(
                sliceUnicodeStringV2(testItem.name, pwc.ITEM_NAME_SLICE_W),
                pwc.ITEM_NAME_SLICE_MAX_W,
                ' ',
              )
              // .padEnd(20, ' ')
              }${testItem.quantity.toString().padStart(pwc.ITEM_QTY_W, ' ')}${ESCPOS_CMD.NEWLINE
              }`;

            if (testItem.remarks) {
              result += `${ESCPOS_CMD.LEFT}${
                // testItem.remarks
                //   .slice(0, 20)
                padEndUnicodeStringV2(
                  sliceUnicodeStringV2(testItem.remarks, pwc.ITEM_FULL_W),
                  pwc.ITEM_FULL_W,
                  ' ',
                )
                // .padEnd(20, ' ')
                }${ESCPOS_CMD.NEWLINE}`;
            }

            if (i !== testItems.length - 1) {
              result += `${ESCPOS_CMD.NEWLINE}`;
            }
          }

          result += `${ESCPOS_CMD.CENTER}${[...Array(pwc.FULL_W)]
            .map((i) => '-')
            .join('')}${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.LEFT}${'Subtotal'.padEnd(24, ' ')}${(
          //   'RM ' +
          //   testItems
          //     .reduce((accum, item) => accum + item.subtotal, 0)
          //     .toFixed(2)
          //     .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
          // ).padStart(24, ' ')}`;
          // result += `${ESCPOS_CMD.LEFT}${'Discount'.padEnd(24, ' ')}${(
          //   'RM ' +
          //   testItems
          //     .reduce((accum, item) => accum + item.discount, 0)
          //     .toFixed(2)
          //     .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
          // ).padStart(24, ' ')}`;
          // result += `${ESCPOS_CMD.LEFT}${'Tax (6%)'.padEnd(24, ' ')}${(
          //   'RM ' +
          //   testItems
          //     .reduce((accum, item) => accum + item.tax, 0)
          //     .toFixed(2)
          //     .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
          // ).padStart(24, ' ')}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.LEFT}${ESCPOS_CMD.SIZE_2X}${'Total'.padEnd(
          //   12,
          //   ' ',
          // )}${(
          //   'RM ' +
          //   testItems
          //     .reduce((accum, item) => accum + item.subtotal, 0)
          //     .toFixed(2)
          //     .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
          // ).padStart(12, ' ')}${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.CENTER}${ESCPOS_CMD.SIZE_NORMAL}${[...Array(48)]
          //   .map((i) => '-')
          //   .join('')}${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.LEFT}${'Received'.padEnd(24, ' ')}${(
          //   'RM ' +
          //   testItems
          //     .reduce((accum, item) => accum + item.subtotal, 0)
          //     .toFixed(2)
          //     .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
          // ).padStart(24, ' ')}${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.LEFT}${'Balance'.padEnd(24, ' ')}${(
          //   'RM ' + (0).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
          // ).padStart(24, ' ')}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;

          // result += `${ESCPOS_CMD.LEFT}${'Notes:'}${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.LEFT}${'1.'}${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.LEFT}${'2.'}${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.LEFT}${'3.'}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE
          //   }${ESCPOS_CMD.NEWLINE}`;

          // result += `${ESCPOS_CMD.CENTER}${ESCPOS_CMD.SIZE_2H
          //   }${'Thank you for your order'}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;

          // result += `${ESCPOS_CMD.CENTER}${ESCPOS_CMD.SIZE_NORMAL}${ESCPOS_CMD.BARCODE_HEIGHT}${ESCPOS_CMD.BARCODE_WIDTH}${ESCPOS_CMD.BARCODE_FONT_A}${ESCPOS_CMD.BARCODE_TXT_OFF}${ESCPOS_CMD.BARCODE_EAN13}978020137962${ESCPOS_CMD.NEWLINE}`;

          // result += `${ESCPOS_CMD.CENTER}${printBarcode({
          //   data: 'TEST12345',
          //   type: 'ean',
          // }, 'cp936')}${ESCPOS_CMD.NEWLINE}`;

          result += `${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;

          result += `${ESCPOS_CMD.CUT_PARTIAL}`;

          if (isUsbPrinter(printerTemp)) {
            try {
              await printToUsbDevice(printerTemp, result, true);
            }
            catch (error) {
              console.error(error);
            }
          }
          else {
            NetPrinter.printText(result, {
              encoding: 'GB18030',
              beep: true,
            });
          }
        } else if (
          printerTemp.types[index] === PRINTER_USAGE_TYPE.ORDER_SUMMARY
        ) {
          let pwc = PW_CONFIG[printerTemp.paperWidth].PUO_ORDER_SUMMARY;

          var result = `${ESCPOS_CMD.SIZE_NORMAL}${ESCPOS_CMD.UNDERLINE_OFF}${ESCPOS_CMD.BOLD_OFF}`;
          // result += `${ESCPOS_CMD.CENTER}${currOutlet.name}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.CENTER}${currOutlet.address}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.CENTER}${currOutlet.phone}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.CENTER}${merchantName}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.CENTER}SST ID No. 0012612771${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
          result += `${ESCPOS_CMD.LEFT}${ESCPOS_CMD.SIZE_2X}ORDER SUMMARY${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
          result += `${ESCPOS_CMD.LEFT}${ESCPOS_CMD.SIZE_2X}TABLE: D7${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
          result += `${ESCPOS_CMD.LEFT}${ESCPOS_CMD.SIZE_2X}ORDER #38${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.CENTER}${ESCPOS_CMD.SIZE_NORMAL}${[...Array(48)]
          //   .map((i) => '-')
          //   .join('')}${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.LEFT}Mark${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.LEFT}0127148876${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
          result += `${ESCPOS_CMD.LEFT}${ESCPOS_CMD.SIZE_NORMAL
            }${moment().format('YYYY.MM.DD')}  ${moment().format('HH:mm:ss')}${ESCPOS_CMD.NEWLINE
            }${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.LEFT}${'Receipt #'.padEnd(12, ' ')} : ${'38'}${ESCPOS_CMD.NEWLINE
          //   }`;
          // result += `${ESCPOS_CMD.LEFT}${'Table'.padEnd(
          //   12,
          //   ' ',
          // )} : ${'D7'}${ESCPOS_CMD.NEWLINE}`;
          result += `${ESCPOS_CMD.LEFT}${'Cashier'.padEnd(
            12,
            ' ',
          )} : ${'Sophie Kim'}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
          result += `${ESCPOS_CMD.LEFT}${'ITEM'.padEnd(pwc.ITEM_W, ' ')}${''.padEnd(
            8,
            ' ',
          )}${''.padEnd(8, ' ')}${''.padEnd(4, ' ')}${'QTY'.padStart(8, ' ')}${ESCPOS_CMD.NEWLINE
            }`;
          result += `${ESCPOS_CMD.CENTER}${[...Array(pwc.FULL_W)]
            .map((i) => '-')
            .join('')}${ESCPOS_CMD.NEWLINE}`;

          for (var i = 0; i < testItems.length; i++) {
            const testItem = testItems[i];

            // console.log(sliceUnicodeStringV2(testItem.name, 42));
            // console.log(padEndUnicodeStringV2(
            //   sliceUnicodeStringV2(testItem.name, 42),
            //   44,
            //   ' ',
            // ));

            result += `${ESCPOS_CMD.LEFT}${
              // encodeGB18030(testItem.name)
              // testItem.name
              //   .slice(0, 20)
              padEndUnicodeStringV2(
                sliceUnicodeStringV2(testItem.name, pwc.ITEM_NAME_SLICE_W),
                pwc.ITEM_NAME_SLICE_MAX_W,
                ' ',
              )
              // .padEnd(20, ' ')
              }${testItem.quantity.toString().padStart(pwc.ITEM_QTY_W, ' ')}${ESCPOS_CMD.NEWLINE
              }`;

            if (testItem.remarks) {
              result += `${ESCPOS_CMD.LEFT}${
                // testItem.remarks
                //   .slice(0, 20)
                padEndUnicodeStringV2(
                  sliceUnicodeStringV2(testItem.remarks, pwc.ITEM_FULL_W),
                  pwc.ITEM_FULL_W,
                  ' ',
                )
                // .padEnd(20, ' ')
                }${ESCPOS_CMD.NEWLINE}`;
            }

            if (i !== testItems.length - 1) {
              result += `${ESCPOS_CMD.NEWLINE}`;
            }
          }

          result += `${ESCPOS_CMD.CENTER}${[...Array(pwc.FULL_W)]
            .map((i) => '-')
            .join('')}${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.LEFT}${'Subtotal'.padEnd(24, ' ')}${(
          //   'RM ' +
          //   testItems
          //     .reduce((accum, item) => accum + item.subtotal, 0)
          //     .toFixed(2)
          //     .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
          // ).padStart(24, ' ')}`;
          // result += `${ESCPOS_CMD.LEFT}${'Discount'.padEnd(24, ' ')}${(
          //   'RM ' +
          //   testItems
          //     .reduce((accum, item) => accum + item.discount, 0)
          //     .toFixed(2)
          //     .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
          // ).padStart(24, ' ')}`;
          // result += `${ESCPOS_CMD.LEFT}${'Tax (6%)'.padEnd(24, ' ')}${(
          //   'RM ' +
          //   testItems
          //     .reduce((accum, item) => accum + item.tax, 0)
          //     .toFixed(2)
          //     .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
          // ).padStart(24, ' ')}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.LEFT}${ESCPOS_CMD.SIZE_2X}${'Total'.padEnd(
          //   12,
          //   ' ',
          // )}${(
          //   'RM ' +
          //   testItems
          //     .reduce((accum, item) => accum + item.subtotal, 0)
          //     .toFixed(2)
          //     .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
          // ).padStart(12, ' ')}${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.CENTER}${ESCPOS_CMD.SIZE_NORMAL}${[...Array(48)]
          //   .map((i) => '-')
          //   .join('')}${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.LEFT}${'Received'.padEnd(24, ' ')}${(
          //   'RM ' +
          //   testItems
          //     .reduce((accum, item) => accum + item.subtotal, 0)
          //     .toFixed(2)
          //     .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
          // ).padStart(24, ' ')}${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.LEFT}${'Balance'.padEnd(24, ' ')}${(
          //   'RM ' + (0).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
          // ).padStart(24, ' ')}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;

          // result += `${ESCPOS_CMD.LEFT}${'Notes:'}${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.LEFT}${'1.'}${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.LEFT}${'2.'}${ESCPOS_CMD.NEWLINE}`;
          // result += `${ESCPOS_CMD.LEFT}${'3.'}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE
          //   }${ESCPOS_CMD.NEWLINE}`;

          // result += `${ESCPOS_CMD.CENTER}${ESCPOS_CMD.SIZE_2H
          //   }${'Thank you for your order'}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;

          // result += `${ESCPOS_CMD.CENTER}${ESCPOS_CMD.SIZE_NORMAL}${ESCPOS_CMD.BARCODE_HEIGHT}${ESCPOS_CMD.BARCODE_WIDTH}${ESCPOS_CMD.BARCODE_FONT_A}${ESCPOS_CMD.BARCODE_TXT_OFF}${ESCPOS_CMD.BARCODE_EAN13}978020137962${ESCPOS_CMD.NEWLINE}`;

          // result += `${ESCPOS_CMD.CENTER}${printBarcode({
          //   data: 'TEST12345',
          //   type: 'ean',
          // }, 'cp936')}${ESCPOS_CMD.NEWLINE}`;

          result += `${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;

          result += `${ESCPOS_CMD.CUT_PARTIAL}`;

          if (isUsbPrinter(printerTemp)) {
            try {
              await printToUsbDevice(printerTemp, result, true);
            }
            catch (error) {
              console.error(error);
            }
          }
          else {
            NetPrinter.printText(result, {
              encoding: 'GB18030',
              beep: true,
            });
          }
        }
      }
    } catch (ex) {
      console.log(ex);
    }
    CommonStore.update((s) => { s.isLoading = false })
  };

  const scanPrinterUsb = async () => {
    try {
      // const devices = await navigator.usb.getDevices();

      // console.log('devices');
      // console.log(devices);

      // const printerDevices = devices.filter(device =>
      //   device.configuration.interfaces.some(interfaceObj =>
      //     interfaceObj.alternate.interfaceClass === 0x07
      //   )
      // );

      // console.log('printerDevices');
      // console.log(printerDevices);

      /////////////////////////////////////////////////
      /////////////////////////////////////////////////

      const device = await navigator.usb.requestDevice({
        filters: [
          { classCode: 0x07 }
        ]
      });

      if (!device) {
        console.error('No printer found');
        throw new Error('No printer found');
      }

      const tempIp = `#${device.vendorId}||${device.productId}||${device.productName}`;
      console.log('tempIp');
      console.log(tempIp);

      if (outletPrinters.find(printer => printer.ip === tempIp)) {
        console.error('Printer already exists');
        throw new Error('Printer already exists');
      }

      let body = {
        outletId: currOutlet.uniqueId,
        merchantId: currOutlet.merchantId,
        printerName: device.productName,
        printerIP: `#${device.vendorId}||${device.productId}||${device.productName}`,
        printerArea: 'Cashier',

        deviceName: device.productName,
        deviceType: PRINTER_DEVICE_TYPE.USB,

        printerTypes: [PRINTER_USAGE_TYPE.RECEIPT],

        blockKDCategoryIdList: [],
        blockOSCategoryIdList: [],
        blockReceiptCategoryIdList: [],
        blockBDCategoryIdList: [],

        blockKDOutletSectionIdList: [],
        blockOSOutletSectionIdList: [],
        blockReceiptOutletSectionIdList: [],
        blockBDOutletSectionIdList: [],

        paperWidth: PRINTER_PAPER_WIDTH._80MM,

        kdVariation: KD_PRINT_VARIATION.SUMMARY,

        userPriority: PRINTER_USER_PRIORITY.NORMAL,

        kdOptionsDeliverReject: KD_OPTIONS_DELIVER_REJECT.DELIVERED_REJECTED_ITEMS,

        groupList: [],
        groupName: '',

        osPrintTimes: '1',

        commandType: PRINTER_COMMAND_TYPE.ESCPOS,
        labelWidth: 70,
        labelHeight: 40,
        labelGap: 2,
      };

      APILocal.createOutletPrinter({ body, uid: userId })
        .then(async (result) => {
          if (result && result.data) {
            await bindPrinter(result.data);
          }
        })
        .catch((err) => {
          // console.log(err);
        });

      /////////////////////////////////////////////////
      /////////////////////////////////////////////////
    }
    catch (ex) {
      if (ex.message === `Failed to execute 'requestDevice' on 'USB': Must be handling a user gesture to show a permission request.`) {
        window.alert(`Error\n\nPlease download the Zadig (https://zadig.akeo.ie/) to install the WebUSB driver for your printer, and restart the Chrome and printers to proceed.\n\nNote: If this isssue persists, can try to scan again, or can try to use the incognito Chrome window as well.`);
      }
      else if (ex.message === `Printer already exists`) {
        window.alert(`Error\n\nThe printer is already paired, please try with another one.`);
      }
      else {
        window.alert(ex.message);
      }
    }
    finally {
      global.isScanning = false;
      setIsScanning(false);
    }
  };

  const scanPrinter = async () => {
    if (Platform.OS === 'ios') {
      // if (false) {
      // ios

      try {
        var intervalCounter = 0;

        var intervalTimer = setInterval(() => {
          if (intervalCounter < 210) {
            intervalCounter++;

            setScanningPercentage((intervalCounter / 210) * 100);
          }
          else {
            clearInterval(intervalTimer);
          }
        }, 500);

        NetPrinter.getDeviceList().then((devices) => {
          // console.log('devices');
          // console.log(devices);

          var newDiscoveredPrintersNum = 0;

          var printerNamingCount = newDiscoveredPrintersNum + outletPrinters.length;

          for (var i = 0; i < devices.length; i++) {
            var foundPrinter = outletPrinters.find(outletPrinter => outletPrinter.ip === devices[i].host);

            if (!foundPrinter) {
              newDiscoveredPrintersNum++;

              printerNamingCount++;

              var body = {
                outletId: currOutlet.uniqueId,
                merchantId: currOutlet.merchantId,
                // printerName: printerObj.device_name,
                printerName: `Printer #${printerNamingCount.toString()}`,
                printerIP: devices[i].host,
                printerArea: 'Cashier',

                printerTypes: [PRINTER_USAGE_TYPE.RECEIPT],

                blockKDCategoryIdList: [],
                blockOSCategoryIdList: [],
                blockReceiptCategoryIdList: [],
                blockBDCategoryIdList: [],

                blockKDOutletSectionIdList: [],
                blockOSOutletSectionIdList: [],
                blockReceiptOutletSectionIdList: [],
                blockBDOutletSectionIdList: [],

                paperWidth: PRINTER_PAPER_WIDTH._80MM,

                kdVariation: KD_PRINT_VARIATION.SUMMARY,

                userPriority: PRINTER_USER_PRIORITY.NORMAL,

                kdOptionsDeliverReject: KD_OPTIONS_DELIVER_REJECT.DELIVERED_REJECTED_ITEMS,

                groupList: [],
                groupName: '',

                osPrintTimes: '1',

                commandType: PRINTER_COMMAND_TYPE.ESCPOS,
                labelWidth: 70,
                labelHeight: 40,
                labelGap: 2,
              };

              // ApiClient.POST(API.createOutletPrinter, body, false)
              APILocal.createOutletPrinter({ body, uid: userId })
                .then(async (result) => {
                  if (result && result.data) {
                    await bindPrinter(result.data);
                  }
                })
                .catch((err) => {
                  // console.log(err);
                });
            }
          }

          clearInterval(intervalTimer);

          setIsScanning(false);

          // CommonStore.update(s => {
          //   s.isLoading = false;
          // });

          setScanningPercentage(0);

          var infoMsg = 'Printers scanning done.\n\n';
          if (newDiscoveredPrintersNum > 0) {
            infoMsg += `${newDiscoveredPrintersNum} new printers found.`;
          } else {
            infoMsg += 'No new printers found.';
          }

          alert('Success: ' + infoMsg);
        });
      }
      catch (ex) {
        // console.log(ex);

        alert('Error: Unable to find the printers, please try again');
      }
    }
    else {
      // android

      NetInfo.fetch().then(async (state) => {
        // console.log('network state');
        // console.log(state);

        if (
          state &&
          state.details &&
          state.details.ipAddress
          // &&
          // state.details.ipAddress.split('.').length >= 4
        ) {

          var ipv4 = '';

          if (state.details.ipAddress.split('.').length >= 4) {
            ipv4 = state.details.ipAddress.split('.');
          }
          else {
            // could be ipv6

            var ipv4Parsed = ipV6ToIpV4(state.details.ipAddress);

            if (ipv4Parsed.split('.').length >= 4) {
              // could be valid

              ipv4 = ipv4Parsed;
            }
            else {
              setIsScanning(false);

              // CommonStore.update(s => {
              //   s.isLoading = false;
              // });

              alert('Error: Unable to scan for available printers, please check if the device connected to the router.');

              return;
            }
          }

          if (ipv4) {
            ///////////////////////////////////////////////////////////////////////////

            // previous implementation (v1.0)

            // var ipAddressArray = state.details.ipAddress.split('.');

            // var newDiscoveredPrintersNum = 0;

            // var scanningIndex = 2;

            // var printerNamingCount =
            //   newDiscoveredPrintersNum + outletPrinters.length;                    

            // // scan through ip address list
            // for (var i = 2; i < 255; i++) {
            //   const isScanningLocal = await AsyncStorage.getItem('isScanning');

            //   if (isScanningLocal === '1') {
            //     ipAddressArray[3] = i.toString();
            //     const currIp = ipAddressArray.join('.');

            //     // console.log(`${i}) scanning ${currIp}`);

            //     const isPortOpenCheck = await isPortOpen(
            //       currIp,
            //       // success callback
            //       () => {
            //         // console.log(`${i}) success`);

            //         var foundPrinter = outletPrinters.find(
            //           (outletPrinter) => outletPrinter.ip === currIp,
            //         );

            //         if (!foundPrinter) {
            //           // proceed to create & store the printer

            //           newDiscoveredPrintersNum++;

            //           printerNamingCount++;

            //           var body = {
            //             outletId: currOutlet.uniqueId,
            //             merchantId: currOutlet.merchantId,
            //             // printerName: printerObj.device_name,
            //             printerName: `Printer #${printerNamingCount.toString()}`,
            //             printerIP: currIp,
            //             printerArea: 'Cashier',

            //             printerTypes: [PRINTER_USAGE_TYPE.RECEIPT],
            //           };

            //           ApiClient.POST(API.createOutletPrinter, body, false)
            //             .then(async (result) => {
            //               if (result && result.data) {
            //                 await bindPrinter(result.data);
            //               }
            //             })
            //             .catch((err) => {
            //               // console.log(err);
            //             });

            //           scanningIndex++;

            //           setScanningPercentage((scanningIndex / 254) * 100);

            //           if (scanningIndex >= 254) {
            //             setIsScanning(false);

            //             // CommonStore.update(s => {
            //             //   s.isLoading = false;
            //             // });

            //             setScanningPercentage(0);

            //             var infoMsg = 'Printers scanning done.\n\n';
            //             if (newDiscoveredPrintersNum > 0) {
            //               infoMsg += `${newDiscoveredPrintersNum} new printers found.`;
            //             } else {
            //               infoMsg += 'No new printers found.';
            //             }

            //             Alert.alert('Success', infoMsg);
            //           }
            //         }
            //       },
            //       // fail callback
            //       () => {
            //         // console.log(`${i}) fail`);

            //         scanningIndex++;

            //         setScanningPercentage((scanningIndex / 254) * 100);

            //         ////////////////////////////////////////////////

            //         if (scanningIndex >= 254) {
            //           setIsScanning(false);

            //           // CommonStore.update(s => {
            //           //   s.isLoading = false;
            //           // });

            //           setScanningPercentage(0);

            //           var infoMsg = 'Printers scanning done.\n\n';
            //           if (newDiscoveredPrintersNum > 0) {
            //             infoMsg += `${newDiscoveredPrintersNum} new printers found.`;
            //           } else {
            //             infoMsg += 'No new printers found.';
            //           }

            //           Alert.alert('Success', infoMsg);
            //         }
            //       },
            //     );

            //     // if (isPortOpenCheck) {
            //     //   // const printerObj = await connectToPrinterFast(currIp);

            //     //   // if (isPortOpenCheck) {
            //     //   //   // means is valid printer, check if the ip existed in outlet printers already

            //     //   //   var foundPrinter = outletPrinters.find(outletPrinter => outletPrinter.ip === printerObj.host);

            //     //   //   if (!foundPrinter) {
            //     //   //     // proceed to create & store the printer

            //     //   //     newDiscoveredPrintersNum++;

            //     //   //     // var body = {
            //     //   //     //   outletId: currOutlet.uniqueId,
            //     //   //     //   merchantId: currOutlet.merchantId,
            //     //   //     //   // printerName: printerObj.device_name,
            //     //   //     //   printerName: `Printer #${(outletPrinters.length + 1).toString()}`,
            //     //   //     //   printerIP: printerObj.host,
            //     //   //     //   printerArea: 'Cashier',

            //     //   //     //   printerTypes: [PRINTER_USAGE_TYPE.RECEIPT],
            //     //   //     // };

            //     //   //     // ApiClient.POST(API.createOutletPrinter, body, false)
            //     //   //     //   .then(async (result) => {
            //     //   //     //     if (result && result.data) {
            //     //   //     //       await bindPrinter(result.data);
            //     //   //     //     }
            //     //   //     //   })
            //     //   //     //   .catch((err) => {
            //     //   //     //     // console.log(err);
            //     //   //     //   });
            //     //   //   }
            //     //   // }
            //     // }

            //     setScanningPercentage((i / 254) * 100);
            //   } else {
            //     break;
            //   }
            // }

            ///////////////////////////////////////////////////////////////////////////          

            // var intervalCounter = 0;

            // var intervalTimer = setInterval(() => {
            //   if (intervalCounter < 210) {
            //     intervalCounter++;

            //     setScanningPercentage((intervalCounter / 210) * 100);
            //   }
            //   else {
            //     clearInterval(intervalTimer);
            //   }
            // }, 500);

            // var newDiscoveredPrintersNum = 0;

            // var printerNamingCount = newDiscoveredPrintersNum + outletPrinters.length;

            // for (var i = 0; i < devices.length; i++) {
            //   var foundPrinter = outletPrinters.find(outletPrinter => outletPrinter.ip === devices[i].host);

            //   if (!foundPrinter) {
            //     newDiscoveredPrintersNum++;

            //     printerNamingCount++;

            //     var body = {
            //       outletId: currOutlet.uniqueId,
            //       merchantId: currOutlet.merchantId,
            //       // printerName: printerObj.device_name,
            //       printerName: `Printer #${printerNamingCount.toString()}`,
            //       printerIP: devices[i].host,
            //       printerArea: 'Cashier',

            //       printerTypes: [PRINTER_USAGE_TYPE.RECEIPT],
            //     };

            //     ApiClient.POST(API.createOutletPrinter, body, false)
            //       .then(async (result) => {
            //         if (result && result.data) {
            //           await bindPrinter(result.data);
            //         }
            //       })
            //       .catch((err) => {
            //         // console.log(err);
            //       });
            //   }
            // }

            /////////////////////////////////////////////////////////////////////////////////

            // new implementation (v2.0)

            var ipAddressArray = ipv4;

            var newDiscoveredPrintersNum = 0;

            var scanningIndex = 2;

            var printerNamingCount =
              newDiscoveredPrintersNum + outletPrinters.length;

            // scan through ip address list

            global.referObj = {
              newDiscoveredPrintersNum: 0,
              scanningIndex: 2,
              printerNamingCount: newDiscoveredPrintersNum + outletPrinters.length,
              ipAddressArray,
            };

            Promise.all([
              scanPrinterBatchAndroid(global.referObj, 2, 12),
              scanPrinterBatchAndroid(global.referObj, 12, 22),
              scanPrinterBatchAndroid(global.referObj, 22, 32),
              scanPrinterBatchAndroid(global.referObj, 32, 42),
              scanPrinterBatchAndroid(global.referObj, 42, 52),
              scanPrinterBatchAndroid(global.referObj, 52, 62),
              scanPrinterBatchAndroid(global.referObj, 62, 72),
              scanPrinterBatchAndroid(global.referObj, 72, 82),
              scanPrinterBatchAndroid(global.referObj, 82, 92),
              scanPrinterBatchAndroid(global.referObj, 92, 102),
              scanPrinterBatchAndroid(global.referObj, 102, 112),
              scanPrinterBatchAndroid(global.referObj, 112, 122),
              scanPrinterBatchAndroid(global.referObj, 122, 132),
              scanPrinterBatchAndroid(global.referObj, 132, 142),
              scanPrinterBatchAndroid(global.referObj, 142, 152),
              scanPrinterBatchAndroid(global.referObj, 152, 162),
              scanPrinterBatchAndroid(global.referObj, 162, 172),
              scanPrinterBatchAndroid(global.referObj, 172, 182),
              scanPrinterBatchAndroid(global.referObj, 182, 192),
              scanPrinterBatchAndroid(global.referObj, 192, 202),
              scanPrinterBatchAndroid(global.referObj, 202, 212),
              scanPrinterBatchAndroid(global.referObj, 212, 222),
              scanPrinterBatchAndroid(global.referObj, 222, 232),
              scanPrinterBatchAndroid(global.referObj, 232, 242),
              scanPrinterBatchAndroid(global.referObj, 242, 252),
              scanPrinterBatchAndroid(global.referObj, 252, 255),
            ]);

            /////////////////////////////////////////////////////////////////////////////////

            // clearInterval(intervalTimer);

            // setIsScanning(false);

            // // CommonStore.update(s => {
            // //   s.isLoading = false;
            // // });

            // setScanningPercentage(0);

            // var infoMsg = 'Printers scanning done.\n\n';
            // if (newDiscoveredPrintersNum > 0) {
            //   infoMsg += `${newDiscoveredPrintersNum} new printers found.`;
            // } else {
            //   infoMsg += 'No new printers found.';
            // }

            // Alert.alert('Success', infoMsg);
          }
        } else {
          setIsScanning(false);

          // CommonStore.update(s => {
          //   s.isLoading = false;
          // });

          alert('Error: Unable to scan for available printers, please check if the device connected to the router.');
        }
      });
    }
  };

  const scanPrinterBatchAndroid = async (referObj, start, end) => {
    return new Promise(async (resolve, reject) => {
      for (var i = start; i < end; i++) {
        // const isScanningLocal = await AsyncStorage.getItem('isScanning');

        const isScanningLocal = global.isScanning;

        if (isScanningLocal === '1') {
          global.referObj.ipAddressArray[3] = i.toString();
          const currIp = global.referObj.ipAddressArray.join('.');

          // console.log(`${referObj.scanningIndex}) scanning ${currIp}`);

          const isPortOpenCheck = await isPortOpen(
            currIp,
            // success callback
            () => {
              // console.log(`${referObj.scanningIndex}) success`);

              var foundPrinter = outletPrinters.find(
                (outletPrinter) => outletPrinter.ip === currIp,
              );

              if (!foundPrinter) {
                // proceed to create & store the printer

                global.referObj.newDiscoveredPrintersNum++;

                global.referObj.printerNamingCount++;

                var body = {
                  outletId: currOutlet.uniqueId,
                  merchantId: currOutlet.merchantId,
                  // printerName: printerObj.device_name,
                  printerName: `Printer #${global.referObj.printerNamingCount.toString()}`,
                  printerIP: currIp,
                  printerArea: 'Cashier',

                  printerTypes: [PRINTER_USAGE_TYPE.RECEIPT],

                  blockKDCategoryIdList: [],
                  blockOSCategoryIdList: [],
                  blockReceiptCategoryIdList: [],
                  blockBDCategoryIdList: [],

                  blockKDOutletSectionIdList: [],
                  blockOSOutletSectionIdList: [],
                  blockReceiptOutletSectionIdList: [],
                  blockBDOutletSectionIdList: [],

                  paperWidth: PRINTER_PAPER_WIDTH._80MM,

                  kdVariation: KD_PRINT_VARIATION.SUMMARY,

                  userPriority: PRINTER_USER_PRIORITY.NORMAL,

                  kdOptionsDeliverReject: KD_OPTIONS_DELIVER_REJECT.DELIVERED_REJECTED_ITEMS,

                  groupList: [],
                  groupName: '',

                  osPrintTimes: '1',

                  commandType: PRINTER_COMMAND_TYPE.ESCPOS,
                  labelWidth: 70,
                  labelHeight: 40,
                  labelGap: 2,
                };

                // ApiClient.POST(API.createOutletPrinter, body, false)
                APILocal.createOutletPrinter({ body, uid: userId })
                  .then(async (result) => {
                    if (result && result.data) {
                      await bindPrinter(result.data);
                    }
                  })
                  .catch((err) => {
                    // console.log(err);
                  });

                global.referObj.scanningIndex++;

                setScanningPercentage((global.referObj.scanningIndex / 254) * 100);

                if (global.referObj.scanningIndex >= 254) {
                  setIsScanning(false);

                  // CommonStore.update(s => {
                  //   s.isLoading = false;
                  // });

                  setScanningPercentage(0);

                  var infoMsg = 'Printers scanning done.\n\n';
                  if (global.referObj.newDiscoveredPrintersNum > 0) {
                    infoMsg += `${global.referObj.newDiscoveredPrintersNum} new printers found.`;
                  } else {
                    infoMsg += 'No new printers found.';
                  }

                  alert('Success: ' + infoMsg);
                }

                if (i === end - 1) {
                  resolve();
                }
              }
            },
            // fail callback
            () => {
              // console.log(`${global.referObj.scanningIndex}) fail`);

              global.referObj.scanningIndex++;

              setScanningPercentage((global.referObj.scanningIndex / 254) * 100);

              ////////////////////////////////////////////////

              if (global.referObj.scanningIndex >= 254) {
                setIsScanning(false);

                // CommonStore.update(s => {
                //   s.isLoading = false;
                // });

                setScanningPercentage(0);

                var infoMsg = 'Printers scanning done.\n\n';
                if (global.referObj.newDiscoveredPrintersNum > 0) {
                  infoMsg += `${global.referObj.newDiscoveredPrintersNum} new printers found.`;
                } else {
                  infoMsg += 'No new printers found.';
                }

                alert('Success: ' + infoMsg);
              }

              if (i === end - 1) {
                resolve();
              }
            },
          );

          setScanningPercentage((global.referObj.scanningIndex / 254) * 100);
        } else {
          break;
        }
      }
    });
  };

  // function end

  return (
    <View
      style={[
        styles.container,
        {
          height: windowHeight,
          width: windowWidth,
          ...getTransformForScreenInsideNavigation(),
        },
      ]}
    >

      <View style={{ flex: 0.8 }}>
        <SideBar
          navigation={props.navigation}
          selectedTab={1}
          expandOperation
        />
      </View>
      {/* Old UX */}
      {/* >
        <View style={{ backgroundColor: Colors.whiteColor }}>
          <View style={{ alignItems: 'center', flexDirection: 'row', alignSelf: "center", marginVertical: 20, marginBottom: 20 }}>
            <Text style={{ fontWeight: "bold", fontSize: 25, marginRight: 10 }}>Queue</Text>
            <Switch
              value={currOutlet.queueStatus}
              onSyncPress={(value) => {
                // setState({ newReservationStatus: item }, function  = () => {
                //   switchQueueStatus();
                // });
                switchQueueStatus(value)
              }}
              width={42}
              circleColorActive={Colors.primaryColor}
              circleColorInactive={Colors.fieldtTxtColor}
              backgroundActive='#dddddd'
            />
            <Text
              style={{ fontSize: 20, marginLeft: 10, color: currOutlet.queueStatus ? Colors.primaryColor : Colors.fieldtTxtColor, alignSelf: 'center' }}>
              {currOutlet.queueStatus ? 'ON' : 'OFF'}
            </Text>
          </View>

          <FlatList
            data={userQueues}
            extraData={userQueues}
            renderItem={renderRow}
            numColumns={2}
            contentContainerStyle={{
              paddingHorizontal: 4,
              paddingBottom: 100,
              paddingLeft: 6,
              paddingTop: 4,
              alignSelf: 'center',
              //backgroundColor: 'red',
              width: '90%',
            }}
            style={{
              paddingTop: 5,
              paddingRight: 0,
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 1,
            }}
          />

        </View> */}

      {/* <ScrollView
          showsVerticalScrollIndicator={false}
          // scrollEnabled={switchMerchant}
          style={{}}>
          
        </ScrollView> */}
      <View style={{ height: windowHeight, flex: 9 }}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{ width: windowWidth * 0.9 }}
          contentContainerStyle={{
            paddingBottom: windowHeight * 0.1,
            backgroundColor: Colors.highlightColor,
          }}
        >
          {/* <ScrollView horizontal={true}> */}
          {/* New UX */}
          <View
            style={{
              paddingVertical: 30,
              marginHorizontal: 30,
            }}>
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
                alignItems: "center",
                marginHorizontal: 30,
                marginTop: 20,
                height: windowHeight * 0.1,
                width: windowWidth * 0.877,
                alignSelf: 'center',
              }}>
              <Text
                style={{
                  fontSize: switchMerchant ? 20 : 26,
                  fontFamily: 'NunitoSans-Bold',
                }}>
                {outletPrinters.length} Printer
                {outletPrinters.length > 1 ? 's' : ''}
              </Text>
              {/* <View style={{flexDirection: 'column' , 
                //right: Platform.OS == 'ios' ? 40 : 0,
                top: Platform.OS == 'ios' ? 3 : 5, 
                //marginLeft: '5%',
                //right: '250%'
                }}> 
                  <Switch
                    value={currOutlet.queueStatus}
                    onSyncPress={(value) => {
                      // setState({ newReservationStatus: item }, function  = () => {
                      //   switchQueueStatus();
                      // });
                      switchQueueStatus(value)
                    }}
                    width={42}
                    circleColorActive={Colors.primaryColor}
                    circleColorInactive={Colors.fieldtTxtColor}
                    backgroundActive='#dddddd'
                  />
                  <Text
                    style={{ fontSize: 18, marginTop: 0, color: currOutlet.reservationStatus ? Colors.primaryColor : Colors.fieldtTxtColor , textAlign:'center', right: Platform.OS == 'ios' ? 1 : 0}}>
                    {currOutlet.reservationStatus ? 'ON' : 'OFF'}
                  </Text>
                </View> */}

              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <TouchableOpacity
                  // disabled={isScanning}
                  style={[
                    styles.submitText,
                    {
                      flexDirection: 'row',
                      justifyContent: 'center',
                      alignItems: 'center',
                      borderRadius: 10,
                      height: switchMerchant ? 35 : 40,
                      left: 0,
                      backgroundColor: '#4E9F7D',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                    },
                  ]}
                  onPress={async () => {
                    CommonStore.update(s => {
                      s.printerCheckingModalVisibility = true;
                    });
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      paddingRight:
                        windowWidth <= 1823 &&
                          windowWidth >= 1820
                          ? 5
                          : 0,
                      paddingBottom:
                        windowWidth <= 1823 &&
                          windowWidth >= 1820
                          ? 2
                          : 0,
                    }}>
                    <Text
                      style={{
                        // marginLeft: 5,
                        color: Colors.primaryColor,
                        fontSize: switchMerchant ? 10 : 16,
                        color: '#FFFFFF',
                        fontFamily: 'NunitoSans-Bold',
                      }}>
                      CHECK
                    </Text>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  // disabled={isScanning}
                  style={[
                    styles.submitText,
                    {
                      flexDirection: 'row',
                      justifyContent: 'center',
                      alignItems: 'center',
                      borderRadius: 10,
                      height: switchMerchant ? 35 : 40,
                      left: 0,
                      backgroundColor: '#4E9F7D',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                    },
                  ]}
                  onPress={async () => {
                    // CommonStore.update(s => {
                    //   s.isLoading = true;
                    // });

                    if (isScanning) {
                      // setIsScanning(false);

                      // await AsyncStorage.setItem('isScanning', '0');

                      if (Platform.OS === 'android') {
                        // android can control

                        setIsScanning(false);

                        global.isScanning = '0';
                      }
                    } else {
                      setIsScanning(true);

                      global.isScanning = '1';

                      scanPrinterUsb();
                    }
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      paddingRight:
                        windowWidth <= 1823 &&
                          windowWidth >= 1820
                          ? 5
                          : 0,
                      paddingBottom:
                        windowWidth <= 1823 &&
                          windowWidth >= 1820
                          ? 2
                          : 0,
                    }}>
                    {isScanning ? (
                      <>
                        <Text
                          style={{
                            marginRight: 5,
                            color: Colors.primaryColor,
                            fontSize: switchMerchant ? 10 : 16,
                            color: '#FFFFFF',
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          {`${scanningPercentage.toFixed(1)}%`}
                        </Text>

                        <ActivityIndicator
                          size={'small'}
                          color={Colors.whiteColor}
                        />

                        <Text
                          style={{
                            marginLeft: 5,
                            color: Colors.primaryColor,
                            fontSize: switchMerchant ? 10 : 16,
                            color: '#FFFFFF',
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          SCANNING
                        </Text>
                      </>
                    ) : (
                      <>
                        <SimpleLineIcons
                          name="magnifier"
                          size={switchMerchant ? 11 : 18}
                          color="#FFFFFF"
                        />
                        <Text
                          style={{
                            marginLeft: 5,
                            color: Colors.primaryColor,
                            fontSize: switchMerchant ? 10 : 16,
                            color: '#FFFFFF',
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          SCAN
                        </Text>
                      </>
                    )}
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  disabled={isLoading}
                  style={[
                    styles.submitText,
                    {
                      flexDirection: 'row',
                      justifyContent: 'center',
                      alignItems: 'center',
                      borderRadius: 10,
                      height: switchMerchant ? 35 : 40,
                      left: 0,
                      backgroundColor: '#4E9F7D',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                    },
                  ]}
                  onPress={() => {
                    setQueueCustomerName('');
                    setQueuePhone('');
                    setQueuePax(0);

                    setSelectedQueue(null);
                    setAddQueueModal(true);
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      paddingRight:
                        windowWidth <= 1823 &&
                          windowWidth >= 1820
                          ? 5
                          : 0,
                      paddingBottom:
                        windowWidth <= 1823 &&
                          windowWidth >= 1820
                          ? 2
                          : 0,
                    }}>
                    <AntDesign
                      name="pluscircle"
                      size={switchMerchant ? 13 : 20}
                      color="#FFFFFF"
                    />
                    <Text
                      style={{
                        marginLeft: 5,
                        color: Colors.primaryColor,
                        fontSize: switchMerchant ? 10 : 16,
                        color: '#FFFFFF',
                        fontFamily: 'NunitoSans-Bold',
                      }}>
                      PRINTER
                    </Text>
                  </View>
                </TouchableOpacity>

                {/* <View style={{ flexDirection: 'row', justifyContent: 'center', alignItems: 'center', borderRadius: 10, height: 40, }}>
                
                    <View style={{
                      flexDirection: 'row',
                      justifyContent: 'center',
                      alignItems: 'center',
                      paddingLeft: 10,
                      borderRadius: 5,
                      height: 40,
                      borderRadius: 5,
                      borderWidth: 1,
                      borderColor: '#E5E5E5',
                      backgroundColor: 'white',
                      marginRight: 15,
                      shadowColor: '#000',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 3,

                    }}>
                      <Text style={{ fontSize: 16, paddingRight: Platform.OS == 'ios' ? 20 : 20, borderColor: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Bold' }}>
                        Filter
                      </Text>
                      <DropDownPicker
                        controller={instance => setController1(instance)}
                        arrowColor={Colors.primaryColor}
                        arrowSize={23}
                        arrowStyle={{ fontWeight: 'bold' }}
                        labelStyle={{ fontFamily: 'NunitoSans-Regular' }}
                        itemStyle={{ justifyContent: 'flex-start', marginLeft: 5 }}
                        placeholderStyle={{ color: 'black' }}
                        style={{ width: 140, borderWidth: 0, paddingHorizontal: 5, paddingVertical: 0, borderRadius: 5, borderColor: '#E5E5E5', borderWidth: 0, borderLeftWidth: 0, }}
                        items={[{ label: 'Pending', value: 0 }, { label: 'Accepted', value: 1 }, { label: 'Seated', value: 2 }, { label: 'Served', value: 3 }, { label: 'Rejected', value: 4 }, { label: 'No Show', value: 5 }]} //Awaiting Authorization
                        placeholder={"Pending"}
                        onChangeItem={selectedFilter => {
                          filterOrders(selectedFilter);
                        }
                        }
                      />
                    </View>
                  </View> */}

                {/* <View style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                  paddingLeft: 10,
                  borderRadius: 5,
                  height: 40,
                  borderRadius: 5,
                  borderWidth: 1,
                  borderColor: '#E5E5E5',
                  backgroundColor: 'white',
                  marginRight: 15,
                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 3,

                }}>
                  <Text style={{ fontSize: 16, paddingRight: Platform.OS == 'ios' ? 20 : 20, borderColor: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Bold' }}>
                    Filter
                  </Text>
                  <DropDownPicker
                    // controller={instance => setController1(instance)}
                    arrowColor={Colors.primaryColor}
                    arrowSize={23}
                    arrowStyle={{ fontWeight: 'bold' }}
                    labelStyle={{ fontFamily: 'NunitoSans-Regular' }}
                    itemStyle={{ justifyContent: 'flex-start', marginLeft: 0 }}
                    placeholderStyle={{ color: 'black' }}
                    style={{ width: 140, borderWidth: 0, paddingHorizontal: 5, paddingVertical: 0, borderRadius: 5, borderColor: '#E5E5E5', borderWidth: 0, borderLeftWidth: 0, }}
                    items={[{ label: 'All', value: 0 }, { label: 'Pending', value: 1 }, { label: 'Seated', value: 3 }, { label: 'Served', value: 4 }, { label: 'Cancelled', value: 5 }]} //Awaiting Authorization
                    // placeholder={"Pending"}
                    defaultValue={filterType}
                    onChangeItem={selectedFilter => {
                      // filterOrders(selectedFilter);
                      setFilterType(selectedFilter.value);
                    }
                    }
                  //onOpen={() => controller.close()}
                  />
                </View> */}

                <View
                  style={[
                    {
                      height: 40,
                    },
                  ]}>
                  <View
                    style={{
                      width: switchMerchant ? 200 : 250,
                      height: 40,
                      backgroundColor: 'white',
                      borderRadius: 5,
                      flexDirection: 'row',
                      alignContent: 'center',
                      alignItems: 'center',
                      shadowColor: '#000',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 3,
                      borderWidth: 1,
                      borderColor: '#E5E5E5',
                    }}>
                    <Icon
                      name="search"
                      size={switchMerchant ? 13 : 18}
                      color={Colors.primaryColor}
                      style={{ marginLeft: 15 }}
                    />
                    <TextInput
                      underlineColorAndroid={Colors.whiteColor}
                      style={{
                        width: switchMerchant ? 180 : 220,
                        fontSize: switchMerchant ? 10 : 15,
                        fontFamily: 'NunitoSans-Regular',
                        paddingLeft: 5,
                        height: 45,
                      }}
                      clearButtonMode="while-editing"
                      placeholder=" Search"
                      placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                      onChangeText={(text) => {
                        setSearch(text);
                        // setSearch(text.trim());
                      }}
                    // value={search}
                    />
                  </View>
                </View>
              </View>
            </View>

            <View style={{
              marginTop: 30,
              // marginBottom: 100,
              zIndex: -1,
            }}>
              <View
                style={{
                  width: '100%',
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingHorizontal: 5,
                }}>
                <View
                  style={{
                    width: switchMerchant ? '12.4%' : '14%',
                    marginHorizontal: 4,
                  }}>
                  <View
                    style={{
                      width: '100%',
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    <View style={{}}>
                      <TouchableOpacity
                        onPress={() => {
                          if (sort === QUEUE_SORT_FIELD_TYPE.NAME_ASC) {
                            setSort(QUEUE_SORT_FIELD_TYPE.NAME_DESC);
                          } else {
                            setSort(QUEUE_SORT_FIELD_TYPE.NAME_ASC);
                          }
                        }}>
                        <Text
                          style={{
                            color: 'black',
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: switchMerchant ? 10 : 16,
                            paddingLeft: switchMerchant ? 8 : windowHeight === 800 && windowWidth === 1280 ? 0 : 5,
                          }}>
                          Printer Name
                        </Text>
                      </TouchableOpacity>
                    </View>
                    <View style={{ marginLeft: 3 }}>
                      {/* <TouchableOpacity onPress={() => { setSort(QUEUE_SORT_FIELD_TYPE.NAME_DESC) }}>
                        <Entypo name='triangle-up' size={14} color={sort === QUEUE_SORT_FIELD_TYPE.NAME_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                      </TouchableOpacity>
                      <TouchableOpacity onPress={() => { setSort(QUEUE_SORT_FIELD_TYPE.NAME_ASC) }}>
                        <Entypo name='triangle-down' size={14} color={sort === QUEUE_SORT_FIELD_TYPE.NAME_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                      </TouchableOpacity> */}
                    </View>
                  </View>
                </View>

                <View
                  style={{
                    width: switchMerchant ? '16.8%' : '18%',
                    marginHorizontal: 4,
                    alignItems: 'flex-start',
                  }}>
                  <View
                    style={{
                      width: '100%',
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    <View style={{}}>
                      <TouchableOpacity
                        onPress={() => {
                          if (sort === QUEUE_SORT_FIELD_TYPE.DATE_TIME_ASC) {
                            setSort(QUEUE_SORT_FIELD_TYPE.DATE_TIME_DESC);
                          } else {
                            setSort(QUEUE_SORT_FIELD_TYPE.DATE_TIME_ASC);
                          }
                        }}>
                        <Text
                          style={{
                            color: 'black',
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: switchMerchant ? 10 : 16,
                            paddingRight: switchMerchant ? 20 : 0,
                            paddingLeft: switchMerchant ? 0 : windowHeight === 800 && windowWidth === 1280 ? 0 : 6,
                          }}>
                          Printer IP
                        </Text>
                      </TouchableOpacity>
                    </View>
                    <View style={{ marginLeft: 3 }}>
                      {/* <TouchableOpacity onPress={() => { setSort(QUEUE_SORT_FIELD_TYPE.DATE_TIME_DESC) }}>
                        <Entypo name='triangle-up' size={14} color={sort === QUEUE_SORT_FIELD_TYPE.DATE_TIME_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                      </TouchableOpacity>
                      <TouchableOpacity onPress={() => { setSort(QUEUE_SORT_FIELD_TYPE.DATE_TIME_ASC) }}>
                        <Entypo name='triangle-down' size={14} color={sort === QUEUE_SORT_FIELD_TYPE.DATE_TIME_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                      </TouchableOpacity> */}
                    </View>
                  </View>
                </View>

                <View
                  style={{
                    width: switchMerchant ? '8%' : '8%',
                    marginHorizontal: 4,
                  }}>
                  <View
                    style={{
                      width: '100%',
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    <View style={{}}>
                      <TouchableOpacity
                        onPress={() => {
                          if (sort === QUEUE_SORT_FIELD_TYPE.NAME_ASC) {
                            setSort(QUEUE_SORT_FIELD_TYPE.NAME_DESC);
                          } else {
                            setSort(QUEUE_SORT_FIELD_TYPE.NAME_ASC);
                          }
                        }}>
                        <Text
                          style={{
                            color: 'black',
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: switchMerchant ? 10 : 16,
                            paddingLeft: switchMerchant ? 8 : windowHeight === 800 && windowWidth === 1280 ? 0 : 5,
                          }}>
                          Area
                        </Text>
                      </TouchableOpacity>
                    </View>
                    <View style={{ marginLeft: 3 }}>
                      {/* <TouchableOpacity onPress={() => { setSort(QUEUE_SORT_FIELD_TYPE.NAME_DESC) }}>
                        <Entypo name='triangle-up' size={14} color={sort === QUEUE_SORT_FIELD_TYPE.NAME_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                      </TouchableOpacity>
                      <TouchableOpacity onPress={() => { setSort(QUEUE_SORT_FIELD_TYPE.NAME_ASC) }}>
                        <Entypo name='triangle-down' size={14} color={sort === QUEUE_SORT_FIELD_TYPE.NAME_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                      </TouchableOpacity> */}
                    </View>
                  </View>
                </View>

                <View
                  style={{
                    width: switchMerchant ? '13.6%' : '14%',
                    marginHorizontal: 4,
                    alignItems: 'flex-start',
                  }}>
                  <View
                    style={{
                      width: '100%',
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    <View style={{}}>
                      <TouchableOpacity
                        onPress={() => {
                          // if (sort === QUEUE_SORT_FIELD_TYPE.WAITING_TIME_ASC) {
                          //   setSort(QUEUE_SORT_FIELD_TYPE.WAITING_TIME_DESC);
                          // } else {
                          //   setSort(QUEUE_SORT_FIELD_TYPE.WAITING_TIME_ASC);
                          // }
                        }}>
                        <Text
                          style={{
                            color: 'black',
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: switchMerchant ? 10 : 16,
                          }}>
                          Printer Type(s)
                        </Text>
                      </TouchableOpacity>
                    </View>
                    <View style={{ marginLeft: 3 }}>
                      {/* <TouchableOpacity onPress={() => { setSort(QUEUE_SORT_FIELD_TYPE.WAITING_TIME_DESC) }}>
                        <Entypo name='triangle-up' size={14} color={sort === QUEUE_SORT_FIELD_TYPE.WAITING_TIME_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                      </TouchableOpacity>
                      <TouchableOpacity onPress={() => { setSort(QUEUE_SORT_FIELD_TYPE.WAITING_TIME_ASC) }}>
                        <Entypo name='triangle-down' size={14} color={sort === QUEUE_SORT_FIELD_TYPE.WAITING_TIME_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                      </TouchableOpacity> */}
                    </View>
                  </View>
                </View>

                <View
                  style={{
                    width: switchMerchant ? '13.5%' : ((!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? '13%' : '14%'),
                    marginHorizontal: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 2 : 4,
                    alignItems: 'flex-start',
                  }}>
                  <View
                    style={{
                      width: '100%',
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    <View style={{}}>
                      <TouchableOpacity
                        onPress={() => {
                          if (sort === QUEUE_SORT_FIELD_TYPE.CAPACITY_ASC) {
                            setSort(QUEUE_SORT_FIELD_TYPE.CAPACITY_DESC);
                          } else {
                            setSort(QUEUE_SORT_FIELD_TYPE.CAPACITY_ASC);
                          }
                        }}>
                        <Text
                          style={{
                            color: 'black',
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: switchMerchant ? 10 : 16,
                          }}>
                          Save Printer
                        </Text>
                      </TouchableOpacity>
                    </View>
                    <View style={{ marginLeft: 3 }}>
                      {/* <TouchableOpacity onPress={() => { setSort(QUEUE_SORT_FIELD_TYPE.CAPACITY_DESC) }}>
                        <Entypo name='triangle-up' size={14} color={sort === QUEUE_SORT_FIELD_TYPE.CAPACITY_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                      </TouchableOpacity>
                      <TouchableOpacity onPress={() => { setSort(QUEUE_SORT_FIELD_TYPE.CAPACITY_ASC) }}>
                        <Entypo name='triangle-down' size={14} color={sort === QUEUE_SORT_FIELD_TYPE.CAPACITY_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                      </TouchableOpacity> */}
                    </View>
                  </View>
                </View>

                {/* <View
                    style={{
                      width: switchMerchant ? '13.6%' : '14%',
                      marginHorizontal: 4,
                      alignItems: 'flex-start',
                    }}>
                    <View
                      style={{
                        width: '100%',
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}>
                      <View style={{}}>
                        <TouchableOpacity
                          onPress={() => {
                            if (sort === QUEUE_SORT_FIELD_TYPE.WAITING_TIME_ASC) {
                              setSort(QUEUE_SORT_FIELD_TYPE.WAITING_TIME_DESC);
                            } else {
                              setSort(QUEUE_SORT_FIELD_TYPE.WAITING_TIME_ASC);
                            }
                          }}>
                          <Text
                            style={{
                              color: 'black',
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 16,
                            }}>
                            Unbind Printer
                          </Text>
                        </TouchableOpacity>
                      </View>
                      <View style={{ marginLeft: 3 }}>
                      </View>
                    </View>
                  </View> */}

                <View
                  style={{
                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? '13%' : '14%',
                    marginHorizontal: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 2 : 4,
                    alignItems: 'flex-start',
                  }}>
                  <View
                    style={{
                      width: '100%',
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    <View style={{}}>
                      <TouchableOpacity
                        onPress={() => {
                          if (sort === QUEUE_SORT_FIELD_TYPE.STATUS_ASC) {
                            setSort(QUEUE_SORT_FIELD_TYPE.STATUS_DESC);
                          } else {
                            setSort(QUEUE_SORT_FIELD_TYPE.STATUS_ASC);
                          }
                        }}>
                        <Text
                          style={{
                            color: 'black',
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: switchMerchant ? 10 : 16,
                          }}>
                          Test Printer
                        </Text>
                      </TouchableOpacity>
                    </View>
                    <View style={{ marginLeft: 3 }}>
                      {/* <TouchableOpacity onPress={() => { setSort(QUEUE_SORT_FIELD_TYPE.STATUS_DESC) }}>
                        <Entypo name='triangle-up' size={14} color={sort === QUEUE_SORT_FIELD_TYPE.STATUS_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                      </TouchableOpacity>
                      <TouchableOpacity onPress={() => { setSort(QUEUE_SORT_FIELD_TYPE.STATUS_ASC) }}>
                        <Entypo name='triangle-down' size={14} color={sort === QUEUE_SORT_FIELD_TYPE.STATUS_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                      </TouchableOpacity> */}
                    </View>
                  </View>
                </View>

                <View
                  style={{
                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? '14.5%' : '14%',
                    marginHorizontal: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 2 : 4,
                    alignItems: 'flex-start',
                  }}>
                  <View
                    style={{
                      width: '100%',
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    <View style={{}}>
                      <TouchableOpacity
                        onPress={() => {
                          if (sort === QUEUE_SORT_FIELD_TYPE.STATUS_ASC) {
                            setSort(QUEUE_SORT_FIELD_TYPE.STATUS_DESC);
                          } else {
                            setSort(QUEUE_SORT_FIELD_TYPE.STATUS_ASC);
                          }
                        }}>
                        <Text
                          style={{
                            color: 'black',
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: switchMerchant ? 10 : 16,
                          }}>
                          Remove Printer
                        </Text>
                      </TouchableOpacity>
                    </View>
                    <View style={{ marginLeft: 3 }}>
                      {/* <TouchableOpacity onPress={() => { setSort(QUEUE_SORT_FIELD_TYPE.STATUS_DESC) }}>
                        <Entypo name='triangle-up' size={14} color={sort === QUEUE_SORT_FIELD_TYPE.STATUS_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                      </TouchableOpacity>
                      <TouchableOpacity onPress={() => { setSort(QUEUE_SORT_FIELD_TYPE.STATUS_ASC) }}>
                        <Entypo name='triangle-down' size={14} color={sort === QUEUE_SORT_FIELD_TYPE.STATUS_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                      </TouchableOpacity> */}
                    </View>
                  </View>
                </View>
              </View>
              {/* <FlatList
                  showsVerticalScrollIndicator={false}
                  data={outletPrinters.filter((printer) => {
                    if (search !== '') {
                      const searchLowerCase = search.toLowerCase();

                      if (
                        printer.name.toLowerCase().includes(searchLowerCase) ||
                        printer.ip.toLowerCase().includes(searchLowerCase)
                      ) {
                        return true;
                      } else {
                        return false;
                      }
                    } else {
                      return true;
                    }
                  })}
                  renderItem={renderPrinter}
                  keyExtractor={(item, index) => String(index)}
                  style={{
                    marginTop: 10,
                    backgroundColor: 'white',
                    height: windowHeight * 0.6,
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                  }}
                /> */}
              <View
                style={{
                  marginTop: 10,
                  backgroundColor: 'white',
                  height: windowHeight * 0.65,
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 1,
                }}>
                <ScrollView nestedScrollEnabled>
                  {outletPrinters
                    .filter((printer) => {
                      if (search !== '') {
                        const searchLowerCase = search.toLowerCase();

                        if (
                          printer.name.toLowerCase().includes(searchLowerCase) ||
                          printer.ip.toLowerCase().includes(searchLowerCase)
                        ) {
                          return true;
                        } else {
                          return false;
                        }
                      } else {
                        return true;
                      }
                    })
                    .map((item, index) => {
                      return (
                        <View
                          style={{
                            paddingVertical: 10,
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,

                            zIndex: activeDropdown === index ? outletPrinters.length + 1 : index,
                            // + ((printerIdDict[item.uniqueId] && printerIdDict[item.uniqueId].groupList) ? (printerIdDict[item.uniqueId].groupList.length * windowHeight * 0.1) : 0)
                          }}>
                          {/* <Swipeable
                            renderRightActions={() => rightAction(item)}
                            // ref={refArray[index]}
                            onSwipeableWillOpen={() => {
                            }}> */}
                          <View
                            style={{
                              // elevation: 1,
                              borderRadius: 5,
                              // backgroundColor: 'white',
                              // height: 300,
                              paddingHorizontal: 5,
                              paddingTop: switchMerchant ? 5 : 0,
                              flex: 1
                            }}>
                            <View
                              style={{
                                flexDirection: 'row',
                                height: switchMerchant
                                  ? windowHeight * 0.1
                                  : windowHeight * 0.06,
                                alignItems: 'center',
                                width: switchMerchant
                                  ? windowWidth * 0.8
                                  : '100%',
                                alignSelf: 'center',
                                zIndex: openPT[index] ? 1000 : undefined,
                                // zIndex: openPT.length !== 0 ? outletPrinters.length + 1 : undefined
                              }}>
                              <View
                                style={{
                                  width: switchMerchant ? '12%' : '14%',
                                  marginHorizontal: switchMerchant ? 0 : 4,
                                }}>
                                <TextInput
                                  style={{
                                    color: Colors.fontDark,
                                    fontSize: switchMerchant ? 10 : 16,
                                    fontFamily: 'NunitoSans-Bold',
                                    borderWidth: 1,
                                    borderRadius: 5,
                                    borderColor: '#E5E5E5',
                                    paddingLeft: switchMerchant ? 5 : 5,
                                    height: 40,
                                    width: switchMerchant
                                      ? Platform.OS === 'ios' ? 98
                                        : windowWidth * 0.098
                                      : Platform.OS === 'ios'
                                        ? windowWidth * 0.1
                                        : ((!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 100 : 140),
                                  }}
                                  placeholder={'Printer Name'}
                                  placeholderTextColor={Colors.descriptionColor}
                                  defaultValue={
                                    printerIdDict[item.uniqueId]
                                      ? printerIdDict[item.uniqueId].name
                                      : ''
                                  }
                                  //iOS
                                  clearTextOnFocus
                                  //////////////////////////////////////////////
                                  //Android
                                  onFocus={() => {
                                    setTemp(printerIdDict[item.uniqueId].name)
                                    setPrinterIdDict({
                                      ...printerIdDict,
                                      [item.uniqueId]: {
                                        ...printerIdDict[item.uniqueId],
                                        name: '',
                                      },
                                    });
                                  }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  onEndEditing={() => {
                                    if (printerIdDict[item.uniqueId].name == '') {
                                      setPrinterIdDict({
                                        ...printerIdDict,
                                        [item.uniqueId]: {
                                          ...printerIdDict[item.uniqueId],
                                          name: temp,
                                        },
                                      });
                                    }
                                  }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    setPrinterIdDict({
                                      ...printerIdDict,
                                      [item.uniqueId]: {
                                        ...printerIdDict[item.uniqueId],
                                        name: text,
                                      },
                                    });
                                  }} />
                              </View>
                              <View style={{ width: '18%', marginHorizontal: 4 }}>
                                {/* <Text style={{ color: Colors.fontDark, fontSize: 16, fontFamily: 'NunitoSans-Bold' }}>
                                    168.88.8888.88
                                  </Text> */}

                                <TextInput
                                  style={{
                                    color: Colors.fontDark,
                                    fontSize: switchMerchant ? 10 : 16,
                                    fontFamily: 'NunitoSans-Bold',
                                    borderWidth: 1,
                                    borderRadius: 5,
                                    borderColor: '#E5E5E5',
                                    paddingLeft: 5,
                                    height: 40,
                                    width: switchMerchant ? 127 : 165,
                                  }}
                                  placeholder={'Printer IP'}
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  keyboardType={'decimal-pad'}
                                  defaultValue={
                                    printerIdDict[item.uniqueId]
                                      ? printerIdDict[item.uniqueId].ip
                                      : ''
                                  }
                                  //iOS
                                  clearTextOnFocus
                                  //////////////////////////////////////////////
                                  //Android
                                  onFocus={() => {
                                    setTemp(printerIdDict[item.uniqueId].ip)
                                    setPrinterIdDict({
                                      ...printerIdDict,
                                      [item.uniqueId]: {
                                        ...printerIdDict[item.uniqueId],
                                        ip: '',
                                      },
                                    });
                                  }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  onEndEditing={() => {
                                    if (printerIdDict[item.uniqueId].ip == '') {
                                      setPrinterIdDict({
                                        ...printerIdDict,
                                        [item.uniqueId]: {
                                          ...printerIdDict[item.uniqueId],
                                          ip: temp,
                                        },
                                      });
                                    }
                                  }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    setPrinterIdDict({
                                      ...printerIdDict,
                                      [item.uniqueId]: {
                                        ...printerIdDict[item.uniqueId],
                                        ip: text,
                                      },
                                    });
                                  }} />
                              </View>
                              <View
                                style={{
                                  width: switchMerchant ? '8%' : '8%',
                                  marginHorizontal: switchMerchant ? 0 : 4,
                                }}>
                                <TextInput
                                  style={{
                                    color: Colors.fontDark,
                                    fontSize: switchMerchant ? 10 : 16,
                                    fontFamily: 'NunitoSans-Bold',
                                    borderWidth: 1,
                                    borderRadius: 5,
                                    borderColor: '#E5E5E5',
                                    paddingLeft: switchMerchant ? 5 : 5,
                                    height: 40,
                                    width: switchMerchant
                                      ? 58
                                      : Platform.OS === 'ios'
                                        ? windowWidth * 0.06
                                        : ((!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 65 : 80),
                                  }}
                                  placeholder={'Kitchen'}
                                  placeholderTextColor={Colors.descriptionColor}
                                  defaultValue={
                                    printerIdDict[item.uniqueId]
                                      ? printerIdDict[item.uniqueId].area
                                      : ''
                                  }
                                  //iOS
                                  clearTextOnFocus
                                  //////////////////////////////////////////////
                                  //Android
                                  onFocus={() => {
                                    setTemp(printerIdDict[item.uniqueId].area)
                                    setPrinterIdDict({
                                      ...printerIdDict,
                                      [item.uniqueId]: {
                                        ...printerIdDict[item.uniqueId],
                                        area: '',
                                      },
                                    });
                                  }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  onEndEditing={() => {
                                    if (printerIdDict[item.uniqueId].area == '') {
                                      setPrinterIdDict({
                                        ...printerIdDict,
                                        [item.uniqueId]: {
                                          ...printerIdDict[item.uniqueId],
                                          area: temp,
                                        },
                                      });
                                    }
                                  }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    setPrinterIdDict({
                                      ...printerIdDict,
                                      [item.uniqueId]: {
                                        ...printerIdDict[item.uniqueId],
                                        area: text,
                                      },
                                    });
                                  }} />
                              </View>

                              <View
                                style={{
                                  width: "14%",
                                  marginHorizontal: switchMerchant ? 4 : 4,
                                  zIndex: 10001 - index,

                                }}
                              >
                                {/* <DropDownPicker
                                                                containerStyle={[
                                                                    { height: 40 },
                                                                    switchMerchant ? { height: 35 } : {},
                                                                ]}
                                                                arrowColor={'black'}
                                                                arrowSize={20}
                                                                arrowStyle={{ fontWeight: 'bold' }}
                                                                labelStyle={{
                                                                    fontFamily: 'NunitoSans-Regular',
                                                                    fontSize: 14,
                                                                }}
                                                                style={[
                                                                    {
                                                                        width: '90%',
                                                                        paddingVertical: 0,
                                                                        backgroundColor: Colors.fieldtBgColor,
                                                                        borderRadius: 10,
                                                                        zIndex: 10001 - index,
                                                                    },
                                                                    switchMerchant
                                                                        ? {
                                                                            width: '90%',
                                                                        }
                                                                        : {},
                                                                ]}
                                                                placeholderStyle={{
                                                                    color: Colors.fieldtTxtColor,
                                                                    fontFamily: 'NunitoSans-Regular',
                                                                    fontSize: 14,
                                                                }}
                                                                //items={[{ label: 'Dine In', value: 1 }, { label: 'Takeaway', value: 2 }, { label: 'Delivery', value: 3 } ]}
                                                                items={PRINTER_USAGE_TYPE_DROPDOWN_LIST}
                                                                itemStyle={{
                                                                    justifyContent: 'flex-start',
                                                                    marginLeft: 5,
                                                                    zIndex: 2,
                                                                    fontFamily: 'NunitoSans-Regular',
                                                                    fontSize: 14,
                                                                }}
                                                                defaultValue={
                                                                    printerIdDict[item.uniqueId]
                                                                        ? printerIdDict[item.uniqueId].types
                                                                        : []
                                                                }
                                                                placeholder={'Type'}
                                                                multipleText={'%d type(s)'}
                                                                multiple={true}
                                                                customTickIcon={(press) => (
                                                                    <Ionicon
                                                                        name={'md-checkbox'}
                                                                        color={
                                                                            press
                                                                                ? Colors.fieldtBgColor
                                                                                : Colors.primaryColor
                                                                        }
                                                                        size={25}
                                                                    />
                                                                )}
                                                                onChangeItem={(items) => {
                                                                    setPrinterIdDict({
                                                                        ...printerIdDict,
                                                                        [item.uniqueId]: {
                                                                            ...printerIdDict[item.uniqueId],
                                                                            types: items,
                                                                        },
                                                                    });
                                                                }}
                                                                dropDownMaxHeight={100}
                                                                dropDownStyle={[
                                                                    {
                                                                        width: '90%',
                                                                        height: 100,
                                                                        backgroundColor: Colors.fieldtBgColor,
                                                                        borderRadius: 10,
                                                                        borderWidth: 1,
                                                                    },
                                                                    switchMerchant
                                                                        ? {
                                                                            width: '90%',
                                                                        }
                                                                        : {},
                                                                ]}
                                                                globalTextStyle={{
                                                                    fontSize: switchMerchant ? 10 : 14,
                                                                }}
                                                                zIndex={10001 - index}
                                                            /> */}
                                <View style={{ width: "90%" }}>
                                  {/* <DropDownPicker
                                      style={{
                                        backgroundColor: Colors.fieldtBgColor,
                                        // width: 210,
                                        height: 40,
                                        borderRadius: 10,
                                        borderWidth: 1,
                                        borderColor: "#E5E5E5",
                                        flexDirection: "row",
                                      }}
                                      dropDownContainerStyle={{
                                        // width: 210,
                                        backgroundColor: Colors.fieldtBgColor,
                                        borderColor: "#E5E5E5",
                                      }}
                                      labelStyle={{
                                        marginLeft: 5,
                                        flexDirection: "row",
                                      }}
                                      textStyle={{
                                        fontSize: 14,
                                        fontFamily: 'NunitoSans-Regular',

                                        marginLeft: 5,
                                        paddingVertical: 10,
                                        flexDirection: "row",
                                      }}
                                      selectedItemContainerStyle={{
                                        flexDirection: "row",
                                      }}

                                      showArrowIcon={true}
                                      ArrowDownIconComponent={({ style }) => (
                                        <Ionicon
                                          size={25}
                                          color={Colors.fieldtTxtColor}
                                          style={{ paddingHorizontal: 5, marginTop: 5 }}
                                          name="chevron-down-outline"
                                        />
                                      )}
                                      ArrowUpIconComponent={({ style }) => (
                                        <Ionicon
                                          size={25}
                                          color={Colors.fieldtTxtColor}
                                          style={{ paddingHorizontal: 5, marginTop: 5 }}
                                          name="chevron-up-outline"
                                        />
                                      )}

                                      showTickIcon={true}
                                      TickIconComponent={({ press }) => (
                                        <Ionicon
                                          style={{ paddingHorizontal: 5, marginTop: 5 }}
                                          color={
                                            press ? Colors.fieldtBgColor : Colors.primaryColor
                                          }
                                          name={'md-checkbox'}
                                          size={25}
                                        />
                                      )}
                                      placeholderStyle={{
                                        color: Colors.fieldtTxtColor,
                                        // marginTop: 15,
                                      }}
                                      dropDownDirection="BOTTOM"
                                      onSelectItem={(items) => {
                                        if (items) {
                                          setPrinterIdDict({
                                            ...printerIdDict,
                                            [item.uniqueId]: {
                                              ...printerIdDict[item.uniqueId],
                                              types: items.map(item => item.value),
                                            },
                                          });
                                        }
                                      }}
                                      value={printerIdDict[item.uniqueId]
                                        ? printerIdDict[item.uniqueId].types
                                        : []}
                                      items={PRINTER_USAGE_TYPE_DROPDOWN_LIST}
                                      multiple={true}
                                      multipleText={`${printerIdDict.length} type(s) selected`}
                                      open={openPT[index]}
                                      setOpen={(value) => {
                                        setOpenPT((prevOpenList) => {
                                          const newOpenList = [...prevOpenList];
                                          newOpenList[index] = value;
                                          return newOpenList;
                                        });
                                      }}
                                    /> */}
                                  {/* <Select
                                      style={{
                                        backgroundColor: Colors.fieldtBgColor,
                                        // width: 210,
                                        height: 40,
                                        borderRadius: 10,
                                        borderWidth: 1,
                                        borderColor: "#E5E5E5",
                                        flexDirection: "row",
                                      }}
                                      onChange={(items) => {
                                        if (items) {
                                          setPrinterIdDict({
                                            ...printerIdDict,
                                            [item.uniqueId]: {
                                              ...printerIdDict[item.uniqueId],
                                              types: items.value,
                                            },
                                          });
                                        }
                                      }}
                                      defaultValue={printerIdDict[item.uniqueId]
                                        ? printerIdDict[item.uniqueId].types
                                        : []}
                                      options={PRINTER_USAGE_TYPE_DROPDOWN_LIST}
                                      isMulti={true}
                                    /> */}
                                  <DropDownPicker
                                    style={{
                                      backgroundColor: Colors.fieldtBgColor,
                                      width: '100%',
                                      height: 40,
                                      borderRadius: 10,
                                      borderWidth: 1,
                                      borderColor: "#E5E5E5",
                                      flexDirection: "row",
                                      paddingVertical: 0,
                                      minHeight: 40,
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                    }}
                                    dropDownContainerStyle={{
                                      width: '100%',
                                      backgroundColor: Colors.fieldtBgColor,
                                      borderColor: "#E5E5E5",
                                    }}
                                    labelStyle={{
                                      marginHorizontal: 7,
                                      // paddingHorizontal: 7,
                                      flexDirection: "row",
                                      marginVertical: 8,
                                    }}
                                    textStyle={{
                                      fontSize: 13,
                                      fontFamily: 'NunitoSans-Regular',
                                      marginVertical: 8,

                                      marginHorizontal: 8,
                                      // paddingHorizontal: 8,
                                      paddingVertical: 0,
                                      flexDirection: "row",
                                    }}
                                    selectedItemContainerStyle={{
                                      flexDirection: "row",
                                    }}
                                    onOpen={() => {
                                      console.log(openPT[index]);

                                      setActiveDropdown(index)
                                    }}
                                    onClose={() => {
                                      console.log(openPT[index])

                                      setActiveDropdown(null)
                                    }}

                                    showArrowIcon={true}
                                    ArrowDownIconComponent={({ style }) => (
                                      <Ionicon
                                        size={25}
                                        color={Colors.fieldtTxtColor}
                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                        name="chevron-down-outline"
                                      />
                                    )}
                                    ArrowUpIconComponent={({ style }) => (
                                      <Ionicon
                                        size={25}
                                        color={Colors.fieldtTxtColor}
                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                        name="chevron-up-outline"
                                      />
                                    )}

                                    showTickIcon={true}
                                    TickIconComponent={({ press }) => (
                                      <Ionicon
                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                        color={
                                          press ? Colors.fieldtBgColor : Colors.primaryColor
                                        }
                                        name={'md-checkbox'}
                                        size={25}
                                      />
                                    )}
                                    items={PRINTER_USAGE_TYPE_DROPDOWN_LIST}
                                    value={
                                      printerIdDict[item.uniqueId]
                                        ? printerIdDict[item.uniqueId].types
                                        : []
                                    }
                                    placeholder={"Select..."}
                                    multipleText={printerIdDict[item.uniqueId] ? `${printerIdDict[item.uniqueId].types.length} type(s) selected` : 'Select'}
                                    placeholderStyle={{
                                      color: Colors.fieldtTxtColor,
                                      // marginTop: 15,
                                    }}
                                    onSelectItem={(items) => {
                                      setPrinterIdDict({
                                        ...printerIdDict,
                                        [item.uniqueId]: {
                                          ...printerIdDict[item.uniqueId],
                                          types: items.map(item => item.value),
                                        },
                                      });
                                    }}
                                    multiple={true}
                                    searchable={true}
                                    // onSearch={(text) => {
                                    //   setSearchingUserTagText(text);
                                    // }}
                                    open={openPT[index]}
                                    setOpen={(value) => {
                                      setOpenPT((prevOpenList) => {
                                        const newOpenList = [...prevOpenList];
                                        newOpenList[index] = value;
                                        return newOpenList;
                                      });
                                    }}
                                  />
                                </View>
                              </View>

                              <View style={{ width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? '13%' : '14%', marginHorizontal: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 2 : 4 }}>
                                <TouchableOpacity
                                  style={{
                                    height: 35,
                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 90 : 100,
                                    borderColor: '#E5E5E5',
                                    borderWidth: 1,
                                    borderRadius: 5,
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    borderColor: Colors.primaryColor,
                                    backgroundColor: Colors.primaryColor,
                                  }}
                                  onPress={() => {
                                    updateOutletPrinter(item.uniqueId);

                                    // bindPrinter(
                                    //   printerIdDict[item.uniqueId]
                                    //     ? printerIdDict[item.uniqueId].ip
                                    //     : '',

                                    bindPrinter(printerIdDict[item.uniqueId]);
                                  }}>
                                  <Text
                                    style={{
                                      color: Colors.whiteColor,
                                      fontSize: switchMerchant ? 10 : 16,
                                      fontFamily: 'NunitoSans-Bold',
                                    }}>
                                    SAVE
                                  </Text>
                                </TouchableOpacity>
                              </View>
                              {/* <View style={{ width: '14%', marginHorizontal: 4 }}>
                                  <TouchableOpacity
                                    style={{
                                      height: 35,
                                      width: 100,
                                      borderColor: '#E5E5E5',
                                      borderWidth: 1,
                                      borderRadius: 5,
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                      borderColor: Colors.tabCyan,
                                      backgroundColor: Colors.tabCyan,
                                    }}
                                    onPress={() => {
                                      unbindPrinter();
                                    }}>
                                    <Text
                                      style={{
                                        color: Colors.whiteColor,
                                        fontSize: switchMerchant ? 10 : 16,
                                        fontFamily: 'NunitoSans-Bold',
                                      }}>
                                      UNBIND
                                    </Text>
                                  </TouchableOpacity>
                                </View> */}
                              <View style={{ width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? '13%' : '14%', marginHorizontal: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 2 : 4 }}>
                                <TouchableOpacity
                                  style={{
                                    height: 35,
                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 90 : 100,
                                    borderColor: '#E5E5E5',
                                    borderWidth: 1,
                                    borderRadius: 5,
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    borderColor: Colors.tabGold,
                                    backgroundColor: Colors.tabGold,
                                  }}
                                  onPress={async () => {
                                    if (printerIdDict[item.uniqueId] && printerIdDict[item.uniqueId].deviceType === PRINTER_DEVICE_TYPE.USB) {
                                      testPrinter(printerIdDict[item.uniqueId]); // Hidden atm 23/6/2023
                                    }
                                    else {
                                      alert('Info\n\nLAN printer is not supported on the BackOffice, please use the POS app at the moment.');
                                    }

                                    // alert("Info: debug printer testing, please remove this when testing printer")

                                    // await printShiftReport({
                                    //   outletName: currOutlet.name,
                                    //   outletAddress: currOutlet.address,
                                    //   outletPhone: currOutlet.phone,
                                    //   merchantName: merchantName,
                                    //   waiterName: userName,

                                    //   outletShiftNum: allOutletShifts.filter(o => o.outletId === currOutlet.uniqueId).length,

                                    //   taxRate: currOutlet.taxRate,
                                    //   taxActive: currOutlet.taxActive,
                                    //   scRate: currOutlet.scRate,
                                    //   scActive: currOutlet.scActive,

                                    //   outletShift: {
                                    //     closeAmount: 500,
                                    //     closeDate: 1641782980163,
                                    //     createdAt: 1640944818565,
                                    //     deletedAt: null,
                                    //     merchantId: '018a0354-8012-4785-b7a5-ba4ac5313d47',
                                    //     openAmount: 20,
                                    //     openDate: 1640944817173,
                                    //     outletId: 'b422c1d9-d30b-4de7-ad49-2e601d950919',
                                    //     uniqueId: '5c325eb9-9e1c-4a9d-8315-6dfa19e6f77f',
                                    //     updatedAt: 1641782981662,
                                    //   },
                                    // });
                                  }}>
                                  <Text
                                    style={{
                                      color: Colors.whiteColor,
                                      fontSize: switchMerchant ? 10 : 16,
                                      fontFamily: 'NunitoSans-Bold',
                                    }}>
                                    {isLoading ? 'Loading...' : 'TEST'}
                                  </Text>
                                </TouchableOpacity>
                              </View>
                              <View style={{ width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? '14.5%' : '14%', marginHorizontal: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 2 : 4 }}>
                                <TouchableOpacity
                                  style={{
                                    height: 35,
                                    width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 90 : 100,
                                    borderColor: '#E5E5E5',
                                    borderWidth: 1,
                                    borderRadius: 5,
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    borderColor: Colors.tabRed,
                                    backgroundColor: Colors.tabRed,
                                  }}
                                  onPress={() => {
                                    // alert(
                                    //   'Info: Are you sure you want to remove this printer?',
                                    //   [
                                    //     {
                                    //       text: 'YES',
                                    //       onPress: () => {
                                    //         deleteOutletPrinter(item.uniqueId);
                                    //       },
                                    //     },
                                    //     {
                                    //       text: 'NO',
                                    //       onPress: () => { },
                                    //     },
                                    //   ],
                                    //   { cancelable: true },
                                    // );
                                    if (window.confirm("Info: Are you sure you want to remove this printer?")) {
                                      deleteOutletPrinter(item.uniqueId);
                                    }
                                  }}>
                                  <Text
                                    style={{
                                      color: Colors.whiteColor,
                                      fontSize: switchMerchant ? 10 : 16,
                                      fontFamily: 'NunitoSans-Bold',
                                    }}>
                                    REMOVE
                                  </Text>
                                </TouchableOpacity>
                              </View>
                            </View>

                            {
                              outletCategories.length > 0
                                ?
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    height: switchMerchant
                                      ? windowHeight * 0.1
                                      : windowHeight * 0.06,
                                    alignItems: 'center',
                                    borderBottomColor: Colors.fieldtBgColor,
                                    width: switchMerchant
                                      ? windowWidth * 0.8
                                      : '100%',
                                    alignSelf: 'center',

                                    marginTop: 20,
                                    // zIndex: 10500101,
                                    zIndex: openBKD[index] || openBOS[index] || openBR[index] || openBBD[index] ? 999 : undefined,

                                    marginBottom: 10,
                                  }}>
                                  <View style={{
                                    width: '24%',

                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'center',

                                    // backgroundColor: 'red',
                                  }}>
                                    <View style={{
                                      width: '100%',
                                    }}>
                                      <Text
                                        style={{
                                          color: 'black',
                                          fontFamily: 'NunitoSans-SemiBold',
                                          fontSize: switchMerchant ? 10 : 14,
                                          // paddingRight: switchMerchant ? 8 : windowHeight === 800 && windowWidth === 1280 ? 0 : 5,
                                          marginHorizontal: switchMerchant ? 0 : 4,

                                          paddingLeft: 2,
                                        }}>
                                        {'Block KD:'}
                                      </Text>
                                    </View>

                                    <View style={{
                                      width: '100%',

                                      paddingLeft: 5,
                                      marginTop: 5,
                                    }}>
                                      <DropDownPicker
                                        style={{
                                          backgroundColor: Colors.fieldtBgColor,
                                          width: '90%',
                                          height: 40,
                                          borderRadius: 10,
                                          borderWidth: 1,
                                          borderColor: "#E5E5E5",
                                          flexDirection: "row",
                                        }}
                                        dropDownContainerStyle={{
                                          width: '90%',
                                          backgroundColor: Colors.fieldtBgColor,
                                          borderColor: "#E5E5E5",
                                        }}
                                        labelStyle={{
                                          marginLeft: 5,
                                          flexDirection: "row",
                                        }}
                                        textStyle={{
                                          fontSize: 14,
                                          fontFamily: 'NunitoSans-Regular',

                                          marginLeft: 5,
                                          paddingVertical: 10,
                                          flexDirection: "row",
                                        }}
                                        selectedItemContainerStyle={{
                                          flexDirection: "row",
                                        }}
                                        onOpen={() => setActiveDropdown(index)}
                                        onClose={() => setActiveDropdown(null)}

                                        showArrowIcon={true}
                                        ArrowDownIconComponent={({ style }) => (
                                          <Ionicon
                                            size={25}
                                            color={Colors.fieldtTxtColor}
                                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                                            name="chevron-down-outline"
                                          />
                                        )}
                                        ArrowUpIconComponent={({ style }) => (
                                          <Ionicon
                                            size={25}
                                            color={Colors.fieldtTxtColor}
                                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                                            name="chevron-up-outline"
                                          />
                                        )}

                                        showTickIcon={true}
                                        TickIconComponent={({ press }) => (
                                          <Ionicon
                                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                                            color={
                                              press ? Colors.fieldtBgColor : Colors.primaryColor
                                            }
                                            name={'md-checkbox'}
                                            size={25}
                                          />
                                        )}
                                        items={outletCategoryDropdownList}
                                        value={
                                          printerIdDict[item.uniqueId]
                                            ? printerIdDict[item.uniqueId].blockKDCategoryIdList
                                            : []
                                        }
                                        placeholder={"Select..."}
                                        // multipleText={`${printerIdDict[item.uniqueId].blockKDCategoryIdList.length} category(s) selected`}
                                        placeholderStyle={{
                                          color: Colors.fieldtTxtColor,
                                          // marginTop: 15,
                                        }}
                                        onSelectItem={(items) => {
                                          setPrinterIdDict({
                                            ...printerIdDict,
                                            [item.uniqueId]: {
                                              ...printerIdDict[item.uniqueId],
                                              blockKDCategoryIdList: items.map(item => item.value),
                                            },
                                          });
                                        }}
                                        multiple={true}
                                        // searchable={true}
                                        // onSearch={(text) => {
                                        //   setSearchingUserTagText(text);
                                        // }}
                                        open={openBKD[index]}
                                        setOpen={(value) => {
                                          setOpenBKD((prevOpenList) => {
                                            const newOpenList = [...prevOpenList];
                                            newOpenList[index] = value;
                                            return newOpenList;
                                          });
                                        }}
                                      />
                                    </View>
                                  </View>

                                  {/* /////////////////////////////////////////// */}

                                  <View style={{
                                    width: '24%',

                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'center',

                                    // backgroundColor: 'red',
                                  }}>
                                    <View style={{
                                      width: '100%',
                                    }}>
                                      <Text
                                        style={{
                                          color: 'black',
                                          fontFamily: 'NunitoSans-SemiBold',
                                          fontSize: switchMerchant ? 10 : 14,
                                          // paddingRight: switchMerchant ? 8 : windowHeight === 800 && windowWidth === 1280 ? 0 : 5,
                                          marginHorizontal: switchMerchant ? 0 : 4,

                                          paddingLeft: 2,
                                        }}>
                                        {'Block OS:'}
                                      </Text>
                                    </View>

                                    <View style={{
                                      width: '100%',

                                      paddingLeft: 5,
                                      marginTop: 5,
                                    }}>
                                      <DropDownPicker
                                        style={{
                                          backgroundColor: Colors.fieldtBgColor,
                                          width: '90%',
                                          height: 40,
                                          borderRadius: 10,
                                          borderWidth: 1,
                                          borderColor: "#E5E5E5",
                                          flexDirection: "row",
                                        }}
                                        dropDownContainerStyle={{
                                          width: '90%',
                                          backgroundColor: Colors.fieldtBgColor,
                                          borderColor: "#E5E5E5",
                                        }}
                                        labelStyle={{
                                          marginLeft: 5,
                                          flexDirection: "row",
                                        }}
                                        textStyle={{
                                          fontSize: 14,
                                          fontFamily: 'NunitoSans-Regular',

                                          marginLeft: 5,
                                          paddingVertical: 10,
                                          flexDirection: "row",
                                        }}
                                        selectedItemContainerStyle={{
                                          flexDirection: "row",
                                        }}
                                        onOpen={() => setActiveDropdown(index)}
                                        onClose={() => setActiveDropdown(null)}

                                        showArrowIcon={true}
                                        ArrowDownIconComponent={({ style }) => (
                                          <Ionicon
                                            size={25}
                                            color={Colors.fieldtTxtColor}
                                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                                            name="chevron-down-outline"
                                          />
                                        )}
                                        ArrowUpIconComponent={({ style }) => (
                                          <Ionicon
                                            size={25}
                                            color={Colors.fieldtTxtColor}
                                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                                            name="chevron-up-outline"
                                          />
                                        )}

                                        showTickIcon={true}
                                        TickIconComponent={({ press }) => (
                                          <Ionicon
                                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                                            color={
                                              press ? Colors.fieldtBgColor : Colors.primaryColor
                                            }
                                            name={'md-checkbox'}
                                            size={25}
                                          />
                                        )}
                                        items={outletCategoryDropdownList}
                                        value={
                                          printerIdDict[item.uniqueId]
                                            ? printerIdDict[item.uniqueId].blockOSCategoryIdList
                                            : []
                                        }
                                        placeholder={"Select..."}
                                        // multipleText={`${printerIdDict[item.uniqueId].blockOSCategoryIdList.length} category(s) selected`}
                                        placeholderStyle={{
                                          color: Colors.fieldtTxtColor,
                                          // marginTop: 15,
                                        }}
                                        onSelectItem={(items) => {
                                          setPrinterIdDict({
                                            ...printerIdDict,
                                            [item.uniqueId]: {
                                              ...printerIdDict[item.uniqueId],
                                              blockOSCategoryIdList: items.map(item => item.value),
                                            },
                                          });
                                        }}
                                        multiple={true}
                                        // searchable={true}
                                        // onSearch={(text) => {
                                        //   setSearchingUserTagText(text);
                                        // }}
                                        open={openBOS[index]}
                                        setOpen={(value) => {
                                          setOpenBOS((prevOpenList) => {
                                            const newOpenList = [...prevOpenList];
                                            newOpenList[index] = value;
                                            return newOpenList;
                                          });
                                        }}
                                      />
                                    </View>
                                  </View>

                                  {/* /////////////////////////////////////////// */}

                                  <View style={{
                                    width: '24%',

                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'center',

                                    // backgroundColor: 'red',
                                  }}>
                                    <View style={{
                                      width: '100%',
                                    }}>
                                      <Text
                                        style={{
                                          color: 'black',
                                          fontFamily: 'NunitoSans-SemiBold',
                                          fontSize: switchMerchant ? 10 : 14,
                                          // paddingRight: switchMerchant ? 8 : windowHeight === 800 && windowWidth === 1280 ? 0 : 5,
                                          marginHorizontal: switchMerchant ? 0 : 4,

                                          paddingLeft: 2,
                                        }}>
                                        {'Block Receipt:'}
                                      </Text>
                                    </View>

                                    <View style={{
                                      width: '100%',

                                      paddingLeft: 5,
                                      marginTop: 5,
                                    }}>
                                      <DropDownPicker
                                        style={{
                                          backgroundColor: Colors.fieldtBgColor,
                                          width: '90%',
                                          height: 40,
                                          borderRadius: 10,
                                          borderWidth: 1,
                                          borderColor: "#E5E5E5",
                                          flexDirection: "row",
                                        }}
                                        dropDownContainerStyle={{
                                          width: '90%',
                                          backgroundColor: Colors.fieldtBgColor,
                                          borderColor: "#E5E5E5",
                                        }}
                                        labelStyle={{
                                          marginLeft: 5,
                                          flexDirection: "row",
                                        }}
                                        textStyle={{
                                          fontSize: 14,
                                          fontFamily: 'NunitoSans-Regular',

                                          marginLeft: 5,
                                          paddingVertical: 10,
                                          flexDirection: "row",
                                        }}
                                        selectedItemContainerStyle={{
                                          flexDirection: "row",
                                        }}
                                        onOpen={() => setActiveDropdown(index)}
                                        onClose={() => setActiveDropdown(null)}

                                        showArrowIcon={true}
                                        ArrowDownIconComponent={({ style }) => (
                                          <Ionicon
                                            size={25}
                                            color={Colors.fieldtTxtColor}
                                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                                            name="chevron-down-outline"
                                          />
                                        )}
                                        ArrowUpIconComponent={({ style }) => (
                                          <Ionicon
                                            size={25}
                                            color={Colors.fieldtTxtColor}
                                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                                            name="chevron-up-outline"
                                          />
                                        )}

                                        showTickIcon={true}
                                        TickIconComponent={({ press }) => (
                                          <Ionicon
                                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                                            color={
                                              press ? Colors.fieldtBgColor : Colors.primaryColor
                                            }
                                            name={'md-checkbox'}
                                            size={25}
                                          />
                                        )}
                                        items={outletCategoryDropdownList}
                                        value={
                                          printerIdDict[item.uniqueId]
                                            ? printerIdDict[item.uniqueId].blockReceiptCategoryIdList
                                            : []
                                        }
                                        placeholder={"Select..."}
                                        // multipleText={`${printerIdDict[item.uniqueId].blockReceiptCategoryIdList.length} category(s) selected`}
                                        placeholderStyle={{
                                          color: Colors.fieldtTxtColor,
                                          // marginTop: 15,
                                        }}
                                        onSelectItem={(items) => {
                                          setPrinterIdDict({
                                            ...printerIdDict,
                                            [item.uniqueId]: {
                                              ...printerIdDict[item.uniqueId],
                                              blockReceiptCategoryIdList: items.map(item => item.value),
                                            },
                                          });
                                        }}
                                        multiple={true}
                                        // searchable={true}
                                        // onSearch={(text) => {
                                        //   setSearchingUserTagText(text);
                                        // }}
                                        open={openBR[index]}
                                        setOpen={(value) => {

                                          setOpenBR((prevOpenList) => {
                                            const newOpenList = [...prevOpenList];
                                            newOpenList[index] = value;
                                            return newOpenList;
                                          });
                                        }}
                                      />
                                    </View>
                                  </View>

                                  {/* /////////////////////////////////////////// */}

                                  <View style={{
                                    width: '24%',

                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'center',

                                    // backgroundColor: 'red',
                                  }}>
                                    <View style={{
                                      width: '100%',
                                    }}>
                                      <Text
                                        style={{
                                          color: 'black',
                                          fontFamily: 'NunitoSans-SemiBold',
                                          fontSize: switchMerchant ? 10 : 14,
                                          // paddingRight: switchMerchant ? 8 : windowHeight === 800 && windowWidth === 1280 ? 0 : 5,
                                          marginHorizontal: switchMerchant ? 0 : 4,

                                          paddingLeft: 2,
                                        }}>
                                        {'Block Beer Docket:'}
                                      </Text>
                                    </View>

                                    <View style={{
                                      width: '100%',

                                      paddingLeft: 5,
                                      marginTop: 5,
                                    }}>
                                      <DropDownPicker
                                        style={{
                                          backgroundColor: Colors.fieldtBgColor,
                                          width: '90%',
                                          height: 40,
                                          borderRadius: 10,
                                          borderWidth: 1,
                                          borderColor: "#E5E5E5",
                                          flexDirection: "row",
                                        }}
                                        dropDownContainerStyle={{
                                          width: '90%',
                                          backgroundColor: Colors.fieldtBgColor,
                                          borderColor: "#E5E5E5",
                                        }}
                                        labelStyle={{
                                          marginLeft: 5,
                                          flexDirection: "row",
                                        }}
                                        textStyle={{
                                          fontSize: 14,
                                          fontFamily: 'NunitoSans-Regular',

                                          marginLeft: 5,
                                          paddingVertical: 10,
                                          flexDirection: "row",
                                        }}
                                        selectedItemContainerStyle={{
                                          flexDirection: "row",
                                        }}
                                        onOpen={() => setActiveDropdown(index)}
                                        onClose={() => setActiveDropdown(null)}

                                        showArrowIcon={true}
                                        ArrowDownIconComponent={({ style }) => (
                                          <Ionicon
                                            size={25}
                                            color={Colors.fieldtTxtColor}
                                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                                            name="chevron-down-outline"
                                          />
                                        )}
                                        ArrowUpIconComponent={({ style }) => (
                                          <Ionicon
                                            size={25}
                                            color={Colors.fieldtTxtColor}
                                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                                            name="chevron-up-outline"
                                          />
                                        )}

                                        showTickIcon={true}
                                        TickIconComponent={({ press }) => (
                                          <Ionicon
                                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                                            color={
                                              press ? Colors.fieldtBgColor : Colors.primaryColor
                                            }
                                            name={'md-checkbox'}
                                            size={25}
                                          />
                                        )}
                                        items={outletCategoryDropdownList}
                                        value={
                                          printerIdDict[item.uniqueId]
                                            ? printerIdDict[item.uniqueId].blockBDCategoryIdList
                                            : []
                                        }
                                        placeholder={"Select..."}
                                        // multipleText={`${printerIdDict[item.uniqueId].blockBDCategoryIdList.length} category(s) selected`}
                                        placeholderStyle={{
                                          color: Colors.fieldtTxtColor,
                                          // marginTop: 15,
                                        }}
                                        onSelectItem={(items) => {
                                          setPrinterIdDict({
                                            ...printerIdDict,
                                            [item.uniqueId]: {
                                              ...printerIdDict[item.uniqueId],
                                              blockBDCategoryIdList: items.map(item => item.value),
                                            },
                                          });
                                        }}
                                        multiple={true}
                                        // searchable={true}
                                        // onSearch={(text) => {
                                        //   setSearchingUserTagText(text);
                                        // }}
                                        open={openBBD[index]}
                                        setOpen={(value) => {
                                          setOpenBBD((prevOpenList) => {
                                            const newOpenList = [...prevOpenList];
                                            newOpenList[index] = value;
                                            return newOpenList;
                                          });
                                        }}
                                      />
                                    </View>
                                  </View>
                                </View>
                                :
                                <></>
                            }

                            <View
                              style={{
                                flexDirection: 'row',
                                height: switchMerchant
                                  ? windowHeight * 0.1
                                  : windowHeight * 0.06,
                                alignItems: 'center',
                                borderBottomColor: Colors.fieldtBgColor,
                                // backgroundColor: 'red',
                                width: switchMerchant
                                  ? windowWidth * 0.8
                                  : '100%',
                                alignSelf: 'center',

                                marginTop: 20,

                                zIndex: openPW[index] || openKDV[index] || openP[index] ? 998 : undefined
                                // zIndex: activeDropdown === 'first' ? 10500101 : 10500100, // Dynamic zIndex
                              }}>
                              <View style={{
                                width: '30%',

                                display: 'flex',
                                flexDirection: 'row',
                                alignItems: 'center',

                                // backgroundColor: 'red',
                              }}>
                                <View style={{
                                  width: '50%',
                                }}>
                                  <Text
                                    style={{
                                      color: 'black',
                                      fontFamily: 'NunitoSans-SemiBold',
                                      fontSize: switchMerchant ? 10 : 14,
                                      // paddingRight: switchMerchant ? 8 : windowHeight === 800 && windowWidth === 1280 ? 0 : 5,
                                      marginHorizontal: switchMerchant ? 0 : 4,

                                      paddingLeft: 2,
                                    }}>
                                    {'Paper Width:'}
                                  </Text>
                                </View>

                                <View style={{
                                  width: '50%',
                                }}>
                                  <DropDownPicker
                                    style={{
                                      backgroundColor: Colors.fieldtBgColor,
                                      width: '90%',
                                      height: 40,
                                      borderRadius: 10,
                                      borderWidth: 1,
                                      borderColor: "#E5E5E5",
                                      flexDirection: "row",
                                    }}
                                    dropDownContainerStyle={{
                                      width: '90%',
                                      backgroundColor: Colors.fieldtBgColor,
                                      borderColor: "#E5E5E5",
                                    }}
                                    labelStyle={{
                                      marginLeft: 5,
                                      flexDirection: "row",
                                    }}
                                    textStyle={{
                                      fontSize: 14,
                                      fontFamily: 'NunitoSans-Regular',

                                      marginLeft: 5,
                                      paddingVertical: 10,
                                      flexDirection: "row",
                                    }}
                                    selectedItemContainerStyle={{
                                      flexDirection: "row",
                                    }}
                                    onOpen={() => setActiveDropdown(index)}
                                    onClose={() => setActiveDropdown(null)}

                                    showArrowIcon={true}
                                    ArrowDownIconComponent={({ style }) => (
                                      <Ionicon
                                        size={25}
                                        color={Colors.fieldtTxtColor}
                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                        name="chevron-down-outline"
                                      />
                                    )}
                                    ArrowUpIconComponent={({ style }) => (
                                      <Ionicon
                                        size={25}
                                        color={Colors.fieldtTxtColor}
                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                        name="chevron-up-outline"
                                      />
                                    )}

                                    showTickIcon={true}
                                    TickIconComponent={({ press }) => (
                                      <Ionicon
                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                        color={
                                          press ? Colors.fieldtBgColor : Colors.primaryColor
                                        }
                                        name={'md-checkbox'}
                                        size={25}
                                      />
                                    )}
                                    items={PRINTER_PAPER_WIDTH_DROPDOWN_LIST}
                                    value={
                                      (printerIdDict[item.uniqueId] && printerIdDict[item.uniqueId].paperWidth)
                                        ? printerIdDict[item.uniqueId].paperWidth
                                        : PRINTER_PAPER_WIDTH._80MM
                                    }
                                    placeholder={"Select..."}
                                    // multipleText={`${printerIdDict[item.uniqueId].blockOSCategoryIdList.length} category(s) selected`}
                                    placeholderStyle={{
                                      color: Colors.fieldtTxtColor,
                                      // marginTop: 15,
                                    }}
                                    onSelectItem={(option) => {
                                      setPrinterIdDict({
                                        ...printerIdDict,
                                        [item.uniqueId]: {
                                          ...printerIdDict[item.uniqueId],
                                          paperWidth: option.value,
                                        },
                                      });
                                    }}
                                    // multiple={true}
                                    // searchable={true}
                                    // onSearch={(text) => {
                                    //   setSearchingUserTagText(text);
                                    // }}
                                    open={openPW[index]}
                                    setOpen={(value) => {
                                      setOpenPW((prevOpenList) => {
                                        const newOpenList = [...prevOpenList];
                                        newOpenList[index] = value;
                                        return newOpenList;
                                      });
                                    }}
                                  />
                                </View>
                              </View>

                              <View style={{
                                width: '30%',

                                display: 'flex',
                                flexDirection: 'row',
                                alignItems: 'center',

                                // backgroundColor: 'red',
                              }}>
                                <View style={{
                                  width: '50%',
                                }}>
                                  <Text
                                    style={{
                                      color: 'black',
                                      fontFamily: 'NunitoSans-SemiBold',
                                      fontSize: switchMerchant ? 10 : 14,
                                      // paddingRight: switchMerchant ? 8 : windowHeight === 800 && windowWidth === 1280 ? 0 : 5,
                                      marginHorizontal: switchMerchant ? 0 : 4,

                                      paddingLeft: 2,
                                    }}>
                                    {'KD Variation:'}
                                  </Text>
                                </View>

                                <View style={{
                                  width: '50%',
                                }}>
                                  <DropDownPicker
                                    style={{
                                      backgroundColor: Colors.fieldtBgColor,
                                      width: '90%',
                                      height: 40,
                                      borderRadius: 10,
                                      borderWidth: 1,
                                      borderColor: "#E5E5E5",
                                      flexDirection: "row",
                                    }}
                                    dropDownContainerStyle={{
                                      width: '90%',
                                      backgroundColor: Colors.fieldtBgColor,
                                      borderColor: "#E5E5E5",
                                    }}
                                    labelStyle={{
                                      marginLeft: 5,
                                      flexDirection: "row",
                                    }}
                                    textStyle={{
                                      fontSize: 14,
                                      fontFamily: 'NunitoSans-Regular',

                                      marginLeft: 5,
                                      paddingVertical: 10,
                                      flexDirection: "row",
                                    }}
                                    selectedItemContainerStyle={{
                                      flexDirection: "row",
                                    }}
                                    onOpen={() => setActiveDropdown(index)}
                                    onClose={() => setActiveDropdown(null)}

                                    showArrowIcon={true}
                                    ArrowDownIconComponent={({ style }) => (
                                      <Ionicon
                                        size={25}
                                        color={Colors.fieldtTxtColor}
                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                        name="chevron-down-outline"
                                      />
                                    )}
                                    ArrowUpIconComponent={({ style }) => (
                                      <Ionicon
                                        size={25}
                                        color={Colors.fieldtTxtColor}
                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                        name="chevron-up-outline"
                                      />
                                    )}

                                    showTickIcon={true}
                                    TickIconComponent={({ press }) => (
                                      <Ionicon
                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                        color={
                                          press ? Colors.fieldtBgColor : Colors.primaryColor
                                        }
                                        name={'md-checkbox'}
                                        size={25}
                                      />
                                    )}
                                    items={KD_PRINT_VARIATION_DROPDOWN_LIST}
                                    value={
                                      (printerIdDict[item.uniqueId] && printerIdDict[item.uniqueId].kdVariation)
                                        ? printerIdDict[item.uniqueId].kdVariation
                                        : KD_PRINT_VARIATION.SUMMARY
                                    }
                                    placeholder={"Select..."}
                                    // multipleText={`${printerIdDict[item.uniqueId].blockOSCategoryIdList.length} category(s) selected`}
                                    placeholderStyle={{
                                      color: Colors.fieldtTxtColor,
                                      // marginTop: 15,
                                    }}
                                    onSelectItem={(option) => {
                                      setPrinterIdDict({
                                        ...printerIdDict,
                                        [item.uniqueId]: {
                                          ...printerIdDict[item.uniqueId],
                                          kdVariation: option.value,
                                        },
                                      });
                                    }}
                                    // multiple={true}
                                    // searchable={true}
                                    // onSearch={(text) => {
                                    //   setSearchingUserTagText(text);
                                    // }}
                                    open={openKDV[index]}
                                    setOpen={(value) => {
                                      setOpenKDV((prevOpenList) => {
                                        const newOpenList = [...prevOpenList];
                                        newOpenList[index] = value;
                                        return newOpenList;
                                      });
                                    }}
                                  />
                                </View>
                              </View>

                              <View style={{
                                width: '30%',

                                display: 'flex',
                                flexDirection: 'row',
                                alignItems: 'center',

                                // backgroundColor: 'red',
                              }}>
                                <View style={{
                                  width: '50%',
                                }}>
                                  <Text
                                    style={{
                                      color: 'black',
                                      fontFamily: 'NunitoSans-SemiBold',
                                      fontSize: switchMerchant ? 10 : 14,
                                      // paddingRight: switchMerchant ? 8 : windowHeight === 800 && windowWidth === 1280 ? 0 : 5,
                                      marginHorizontal: switchMerchant ? 0 : 4,

                                      paddingLeft: 2,
                                    }}>
                                    {'Priority:'}
                                  </Text>
                                </View>

                                <View style={{
                                  width: '50%',
                                }}>
                                  <DropDownPicker
                                    style={{
                                      backgroundColor: Colors.fieldtBgColor,
                                      width: '90%',
                                      height: 40,
                                      borderRadius: 10,
                                      borderWidth: 1,
                                      borderColor: "#E5E5E5",
                                      flexDirection: "row",
                                    }}
                                    dropDownContainerStyle={{
                                      width: '90%',
                                      backgroundColor: Colors.fieldtBgColor,
                                      borderColor: "#E5E5E5",
                                    }}
                                    labelStyle={{
                                      marginLeft: 5,
                                      flexDirection: "row",
                                    }}
                                    textStyle={{
                                      fontSize: 14,
                                      fontFamily: 'NunitoSans-Regular',

                                      marginLeft: 5,
                                      paddingVertical: 10,
                                      flexDirection: "row",
                                    }}
                                    selectedItemContainerStyle={{
                                      flexDirection: "row",
                                    }}
                                    onOpen={() => setActiveDropdown(index)}
                                    onClose={() => setActiveDropdown(null)}

                                    showArrowIcon={true}
                                    ArrowDownIconComponent={({ style }) => (
                                      <Ionicon
                                        size={25}
                                        color={Colors.fieldtTxtColor}
                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                        name="chevron-down-outline"
                                      />
                                    )}
                                    ArrowUpIconComponent={({ style }) => (
                                      <Ionicon
                                        size={25}
                                        color={Colors.fieldtTxtColor}
                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                        name="chevron-up-outline"
                                      />
                                    )}

                                    showTickIcon={true}
                                    TickIconComponent={({ press }) => (
                                      <Ionicon
                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                        color={
                                          press ? Colors.fieldtBgColor : Colors.primaryColor
                                        }
                                        name={'md-checkbox'}
                                        size={25}
                                      />
                                    )}
                                    items={PRINTER_USER_PRIORITY_DROPDOWN_LIST}
                                    value={
                                      (printerIdDict[item.uniqueId] && printerIdDict[item.uniqueId].userPriority)
                                        ? printerIdDict[item.uniqueId].userPriority
                                        : PRINTER_USER_PRIORITY.NORMAL
                                    }
                                    placeholder={"Select..."}
                                    // multipleText={`${printerIdDict[item.uniqueId].blockOSCategoryIdList.length} category(s) selected`}
                                    placeholderStyle={{
                                      color: Colors.fieldtTxtColor,
                                      // marginTop: 15,
                                    }}
                                    onSelectItem={(option) => {
                                      setPrinterIdDict({
                                        ...printerIdDict,
                                        [item.uniqueId]: {
                                          ...printerIdDict[item.uniqueId],
                                          userPriority: option.value,
                                        },
                                      });
                                    }}
                                    // multiple={true}
                                    // searchable={true}
                                    // onSearch={(text) => {
                                    //   setSearchingUserTagText(text);
                                    // }}
                                    open={openP[index]}
                                    setOpen={(value) => {
                                      setOpenP((prevOpenList) => {
                                        const newOpenList = [...prevOpenList];
                                        newOpenList[index] = value;
                                        return newOpenList;
                                      });
                                    }}
                                  />
                                </View>
                              </View>
                            </View>

                            {/* //////////////////////////////////// */}

                            {/* 2022-11-09 - Group list section */}

                            <View
                              style={{
                                flexDirection: 'row',
                                // height: switchMerchant
                                //   ? windowHeight * 0.1
                                //   : windowHeight * 0.06,
                                alignItems: 'center',
                                borderBottomColor: Colors.fieldtBgColor,
                                width: switchMerchant
                                  ? windowWidth * 0.8
                                  : '100%',
                                alignSelf: 'center',

                                marginTop: 20,

                                // zIndex: -10503,
                                zIndex: openGL[index] === true ? 997 : undefined,
                              }}>
                              <View style={{
                                width: '80%',

                                display: 'flex',
                                flexDirection: 'row',
                                alignItems: 'flex-start',

                                justifyContent: 'flex-start',

                                // backgroundColor: 'red',
                              }}>
                                <View style={{
                                  width: '35%',
                                  height: '100%',

                                  flexDirection: 'row',
                                  alignItems: 'flex-start',
                                  justifyContent: 'flex-start',

                                  // backgroundColor: 'red',

                                  paddingTop: switchMerchant ? 8.75 : 10,
                                }}>
                                  <Text
                                    style={{
                                      color: 'black',
                                      fontFamily: 'NunitoSans-SemiBold',
                                      fontSize: switchMerchant ? 8 : 14,
                                      // paddingRight: switchMerchant ? 8 : windowHeight === 800 && windowWidth === 1280 ? 0 : 5,
                                      marginHorizontal: switchMerchant ? 0 : 4,

                                      paddingLeft: 2,
                                    }}>
                                    {'Group List\n(For KD Category Summary):'}
                                  </Text>

                                  <TouchableOpacity
                                    onPress={() => {
                                      var isValidToAdd = false;

                                      var groupList = [];
                                      if (printerIdDict[item.uniqueId].groupList) {
                                        groupList = printerIdDict[item.uniqueId].groupList;
                                      }

                                      if (groupList) {
                                        if (groupList.length === 0) {
                                          isValidToAdd = true;
                                        }
                                        else {
                                          // var lastIndex = groupList.length - 1;

                                          var isEmptyGroupFound = groupList.find(group => group.length === 0);

                                          // if (groupList[lastIndex] && groupList[lastIndex].length === 0) {
                                          if (isEmptyGroupFound) {
                                            // need choose first, then only can further add

                                            alert('Info: There is an empty group existed, please assign at least one category first.');
                                          }
                                          else {
                                            isValidToAdd = true;
                                          }
                                        }
                                      }

                                      if (isValidToAdd) {
                                        setPrinterIdDict({
                                          ...printerIdDict,
                                          [item.uniqueId]: {
                                            ...printerIdDict[item.uniqueId],
                                            groupList: [
                                              ...printerIdDict[item.uniqueId].groupList,
                                              [], // add new one
                                            ],
                                          }
                                        });
                                      }
                                    }}
                                    style={{
                                      // backgroundColor: Colors.whiteColor,
                                      alignItems: 'center',
                                      flexDirection: 'row',

                                      marginLeft: 5,

                                      width: switchMerchant ? 17 : 20,
                                    }}>
                                    <Icon
                                      name="plus-circle"
                                      size={switchMerchant ? 17 : 20}
                                      color={Colors.primaryColor}
                                    />
                                  </TouchableOpacity>
                                </View>

                                <View style={{
                                  width: '40%',
                                  height: '100%',

                                  // backgroundColor: 'blue',
                                }}>
                                  {
                                    printerIdDict[item.uniqueId] && printerIdDict[item.uniqueId].groupList &&
                                    printerIdDict[item.uniqueId].groupList.map((group, groupIndex) => {
                                      // ([1, 2]).map((group, groupIndex) => {
                                      { console.log('inside loop') }

                                      return (
                                        <View style={{
                                          flexDirection: 'row',
                                          alignItems: 'center',
                                          justifyContent: 'flex-start',

                                          // backgroundColor: 'green',

                                          width: '100%',
                                          // height: '100%',
                                          marginBottom: windowHeight * 0.05,

                                          zIndex: -10502 - groupIndex,
                                        }}>
                                          {
                                            (
                                              ((printerIdDict[item.uniqueId] && printerIdDict[item.uniqueId].groupList &&
                                                printerIdDict[item.uniqueId].groupList[groupIndex])
                                                ? printerIdDict[item.uniqueId].groupList[groupIndex]
                                                : [])
                                                .every((val) =>
                                                  ((printerIdCategoryDropdownListDict[item.uniqueId + groupIndex]) ? printerIdCategoryDropdownListDict[item.uniqueId + groupIndex] : [])
                                                    .map((option) => option.value)
                                                    .includes(val)
                                                )
                                              ||
                                              ((printerIdDict[item.uniqueId] && printerIdDict[item.uniqueId].groupList &&
                                                printerIdDict[item.uniqueId].groupList[groupIndex])
                                                ? printerIdDict[item.uniqueId].groupList[groupIndex]
                                                : [])
                                                .length === 0
                                            )
                                              ?
                                              <DropDownPicker
                                                style={{
                                                  backgroundColor: Colors.fieldtBgColor,
                                                  width: windowWidth * 0.2,
                                                  height: 40,
                                                  borderRadius: 10,
                                                  borderWidth: 1,
                                                  borderColor: "#E5E5E5",
                                                  flexDirection: "row",
                                                }}
                                                dropDownContainerStyle={{
                                                  width: windowWidth * 0.2,
                                                  backgroundColor: Colors.fieldtBgColor,
                                                  borderColor: "#E5E5E5",
                                                }}
                                                labelStyle={{
                                                  marginLeft: 5,
                                                  flexDirection: "row",
                                                }}
                                                textStyle={{
                                                  fontSize: 14,
                                                  fontFamily: 'NunitoSans-Regular',

                                                  marginLeft: 5,
                                                  paddingVertical: 10,
                                                  flexDirection: "row",
                                                }}
                                                selectedItemContainerStyle={{
                                                  flexDirection: "row",
                                                }}
                                                onOpen={() => setActiveDropdown(index)}
                                                onClose={() => setActiveDropdown(null)}

                                                showArrowIcon={true}
                                                ArrowDownIconComponent={({ style }) => (
                                                  <Ionicon
                                                    size={25}
                                                    color={Colors.fieldtTxtColor}
                                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                                    name="chevron-down-outline"
                                                  />
                                                )}
                                                ArrowUpIconComponent={({ style }) => (
                                                  <Ionicon
                                                    size={25}
                                                    color={Colors.fieldtTxtColor}
                                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                                    name="chevron-up-outline"
                                                  />
                                                )}

                                                showTickIcon={true}
                                                TickIconComponent={({ press }) => (
                                                  <Ionicon
                                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                                    color={
                                                      press ? Colors.fieldtBgColor : Colors.primaryColor
                                                    }
                                                    name={'md-checkbox'}
                                                    size={25}
                                                  />
                                                )}
                                                items={(printerIdCategoryDropdownListDict[item.uniqueId + groupIndex]) ? printerIdCategoryDropdownListDict[item.uniqueId + groupIndex] : []}
                                                value={
                                                  (printerIdDict[item.uniqueId] && printerIdDict[item.uniqueId].groupList &&
                                                    printerIdDict[item.uniqueId].groupList[groupIndex])
                                                    ? printerIdDict[item.uniqueId].groupList[groupIndex]
                                                    : []
                                                }
                                                placeholder={"Select..."}
                                                multipleText={`${printerIdDict[item.uniqueId].groupList[groupIndex].length} category(s) selected`}
                                                placeholderStyle={{
                                                  color: Colors.fieldtTxtColor,
                                                  // marginTop: 15,
                                                }}
                                                onSelectItem={(options) => {
                                                  var groupListNew = printerIdDict[item.uniqueId].groupList.map((groupOld, groupIndexOld) => {
                                                    if (groupIndexOld === groupIndex) {
                                                      return options;
                                                    }
                                                    else {
                                                      return groupOld;
                                                    }
                                                  });

                                                  setPrinterIdDict({
                                                    ...printerIdDict,
                                                    [item.uniqueId]: {
                                                      ...printerIdDict[item.uniqueId],
                                                      groupList: groupListNew,
                                                    },
                                                  });
                                                }}
                                                multiple={true}
                                                // searchable={true}
                                                // onSearch={(text) => {
                                                //   setSearchingUserTagText(text);
                                                // }}
                                                open={openGL[index]}
                                                setOpen={(value) => {
                                                  setOpenGL((prevOpenList) => {
                                                    const newOpenList = [...prevOpenList];
                                                    newOpenList[index] = value;
                                                    return newOpenList;
                                                  });
                                                }}
                                              />
                                              :
                                              <></>
                                          }

                                          <TouchableOpacity
                                            onPress={() => {
                                              var groupList = [];
                                              if (printerIdDict[item.uniqueId].groupList) {
                                                groupList = printerIdDict[item.uniqueId].groupList;
                                              }

                                              groupList = groupList.filter((groupRemove, groupRemoveIndex) => groupRemoveIndex !== groupIndex);

                                              setPrinterIdDict({
                                                ...printerIdDict,
                                                [item.uniqueId]: {
                                                  ...printerIdDict[item.uniqueId],
                                                  groupList,
                                                }
                                              });
                                            }}
                                            style={{
                                              backgroundColor: Colors.whiteColor,
                                              alignItems: 'center',
                                              flexDirection: 'row',

                                              marginLeft: -50,
                                            }}>
                                            <Icon
                                              name="minus-circle"
                                              size={switchMerchant ? 17 : 20}
                                              color={Colors.tabRed}
                                            />
                                          </TouchableOpacity>

                                        </View>
                                      );
                                    })
                                  }
                                </View>

                                <View style={{
                                  width: '35%',

                                  display: 'flex',
                                  flexDirection: 'row',
                                  alignItems: 'center',

                                  // backgroundColor: 'red',
                                }}>
                                  <View style={{
                                    width: '50%',
                                  }}>
                                    <Text
                                      style={{
                                        color: 'black',
                                        fontFamily: 'NunitoSans-SemiBold',
                                        fontSize: switchMerchant ? 10 : 14,
                                        // paddingRight: switchMerchant ? 8 : windowHeight === 800 && windowWidth === 1280 ? 0 : 5,
                                        marginHorizontal: switchMerchant ? 0 : 4,

                                        paddingLeft: 2,
                                      }}>
                                      {'Group Name:'}
                                    </Text>
                                  </View>

                                  <View style={{
                                    width: '50%',
                                  }}>
                                    <TextInput
                                      style={{
                                        color: Colors.fontDark,
                                        fontSize: switchMerchant ? 10 : 16,
                                        fontFamily: 'NunitoSans-Bold',
                                        borderWidth: 1,
                                        borderRadius: 5,
                                        borderColor: '#E5E5E5',
                                        paddingLeft: switchMerchant ? 5 : 5,
                                        height: 40,
                                        width: switchMerchant
                                          ? Platform.OS === 'ios' ? 98
                                            : windowWidth * 0.098
                                          : Platform.OS === 'ios'
                                            ? windowWidth * 0.1
                                            : ((!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 100 : 150),

                                        paddingLeft: 15,
                                      }}
                                      placeholder={`${item.name}`}
                                      placeholderTextColor={Colors.descriptionColor}
                                      defaultValue={
                                        printerIdDict[item.uniqueId]
                                          ? printerIdDict[item.uniqueId].groupName
                                          : ''
                                      }
                                      //iOS
                                      clearTextOnFocus
                                      //////////////////////////////////////////////
                                      //Android
                                      onFocus={() => {
                                        setTemp(printerIdDict[item.uniqueId].groupName)
                                        setPrinterIdDict({
                                          ...printerIdDict,
                                          [item.uniqueId]: {
                                            ...printerIdDict[item.uniqueId],
                                            groupName: '',
                                          },
                                        });
                                      }}
                                      ///////////////////////////////////////////////
                                      //When textinput is not selected
                                      onEndEditing={() => {
                                        if (printerIdDict[item.uniqueId].groupName == '') {
                                          setPrinterIdDict({
                                            ...printerIdDict,
                                            [item.uniqueId]: {
                                              ...printerIdDict[item.uniqueId],
                                              groupName: temp,
                                            },
                                          });
                                        }
                                      }}
                                      //////////////////////////////////////////////
                                      onChangeText={(text) => {
                                        setPrinterIdDict({
                                          ...printerIdDict,
                                          [item.uniqueId]: {
                                            ...printerIdDict[item.uniqueId],
                                            groupName: text,
                                          },
                                        });
                                      }} />

                                    {/* <DropDownPicker
                                          containerStyle={[
                                            { height: 40 },
                                            switchMerchant ? { height: 35 } : {},
                                          ]}
                                          arrowColor={'black'}
                                          arrowSize={20}
                                          arrowStyle={{ fontWeight: 'bold' }}
                                          labelStyle={{
                                            fontFamily: 'NunitoSans-Regular',
                                            fontSize: switchMerchant ? 10 : 14,
                                          }}
                                          style={[
                                            {
                                              width: '90%',
                                              paddingVertical: 0,
                                              backgroundColor: Colors.fieldtBgColor,
                                              borderRadius: 10,
                                              zIndex: -10102 - index,
                                            },
                                            switchMerchant
                                              ? {
                                                width: '90%',
                                              }
                                              : {},
                                          ]}
                                          placeholderStyle={{
                                            color: Colors.fieldtTxtColor,
                                            fontFamily: 'NunitoSans-Regular',
                                            fontSize: switchMerchant ? 10 : 14,
                                          }}
                                          //items={[{ label: 'Dine In', value: 1 }, { label: 'Takeaway', value: 2 }, { label: 'Delivery', value: 3 } ]}
                                          items={PRINTER_USER_PRIORITY_DROPDOWN_LIST}
                                          itemStyle={{
                                            justifyContent: 'flex-start',
                                            marginLeft: 5,
                                            zIndex: 2,
                                            fontFamily: 'NunitoSans-Regular',
                                            fontSize: switchMerchant ? 10 : 14,
                                          }}
                                          defaultValue={
                                            (printerIdDict[item.uniqueId] && printerIdDict[item.uniqueId].userPriority)
                                              ? printerIdDict[item.uniqueId].userPriority
                                              : PRINTER_USER_PRIORITY.NORMAL
                                          }
                                          placeholder={'Priority'}
                                          // multipleText={'%d category(s)'}
                                          // multiple={true}
                                          // customTickIcon={(press) => (
                                          //   <Ionicon
                                          //     name={'md-checkbox'}
                                          //     color={
                                          //       press
                                          //         ? Colors.fieldtBgColor
                                          //         : Colors.primaryColor
                                          //     }
                                          //     size={25}
                                          //   />
                                          // )}
                                          onChangeItem={(option) => {
                                            setPrinterIdDict({
                                              ...printerIdDict,
                                              [item.uniqueId]: {
                                                ...printerIdDict[item.uniqueId],
                                                userPriority: option.value,
                                              },
                                            });
                                          }}
                                          dropDownMaxHeight={windowHeight * 0.15}
                                          dropDownStyle={[
                                            {
                                              width: '90%',
                                              height: windowHeight * 0.15,
                                              backgroundColor: Colors.fieldtBgColor,
                                              borderRadius: 10,
                                              borderWidth: 1,
                                            },
                                            switchMerchant
                                              ? {
                                                width: '90%',
                                              }
                                              : {},
                                          ]}
                                          globalTextStyle={{
                                            fontSize: switchMerchant ? 10 : 14,
                                          }}
                                          zIndex={-10102 - index}
                                        /> */}
                                  </View>
                                </View>
                              </View>
                            </View>

                            {/* //////////////////////////////////// */}

                            {/* 2023-03-02 - New row for KD print whole docket/individual docket */}

                            <View
                              style={{
                                flexDirection: 'row',
                                // height: switchMerchant
                                //   ? windowHeight * 0.1
                                //   : windowHeight * 0.06,
                                alignItems: 'center',
                                borderBottomColor: Colors.fieldtBgColor,
                                // backgroundColor: 'red',
                                width: switchMerchant
                                  ? windowWidth * 0.8
                                  : '100%',
                                alignSelf: 'center',

                                marginTop: 20,

                                zIndex: openKDO[index] ? 996 : undefined
                                // zIndex: activeDropdown === 'second' ? 10500101 : 10500100, // Dynamic zIndex
                              }}>
                              <View style={{
                                width: '80%',

                                display: 'flex',
                                flexDirection: 'row',
                                alignItems: 'flex-start',

                                justifyContent: 'flex-start',

                                // backgroundColor: 'red',
                              }}>
                                <View style={{
                                  width: '35%',
                                  height: '100%',

                                  flexDirection: 'row',
                                  alignItems: 'flex-start',
                                  justifyContent: 'flex-start',

                                  // backgroundColor: 'red',

                                  paddingTop: switchMerchant ? 8.75 : 10,
                                }} />

                                <View style={{
                                  width: '40%',
                                  height: '100%',

                                  display: 'flex',
                                  flexDirection: 'row',
                                  alignItems: 'center',

                                  // backgroundColor: 'blue',
                                }}>
                                  <View style={{
                                    width: '50%',
                                  }}>
                                    <Text
                                      style={{
                                        color: 'black',
                                        fontFamily: 'NunitoSans-SemiBold',
                                        fontSize: switchMerchant ? 10 : 14,
                                        // paddingRight: switchMerchant ? 8 : windowHeight === 800 && windowWidth === 1280 ? 0 : 5,
                                        marginHorizontal: switchMerchant ? 0 : 4,

                                        paddingLeft: 2,
                                      }}>
                                      {`OS Print Times:`}
                                    </Text>
                                  </View>

                                  <View style={{
                                    width: '50%',
                                    zIndex: -1050100,
                                  }}>
                                    <TextInput
                                      style={{
                                        color: Colors.fontDark,
                                        fontSize: switchMerchant ? 10 : 16,
                                        fontFamily: 'NunitoSans-Bold',
                                        borderWidth: 1,
                                        borderRadius: 5,
                                        borderColor: '#E5E5E5',
                                        paddingLeft: switchMerchant ? 5 : 5,
                                        height: 40,
                                        width: switchMerchant
                                          ? Platform.OS === 'ios' ? 98
                                            : windowWidth * 0.098
                                          : Platform.OS === 'ios'
                                            ? windowWidth * 0.1
                                            : ((!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 100 : 150),

                                        paddingLeft: 15,
                                        zIndex: -1050100,
                                      }}
                                      placeholder={`${item.name}`}
                                      placeholderTextColor={Colors.descriptionColor}
                                      defaultValue={
                                        printerIdDict[item.uniqueId]
                                          ? printerIdDict[item.uniqueId].osPrintTimes
                                          : ''
                                      }
                                      //iOS
                                      clearTextOnFocus
                                      //////////////////////////////////////////////
                                      //Android
                                      onFocus={() => {
                                        setTemp(printerIdDict[item.uniqueId].osPrintTimes)
                                        setPrinterIdDict({
                                          ...printerIdDict,
                                          [item.uniqueId]: {
                                            ...printerIdDict[item.uniqueId],
                                            osPrintTimes: '',
                                          },
                                        });
                                      }}
                                      ///////////////////////////////////////////////
                                      //When textinput is not selected
                                      onEndEditing={() => {
                                        if (printerIdDict[item.uniqueId].osPrintTimes == '') {
                                          setPrinterIdDict({
                                            ...printerIdDict,
                                            [item.uniqueId]: {
                                              ...printerIdDict[item.uniqueId],
                                              osPrintTimes: temp,
                                            },
                                          });
                                        }
                                      }}
                                      //////////////////////////////////////////////
                                      onChangeText={(text) => {
                                        setPrinterIdDict({
                                          ...printerIdDict,
                                          [item.uniqueId]: {
                                            ...printerIdDict[item.uniqueId],
                                            osPrintTimes: text,
                                          },
                                        });
                                      }}
                                      keyboardType='numeric'
                                    />

                                  </View>
                                </View>

                                <View style={{
                                  width: '35%',

                                  display: 'flex',
                                  flexDirection: 'row',
                                  alignItems: 'center',

                                  // backgroundColor: 'red',
                                }}>
                                  <View style={{
                                    width: '50%',
                                  }}>
                                    <Text
                                      style={{
                                        color: 'black',
                                        fontFamily: 'NunitoSans-SemiBold',
                                        fontSize: switchMerchant ? 10 : 14,
                                        // paddingRight: switchMerchant ? 8 : windowHeight === 800 && windowWidth === 1280 ? 0 : 5,
                                        marginHorizontal: switchMerchant ? 0 : 4,

                                        paddingLeft: 2,
                                      }}>
                                      {`KD Options\n(Deliver/Reject):`}
                                    </Text>
                                  </View>

                                  <View style={{
                                    width: '50%',
                                  }}>
                                    <DropDownPicker
                                      style={{
                                        backgroundColor: Colors.fieldtBgColor,
                                        width: '120%',
                                        height: 40,
                                        borderRadius: 10,
                                        borderWidth: 1,
                                        borderColor: "#E5E5E5",
                                        flexDirection: "row",
                                      }}
                                      dropDownContainerStyle={{
                                        width: '120%',
                                        backgroundColor: Colors.fieldtBgColor,
                                        borderColor: "#E5E5E5",
                                      }}
                                      labelStyle={{
                                        marginLeft: 5,
                                        flexDirection: "row",
                                        whiteSpace: 'nowrap',
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis',
                                      }}
                                      textStyle={{
                                        fontSize: 14,
                                        fontFamily: 'NunitoSans-Regular',

                                        marginLeft: 5,
                                        paddingVertical: 10,
                                        flexDirection: "row",
                                      }}
                                      selectedItemContainerStyle={{
                                        flexDirection: "row",
                                      }}
                                      onOpen={() => setActiveDropdown(index)}
                                      onClose={() => setActiveDropdown(null)}

                                      showArrowIcon={true}
                                      ArrowDownIconComponent={({ style }) => (
                                        <Ionicon
                                          size={25}
                                          color={Colors.fieldtTxtColor}
                                          style={{ paddingHorizontal: 5, marginTop: 5 }}
                                          name="chevron-down-outline"
                                        />
                                      )}
                                      ArrowUpIconComponent={({ style }) => (
                                        <Ionicon
                                          size={25}
                                          color={Colors.fieldtTxtColor}
                                          style={{ paddingHorizontal: 5, marginTop: 5 }}
                                          name="chevron-up-outline"
                                        />
                                      )}

                                      showTickIcon={true}
                                      TickIconComponent={({ press }) => (
                                        <Ionicon
                                          style={{ paddingHorizontal: 5, marginTop: 5 }}
                                          color={
                                            press ? Colors.fieldtBgColor : Colors.primaryColor
                                          }
                                          name={'md-checkbox'}
                                          size={25}
                                        />
                                      )}
                                      items={KD_OPTIONS_DELIVER_REJECT_DROPDOWN_LIST}
                                      value={
                                        (printerIdDict[item.uniqueId] && printerIdDict[item.uniqueId].kdOptionsDeliverReject)
                                          ? printerIdDict[item.uniqueId].kdOptionsDeliverReject
                                          : KD_OPTIONS_DELIVER_REJECT.DELIVERED_REJECTED_ITEMS
                                      }
                                      placeholder={"Select..."}
                                      // multipleText={`${printerIdDict[item.uniqueId].blockOSCategoryIdList.length} category(s) selected`}
                                      placeholderStyle={{
                                        color: Colors.fieldtTxtColor,
                                        // marginTop: 15,
                                      }}
                                      onSelectItem={(option) => {
                                        setPrinterIdDict({
                                          ...printerIdDict,
                                          [item.uniqueId]: {
                                            ...printerIdDict[item.uniqueId],
                                            kdOptionsDeliverReject: option.value,
                                          },
                                        });
                                      }}
                                      // multiple={true}
                                      // searchable={true}
                                      // onSearch={(text) => {
                                      //   setSearchingUserTagText(text);
                                      // }}
                                      open={openKDO[index]}
                                      setOpen={(value) => {
                                        setOpenKDO((prevOpenList) => {
                                          const newOpenList = [...prevOpenList];
                                          newOpenList[index] = value;
                                          return newOpenList;
                                        });
                                      }}
                                    />
                                  </View>
                                </View>
                              </View>
                            </View>

                            {/* //////////////////////////////////// */}

                            {/* 2024-12-23 - For the tsc printer configuration section */}

                            <View
                              style={{
                                flexDirection: 'row',
                                height: switchMerchant
                                  ? windowHeight * 0.1
                                  : windowHeight * 0.06,
                                alignItems: 'center',
                                borderBottomColor: Colors.fieldtBgColor,
                                // backgroundColor: 'red',
                                width: switchMerchant
                                  ? windowWidth * 0.8
                                  : '100%',
                                alignSelf: 'center',

                                marginTop: 20,

                                zIndex: openPW[index] || openKDV[index] || openP[index] ? 998 : undefined
                                // zIndex: activeDropdown === 'first' ? 10500101 : 10500100, // Dynamic zIndex
                              }}>
                              <View style={{
                                width: '20%',

                                display: 'flex',
                                flexDirection: 'row',
                                alignItems: 'center',

                                // backgroundColor: 'red',
                              }}>
                                <View style={{
                                  width: '50%',
                                }}>
                                  <Text
                                    style={{
                                      color: 'black',
                                      fontFamily: 'NunitoSans-SemiBold',
                                      fontSize: switchMerchant ? 10 : 14,
                                      // paddingRight: switchMerchant ? 8 : windowHeight === 800 && windowWidth === 1280 ? 0 : 5,
                                      marginHorizontal: switchMerchant ? 0 : 4,

                                      paddingLeft: 2,
                                    }}>
                                    {'Command Type:'}
                                  </Text>
                                </View>

                                <View style={{
                                  width: '50%',
                                }}>
                                  <DropDownPicker
                                    style={{
                                      backgroundColor: Colors.fieldtBgColor,
                                      width: '90%',
                                      height: 40,
                                      borderRadius: 10,
                                      borderWidth: 1,
                                      borderColor: "#E5E5E5",
                                      flexDirection: "row",
                                    }}
                                    dropDownContainerStyle={{
                                      width: '90%',
                                      backgroundColor: Colors.fieldtBgColor,
                                      borderColor: "#E5E5E5",
                                    }}
                                    labelStyle={{
                                      marginLeft: 5,
                                      flexDirection: "row",
                                    }}
                                    textStyle={{
                                      fontSize: 14,
                                      fontFamily: 'NunitoSans-Regular',

                                      marginLeft: 5,
                                      paddingVertical: 10,
                                      flexDirection: "row",
                                    }}
                                    selectedItemContainerStyle={{
                                      flexDirection: "row",
                                    }}
                                    onOpen={() => setActiveDropdown(index)}
                                    onClose={() => setActiveDropdown(null)}

                                    showArrowIcon={true}
                                    ArrowDownIconComponent={({ style }) => (
                                      <Ionicon
                                        size={25}
                                        color={Colors.fieldtTxtColor}
                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                        name="chevron-down-outline"
                                      />
                                    )}
                                    ArrowUpIconComponent={({ style }) => (
                                      <Ionicon
                                        size={25}
                                        color={Colors.fieldtTxtColor}
                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                        name="chevron-up-outline"
                                      />
                                    )}

                                    showTickIcon={true}
                                    TickIconComponent={({ press }) => (
                                      <Ionicon
                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                        color={
                                          press ? Colors.fieldtBgColor : Colors.primaryColor
                                        }
                                        name={'md-checkbox'}
                                        size={25}
                                      />
                                    )}
                                    items={PRINTER_COMMAND_TYPE_DROPDOWN_LIST}
                                    value={
                                      (printerIdDict[item.uniqueId] && printerIdDict[item.uniqueId].commandType)
                                        ? printerIdDict[item.uniqueId].commandType
                                        : PRINTER_COMMAND_TYPE.ESCPOS
                                    }
                                    placeholder={"Select..."}
                                    // multipleText={`${printerIdDict[item.uniqueId].blockOSCategoryIdList.length} category(s) selected`}
                                    placeholderStyle={{
                                      color: Colors.fieldtTxtColor,
                                      // marginTop: 15,
                                    }}
                                    onSelectItem={(option) => {
                                      setPrinterIdDict({
                                        ...printerIdDict,
                                        [item.uniqueId]: {
                                          ...printerIdDict[item.uniqueId],
                                          commandType: option.value,
                                        },
                                      });
                                    }}
                                    // multiple={true}
                                    // searchable={true}
                                    // onSearch={(text) => {
                                    //   setSearchingUserTagText(text);
                                    // }}
                                    open={openPCT[index]}
                                    setOpen={(value) => {
                                      setOpenPCT((prevOpenList) => {
                                        const newOpenList = [...prevOpenList];
                                        newOpenList[index] = value;
                                        return newOpenList;
                                      });
                                    }}
                                  />
                                </View>
                              </View>

                              {
                                ((printerIdDict[item.uniqueId] && printerIdDict[item.uniqueId].commandType)
                                  ? printerIdDict[item.uniqueId].commandType
                                  : PRINTER_COMMAND_TYPE.ESCPOS
                                ) === PRINTER_COMMAND_TYPE.TSCPOS
                                  ?
                                  <>
                                    <View style={{
                                      width: '20%',

                                      display: 'flex',
                                      flexDirection: 'row',
                                      alignItems: 'center',

                                      // backgroundColor: 'red',
                                    }}>
                                      <View style={{
                                        width: '50%',
                                      }}>
                                        <Text
                                          style={{
                                            color: 'black',
                                            fontFamily: 'NunitoSans-SemiBold',
                                            fontSize: switchMerchant ? 10 : 14,
                                            // paddingRight: switchMerchant ? 8 : windowHeight === 800 && windowWidth === 1280 ? 0 : 5,
                                            marginHorizontal: switchMerchant ? 0 : 4,

                                            paddingLeft: 2,
                                          }}>
                                          {'Label Width:'}
                                        </Text>
                                      </View>

                                      <View style={{
                                        width: '40%',
                                      }}>
                                        <TextInput
                                          style={{
                                            color: Colors.fontDark,
                                            fontSize: switchMerchant ? 10 : 16,
                                            fontFamily: 'NunitoSans-Bold',
                                            borderWidth: 1,
                                            borderRadius: 5,
                                            borderColor: '#E5E5E5',
                                            paddingLeft: 5,
                                            height: 40,
                                            // width: switchMerchant ? 127 : 165,
                                          }}
                                          placeholder={'Width (mm)'}
                                          placeholderTextColor={Platform.select({
                                            ios: '#a9a9a9',
                                          })}
                                          keyboardType={'decimal-pad'}
                                          defaultValue={
                                            printerIdDict[item.uniqueId]
                                              ? printerIdDict[item.uniqueId].labelWidth
                                              : ''
                                          }
                                          //iOS
                                          clearTextOnFocus
                                          //////////////////////////////////////////////
                                          //Android
                                          onFocus={() => {
                                            setTemp(printerIdDict[item.uniqueId].labelWidth)
                                            setPrinterIdDict({
                                              ...printerIdDict,
                                              [item.uniqueId]: {
                                                ...printerIdDict[item.uniqueId],
                                                labelWidth: '',
                                              },
                                            });
                                          }}
                                          ///////////////////////////////////////////////
                                          //When textinput is not selected
                                          onEndEditing={() => {
                                            if (printerIdDict[item.uniqueId].labelWidth == '') {
                                              setPrinterIdDict({
                                                ...printerIdDict,
                                                [item.uniqueId]: {
                                                  ...printerIdDict[item.uniqueId],
                                                  labelWidth: temp,
                                                },
                                              });
                                            }
                                          }}
                                          //////////////////////////////////////////////
                                          onChangeText={(text) => {
                                            setPrinterIdDict({
                                              ...printerIdDict,
                                              [item.uniqueId]: {
                                                ...printerIdDict[item.uniqueId],
                                                labelWidth: text,
                                              },
                                            });
                                          }} />
                                      </View>
                                    </View>

                                    <View style={{
                                      width: '20%',

                                      display: 'flex',
                                      flexDirection: 'row',
                                      alignItems: 'center',

                                      // backgroundColor: 'red',
                                    }}>
                                      <View style={{
                                        width: '50%',
                                      }}>
                                        <Text
                                          style={{
                                            color: 'black',
                                            fontFamily: 'NunitoSans-SemiBold',
                                            fontSize: switchMerchant ? 10 : 14,
                                            // paddingRight: switchMerchant ? 8 : windowHeight === 800 && windowWidth === 1280 ? 0 : 5,
                                            marginHorizontal: switchMerchant ? 0 : 4,

                                            paddingLeft: 2,
                                          }}>
                                          {'Label Height:'}
                                        </Text>
                                      </View>

                                      <View style={{
                                        width: '40%',
                                      }}>
                                        <TextInput
                                          style={{
                                            color: Colors.fontDark,
                                            fontSize: switchMerchant ? 10 : 16,
                                            fontFamily: 'NunitoSans-Bold',
                                            borderWidth: 1,
                                            borderRadius: 5,
                                            borderColor: '#E5E5E5',
                                            paddingLeft: 5,
                                            height: 40,
                                            // width: switchMerchant ? 127 : 165,
                                          }}
                                          placeholder={'Height (mm)'}
                                          placeholderTextColor={Platform.select({
                                            ios: '#a9a9a9',
                                          })}
                                          keyboardType={'decimal-pad'}
                                          defaultValue={
                                            printerIdDict[item.uniqueId]
                                              ? printerIdDict[item.uniqueId].labelHeight
                                              : ''
                                          }
                                          //iOS
                                          clearTextOnFocus
                                          //////////////////////////////////////////////
                                          //Android
                                          onFocus={() => {
                                            setTemp(printerIdDict[item.uniqueId].labelHeight)
                                            setPrinterIdDict({
                                              ...printerIdDict,
                                              [item.uniqueId]: {
                                                ...printerIdDict[item.uniqueId],
                                                labelHeight: '',
                                              },
                                            });
                                          }}
                                          ///////////////////////////////////////////////
                                          //When textinput is not selected
                                          onEndEditing={() => {
                                            if (printerIdDict[item.uniqueId].labelHeight == '') {
                                              setPrinterIdDict({
                                                ...printerIdDict,
                                                [item.uniqueId]: {
                                                  ...printerIdDict[item.uniqueId],
                                                  labelHeight: temp,
                                                },
                                              });
                                            }
                                          }}
                                          //////////////////////////////////////////////
                                          onChangeText={(text) => {
                                            setPrinterIdDict({
                                              ...printerIdDict,
                                              [item.uniqueId]: {
                                                ...printerIdDict[item.uniqueId],
                                                labelHeight: text,
                                              },
                                            });
                                          }} />
                                      </View>
                                    </View>

                                    <View style={{
                                      width: '20%',

                                      display: 'flex',
                                      flexDirection: 'row',
                                      alignItems: 'center',

                                      // backgroundColor: 'red',
                                    }}>
                                      <View style={{
                                        width: '50%',
                                      }}>
                                        <Text
                                          style={{
                                            color: 'black',
                                            fontFamily: 'NunitoSans-SemiBold',
                                            fontSize: switchMerchant ? 10 : 14,
                                            // paddingRight: switchMerchant ? 8 : windowHeight === 800 && windowWidth === 1280 ? 0 : 5,
                                            marginHorizontal: switchMerchant ? 0 : 4,

                                            paddingLeft: 2,
                                          }}>
                                          {'Label Gap:'}
                                        </Text>
                                      </View>

                                      <View style={{
                                        width: '40%',
                                      }}>
                                        <TextInput
                                          style={{
                                            color: Colors.fontDark,
                                            fontSize: switchMerchant ? 10 : 16,
                                            fontFamily: 'NunitoSans-Bold',
                                            borderWidth: 1,
                                            borderRadius: 5,
                                            borderColor: '#E5E5E5',
                                            paddingLeft: 5,
                                            height: 40,
                                            // width: switchMerchant ? 127 : 165,
                                          }}
                                          placeholder={'Gap (mm)'}
                                          placeholderTextColor={Platform.select({
                                            ios: '#a9a9a9',
                                          })}
                                          keyboardType={'decimal-pad'}
                                          defaultValue={
                                            printerIdDict[item.uniqueId]
                                              ? printerIdDict[item.uniqueId].labelGap
                                              : ''
                                          }
                                          //iOS
                                          clearTextOnFocus
                                          //////////////////////////////////////////////
                                          //Android
                                          onFocus={() => {
                                            setTemp(printerIdDict[item.uniqueId].labelGap)
                                            setPrinterIdDict({
                                              ...printerIdDict,
                                              [item.uniqueId]: {
                                                ...printerIdDict[item.uniqueId],
                                                labelGap: '',
                                              },
                                            });
                                          }}
                                          ///////////////////////////////////////////////
                                          //When textinput is not selected
                                          onEndEditing={() => {
                                            if (printerIdDict[item.uniqueId].labelGap == '') {
                                              setPrinterIdDict({
                                                ...printerIdDict,
                                                [item.uniqueId]: {
                                                  ...printerIdDict[item.uniqueId],
                                                  labelGap: temp,
                                                },
                                              });
                                            }
                                          }}
                                          //////////////////////////////////////////////
                                          onChangeText={(text) => {
                                            setPrinterIdDict({
                                              ...printerIdDict,
                                              [item.uniqueId]: {
                                                ...printerIdDict[item.uniqueId],
                                                labelGap: text,
                                              },
                                            });
                                          }} />
                                      </View>
                                    </View>
                                  </>
                                  :
                                  <></>
                              }
                            </View>

                            {/* //////////////////////////////////// */}
                          </View>
                          {/* </Swipeable> */}
                          <View style={{ borderBottomWidth: 0, opacity: 0.4, position: 'relative', top: windowHeight * 0.1, zIndex: -10504 }} />
                        </View>
                      );
                    })}
                </ScrollView>
              </View>
            </View>
          </View>
          {/* </ScrollView> */}
        </ScrollView >

        {/* add modal */}
        < Modal supportedOrientations={['landscape', 'portrait']} style={{ flex: 1 }} visible={addQueueModal} transparent >
          <View style={styles.modalContainer}>
            <View
              style={{
                width: windowWidth * 0.4,
                //height: windowHeight * 0.47,
                backgroundColor: Colors.whiteColor,
                borderRadius: 10,
                padding: windowWidth * 0.04,
                paddingBottom: 0,
                alignItems: 'center',
                justifyContent: 'space-between',

                ...getTransformForModalInsideNavigation(),
              }}>
              <View
                style={{
                  justifyContent: 'space-between',
                  flexDirection: 'column',
                }}>
                <Text
                  style={{
                    fontSize: switchMerchant ? 16 : 24,
                    justifyContent: 'center',
                    alignSelf: 'center',
                    fontFamily: 'NunitoSans-Bold',
                  }}>
                  {/* {selectedQueue ? 'Queue Info' : 'Add Queue'} */}
                  Add Printer
                </Text>
                {/* <Text style={{ fontSize: 20, justifyContent: "center", alignSelf: "center", marginTop: 5, color: Colors.descriptionColor, fontFamily: 'NunitoSans-Regular' }}>
                  {`There are currently ${outletPrinters.length} parties in printer setting.`}
                </Text> */}
                <View
                  style={{
                    justifyContent: 'center',
                    alignSelf: 'center',
                    alignContent: 'center',
                    marginTop: 30,
                    flexDirection: 'row',
                    width: '100%',
                  }}>
                  <View style={{ justifyContent: 'center', width: '45%' }}>
                    <Text
                      style={{
                        color: 'black',
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 10 : 20,
                      }}>
                      Printer Name
                    </Text>
                  </View>
                  <View style={{ justifyContent: 'center', width: '60%' }}>
                    <TextInput
                      placeholder="HP 1102 PowerZ"
                      placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                      style={{
                        fontSize: switchMerchant ? 10 : 20,
                        backgroundColor: Colors.fieldtBgColor,
                        width: switchMerchant ? 150 : 200,
                        height: switchMerchant ? 35 : 40,
                        borderRadius: 5,
                        padding: 5,
                        marginVertical: 5,
                        borderWidth: 1,
                        borderColor: '#E5E5E5',
                        paddingLeft: 10,
                      }}
                      //iOS
                      clearTextOnFocus
                      //////////////////////////////////////////////
                      //Android
                      onFocus={() => {
                        setTemp(printerName)
                        setPrinterName('');
                      }}
                      ///////////////////////////////////////////////
                      //When textinput is not selected
                      onEndEditing={() => {
                        if (printerName == '') {
                          setPrinterName(temp);
                        }
                      }}
                      //////////////////////////////////////////////
                      onChangeText={(text) => {
                        // setQueueCustomerName(text);
                        setPrinterName(text);
                      }}
                      defaultValue={printerName}
                    />
                  </View>
                </View>

                <View
                  style={{
                    justifyContent: 'center',
                    alignSelf: 'center',
                    alignContent: 'center',
                    marginTop: 20,
                    flexDirection: 'row',
                    width: '100%',
                  }}>
                  <View style={{ justifyContent: 'center', width: '45%' }}>
                    <Text
                      style={{
                        color: 'black',
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 10 : 20,
                      }}>
                      Printer IP
                    </Text>
                  </View>
                  <View style={{ justifyContent: 'center', width: '60%' }}>
                    <TextInput
                      placeholder="*************"
                      placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                      style={{
                        fontSize: switchMerchant ? 10 : 20,
                        backgroundColor: Colors.fieldtBgColor,
                        width: switchMerchant ? 150 : 200,
                        height: switchMerchant ? 35 : 40,
                        borderRadius: 5,
                        padding: 5,
                        marginVertical: 5,
                        borderWidth: 1,
                        borderColor: '#E5E5E5',
                        paddingLeft: 10,
                      }}
                      keyboardType={'decimal-pad'}
                      //iOS
                      // clearTextOnFocus
                      // //////////////////////////////////////////////
                      // //Android
                      // onFocus={() => {
                      //   setTemp(printerIP)
                      //   setPrinterIP('');
                      // }}
                      ///////////////////////////////////////////////
                      //When textinput is not selected
                      // onEndEditing={() => {
                      //   if (printerIP == '') {
                      //     setPrinterIP(temp);
                      //   }
                      // }}
                      //////////////////////////////////////////////
                      onChangeText={(text) => {
                        // setQueuePhone(text);
                        setPrinterIP(text);
                      }}
                      defaultValue={printerIP}
                    />
                  </View>
                </View>

                <View
                  style={{
                    justifyContent: 'center',
                    alignSelf: 'center',
                    alignContent: 'center',
                    marginTop: 20,
                    flexDirection: 'row',
                    width: '100%',
                  }}>
                  <View style={{ justifyContent: 'center', width: '45%' }}>
                    <Text
                      style={{
                        color: 'black',
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 10 : 20,
                      }}>
                      Printer Area
                    </Text>
                  </View>
                  <View style={{ justifyContent: 'center', width: '60%' }}>
                    <TextInput
                      placeholder="Kitchen"
                      placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                      style={{
                        fontSize: switchMerchant ? 10 : 20,
                        backgroundColor: Colors.fieldtBgColor,
                        width: switchMerchant ? 150 : 200,
                        height: switchMerchant ? 35 : 40,
                        borderRadius: 5,
                        padding: 5,
                        marginVertical: 5,
                        borderWidth: 1,
                        borderColor: '#E5E5E5',
                        paddingLeft: 10,
                      }}
                      //iOS
                      clearTextOnFocus
                      //////////////////////////////////////////////
                      //Android
                      onFocus={() => {
                        setTemp(printerArea)
                        setPrinterArea('');
                      }}
                      ///////////////////////////////////////////////
                      //When textinput is not selected
                      onEndEditing={() => {
                        if (printerArea == '') {
                          setPrinterArea(temp);
                        }
                      }}
                      //////////////////////////////////////////////
                      onChangeText={(text) => {
                        // setQueuePhone(text);
                        setPrinterArea(text);
                      }}
                      defaultValue={printerArea}
                    />
                  </View>
                </View>

                {/* <View
                  style={{
                    justifyContent: 'center',
                    alignSelf: 'center',
                    alignContent: 'center',
                    marginTop: 20,
                    flexDirection: 'row',
                    width: '100%',
                  }}>
                  <View style={{ justifyContent: 'center', width: '45%' }}>
                    <Text
                      style={{
                        color: 'black',
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 10 : 20,
                      }}>
                      Printer Type(s)
                    </Text>
                  </View>
                  <View style={{ justifyContent: 'center', width: '60%' }}>
                    <DropDownPicker
                      containerStyle={[{ height: 40 }, switchMerchant ? { height: 35, } : {}]}
                      arrowColor={'black'}
                      arrowSize={20}
                      arrowStyle={{ fontWeight: 'bold' }}
                      labelStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: 14 }}
                      style={[{
                        width: '90%', paddingVertical: 0, backgroundColor: Colors.fieldtBgColor,
                        borderRadius: 10,
                      }, switchMerchant ? {
                        width: '90%',
                      } : {}]}
                      placeholderStyle={{ color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Regular', fontSize: 14 }}
                      //items={[{ label: 'Dine In', value: 1 }, { label: 'Takeaway', value: 2 }, { label: 'Delivery', value: 3 } ]}
                      items={PRINTER_USAGE_TYPE_DROPDOWN_LIST}
                      itemStyle={{ justifyContent: 'flex-start', marginLeft: 5, zIndex: 2, fontFamily: 'NunitoSans-Regular', fontSize: 14 }}
                      defaultValue={printerTypes}
                      placeholder={"Type"}
                      multipleText={'%d type(s)'}
                      multiple={true}
                      customTickIcon={(press) => <Ionicon name={"md-checkbox"} color={press ? Colors.fieldtBgColor : Colors.primaryColor} size={25} />}
                      onChangeItem={(items) => {
                        setPrinterTypes(items);
                      }}
                      dropDownMaxHeight={100}
                      dropDownStyle={[{ width: '90%', height: 100, backgroundColor: Colors.fieldtBgColor, borderRadius: 10, borderWidth: 1, }, switchMerchant ? {
                        width: '90%',
                      } : {}]}
                      globalTextStyle={{
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                    />
                  </View>
                </View> */}

                {/* <View style={{ justifyContent: "center", alignSelf: "center", alignContent: 'center', marginTop: 20, flexDirection: 'row', width: '100%' }}>
                  <View style={{ justifyContent: 'center', width: '45%' }}>
                    <Text style={{ color: 'black', fontFamily: 'NunitoSans-Bold', fontSize: 20, }}>
                      Capacity:
                    </Text>
                  </View>
                  <View style={{ justifyContent: 'center', width: '60%' }}>
                    <View style={{ flexDirection: 'row', }}>
                      <TouchableOpacity style={{ justifyContent: 'center', alignItems: 'center', paddingRight: 10 }} onPress={() => {
                        setQueuePax(queuePax - 1 >= 0 ? queuePax - 1 : 0);
                      }}>
                        <MIcon name="minus-circle" size={20} color={Colors.primaryColor} />
                      </TouchableOpacity>
                      <View style={{
                        paddingRight: 10, alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                        <TextInput
                          placeholder='10'
                          placeholderTextColor={Colors.descriptionColor}
                          style={{
                            backgroundColor: Colors.fieldtBgColor,
                            width: 60,
                            height: 40,
                            borderRadius: 5,
                            padding: 5,
                            marginVertical: 5,
                            borderWidth: 1,
                            borderColor: '#E5E5E5',
                            paddingLeft:10,
                          }}
                          onChangeText={text => {
                            setQueuePax(text.length > 0 ? parseInt(text) : 0);
                          }}
                          defaultValue={queuePax.toFixed(0)}
                        />
                      </View>
                      <TouchableOpacity style={{ justifyContent: 'center', alignItems: 'center', }} onPress={() => {
                        setQueuePax(queuePax + 1);
                      }}>
                        <MIcon name="plus-circle" size={20} color={Colors.primaryColor} />
                      </TouchableOpacity>
                    </View>
                  </View>
                </View> */}

                {/* <View style={{ justifyContent: "center", alignSelf: "center", alignContent: 'center', marginTop: 20, flexDirection: 'row', width: '100%' }}>
                  <View style={{ justifyContent: 'center', width: '45%' }}>
                    <Text style={{ color: 'black', fontFamily: 'NunitoSans-Bold', fontSize: 20, }}>
                      Room Type:
                    </Text>
                  </View>
                  <View style={{ justifyContent: 'center', width: '60%' }}>
                    <DropDownPicker
                      containerStyle={{ height: 40 }}
                      arrowColor={'black'}
                      arrowSize={20}
                      arrowStyle={{ fontWeight: 'bold' }}
                      labelStyle={{ fontFamily: 'NunitoSans-Regular', }}
                      style={{ marginTop: 0, width: 250, paddingVertical: 0, borderColor: Colors.fieldtBgColor, borderRadius: 10, backgroundColor: Colors.fieldtBgColor, }}
                      placeholderStyle={{ color: Colors.fieldtTxtColor }}
                      // items={crmUserTagsDropdownList}
                      items={[{ label: 'Normal Seats', value: 0 }, { label: 'VIP Room', value: 1 },]}
                      itemStyle={{ justifyContent: 'flex-start', marginLeft: 5 }}
                      // onChangeItem={item => {
                      //   setSelectedTargetUserGroup(item.value);
                      // }}
                      placeholder='Normal Seats'
                      dropDownMaxHeight={150}
                      dropDownStyle={{ width: 250, height: 40, borderRadius: 10, }}
                    />
                  </View>
                </View> */}

                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginTop: 35,
                  }}>
                  <TouchableOpacity
                    disabled={isLoading}
                    onPress={() => {
                      // setConfirmQueueModal(true)
                      // setAddQueueModal(false)

                      // if (selectedQueue === null) {
                      //   createUserQueueByMerchant();
                      // }
                      // else {
                      //   updateUserQueueByMerchant();
                      // }

                      createOutletPrinter();
                    }}
                    style={{
                      backgroundColor: Colors.fieldtBgColor,
                      width: '57.8%',
                      justifyContent: 'center',
                      alignItems: 'center',
                      alignContent: 'center',
                      height: 60,
                      borderBottomLeftRadius: 10,
                      borderRightWidth: StyleSheet.hairlineWidth,
                      borderTopWidth: StyleSheet.hairlineWidth,
                    }}>
                    {isLoading ? (
                      <ActivityIndicator
                        size={'large'}
                        color={Colors.primaryColor}
                      />
                    ) : (
                      <Text
                        style={{
                          fontSize: switchMerchant ? 15 : 22,
                          color: Colors.primaryColor,
                          fontFamily: 'NunitoSans-SemiBold',
                        }}>
                        Confirm
                      </Text>
                    )}
                  </TouchableOpacity>
                  <TouchableOpacity
                    disabled={isLoading}
                    onPress={() => {
                      // setState({ visible: false });
                      setAddQueueModal(false);
                    }}
                    style={{
                      backgroundColor: Colors.fieldtBgColor,
                      width: '57.8%',
                      justifyContent: 'center',
                      alignItems: 'center',
                      alignContent: 'center',
                      height: 60,
                      borderBottomRightRadius: 10,
                      borderTopWidth: StyleSheet.hairlineWidth,
                    }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 15 : 22,
                        color: Colors.descriptionColor,
                        fontFamily: 'NunitoSans-SemiBold',
                      }}>
                      Cancel
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
        </Modal >

        {/* confirm modal */}
        < Modal supportedOrientations={['landscape', 'portrait']} style={{ flex: 1 }} visible={confirmQueueModal} transparent >
          <View style={styles.modalContainer}>
            <View
              style={{
                width: windowWidth * 0.4,
                height: windowHeight * 0.4,
                backgroundColor: Colors.whiteColor,
                borderRadius: windowWidth * 0.03,
                padding: windowWidth * 0.04,
                alignItems: 'center',
                justifyContent: 'space-between',
              }}>
              <View style={{}}>
                <View style={{ height: windowHeight * 0.1 }}>
                  <Text
                    style={{
                      textAlign: 'center',
                      fontWeight: '700',
                      fontSize: 30,
                    }}>
                    Done!
                  </Text>
                </View>

                <View
                  style={{
                    justifyContent: 'center',
                    alignItems: 'center',
                    alignContent: 'center',
                    alignSelf: 'center',
                  }}>
                  <Text
                    style={{
                      textAlign: 'center',
                      color: Colors.descriptionColor,
                      fontSize: 25,
                      width: '80%',
                      alignSelf: 'center',
                    }}>
                    You’ve added queue
                  </Text>
                  <Text
                    style={{
                      textAlign: 'center',
                      color: Colors.descriptionColor,
                      fontSize: 25,
                      width: '80%',
                      alignSelf: 'center',
                    }}>
                    successfully with number:{' '}
                    {selectedQueue ? `#${selectedQueue.number}` : 'N/A'}
                    {/* {item.number} */}
                  </Text>
                </View>

                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                    height: windowHeight * 0.25,
                    width: windowWidth * 0.4,
                  }}>
                  <TouchableOpacity
                    onPress={() => {
                      setConfirmQueueModal(false);
                    }}
                    style={{
                      backgroundColor: Colors.fieldtBgColor,
                      width: windowWidth * 0.4,
                      justifyContent: 'center',
                      alignItems: 'center',
                      alignContent: 'center',
                      height: 60,
                      borderBottomLeftRadius: 35,
                      borderBottomRightRadius: 35,
                      borderTopWidth: StyleSheet.hairlineWidth,
                    }}>
                    <Text
                      style={{
                        fontSize: 22,
                        color: Colors.primaryColor,
                        fontFamily: 'NunitoSans-SemiBold',
                      }}>
                      Confirm
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
        </Modal >
      </View >
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: "row",
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItem: {
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  content7: {
    backgroundColor: '#e4e8eb',
    width: 800,
    height: 120,
    marginLeft: 50,
    marginVertical: 30,
    borderRadius: 20,
  },

  content8: {
    flex: 3,
    backgroundColor: '#e4e8eb',

    height: 120,
  },
  content9: {
    flex: 1,
    backgroundColor: Colors.primaryColor,

    height: 120,
  },
  content10: {
    flex: 1,
    backgroundColor: Colors.secondaryColor,

    height: 120,
  },
  content11: {
    flex: 1,
    backgroundColor: '#848f96',

    height: 120,
  },
  content6: {
    backgroundColor: Colors.whiteColor,
    width: 120,
    shadowColor: '#000',
    shadowOffset: {
      width: 8,
      height: 8,
    },
    shadowOpacity: 0.55,
    shadowRadius: 10.32,
    width: 120,
    height: 120,
    marginLeft: 50,
    marginVertical: 15,
    borderRadius: 5,
  },

  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    width: Dimensions.get('window').width * 0.87,
    alignSelf: 'center',
    padding: 16,
    marginHorizontal: 15,
  },
  headerLeftStyle: {
    width: Dimensions.get('window').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  submitText: {
    height:
      Platform.OS == 'ios'
        ? Dimensions.get('window').height * 0.06
        : Dimensions.get('window').height * 0.05,
    left: 295,
    paddingVertical: 5,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
export default SettingPrinterScreen;
