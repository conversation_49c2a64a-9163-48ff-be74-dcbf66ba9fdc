// import { Text } from "react-native-fast-text";
import React, { Component, useEffect, useReducer, useState, useCallback, useRef } from 'react';
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Alert,
  TouchableOpacity,
  TextInput,
  Dimensions,
  FlatList,
  Modal,
  PermissionsAndroid,
  Platform,
  useWindowDimensions,
  ActivityIndicator,
  Text,
} from 'react-native';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import * as User from '../util/User';
import Icon from 'react-native-vector-icons/Feather';
import Icon1 from 'react-native-vector-icons/FontAwesome';
import Icon2 from 'react-native-vector-icons/EvilIcons';
import Icon3 from 'react-native-vector-icons/Foundation';
import Icon4 from 'react-native-vector-icons/FontAwesome5';
import DropDownPicker from 'react-native-dropdown-picker';
// import { ceil } from 'react-native-reanimated';
// import CheckBox from '@react-native-community/checkbox';
import DateTimePicker from "react-native-modal-datetime-picker";
import DateTimePickerModal from "react-native-modal-datetime-picker";
import moment from 'moment';
import Close from 'react-native-vector-icons/AntDesign';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Styles from '../constant/Styles'
// import DocumentPicker from 'react-native-document-picker';
import Ionicons from 'react-native-vector-icons/Ionicons';
// import RNFetchBlob from 'rn-fetch-blob';
import {
  // isTablet,
  getTransformForModalInsideNavigation,
  getTransformForScreenInsideNavigation,
  sortWOList,
  consumeOutletSupplyItemRecursive,
  recordOutletSupplyItemTransaction,
  calculateQuantityUsageAndQuantityWastage,
  consumeOutletSupplyItemRecursiveWO,
} from '../util/common';
import 'react-native-get-random-values';
import { customAlphabet } from 'nanoid';
import { CommonStore } from '../store/commonStore';
import { MerchantStore } from '../store/merchantStore';
import { UserStore } from '../store/userStore';
import {
  STOCK_TRANSFER_STATUS,
  STOCK_TRANSFER_STATUS_PARSED,
  EMAIL_REPORT_TYPE,
  PURCHASE_ORDER_STATUS,
  EXPAND_TAB_TYPE,
  WOLIST_SORT_FIELD_TYPE,
} from '../constant/common';
import { convertArrayToCSV, generateEmailReport } from '../util/common';
// import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useKeyboard } from '../hooks';
import AntDesign from 'react-native-vector-icons/AntDesign';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import XLSX from 'xlsx';
import { Row } from 'react-native-table-component';
import { v4 as uuidv4 } from 'uuid';
import personicon from "../assets/image/default-profile.png";
import headerLogo from "../assets/image/logo.png";
// import GCalendar from '../assets/svg/GCalendar.svg'
// import GCalendarGrey from '../assets/svg/GCalendarGrey.svg'
import { ReactComponent as GCalendar } from "../assets/svg/GCalendar.svg";
import { ReactComponent as GCalendarGrey } from "../assets/svg/GCalendarGrey.svg";
import { OutletStore } from '../store/outletStore';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
// import ViewShot from 'react-native-view-shot';
// import RNHTMLtoPDF from 'react-native-html-to-pdf';
// import stockTransferOrderAmountsHtml from '../templates/stock_transfer_order_amounts.html';
import { STOCK_TRANSFER_ORDER_AMOUNTS_HTML } from '../templates/stock_transfer_order_amounts';
import { KEYWORDS } from '../templates/keywords';
import APILocal from '../util/apiLocalReplacers';
import { logEventAnalytics } from '../util/common';
import { ANALYTICS, ANALYTICS_PARSED } from '../constant/analytics';
import Entypo from 'react-native-vector-icons/Entypo';
// import firestore from '@react-native-firebase/firestore';
import firebase from "firebase";
import { Collections } from "../constant/firebase";

// const RNFS = require('react-native-fs');

const alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
const nanoid = customAlphabet(alphabet, 12);

const isTablet = () => {
  return true;
};

const WorkOrderListScreen = props => {
  const {
    navigation,
  } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const [keyboardHeight] = useKeyboard();

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const viewShotRef = useRef();


  const [currWOListSort, setCurrWOListSort] = useState('');
  const [stockTransfer, setStockTransfer] = useState(true);
  const [addPurchase, setAddPurchase] = useState(false);
  const [editPurchase, setEditPurchase] = useState(false);
  const [addStockTransfer, setAddStockTransfer] = useState(false);
  const [stockList, setStockList] = useState([]);
  const [stockTransferList, setStockTransferList] = useState([]);
  const [stockTakeList, setStockTakeList] = useState([]);
  const [orderList, setOrderList] = useState([]);
  const [itemsToOrder, setItemsToOrder] = useState([{}, {}, {}],);
  const [itemsToOrder2, setItemsToOrder2] = useState([{}, {}, {}],);
  const [addStockTransferList, setAddStockTransferList] = useState([{}, {}, {}],);
  const [addCountedStockTakeList, setAddCountedStockTakeList] = useState([{}, {}, {}],);
  const [addUnCountedStockTakeList, setAddUnCountedStockTakeList] = useState([{}, {}, {}],);
  const [productList, setProductList] = useState([]);
  const [isSelected, setIsSelected] = useState(false);
  const [isSelected2, setIsSelected2] = useState(false);
  const [isSelected3, setIsSelected3] = useState(true);
  const [isSelected4, setIsSelected4] = useState(false);
  const [isDateTimePickerVisible, setIsDateTimePickerVisible] = useState(false);
  const [date, setDate] = useState(Date.now());
  const [date1, setDate1] = useState(Date.now());
  const [createdDate, setCreatedDate] = useState(Date.now());
  const [visible, setVisible] = useState(false);
  const [Email, setEmail] = useState('');
  const [modal, setModal] = useState(false);
  // const [// outletId, set// outletId] = useState(1);
  const [outletId, setOutletId] = useState(User.getOutletId());
  const [search, setSearch] = useState('');
  const [search2, setSearch2] = useState('');
  const [search3, setSearch3] = useState('');
  const [ideal, setIdeal] = useState('');
  const [minimum, setMinimum] = useState('');
  const [itemId, setItemId] = useState('');
  const [choose, setChoose] = useState(null);

  const [loading, setLoading] = useState(false);

  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  const [showDateTimePicker1, setShowDateTimePicker1] = useState(false);
  const [rev_date, setRev_date] = useState(moment().subtract(6, 'days').startOf('day'),);
  const [rev_date1, setRev_date1] = useState(moment().endOf(Date.now()).endOf('day'),);
  //////////////////////////////////////////////////////////////////////

  const [poId, setPoId] = useState('');
  const [editMode, setEditMode] = useState(false);
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  const [exportEmail, setExportEmail] = useState('');
  const [exportModal, setExportModal] = useState(false);
  const [importModal, setImportModal] = useState(false);

  const [doModal, setDoModal] = useState(false);
  const [doItem, setDoItem] = useState(null);

  const [isLoadingExcel, setIsLoadingExcel] = useState(false);
  const [isLoadingCsv, setIsLoadingCsv] = useState(false);
  const [isLoadingLocalExcel, setIsLoadingLocalExcel] = useState(false);
  const [isLoadingLocalCsv, setIsLoadingLocalCsv] = useState(false);

  const [poStatus, setPoStatus] = useState(STOCK_TRANSFER_STATUS.CREATED);

  const [targetOutletDropdownList, setTargetOutletDropdownList] = useState([]);
  const [selectedTargetOutletId, setSelectedTargetOutletId] = useState('');

  const [selectedSourceOutletIdPrev, setSelectedSourceOutletIdPrev] = useState('');
  const [selectedSourceOutletId, setSelectedSourceOutletId] = useState('');

  const [outletSupplyItemDropdownList, setOutletSupplyItemDropdownList] = useState([]);

  const [poItems, setPoItems] = useState([
    {
      outletSupplyItemId: '',
      name: '',
      sku: '',
      unit: '',
      skuMerchant: '',
      quantity: 0,
      transferQuantity: 0,
      balance: 0,
      price: 0,
      totalPrice: 0,

      supplyItem: null,
    }
  ]);

  const [subtotal, setSubtotal] = useState(0);
  const [taxTotal, setTaxTotal] = useState(0);
  const [discountTotal, setDiscountTotal] = useState(0);
  const [finalTotal, setFinalTotal] = useState(0);

  const [outletSupplyItems, setOutletSupplyItems] = useState([]);

  const [selectedIndex, setSelectedIndex] = useState(null);

  // const [supplyItems, setSupplyItems] = useState([]);

  const outletItems = OutletStore.useState(s => s.outletItems);
  const allOutletsItems = OutletStore.useState(s => s.allOutletsItems);

  const supplyItems = CommonStore.useState(s => s.supplyItems);
  const supplyItemsSkuDict = CommonStore.useState(s => s.supplyItemsSkuDict);

  const allOutletsSupplyItemsSkuDict = CommonStore.useState(s => s.allOutletsSupplyItemsSkuDict);
  const allOutletsSupplyItems = CommonStore.useState(s => s.allOutletsSupplyItems);
  const allOutletsSupplyItemsDict = CommonStore.useState(s => s.allOutletsSupplyItemsDict);

  const outletSupplyItemsDict = CommonStore.useState(s => s.outletSupplyItemsDict);
  const outletSupplyItemsSkuDict = CommonStore.useState(s => s.outletSupplyItemsSkuDict);

  const allOutlets = MerchantStore.useState(s => s.allOutlets);
  const merchantId = UserStore.useState(s => s.merchantId);
  const stockTransfersProduct = CommonStore.useState(s => s.stockTransfersProduct);

  const userName = UserStore.useState(s => s.name);
  const userId = UserStore.useState(s => s.firebaseUid);
  const merchantName = MerchantStore.useState(s => s.name);

  const currOutlet = MerchantStore.useState(s => s.currOutlet);
  const currOutletId = MerchantStore.useState(s => s.currOutletId);

  const isLoading = CommonStore.useState((s) => s.isLoading);

  const dropDownRef = React.useRef();
  const dropDownRef1 = React.useRef();

  const selectedStockTransferEdit = CommonStore.useState(s => s.selectedStockTransferEdit);
  const outletSelectDropdownView = CommonStore.useState(s => s.outletSelectDropdownView);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  const woList = CommonStore.useState(s => s.woList);
  const [selectedWorkOrder, setSelectedWorkOrder] = useState([]);

  useEffect(() => {
    if (currOutletId !== '' &&
      allOutlets.length > 0 &&
      stockTransfersProduct.length > 0) {
      var stockTransferProductTemp = [];
      for (var i = 0; i < stockTransfersProduct.length; i++) {
        if (moment(rev_date).isSameOrBefore(stockTransfersProduct[i].createdAt) &&
          moment(rev_date1).isAfter(stockTransfersProduct[i].createdAt)
        ) {
          stockTransferProductTemp.push(stockTransfersProduct[i]);
        }
      }
      stockTransferProductTemp.sort((a, b) => b.orderDate - a.orderDate)
      setStockTransferList(stockTransferProductTemp);
    }
  }, [currOutletId, rev_date, rev_date1, stockTransfersProduct])

  useEffect(() => {
    if (selectedStockTransferEdit) {
      // insert info

      setEditMode(false);

      setPoId(selectedStockTransferEdit.stId);
      setPoStatus(selectedStockTransferEdit.status);
      setSelectedSourceOutletId(selectedStockTransferEdit.sourceOutletId);
      setSelectedTargetOutletId(selectedStockTransferEdit.targetOutletId);
      setDate(selectedStockTransferEdit.estimatedArrivalDate);
      setCreatedDate(selectedStockTransferEdit.createdAt);


      if (selectedStockTransferEdit.stItems) {
        setPoItems(selectedStockTransferEdit.stItems);

        // delay the state update
        setTimeout(() => {
          setPoItems(selectedStockTransferEdit.stItems);
        }, 100);
      }
    }
    else {
      // designed to always mounted, thus need clear manually...

      if (allOutlets && allOutlets.length > 0) {
        setEditMode(false);

        if (stockTransfersProduct.length > 0) {
          // setPoId(`ST${moment().format('MMM').toUpperCase() + moment().format('YY') + (stockTransfers.length + 1).toString().padStart(4, '0')}`);
          setPoId(`ST${(stockTransfersProduct.length + 1).toString().padStart(4, '0')}`);
        }
        setPoStatus(STOCK_TRANSFER_STATUS.CREATED);
        setSelectedSourceOutletId(allOutlets[0].uniqueId);
        setSelectedTargetOutletId(allOutlets[0].uniqueId);
        setDate(Date.now());

        if (outletItems.length > 0) {
          setPoItems([
            {
              outletSupplyItemId: outletItems[0].uniqueId,
              name: outletItems[0].name,
              sku: outletItems[0].sku,
              unit: '',
              skuMerchant: outletItems[0].skuMerchant,
              quantity: outletItems[0].stockCount || 0,
              transferQuantity: 0,
              balance: 0,
              price: outletItems[0].price,
              totalPrice: 0,

              supplyItem: outletItems[0],
            }
          ]);
        }
      }

      // if (outletSupplyItems.length > 0 && Object.keys(allOutletsSupplyItemsDict).length > 0) {
      // if (outletSupplyItems.length > 0) {
      //   setPoItems([
      //     {
      //       outletSupplyItemId: outletSupplyItems[0].uniqueId,
      //       name: outletSupplyItems[0].name,
      //       sku: outletSupplyItems[0].sku,
      //       quantity: outletSupplyItems[0].quantity,
      //       transferQuantity: 0,
      //       price: outletSupplyItems[0].price,
      //       totalPrice: 0,
      //     }
      //   ]);
      // }
      // else {
      //   setPoItems([
      //     {
      //       outletSupplyItemId: '',
      //       name: '',
      //       sku: '',
      //       quantity: 0,
      //       transferQuantity: 0,
      //       price: 0,
      //       totalPrice: 0,
      //     }
      //   ]);
      // }
    }
  }, [selectedStockTransferEdit, addStockTransfer, allOutlets]);

  useEffect(() => {
    if (selectedStockTransferEdit === null && stockTransfersProduct.length > 0) {
      // setPoId(`ST${moment().format('MMM').toUpperCase() + moment().format('YY') + (stockTransfers.length + 1).toString().padStart(4, '0')}`);
      setPoId(`ST${(stockTransfersProduct.length + 1).toString().padStart(4, '0')}`);
    }
  }, [stockTransfersProduct]);

  useEffect(() => {
    if (outletItems.length > 0) {
      // setPoItems([
      //   {
      //     outletSupplyItemId: outletSupplyItems[0].uniqueId,
      //     name: outletSupplyItems[0].name,
      //     sku: outletSupplyItems[0].sku,
      //     skuMerchant: outletSupplyItems[0].skuMerchant,
      //     quantity: outletSupplyItems[0].quantity,
      //     transferQuantity: outletSupplyItems[0].transferQuantity,
      //     balance: outletSupplyItems[0].balance,
      //     price: outletSupplyItems[0].price,
      //     totalPrice: 0,

      //     supplyItem: supplyItems[0],
      //   }
      // ]);

      // delay the state updating
      // setTimeout(() => {
      //   setPoItems([
      //     {
      //       outletSupplyItemId: outletSupplyItems[0].uniqueId,
      //       name: outletSupplyItems[0].name,
      //       sku: outletSupplyItems[0].sku,
      //       skuMerchant: outletSupplyItems[0].skuMerchant,
      //       quantity: outletSupplyItems[0].quantity,
      //       transferQuantity: outletSupplyItems[0].transferQuantity,
      //       balance: outletSupplyItems[0].balance,
      //       price: outletSupplyItems[0].price,
      //       totalPrice: 0,

      //       supplyItem: supplyItems[0],
      //     }
      //   ]);
      // }, 500);
    }
    else {
      setPoItems([
        {
          outletSupplyItemId: '',
          name: '',
          sku: '',
          unit: '',
          skuMerchant: '',
          quantity: 0,
          transferQuantity: 0,
          price: 0,
          totalPrice: 0,

          supplyItem: null,
        }
      ]);
    }
  }, [selectedSourceOutletId]);

  useEffect(() => {
    setOutletSupplyItems(allOutletsSupplyItems.filter(outletSupplyItem => {
      if (outletSupplyItem.outletId === selectedSourceOutletId && outletSupplyItem.quantity > 0) {
        return true;
      }
    }));
  }, [allOutletsSupplyItems, selectedSourceOutletId]);

  useEffect(() => {
    const outletDropdownListTemp = allOutlets.map(outlet => ({ label: outlet.name, value: outlet.uniqueId }))

    setTargetOutletDropdownList(outletDropdownListTemp);

    if (selectedTargetOutletId === '' && allOutlets.length > 0) {
      setSelectedTargetOutletId(allOutlets[0].uniqueId);
      setSelectedSourceOutletId(allOutlets[0].uniqueId);
    }
  }, [allOutlets]);

  useEffect(() => {
    setOutletSupplyItemDropdownList(outletItems.map(outletSupplyItem => {
      // if (selectedSupplierId === supplyItem.supplierId) {
      //   return { label: supplyItem.name, value: supplyItem.uniqueId };
      // }      

      return { label: outletSupplyItem.name, value: outletSupplyItem.uniqueId };
    }));

    if (outletItems.length > 0 &&
      poItems.length === 1 &&
      poItems[0].outletSupplyItemId === '') {
      setPoItems([
        {
          outletSupplyItemId: outletItems[0].uniqueId,
          name: outletItems[0].name,
          sku: outletItems[0].sku,
          unit: '',
          skuMerchant: outletItems[0].skuMerchant,
          quantity: outletItems[0].stockCount || 0,
          transferQuantity: 0,
          balance: 0,
          price: outletItems[0].price,
          totalPrice: 0,

          supplyItem: outletItems[0],
        }
      ]);
    }
    else if (
      poItems[0].outletSupplyItemId !== ''
      // &&
      // Object.keys(allOutletsSupplyItemsDict).length > 0
    ) {
      if (selectedSourceOutletIdPrev.length > 0 &&
        selectedSourceOutletIdPrev !== selectedSourceOutletId) {
        // reset current outlet supply items

        setPoItems([
          {
            outletSupplyItemId: outletItems[0].uniqueId,
            name: outletItems[0].name,
            unit: '',
            sku: outletItems[0].sku,
            skuMerchant: outletItems[0].skuMerchant,
            quantity: outletItems[0].stockCount || 0,
            transferQuantity: 0,
            balance: 0,
            price: outletItems[0].price,
            totalPrice: 0,

            supplyItem: outletItems[0],
          }
        ]);

        // disabled first, outletSupplyItems might slow to retrieve
        // setSelectedSourceOutletIdPrev(selectedSourceOutletId);
      }
      else {
        var poItemsTemp = [
          ...poItems,
        ];

        for (var i = 0; i < poItemsTemp.length; i++) {
          poItemsTemp[i] = {
            ...poItemsTemp[i],
            // quantity: allOutletsSupplyItemsDict[poItemsTemp[i].outletSupplyItemId] ? allOutletsSupplyItemsDict[poItemsTemp[i].outletSupplyItemId].quantity : 0, // check if the supply item sku for this outlet existed | might changed in real time
            // price: allOutletsSupplyItemsDict[poItemsTemp[i].outletSupplyItemId] ? allOutletsSupplyItemsDict[poItemsTemp[i].outletSupplyItemId].price : 0, // might changed in real time
            quantity: allOutletsItems.find(item => item.uniqueId === poItemsTemp[i].outletSupplyItemId) ? (allOutletsItems.find(item => item.uniqueId === poItemsTemp[i].outletSupplyItemId).stockCount || 0) : 0,
            price: allOutletsItems.find(item => item.uniqueId === poItemsTemp[i].outletSupplyItemId) ? (allOutletsItems.find(item => item.uniqueId === poItemsTemp[i].outletSupplyItemId).price || 0) : 0,
          };
        }

        setPoItems(poItemsTemp);
      }
    }
  }, [
    // outletSupplyItems,
    // allOutletsSupplyItemsDict,
    // supplyItems,
    outletItems,
    allOutletsItems,
    selectedSourceOutletIdPrev
  ]);

  useEffect(() => {
    // console.log('balance');
    // console.log(poItems.reduce((accum, poItem) => accum + poItem.balance, 0));
    setSubtotal(poItems.reduce((accum, poItem) => accum + poItem.balance, 0));
  }, [poItems]);

  useEffect(() => {
    // console.log('subtotal');
    // console.log(poItems.reduce((accum, poItem) => accum + poItem.totalPrice, 0));
    setSubtotal(poItems.reduce((accum, poItem) => accum + poItem.totalPrice, 0));
  }, [poItems]);

  // useEffect(() => {
  //   // console.log('taxTotal');
  //   // console.log(subtotal * selectedSupplier.taxRate);
  //   setTaxTotal(subtotal * selectedSupplier.taxRate);
  // }, [subtotal]);

  useEffect(() => {
    // console.log('finalTotal');
    // console.log((subtotal - discountTotal) + taxTotal);
    setFinalTotal((subtotal - discountTotal) + taxTotal);
  }, [subtotal, discountTotal, taxTotal]);

  useEffect(() => {
    requestStoragePermission();

    setPoId(nanoid());
  }, []);


  // useEffect(() => {
  //   if (poItems.find(poItem => poItem.outletSupplyItemId === poItem.outletSupplyItemId)){
  //     Alert.alert(
  //       'Error',
  //       'Same Supply Item.',
  //       [
  //         {
  //           text: "OK", onPress: () => {
  //           }
  //         }
  //       ],
  //     );
  //   return;
  //   }
  // }, [poItems]);

  //////////////////////////////////////////////////////////////////////

  const setState = () => { };

  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false
  // });

  navigation.setOptions({
    headerLeft: () => (
      <View
        style={[
          styles.headerLeftStyle,
          {
            width: windowWidth * 0.17,
          },
        ]}
      >
        <img src={headerLogo} width={124} height={26} />
        {/* <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        /> */}
      </View>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            justifyContent: "center",
            alignItems: "center",
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            //width:  "55%",
          },
          Dimensions.get("screen").width <= 768
            ? { right: Dimensions.get("screen").width * 0.12 }
            : {},
        ]}
      >
        <Text
          style={{
            fontSize: 24,
            // lineHeight: 25,
            textAlign: "center",
            fontFamily: "NunitoSans-Bold",
            color: Colors.whiteColor,
            opacity: 1,
          }}
        >
          Work Order List
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        {/* {console.log('edward test')} */}
        {/* {console.log(outletSelectDropdownView)} */}
        {outletSelectDropdownView && outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: "white",
            width: 0.5,
            height: Dimensions.get("screen").height * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
            // borderWidth: 1
          }}
        ></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate("General Settings - KooDoo BackOffice")
            }
          }}
          style={{ flexDirection: "row", alignItems: "center" }}
        >
          <Text
            style={{
              fontFamily: "NunitoSans-SemiBold",
              fontSize: 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }}
          >
            {userName}
          </Text>
          <View
            style={{
              //backgroundColor: 'red',
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "white",
            }}
          >
            <img
              src={personicon}
              width={windowHeight * 0.035}
              height={windowHeight * 0.035}
            />
            {/* <Image
              style={{
                width: windowHeight * 0.05,
              height: windowHeight * 0.05,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            /> */}
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  // componentDidMount = () => {
  //   // setInterval(() => {
  //   //   getStockOrder()
  //   //   getStockTransfer()
  //   //   getLowStock()
  //   // }, 1000);
  //   getStockOrder()
  //   getStockTransfer()
  //   getLowStock()
  // }

  // async componentWillMount = () => {
  //   await requestStoragePermission()
  // }

  const requestStoragePermission = async () => {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
        {
          title: "KooDoo Merchant Storage Permission",
          message:
            "KooDoo Merchant App needs access to your storage ",
          buttonNegative: "Cancel",
          buttonPositive: "OK"
        }
      );
      if (granted === PermissionsAndroid.RESULTS.GRANTED) {
        // console.log("Storage permission granted");
      } else {
        // console.log("Storage permission denied");
      }
    } catch (err) {
      console.warn(err);
    }
  }

  // const importSelectFile = async () => {
  //   try {
  //     const res = await DocumentPicker.pickSingle({
  //       type: [DocumentPicker.types.xlsx],
  //     });

  //     // console.log(res)

  //   } catch (err) {

  //     if (DocumentPicker.isCancel(err)) {

  //     } else {
  //       throw err;
  //     }
  //   }
  // }

  const convertDataToExcelFormat = () => {
    var excelData = [];

    for (var i = 0; i < stockTransferList.length; i++) {
      for (var j = 0; j < stockTransferList[i].stItems.length; j++) {
        var excelRow = {
          'Transfer Product': stockTransferList[i].stItems[j].name,
          'In Stock': stockTransferList[i].stItems[j].quantity ? stockTransferList[i].stItems[j].quantity.toFixed(2) : '0',
          'Transfer Quantity': stockTransferList[i].stItems[j].transferQuantity ? stockTransferList[i].stItems[j].transferQuantity : '0',
          'Balance': stockTransferList[i].stItems[j].balance ? stockTransferList[i].stItems[j].balance.toFixed(2) : '0',
          'Stock Transfer ID': stockTransferList[i].stId,
          'Created Date': moment(stockTransferList[i].orderDate).format('DD/MM/YYYY'),
          'From': stockTransferList[i].sourceOutletName,
          'To': stockTransferList[i].targetOutletName,
          'Status': stockTransferList[i].status,

        };

        excelData.push(excelRow);
      }
    }



    // console.log('excelData');
    // console.log(excelData);

    return excelData;
  };

  // const handleExportExcel = () => {
  //   const excelData = convertDataToExcelFormat();

  //   var ws = XLSX.utils.json_to_sheet(excelData);
  //   var wb = XLSX.utils.book_new();

  //   XLSX.utils.book_append_sheet(wb, ws, "KooDoo Transfer Report");
  //   const wbout = XLSX.write(wb, { type: 'binary', bookType: "xlsx" });
  //   RNFS.writeFile(`${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : RNFS.DownloadDirectoryPath}/KooDoo-Transfer-Report-${moment().format('YYYY-MM-DD-HH-mm-ss',)}.xlsx`, wbout, 'ascii').then((success) => {
  //     Alert.alert(
  //       'Success',
  //       `Exported to ${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : RNFS.DownloadDirectoryPath}/KooDoo-Transfer-Report-${moment().format('YYYY-MM-DD-HH-mm-ss',)}.xlsx`,
  //       [
  //         {
  //           text: 'OK',
  //           onPress: () => {
  //             CommonStore.update((s) => {
  //               s.isLoading = false;
  //             });
  //             setIsLoadingLocalExcel(false);
  //             setExportModal(false);

  //             logEventAnalytics({
  //               eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_SUCCESS_ALERT,
  //               eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_SUCCESS_ALERT,
  //             })
  //           },
  //         },
  //       ],
  //       { cancelable: false },
  //     );
  //     console.log('Success');
  //   }).catch((e) => {
  //     console.log('Error', e);
  //   });
  // };

  // const handleExportCsv = () => {

  //   const excelData = convertDataToExcelFormat();

  //   var ws = XLSX.utils.json_to_sheet(excelData);
  //   var wb = XLSX.utils.book_new();

  //   XLSX.utils.book_append_sheet(wb, ws, "KooDoo Transfer Report");
  //   const wbout = XLSX.write(wb, { type: 'binary', bookType: "csv" });
  //   RNFS.writeFile(`${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : RNFS.DownloadDirectoryPath}/KooDoo-Transfer-Report-${moment().format('YYYY-MM-DD-HH-mm-ss',)}.csv`, wbout, 'ascii').then((success) => {
  //     Alert.alert(
  //       'Success',
  //       `Exported to ${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : RNFS.DownloadDirectoryPath}/KooDoo-Transfer-Report-${moment().format('YYYY-MM-DD-HH-mm-ss',)}.csv`,
  //       [
  //         {
  //           text: 'OK',
  //           onPress: () => {
  //             CommonStore.update((s) => {
  //               s.isLoading = false;
  //             });
  //             setIsLoadingLocalCsv(false);
  //             setExportModal(false);

  //             logEventAnalytics({
  //               eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV_SUCCESS_ALERT,
  //               eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV_SUCCESS_ALERT,
  //             })
  //           },
  //         },
  //       ],
  //       { cancelable: false },
  //     );
  //     console.log('Success');
  //   }).catch((e) => {
  //     console.log('Error', e);
  //   });

  // };

  // function here

  const exportHtmlToPdf = async (item) => {
    var sourceOutlet = allOutlets.find(outlet => outlet.uniqueId === item.sourceOutletId);
    var targetOutlet = allOutlets.find(outlet => outlet.uniqueId === item.targetOutletId);
    var sourceOutletEmail = (sourceOutlet && sourceOutlet.email) ? sourceOutlet.email : 'N/A';
    var sourceOutletPhone = (sourceOutlet && sourceOutlet.phone) ? sourceOutlet.phone : 'N/A';
    var targetOutletEmail = (targetOutlet && targetOutlet.email) ? targetOutlet.email : 'N/A';
    var targetOutletPhone = (targetOutlet && targetOutlet.phone) ? targetOutlet.phone : 'N/A';

    var totalPriceForAll = 0;
    var totalTransferQtyForAll = 0;

    var itemStrList = [];
    for (var i = 0; i < item.stItems.length; i++) {
      // totalPriceForAll += item.stItems[i].totalPrice ? item.stItems[i].totalPrice : 0;
      totalTransferQtyForAll += item.stItems[i].transferQuantity ? item.stItems[i].transferQuantity : 0;

      // itemStrList.push(
      //   `<tr>
      //   <td>
      //     <b>${item.stItems[i].name || '-'}</b>
      //     <br />
      //     ${item.stItems[i].skuMerchant || '-'}
      //   </td>
      //   <td>
      //   ${item.stItems[i].quantity.toFixed(0)}
      //   </td>
      //   <td>
      //   ${item.stItems[i].transferQuantity ? item.stItems[i].transferQuantity.toFixed(0) : '0'}
      //   </td>
      //   <td>
      //   ${item.stItems[i].totalPrice ? item.stItems[i].totalPrice.toFixed(2) : '0.00'}
      //   </td>
      // </tr>`
      // );

      itemStrList.push(
        `<tr>
        <td>
          <b>${item.stItems[i].name || '-'}</b>
          <br />
          ${item.stItems[i].skuMerchant || '-'}
        </td>
        <td>
        ${item.stItems[i].transferQuantity ? item.stItems[i].transferQuantity.toFixed(0) : '0'}
        </td>
      </tr>`
      );
    }

    let replacedHtml = STOCK_TRANSFER_ORDER_AMOUNTS_HTML
      .replaceAll(KEYWORDS.MERCHANT_NAME, merchantName)
      .replaceAll(KEYWORDS.TITLE, 'Delivery Order')
      .replaceAll(KEYWORDS.HEADER_DATE, moment(item.createdAt).format('YYYY-MM-DD'))
      .replaceAll(KEYWORDS.HEADER_ID, item.stId)
      .replaceAll(KEYWORDS.OUTLET_NAME_FROM, item.sourceOutletName)
      .replaceAll(KEYWORDS.OUTLET_EMAIL_FROM, sourceOutletEmail)
      .replaceAll(KEYWORDS.OUTLET_PHONE_FROM, sourceOutletPhone)
      .replaceAll(KEYWORDS.OUTLET_NAME_TO, item.targetOutletName)
      .replaceAll(KEYWORDS.OUTLET_EMAIL_TO, targetOutletEmail)
      .replaceAll(KEYWORDS.OUTLET_PHONE_TO, targetOutletPhone)
      .replaceAll(KEYWORDS.REMARKS, item.remarks ? item.remarks : 'N/A')
      .replaceAll(KEYWORDS.ROW_ITEMS, itemStrList.join(''))
      .replaceAll(KEYWORDS.FOOTER_TOTAL, totalTransferQtyForAll.toFixed(0));

    // return replacedHtml;

    let options = {
      // html: STOCK_TRANSFER_ORDER_AMOUNTS_HTML,
      html: replacedHtml,
      fileName: `stock-transfer-do-${item && item.stId ? moment(item.createdAt).format('YYYY-MM-DD') + '-' + item.stId : item.stId}-${moment().valueOf()}`,
      // directory: Platform.OS === 'ios'
      //   ? RNFS.DocumentDirectoryPath
      //   : RNFS.DownloadDirectoryPath,
      directory: Platform.OS === 'ios'
        ? 'Documents'
        : 'Download',
    };

    // let file = await RNHTMLtoPDF.convert(options);

    // if (file && file.filePath) {
    //   Alert.alert(
    //     'Success',
    //     `Exported to ${file.filePath}`,
    //   );
    // }
  };

  const updateStatusToRejected = async (uniqueId, currentStatus) => {
    try {
      const newStatus = currentStatus !== 'REJECTED' ? 'REJECTED' : 'PENDING';
      await firebase.firestore().collection(Collections.WOList).doc(uniqueId).update({
        status: newStatus,
        updatedAt: Date.now(),
      });
      // Alert.alert('Success', `The work order has been ${newStatus === 'REJECTED' ? 'rejected' : 'restored to pending'}.`);
      window.confirm(`The work order has been ${newStatus === 'REJECTED' ? 'rejected' : 'restored to pending'}.`);
    } catch (error) {
      console.error('Error updating work order status:', error);
      // Alert.alert('Error', 'An error occurred while updating the work order status.');
      window.confirm('An error occurred while updating the work order status.');
    }
  };

  const updateStatusToComplete = async (item) => {
    try {
      const newStatus = item.status === 'PENDING' ? 'COMPLETED' : 'PENDING';
      await firebase.firestore().collection(Collections.WOList).doc(item.uniqueId).update({
        status: newStatus,
        updatedAt: Date.now(),
      });

      const userOrderStatus = item.status === 'PENDING' ? 'WOD' : 'WOIP';

      let woType = '';
      for (let i = 0; i < item.woItems.length; i++) {
        if (item.woItems[i].type) {
          woType = item.woItems[i].type;
          break;
        }
      }

      const updatePromises = item.woItems.map(woItem => {
        let promise = null;

        if (woType === 'PO_STOCK') {
          promise = new Promise((resolve) => resolve());
        }
        else {
          promise = firebase.firestore().collection(Collections.UserOrder).doc(woItem.userOrderId).update({
            woStatus: userOrderStatus, // Update woStatus accordingly
            updatedAt: Date.now(),
          });
        }

        return promise;
      });

      // Wait for all updates to complete
      await Promise.all(updatePromises);
      // Alert.alert('Success', `The work order has been ${newStatus === 'COMPLETED' ? 'marked as completed.' : 'restored to pending'}.`,
      //   [
      //     {
      //       cancelable: false
      //     },
      //     {
      //       text: "OK",
      //       onPress: () => {
      //         setStockTransfer(true);
      //         setAddStockTransfer(false);
      //         setSelectedWorkOrder([]);
      //       }
      //     }
      //   ]);

      window.confirm(`The work order has been ${newStatus === 'COMPLETED' ? 'marked as completed.' : 'restored to pending'}.`);
      setStockTransfer(true);
      setAddStockTransfer(false);
      setSelectedWorkOrder([]);

    } catch (error) {
      console.error('Error updating work order status:', error);
      // Alert.alert('Error', 'An error occurred while marking the work order as completed.');
      window.confirm('An error occurred while marking the work order as completed.');
    }
  };

  const filterOrders = (param) => {
    if (param.value === 1) {
      CommonStore.update((s) => {
        s.woList = s.woListTemp || s.woList;
      });
      return;
    }

    if (param.value === 2) {
      CommonStore.update((s) => {
        s.woList = s.woListTemp.filter((wo) => wo.status === 'PENDING');
      });
    }

    if (param.value === 3) {
      CommonStore.update((s) => {
        s.woList = s.woListTemp.filter((wo) => wo.status === 'COMPLETED');
      });
    }

    if (param.value === 4) {
      CommonStore.update((s) => {
        s.woList = s.woListTemp.filter((wo) => wo.status === 'REJECTED');
      });
    }
  };

  const renderWO = ({ item, index }) => {
    let woItemIdHumanList = item.woItems.map(woItem => woItem.userOrderIdHuman).join(',\n');

    return (
      <TouchableOpacity
        disabled
        onPress={() => { }}
        style={{
          flexDirection: 'row',
          paddingVertical: 10,
          paddingHorizontal: 6,
          alignItems: 'center',
          borderBottomWidth: 0.5,
          // background: 'red',
        }}>
        <View style={{
          flexDirection: 'row', alignItems: 'center', width: '100%'
        }}>
          <View style={{ width: '20%', }}>
            <Text style={{
              fontSize: switchMerchant ? 10 : 13, fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 5,
            }}>
              {`${item.woId}\n(${woItemIdHumanList})`}
            </Text>
          </View>
          <View style={{ width: '15%', }}>
            <Text style={{
              fontSize: switchMerchant ? 10 : 13, fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 5,
            }}>
              {moment(item.orderDate).format('DD MMM YYYY')}
            </Text>
          </View>
          <View style={{ width: '30%', }}>
            <Text style={{
              fontSize: switchMerchant ? 10 : 13, fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 5,
            }}>
              {/* {item.targetOutletName.includes(item.sourceOutletName)
                ? 'Same Store'
                : `${item.sourceOutletName} to ${item.targetOutletName.join(', ')}`} */}
              {'Purchase Order'}
            </Text>
          </View>
          {/* <View style={{ width: '16%', padding: 2 }}>
          <Text style={{ fontSize: switchMerchant ? 10 : 13 }}>{item.remark}</Text>
        </View> */}
          <View style={{ width: '15%', }}>
            <Text style={{
              fontSize: switchMerchant ? 10 : 13, fontFamily: 'NunitoSans-Regular',
              textAlign: 'left',
              paddingLeft: 5,
            }}>
              {item.status}
            </Text>
          </View>
          <View style={{ width: '20%', flexDirection: 'row', }}>
            <TouchableOpacity onPress={() => {
              setStockTransfer(false);
              setAddStockTransfer(true);
              setSelectedWorkOrder(item);
              console.log(item)
            }}
              style={{
                justifyContent: 'center',
                flexDirection: 'row',
                borderWidth: 1,
                borderColor: Colors.fieldtBgColor,
                backgroundColor: Colors.fieldtBgColor,
                borderRadius: 5,
                //width: 160,
                paddingHorizontal: switchMerchant ? 5 : 10,
                height: switchMerchant ? 35 : 40,
                alignItems: 'center',
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 1,
                zIndex: -1,
                marginRight: 10,
              }}>
              <Text style={{ fontSize: switchMerchant ? 10 : 13, color: Colors.primaryColor, }}>
                VIEW
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                const actionText = item.status === 'REJECTED' ? 'restore' : 'reject';
                Alert.alert(
                  `Confirm ${actionText.charAt(0).toUpperCase() + actionText.slice(1)}`,
                  `Are you sure you want to ${actionText} this work order?`,
                  [
                    {
                      text: "Cancel",
                      style: "cancel"
                    },
                    {
                      text: "OK",
                      onPress: () => {
                        // Call the function to update the status of the selected work order
                        updateStatusToRejected(item.uniqueId, item.status);
                      }
                    }
                  ]
                );
              }}
              style={{
                justifyContent: 'center',
                flexDirection: 'row',
                borderWidth: 1,
                borderColor: Colors.fieldtBgColor,
                backgroundColor: Colors.fieldtBgColor,
                borderRadius: 5,
                //width: 160,
                paddingHorizontal: switchMerchant ? 5 : 10,
                height: switchMerchant ? 35 : 40,
                alignItems: 'center',
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 1,
                zIndex: -1,
                marginLeft: 10,
              }}>
              <Text style={{ fontSize: switchMerchant ? 10 : 13, color: item.status === 'REJECTED' ? Colors.primaryColor : "red" }}>
                {item.status === 'REJECTED' ? 'UNDO' : 'REJECT'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </TouchableOpacity>
    )
  }

  const renderWorkOrderItemList = ({ item, index }) => {
    console.log(item);
    console.log('stop');
    return (
      <View>

        <View
          key={index}
          style={{
            backgroundColor: '#ffffff',
            flexDirection: 'row',
            paddingVertical: 20,
            paddingHorizontal: 10,
            //paddingBottom: 100,
            borderBottomWidth: StyleSheet.hairlineWidth,
            borderBottomColor: '#c4c4c4',
            alignItems: 'center',
          }}>


          <View style={{ width: '20%' }}>
            <View style={{
              width: '100%',
              height: 35,
              backgroundColor: 'white',
              //alignItems: 'center',
              justifyContent: 'center',
              paddingLeft: 0,
            }}>
              <Text style={{ width: '100%', color: "black", marginLeft: 0, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
                {item.osiName || '-'}
              </Text>
            </View>
          </View>


          <Text style={{ width: '14%', color: "black", marginLeft: 10, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
            {item.osiSkuM || '-'}
          </Text>

          <Text style={{ width: '9%', color: "black", fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
            {item.osiUnit || '-'}
          </Text>

          <Text style={{ width: '9%', color: "black", fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
            {item.osiQuantity.toFixed(2)}
          </Text>

          <Text style={{ width: '11%', color: "black", fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
            {Math.abs(item.totalUsage).toFixed(2)}
          </Text>

          <Text style={{ width: '13%', color: "black", fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
            {(item.osiQuantity + Math.abs(item.totalUsage)).toFixed(2)}
          </Text>

          <Text style={{ width: '10%', color: "black", fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
            {item.osiPrice.toFixed(2)}
          </Text>

          <Text style={{ width: '11%', color: "black", fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
            {item.totalOsiP ? item.totalOsiP.toFixed(2) : '0.00'}
          </Text>

          {/* <TouchableOpacity
          style={{ marginRight: 20, left: Platform.OS === 'ios' ? 0 : '-10%' }}
          onPress={() => {
            setPoItems([
              ...poItems.slice(0, index),
              ...poItems.slice(index + 1),
            ]);
          }}>
          <Icon name="trash-2" size={switchMerchant ? 15 : 20} color="#eb3446" />
        </TouchableOpacity> */}

        </View>
      </View>
    )
  };

  return (
    <UserIdleWrapper disabled={!isMounted}>
      <View style={[styles.container, !isTablet() ? {
        transform: [
          { scaleX: 1 },
          { scaleY: 1 },
        ],
      } : {},
      {
        ...getTransformForScreenInsideNavigation(),
      }]}>
        <View style={[styles.sidebar, !isTablet() ? {
          width: Dimensions.get('screen').width * 0.08,
        } : {}, switchMerchant ? {
          // width: '10%'
        } : {},
        {
          width: windowWidth * 0.08,
        }]}>
          <SideBar navigation={props.navigation} selectedTab={3} expandInventory={true} />
        </View>
        <ScrollView
          scrollEnabled={switchMerchant}
          horizontal={true}
        // contentContainerStyle={{backgroundColor: 'red'}}
        // style={{ backgroundColor: 'red'}}
        >
          <View style={[styles.content, {
            padding: 16,
            width: Dimensions.get('screen').width * (1 - Styles.sideBarWidth),
          }]}>

            <Modal
              supportedOrientations={['portrait', 'landscape']}
              style={{
                // flex: 1
              }}
              visible={doModal}
              transparent={true}
              animationType={'fade'}
            >
              <View style={{
                flex: 1,
                backgroundColor: Colors.modalBgColor,
                alignItems: 'center',
                justifyContent: 'center',

                top:
                  Platform.OS === 'android'
                    ? 0
                    : keyboardHeight > 0
                      ? -keyboardHeight * 0.45
                      : 0,
              }}>
                <View style={{
                  height: Dimensions.get('screen').width * 0.3,
                  width: Dimensions.get('screen').width * 0.4,
                  backgroundColor: Colors.whiteColor,
                  borderRadius: 12,
                  padding: Dimensions.get('screen').width * 0.03,
                  alignItems: 'center',
                  justifyContent: 'center',

                  // top: keyboardHeight > 0 ? -keyboardHeight * 0.45 : 0,
                  ...getTransformForModalInsideNavigation(),
                }}>
                  <TouchableOpacity
                    disabled={isLoading}
                    style={{
                      position: 'absolute',
                      right: Dimensions.get('screen').width * 0.02,
                      top: Dimensions.get('screen').width * 0.02,

                      elevation: 1000,
                      zIndex: 1000,
                    }}
                    onPress={() => {
                      setDoModal(false);
                    }}>
                    <AntDesign name="closecircle" size={switchMerchant ? 15 : 25} color={Colors.fieldtTxtColor} />
                  </TouchableOpacity>
                  <View style={{
                    alignItems: 'center',
                    top: '20%',
                    position: 'absolute',
                  }}>
                    <Text style={{
                      fontFamily: 'NunitoSans-Bold',
                      textAlign: 'center',
                      fontSize: switchMerchant ? 18 : 20,
                    }}>
                      Download Delivery Order
                    </Text>
                  </View>
                  <View style={{ top: switchMerchant ? '14%' : '10%', }}>
                    <Text style={{ fontSize: switchMerchant ? 10 : 16, fontFamily: 'NunitoSans-Bold' }}>Email Address:</Text>
                    <TextInput
                      underlineColorAndroid={Colors.fieldtBgColor}
                      style={{
                        backgroundColor: Colors.fieldtBgColor,
                        width: switchMerchant ? 240 : 370,
                        height: switchMerchant ? 35 : 50,
                        borderRadius: 5,
                        padding: 5,
                        marginVertical: 5,
                        borderWidth: 1,
                        borderColor: '#E5E5E5',
                        paddingLeft: 10,
                        fontSize: switchMerchant ? 10 : 16,
                      }}
                      autoCapitalize='none'
                      placeholderStyle={{ padding: 5 }}
                      placeholder="Enter your email"
                      placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                      onChangeText={(text) => {
                        setExportEmail(text);
                      }}
                      value={exportEmail}
                    />
                    <Text style={{ fontSize: switchMerchant ? 10 : 16, fontFamily: 'NunitoSans-Bold', marginTop: 15 }}>Send As:</Text>


                    <View style={{
                      alignItems: 'center',
                      justifyContent: 'center',
                      //top: '10%',
                      flexDirection: 'row',
                      marginTop: 10,
                    }}>
                      <TouchableOpacity
                        disabled={isLoading}
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#4E9F7D',
                          borderRadius: 5,
                          width: switchMerchant ? 100 : 120,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 35 : 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                          marginRight: 15,
                        }}
                        onPress={async () => {
                          if (exportEmail.length > 0) {
                            if (doItem && doItem.uniqueId) {
                              CommonStore.update(s => {
                                s.isLoading = true;
                              });

                              var body = {
                                emailToSent: exportEmail,
                                htmlContent: await exportHtmlToPdf(doItem),
                                emailTitle: `KooDoo Stock Transfer DO - ${doItem && doItem.stId ? moment(doItem.createdAt).format('YYYY-MM-DD') + ' - ' + doItem.stId : doItem.stId}.pdf`,
                                fileName: `stock-transfer-do-${doItem && doItem.stId ? moment(doItem.createdAt).format('YYYY-MM-DD') + '-' + doItem.stId : doItem.stId}.pdf`,
                              };

                              ApiClient.POST(API.sendDeliveryOrder, body).then((result) => {
                                if (result && result.status === 'success') {
                                  Alert.alert('Success', 'Delivery order will be sent to the email address shortly');

                                  CommonStore.update(s => {
                                    s.isLoading = false;
                                  });

                                  setDoModal(false);
                                }
                                else {
                                  Alert.alert(
                                    'Error',
                                    'Failed to send delivery order',
                                    [
                                      { text: "OK", onPress: () => { } }
                                    ],
                                    { cancelable: false },
                                  );

                                  CommonStore.update(s => {
                                    s.isLoading = false;
                                  });

                                  setDoModal(false);
                                }
                              });
                            }
                            else {
                              Alert.alert('Info', 'Invalid stock transfer to send.');
                            }
                          }
                          else {
                            Alert.alert('Info', 'Invalid email address');
                          }
                        }}>
                        {
                          isLoadingExcel
                            ?
                            <ActivityIndicator
                              size={'small'}
                              color={Colors.whiteColor}
                            />
                            :
                            <Text style={{
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                            }}>
                              SEND</Text>

                        }
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              </View>
            </Modal>

            <Modal
              supportedOrientations={['portrait', 'landscape']}
              style={{
                // flex: 1
              }}
              visible={exportModal}
              transparent={true}
              animationType={'fade'}
            >
              <View style={{
                flex: 1,
                backgroundColor: Colors.modalBgColor,
                alignItems: 'center',
                justifyContent: 'center',

                top:
                  Platform.OS === 'android'
                    ? 0
                    : keyboardHeight > 0
                      ? -keyboardHeight * 0.45
                      : 0,
              }}>
                <View style={{
                  height: Dimensions.get('screen').width * 0.32,
                  width: Dimensions.get('screen').width * 0.4,
                  backgroundColor: Colors.whiteColor,
                  borderRadius: 12,
                  padding: Dimensions.get('screen').width * 0.03,
                  alignItems: 'center',
                  justifyContent: 'center',

                  // top: keyboardHeight > 0 ? -keyboardHeight * 0.45 : 0,
                  ...getTransformForModalInsideNavigation(),
                }}>
                  <TouchableOpacity
                    disabled={isLoading}
                    style={{
                      position: 'absolute',
                      right: Dimensions.get('screen').width * 0.02,
                      top: Dimensions.get('screen').width * 0.02,

                      elevation: 1000,
                      zIndex: 1000,
                    }}
                    onPress={() => {
                      setExportModal(false);

                      logEventAnalytics({
                        eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_C_CLOSE,
                        eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_C_CLOSE,
                      })
                    }}>
                    <AntDesign name="closecircle" size={switchMerchant ? 15 : 25} color={Colors.fieldtTxtColor} />
                  </TouchableOpacity>
                  <View style={{
                    alignItems: 'center',
                    top: 20,
                    position: 'absolute',
                  }}>
                    <Text style={{
                      fontFamily: 'NunitoSans-Bold',
                      textAlign: 'center',
                      fontSize: switchMerchant ? 18 : 20,
                    }}>
                      Download Report
                    </Text>
                  </View>
                  <View style={{ top: switchMerchant ? '14%' : '10%', }}>
                    <Text style={{ fontSize: switchMerchant ? 10 : 16, fontFamily: 'NunitoSans-Bold' }}>Email Address:</Text>
                    <TextInput
                      underlineColorAndroid={Colors.fieldtBgColor}
                      style={{
                        backgroundColor: Colors.fieldtBgColor,
                        width: switchMerchant ? 240 : 370,
                        height: switchMerchant ? 35 : 50,
                        borderRadius: 5,
                        padding: 5,
                        marginVertical: 5,
                        borderWidth: 1,
                        borderColor: '#E5E5E5',
                        paddingLeft: 10,
                        fontSize: switchMerchant ? 10 : 16,
                      }}
                      autoCapitalize='none'
                      placeholderStyle={{ padding: 5 }}
                      placeholder="Enter your email"
                      placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                      onChangeText={(text) => {
                        setExportEmail(text);

                        logEventAnalytics({
                          eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS,
                          eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS,
                        })
                      }}
                      value={exportEmail}
                    />
                    <Text style={{ fontSize: switchMerchant ? 10 : 16, fontFamily: 'NunitoSans-Bold', marginTop: 15 }}>Send As:</Text>


                    <View style={{
                      alignItems: 'center',
                      justifyContent: 'center',
                      //top: '10%',
                      flexDirection: 'row',
                      marginTop: 10,
                    }}>
                      <TouchableOpacity
                        disabled={isLoading}
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#4E9F7D',
                          borderRadius: 5,
                          width: switchMerchant ? 100 : 120,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 35 : 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                          marginRight: 15,
                        }}
                        onPress={() => {
                          if (exportEmail.length > 0) {
                            CommonStore.update(s => {
                              s.isLoading = true;
                            });
                            setIsLoadingExcel(true);
                            const excelData = convertDataToExcelFormat();

                            if (excelData && excelData.length > 0) {
                              generateEmailReport(
                                EMAIL_REPORT_TYPE.EXCEL,
                                excelData,
                                'KooDoo Stock Transfer Report',
                                'KooDoo Stock Transfer Report.xlsx',
                                `/merchant/${merchantId}/reports/${uuidv4()}.xlsx`,
                                exportEmail,
                                'KooDoo Stock Transfer Report',
                                'KooDoo Stock Transfer Report',
                                () => {
                                  CommonStore.update(s => {
                                    s.isLoading = false;
                                  });
                                  setIsLoadingExcel(false);

                                  Alert.alert('Success', 'Report will be sent to the email address shortly');

                                  setExportModal(false);
                                },
                              );
                            }
                            else {
                              Alert.alert('Info', 'Empty data to export.');

                              CommonStore.update(s => {
                                s.isLoading = false;
                              });
                              setIsLoadingExcel(false);
                            }
                          }
                          else {
                            Alert.alert('Info', 'Invalid email address');
                          }

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_SEND_AS_C_EXCEL,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_SEND_AS_C_EXCEL,
                          })
                        }}>
                        {
                          isLoadingExcel
                            ?
                            <ActivityIndicator
                              size={'small'}
                              color={Colors.whiteColor}
                            />
                            :
                            <Text style={{
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                            }}>
                              EXCEL</Text>

                        }
                      </TouchableOpacity>

                      <TouchableOpacity
                        disabled={isLoading}
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#4E9F7D',
                          borderRadius: 5,
                          width: switchMerchant ? 100 : 120,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 35 : 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        }}
                        onPress={() => {
                          if (exportEmail.length > 0) {
                            CommonStore.update(s => {
                              s.isLoading = true;
                            });
                            setIsLoadingCsv(true);
                            const csvData = convertArrayToCSV(stockTransferList);

                            generateEmailReport(
                              EMAIL_REPORT_TYPE.CSV,
                              csvData,
                              'KooDoo Stock Transfer Report',
                              'KooDoo Stock Transfer Report.csv',
                              `/merchant/${merchantId}/reports/${uuidv4()}.csv`,
                              exportEmail,
                              'KooDoo Stock Transfer Report',
                              'KooDoo Stock Transfer Report',
                              () => {
                                CommonStore.update(s => {
                                  s.isLoading = false;
                                });
                                setIsLoadingCsv(false);
                                Alert.alert('Success', 'Report will be sent to the email address shortly');

                                setExportModal(false);
                              },
                            );
                          }
                          else {
                            Alert.alert('Info', 'Invalid email address');
                          }

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_SEND_AS_C_CSV,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_SEND_AS_C_CSV,
                          })
                        }}>
                        {
                          isLoadingCsv
                            ?
                            <ActivityIndicator
                              size={'small'}
                              color={Colors.whiteColor}
                            />
                            :
                            <Text style={{
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                            }}>
                              CSV
                            </Text>
                        }
                      </TouchableOpacity>
                    </View>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-Bold',
                        marginTop: 15,
                      }}>
                      Download As:
                    </Text>
                    <View
                      style={{
                        alignItems: 'center',
                        justifyContent: 'center',
                        flexDirection: 'row',
                        marginTop: 10,
                      }}>
                      <TouchableOpacity
                        disabled={isLoading}
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#4E9F7D',
                          borderRadius: 5,
                          width: switchMerchant ? 100 : 120,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 35 : 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                          marginRight: 15,
                        }}
                        onPress={() => {
                          CommonStore.update((s) => {
                            s.isLoading = true;
                          });
                          setIsLoadingLocalExcel(true);
                          // handleExportExcel();

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL,
                          })
                        }}>
                        {isLoadingLocalExcel ? (
                          <ActivityIndicator
                            size={'small'}
                            color={Colors.whiteColor}
                          />
                        ) : (
                          <Text
                            style={{
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                            }}>
                            EXCEL
                          </Text>
                        )}
                      </TouchableOpacity>
                      <TouchableOpacity
                        disabled={isLoading}
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#4E9F7D',
                          borderRadius: 5,
                          width: switchMerchant ? 100 : 120,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 35 : 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        }}
                        onPress={() => {
                          CommonStore.update((s) => {
                            s.isLoading = true;
                          });
                          setIsLoadingLocalCsv(true);
                          // handleExportCsv();

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV,
                          })
                        }}>
                        {isLoadingLocalCsv ? (
                          <ActivityIndicator
                            size={'small'}
                            color={Colors.whiteColor}
                          />
                        ) : (
                          <Text
                            style={{
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                            }}>
                            CSV
                          </Text>
                        )}
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              </View>
            </Modal>

            <Modal
              style={{
                // flex: 1
              }}
              supportedOrientations={['portrait', 'landscape']}
              visible={importModal}
              transparent={true}
              animationType={'fade'}
            >
              <View style={styles.modalContainer}>
                <View style={[styles.modalViewImport, {
                  top: Platform.OS === 'ios' && keyboardHeight > 0 ? -keyboardHeight * 0.3 : 0,
                  ...getTransformForModalInsideNavigation(),
                }]}>
                  <TouchableOpacity
                    style={styles.closeButton}
                    onPress={() => {
                      // setState({ changeTable: false });
                      setImportModal(false);
                    }}>
                    <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
                  </TouchableOpacity>
                  <View style={{ padding: 10, margin: 30 }}>
                    <View style={[styles.modalTitle1, { justifyContent: 'center', alignItems: 'center' }]}>
                      <Text style={[styles.modalTitleText1, { fontSize: 16, fontWeight: '500' }]}>
                        Imported List
                      </Text>
                    </View>
                    {/* <View style={{
                    heigth: 70,
                    marginVertical: 10,
                    borderWidth: 1,
                    borderColor: '#E5E5E5',
                    height: '80%'
                  }}>
                  <Table borderStyle={{ borderWidth: 1 }}>
                    <Row data={TableData.tableHead} flexArr={[1, 2, 1, 1]} style={{}}/>
                    <TableWrapper style={{}}>
                    <Col data={TableData.tableTitle} style={{flex: 1}} heightArr={[28, 28, 28, 28]} textStyle={{}}/>
                    <Rows data={TableData.tableData} flexArr={[1, 2, 1, 1]} style={{height: 28}} textStyle={{textAlign: 'center'}}/>
                    </TableWrapper>
                  </Table>
                  </View> */}
                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                      <View
                        style={{
                          backgroundColor: Colors.primaryColor,
                          width: 150,
                          height: 40,
                          marginVertical: 15,
                          borderRadius: 5,
                          alignSelf: 'center',
                        }}>
                        <TouchableOpacity onPress={() => {
                          // importSelectFile()
                        }}>
                          <Text
                            style={{
                              color: Colors.whiteColor,
                              alignSelf: 'center',
                              marginVertical: 10,
                            }}>
                            IMPORT
                          </Text>
                        </TouchableOpacity>
                      </View>
                      <View style={{ flexDirection: 'row' }}>
                        <View
                          style={{
                            backgroundColor: Colors.whiteColor,
                            width: 150,
                            height: 40,
                            marginVertical: 15,
                            borderRadius: 5,
                            alignSelf: 'center',
                          }}>
                          <TouchableOpacity onPress={() => {
                            // setImportTemplate(false) 
                          }}>
                            <Text
                              style={{
                                color: Colors.primaryColor,
                                alignSelf: 'center',
                                marginVertical: 10,
                              }}>
                              CANCEL
                            </Text>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            backgroundColor: Colors.primaryColor,
                            width: 150,
                            height: 40,
                            marginVertical: 15,
                            borderRadius: 5,
                            alignSelf: 'center',
                          }}>
                          <TouchableOpacity onPress={() => { }}>
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                alignSelf: 'center',
                                marginVertical: 10,
                              }}>
                              SAVE
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>
                  </View>
                </View>
              </View>
            </Modal>

            <DateTimePickerModal
              isVisible={showDateTimePicker}
              mode={'date'}
              onConfirm={(text) => {
                setRev_date(moment(text).startOf('day'));
                setShowDateTimePicker(false);
              }}
              onCancel={() => {
                setShowDateTimePicker(false);
              }}
              maximumDate={moment(rev_date1).toDate()}
              date={moment(rev_date).toDate()}
            />

            <DateTimePickerModal
              isVisible={showDateTimePicker1}
              mode={'date'}
              onConfirm={(text) => {
                setRev_date1(moment(text).endOf('day'));
                setShowDateTimePicker1(false);
              }}
              onCancel={() => {
                setShowDateTimePicker1(false);
              }}
              minimumDate={moment(rev_date).toDate()}
              date={moment(rev_date1).toDate()}
            />

            {/* <View style={{ flexDirection: 'row', marginBottom: Platform.OS == 'ios' ? 0 : 10, width: '100%', justifyContent: 'space-between' }} >
            <View style={{}}>
              <View style={{ flexDirection: 'row', flex: 1, }}> */}
            {/* <TouchableOpacity style={styles.submitText} onPress={() => { importCSV() }}>
                  <View style={{ flexDirection: 'row' }}>
                    <Icon name="download" size={20} color={Colors.primaryColor} />
                    <Text style={{ marginLeft: 10, color: Colors.primaryColor }}>
                      Import
                  </Text>
                  </View>
                </TouchableOpacity> */}
            {/* <TouchableOpacity style={[styles.submitText, { height: 40 }]} onPress={() => { setExportModal(true) }}>
                  <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <Icon name="download" size={20} color={Colors.primaryColor} />
                    <Text style={{ marginLeft: 10, color: Colors.primaryColor }}>
                      Download
                    </Text>
                  </View>
                </TouchableOpacity> */}
            {/* <TouchableOpacity style={styles.submitText} onPress={() => { setExportModal(true) }}>
                  <View style={{ flexDirection: 'row' }}>
                    <Icon name="upload" size={20} color={Colors.primaryColor} />
                    <Text style={{ marginLeft: 10, color: Colors.primaryColor }}>
                      Export
                  </Text>
                  </View>
                </TouchableOpacity> */}
            {/* </View>
            </View> */}
            {/* <TextInput
              editable={!loading}
              underlineColorAndroid={Colors.whiteColor}
              clearButtonMode="while-editing"
              style={styles.textInput}
              placeholder="🔍  Search"
              onChangeText={(text) => {
                setState({ search: text.trim() });
              }}
              value={email}
            /> */}

            {/* <Ionicons
              name="search-outline"
              size={20}
              style={styles.searchIcon}
            />
            <TextInput
              editable={loading}
              clearButtonMode="while-editing"
              style={[styles.textInput, { fontFamily: "NunitoSans-Bold", }]}
              placeholder="Search"
              onChangeText={(text) => {
                setState({
                  search: text.trim(),
                });
              }}
              value={email}
            /> */}
            {/* 
            <View
              style={[{
                width: '28%',
                height: 40,

              }, !isTablet() ? {
                marginLeft: 0,
              } : {}]}>
              <View style={{
                width: 250,
                height: 40,
                backgroundColor: 'white',
                borderRadius: 10,
                // marginLeft: '53%',
                flexDirection: 'row',
                alignContent: 'center',
                alignItems: 'center',
                alignSelf: 'flex-end',

                // marginRight: Dimensions.get('screen').width * Styles.sideBarWidth,

                position: 'absolute',
                //right: '35%',

                shadowColor: '#000',
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 3,
              }}>
                <Icon name="search" size={18} color={Colors.primaryColor} style={{ marginLeft: 15 }} />
                <TextInput
                  editable={!loading}
                  underlineColorAndroid={Colors.whiteColor}
                  style={{
                    width: 250,
                    fontSize: 15,
                    fontFamily: 'NunitoSans-Regular',
                  }}
                  clearButtonMode="while-editing"
                  placeholder=" Search"
                  onChangeText={(text) => {
                    setSearch(text.trim());
                  }}
                  value={search}
                />
              </View>
            </View> */}

            {/* </View> */}

            {/*  <View
            style={{
              flexDirection: 'row',
              backgroundColor: Colors.highlightColor,
              padding: 12,
            }}>
            <TouchableOpacity
              onPress={() => {
                setState({
                  lowStockAlert: true,
                  purchaseOrder: false,
                  stockTransfer: false,
                  stockTake: false,
                  addPurchase: false,
                  editPurchase: false,
                  addStockTransfer: false,
                  addStockTake: false,
                });
              }}>
              <Text>Low Stock Alerts</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setState({
                  lowStockAlert: false,
                  purchaseOrder: true,
                  stockTransfer: false,
                  stockTake: false,
                  addPurchase: false,
                  editPurchase: false,
                  addStockTransfer: false,
                  addStockTake: false,
                });
              }}>
              <Text style={{ marginLeft: 30 }}>Purchase Order</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setState({
                  lowStockAlert: false,
                  purchaseOrder: false,
                  stockTransfer: true,
                  stockTake: false,
                  addPurchase: false,
                  editPurchase: false,
                  addStockTransfer: false,
                  addStockTake: false,
                });
              }}>
              <Text style={{ marginLeft: 30 }}>Stock Transfer</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setState({
                  lowStockAlert: false,
                  purchaseOrder: false,
                  stockTransfer: false,
                  stockTake: true,
                  addPurchase: false,
                  editPurchase: false,
                  addStockTransfer: false,
                  addStockTake: false,
                });
              }}>
              <Text style={{ marginLeft: 30 }}>Stock Take</Text>
            </TouchableOpacity>
          </View> */}


            {console.log(`stockTransfer`)}
            {console.log(stockTransfer)}

            {stockTransfer ? (
              // <ScrollView 
              //   scrollEnabled={switchMerchant}
              //   horizontal={true}
              //   contentContainerStyle={{ paddingRight: switchMerchant ? '7%' : 0, }}
              // >
              <ScrollView
                nestedScrollEnabled={true}
                contentContainerStyle={{ paddingBottom: switchMerchant ? '20%' : '5%', }}
                //style={{ backgroundColor: 'red' }}
                scrollEnabled={switchMerchant}
              >
                <View style={{
                  height: Platform.OS === 'ios' ? Dimensions.get('window').height - 200 : Dimensions.get('screen').height * 0.73,
                  // width: Dimensions.get('screen').width * 0.87,
                }}>

                  <View style={{ width: Dimensions.get('screen').width * 0.87, alignSelf: 'center' }}>
                    <View style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                    }}>
                      <View style={{ alignItems: 'center', flexDirection: 'row', }}>
                        <Text style={{  //fontSize: 30,
                          fontSize: switchMerchant ? 20 : 26,
                          fontFamily: 'NunitoSans-Bold'
                        }}>
                          {/* {stockTransferList.length} Stock Transfer */}
                          Work Order List
                        </Text>
                      </View>

                      <View style={{ flexDirection: 'row', }}>
                        {/* 2025-03-29 - hide first */}
                        {/* <View style={{ marginTop: 0, marginRight: 10, elevation: 5, }}>                          
                          <DropDownPicker
                            arrowColor={'black'}
                            arrowSize={switchMerchant ? 13 : 20}
                            arrowStyle={[
                              { fontWeight: 'bold' },
                              switchMerchant
                                ? {
                                  // bottom: '0%',
                                  height: '700%',
                                  // borderWidth: 1
                                }
                                : {},
                            ]}
                            containerStyle={[
                              { height: 40, },
                              switchMerchant
                                ? {
                                  height: 35,
                                  // borderWidth: 1
                                }
                                : {},
                            ]}
                            style={[
                              {
                                width: 190,
                                paddingVertical: 0,
                                backgroundColor: Colors.fieldtBgColor,
                                borderRadius: 10,
                                //marginTop: '2%',
                                shadowOffset: {
                                  width: 0,
                                  height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                ...(switchMerchant && windowWidth < 775 && windowHeight < 412 && {
                                  width: 150,
                                })
                              },
                              switchMerchant
                                ? {
                                  // height: '1%'
                                }
                                : {},
                            ]}
                            placeholderStyle={{ color: Colors.fieldtTxtColor }}
                            items={[
                              { label: 'All', value: 1 },
                              { label: 'Pending', value: 2 },
                              { label: 'Completed', value: 3 },
                              { label: 'Rejected', value: 4 },
                            ]}
                            itemStyle={{ justifyContent: 'flex-start', Color: "black", }}
                            placeholder={'All'}
                            onChangeItem={(item) => {
                              filterOrders(item);
                            }}
                            defaultValue={1}
                            dropDownMaxHeight={350}
                            dropDownStyle={{
                              width: 190,
                              height: 90,
                              backgroundColor: Colors.fieldtBgColor,
                              borderRadius: 10,
                              borderWidth: 1,
                              textAlign: 'left',
                              ...(switchMerchant && windowWidth < 775 && windowHeight < 412 && {
                                width: 150,
                              })
                            }}
                            globalTextStyle={{
                              fontFamily: 'NunitoSans-SemiBold',
                              fontSize: switchMerchant ? 10 : 16,
                              color: Colors.fontDark,
                              marginLeft: 5,
                            }}
                          />
                        </View> */}

                        <View style={{ flexDirection: 'row', }}>
                          <View
                            style={[{
                              height: 40,

                            }, !isTablet() ? {
                              marginLeft: 0,
                            } : {}]}>
                            <View style={{
                              width: Dimensions.get('screen').width <= 1024 ? 160 : 250,
                              height: switchMerchant ? 35 : 40,
                              backgroundColor: 'white',
                              borderRadius: 5,
                              // marginLeft: '53%',
                              flexDirection: 'row',
                              alignContent: 'center',
                              alignItems: 'center',
                              alignSelf: 'flex-end',
                              shadowColor: '#000',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 3,
                              borderWidth: 1,
                              borderColor: '#E5E5E5',
                            }}>
                              <Icon name="search" size={switchMerchant ? 13 : 18} color={Colors.primaryColor} style={{ marginLeft: 15 }} />
                              <TextInput
                                editable={!loading}
                                underlineColorAndroid={Colors.whiteColor}
                                style={{
                                  width: Dimensions.get('screen').width <= 1024 ? 140 : 220,
                                  fontSize: switchMerchant ? 10 : 15,
                                  fontFamily: 'NunitoSans-Regular',
                                  paddingLeft: 5,
                                  height: 45,
                                }}
                                clearButtonMode="while-editing"
                                placeholder="Search"
                                placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                onChangeText={(text) => {
                                  // setSearch(text.trim());
                                  // setList1(false);
                                  // setSearchList(true);
                                  // setSearch(text.trim());
                                  setSearch(text);

                                  logEventAnalytics({
                                    eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_TB_SEARCH,
                                    eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_TB_SEARCH,
                                  })
                                }}
                                value={search}
                              />
                            </View>
                          </View>
                        </View>
                      </View>
                    </View>
                    {/* <View style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
                      <View
                        style={[
                          {
                            //marginRight: Platform.OS === 'ios' ? 0 : 10,
                            // paddingLeft: 15,
                            paddingHorizontal: 15,
                            flexDirection: 'row',
                            alignItems: 'center',
                            borderRadius: 10,
                            paddingVertical: 10,
                            justifyContent: 'center',
                            backgroundColor: Colors.whiteColor,
                            shadowOpacity: 0,
                            shadowColor: '#000',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            marginBottom: 10,
                            marginTop: 20,
                          },
                        ]}>

                        <View
                          style={{ alignSelf: 'center', marginRight: 5 }}
                          onPress={() => {
                            setState({
                              pickerMode: 'date',
                              showDateTimePicker: true,
                            });
                          }}>
                          <GCalendar
                            width={switchMerchant ? 15 : 20}
                            height={switchMerchant ? 15 : 20}
                          />
                        </View>

                        <TouchableOpacity
                          onPress={() => {
                            setShowDateTimePicker(true);
                            setShowDateTimePicker1(false);

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_C_START_DATE,
                              eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_C_START_DATE,
                            })
                          }}
                          style={{
                            marginHorizontal: 4,
                          }}>
                          <Text
                            style={
                              switchMerchant
                                ? { fontSize: 10, fontFamily: 'NunitoSans-Regular' }
                                : { fontFamily: 'NunitoSans-Regular' }
                            }>
                            {moment(rev_date).format('DD MMM yyyy')}
                          </Text>
                        </TouchableOpacity>

                        <Text
                          style={
                            switchMerchant
                              ? { fontSize: 10, fontFamily: 'NunitoSans-Regular' }
                              : { fontFamily: 'NunitoSans-Regular' }
                          }>
                          -
                        </Text>

                        <TouchableOpacity
                          onPress={() => {
                            setShowDateTimePicker(false);
                            setShowDateTimePicker1(true);

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_C_END_DATE,
                              eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_C_END_DATE,
                            })
                          }}
                          style={{
                            marginHorizontal: 4,
                          }}>
                          <Text
                            style={
                              switchMerchant
                                ? { fontSize: 10, fontFamily: 'NunitoSans-Regular' }
                                : { fontFamily: 'NunitoSans-Regular' }
                            }>
                            {moment(rev_date1).format('DD MMM yyyy')}
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View> */}

                    <View style={{
                      backgroundColor: Colors.whiteColor,
                      width: Dimensions.get('screen').width * 0.87,
                      height: Dimensions.get('screen').height * 0.67,
                      marginTop: 10,
                      marginHorizontal: 30,
                      marginBottom: 30,
                      alignSelf: 'center',
                      borderRadius: 5,
                      shadowOpacity: 0,
                      shadowColor: '#000',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 3,
                      zIndex: -1,
                    }}>
                      {/* title (JJ's comment) */}
                      <View
                        style={{
                          borderTopLeftRadius: 10,
                          borderTopRightRadius: 10,
                          backgroundColor: '#ffffff',
                          flexDirection: 'row',
                          paddingVertical: 20,
                          paddingHorizontal: 6,
                          //marginTop: 10,
                          borderBottomWidth: StyleSheet.hairlineWidth,
                          elevation: 3,
                        }}>
                        <View style={{ width: '20%' }}>
                          <TouchableOpacity style={{ width: '100%', paddingLeft: 5, flexDirection: 'row' }}
                            onPress={() => {
                              if (
                                currWOListSort ===
                                WOLIST_SORT_FIELD_TYPE.DOCUMENT_NO_ASC
                              ) {
                                setCurrWOListSort(
                                  WOLIST_SORT_FIELD_TYPE.DOCUMENT_NO_DESC,
                                );
                              } else {
                                setCurrWOListSort(
                                  WOLIST_SORT_FIELD_TYPE.DOCUMENT_NO_ASC,
                                );
                              }
                            }}>
                            <Text style={{ fontSize: switchMerchant ? 10 : 14, alignSelf: 'center', fontFamily: 'NunitoSans-Bold', marginRight: 0 }}>
                              Document No
                            </Text>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  Colors.descriptionColor
                                  // productSort ===
                                  //   PRODUCT_SORT.PRODUCT_NAME_ASC
                                  //   ? Colors.secondaryColor
                                  //   : Colors.descriptionColor
                                } />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  Colors.descriptionColor
                                  // productSort ===
                                  //   PRODUCT_SORT.PRODUCT_NAME_ASC
                                  //   ? Colors.secondaryColor
                                  //   : Colors.descriptionColor
                                } />
                            </View>
                          </TouchableOpacity>
                        </View>

                        <View style={{ width: '15%' }}>
                          <TouchableOpacity style={{ width: '100%', paddingLeft: 5, flexDirection: 'row', }}
                            onPress={() => {
                              if (
                                currWOListSort ===
                                WOLIST_SORT_FIELD_TYPE.ISSUES_DATE_ASC
                              ) {
                                setCurrWOListSort(
                                  WOLIST_SORT_FIELD_TYPE.ISSUES_DATE_DESC,
                                );
                              } else {
                                setCurrWOListSort(
                                  WOLIST_SORT_FIELD_TYPE.ISSUES_DATE_ASC,
                                );
                              }
                            }}>
                            <Text style={{ fontSize: switchMerchant ? 10 : 14, alignSelf: 'center', fontFamily: 'NunitoSans-Bold', marginHorizontal: 0 }}>
                              Issues Date
                            </Text>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  Colors.descriptionColor
                                  // productSort ===
                                  //   PRODUCT_SORT.PRODUCT_NAME_ASC
                                  //   ? Colors.secondaryColor
                                  //   : Colors.descriptionColor
                                } />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  Colors.descriptionColor
                                  // productSort ===
                                  //   PRODUCT_SORT.PRODUCT_NAME_ASC
                                  //   ? Colors.secondaryColor
                                  //   : Colors.descriptionColor
                                } />
                            </View>
                          </TouchableOpacity>
                        </View>

                        <View style={{ width: '30%' }}>
                          <TouchableOpacity style={{ width: '100%', paddingLeft: 5, flexDirection: 'row', }}
                            onPress={() => {
                              if (
                                currWOListSort ===
                                WOLIST_SORT_FIELD_TYPE.DESCRIPTION_ASC
                              ) {
                                setCurrWOListSort(
                                  WOLIST_SORT_FIELD_TYPE.DESCRIPTION_DESC,
                                );
                              } else {
                                setCurrWOListSort(
                                  WOLIST_SORT_FIELD_TYPE.DESCRIPTION_ASC,
                                );
                              }
                            }}>
                            <Text style={{ fontSize: switchMerchant ? 10 : 14, alignSelf: 'center', fontFamily: 'NunitoSans-Bold', marginHorizontal: 0 }}>
                              Description
                            </Text>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  Colors.descriptionColor
                                  // productSort ===
                                  //   PRODUCT_SORT.PRODUCT_NAME_ASC
                                  //   ? Colors.secondaryColor
                                  //   : Colors.descriptionColor
                                } />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  Colors.descriptionColor
                                  // productSort ===
                                  //   PRODUCT_SORT.PRODUCT_NAME_ASC
                                  //   ? Colors.secondaryColor
                                  //   : Colors.descriptionColor
                                } />
                            </View>
                          </TouchableOpacity>
                        </View>

                        {/* <View style={{ width: '16.2%' }}>
                          <TouchableOpacity style={{ width: '60%', marginHorizontal: 4, flexDirection: 'row', }}>
                            <Text style={{ fontSize: switchMerchant ? 10 : 14, alignSelf: 'center', fontFamily: 'NunitoSans-Bold', marginHorizontal: 4 }}>
                              Remark
                            </Text>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  Colors.descriptionColor
                                  // productSort ===
                                  //   PRODUCT_SORT.PRODUCT_NAME_ASC
                                  //   ? Colors.secondaryColor
                                  //   : Colors.descriptionColor
                                } />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  Colors.descriptionColor
                                  // productSort ===
                                  //   PRODUCT_SORT.PRODUCT_NAME_ASC
                                  //   ? Colors.secondaryColor
                                  //   : Colors.descriptionColor
                                } />
                            </View>
                          </TouchableOpacity>
                        </View> */}

                        <View style={{ width: '15%' }}>
                          <TouchableOpacity style={{ width: '100%', paddingLeft: 5, flexDirection: 'row', }}
                            onPress={() => {
                              if (
                                currWOListSort ===
                                WOLIST_SORT_FIELD_TYPE.STATUS_ASC
                              ) {
                                setCurrWOListSort(
                                  WOLIST_SORT_FIELD_TYPE.STATUS_DESC,
                                );
                              } else {
                                setCurrWOListSort(
                                  WOLIST_SORT_FIELD_TYPE.STATUS_ASC,
                                );
                              }
                            }}>
                            <Text style={{ fontSize: switchMerchant ? 10 : 14, alignSelf: 'center', fontFamily: 'NunitoSans-Bold', marginLeft: 0, }}>
                              Status
                            </Text>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  Colors.descriptionColor
                                  // productSort ===
                                  //   PRODUCT_SORT.PRODUCT_NAME_ASC
                                  //   ? Colors.secondaryColor
                                  //   : Colors.descriptionColor
                                } />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  Colors.descriptionColor
                                  // productSort ===
                                  //   PRODUCT_SORT.PRODUCT_NAME_ASC
                                  //   ? Colors.secondaryColor
                                  //   : Colors.descriptionColor
                                } />
                            </View>
                          </TouchableOpacity>
                        </View>

                        <View style={{ width: '20%' }}>
                          <TouchableOpacity style={{ width: '100%', paddingLeft: 5, flexDirection: 'row', }}>
                            <Text style={{ fontSize: switchMerchant ? 10 : 14, alignSelf: 'center', fontFamily: 'NunitoSans-Bold', marginLeft: 0, }}>
                              Action
                            </Text>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  Colors.descriptionColor
                                  // productSort ===
                                  //   PRODUCT_SORT.PRODUCT_NAME_ASC
                                  //   ? Colors.secondaryColor
                                  //   : Colors.descriptionColor
                                } />

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  Colors.descriptionColor
                                  // productSort ===
                                  //   PRODUCT_SORT.PRODUCT_NAME_ASC
                                  //   ? Colors.secondaryColor
                                  //   : Colors.descriptionColor
                                } />
                            </View>
                          </TouchableOpacity>
                        </View>
                      </View>

                      <FlatList
                        nestedScrollEnabled={true}
                        showsVerticalScrollIndicator={false}
                        // data={woList}
                        data={
                          sortWOList(
                            woList,
                            currWOListSort,
                          )}
                        renderItem={renderWO}
                        keyExtractor={(item, index) => String(index)}
                      />

                    </View>
                  </View>
                </View>
              </ScrollView>
              // </ScrollView>

            ) : null}

            {console.log(`addStockTransfer`)}
            {console.log(addStockTransfer)}
            {console.log(`selectedWorkOrder`)}
            {console.log(selectedWorkOrder)}

            {addStockTransfer && selectedWorkOrder ? (
              <View style={{ height: Dimensions.get('window').height }}>
                <View
                  style={{
                    paddingBottom: Dimensions.get('screen').height * 0.3,
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.32,
                    shadowRadius: 3.22,
                    elevation: 1,
                  }}
                  contentContainerStyle={{
                    // top: Platform.OS === 'ios' ? -keyboardHeight * 0.3 : 0,
                  }}>
                  {/* <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginHorizontal: 0, }}> */}

                  <View style={{
                    width: switchMerchant ? null : Dimensions.get('screen').width * 0.886,
                    alignSelf: switchMerchant ? null : 'center',
                    marginBottom: switchMerchant ? 5 : 0,
                    marginTop: Platform.OS === 'ios' ? 0 : -5,
                    alignItems: 'baseline',
                  }}>
                    <TouchableOpacity style={{ marginBottom: switchMerchant ? 0 : 10, flexDirection: 'row', alignItems: 'center', marginTop: switchMerchant ? 0 : 10, }}
                      onPress={() => {
                        setSelectedWorkOrder([]);
                        setStockTransfer(true);
                        setAddStockTransfer(false);

                      }}>
                      <Icon name="chevron-left" size={switchMerchant ? 20 : 30} color={Colors.primaryColor} />
                      <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 14 : 17, color: Colors.primaryColor, marginBottom: Platform.OS === 'android' ? 2 : 0, }}>Back</Text>
                    </TouchableOpacity>
                  </View>

                  <View style={{
                    backgroundColor: Colors.whiteColor,
                    width: Dimensions.get('screen').width * 0.87,
                    height: Dimensions.get('screen').height * 0.69,
                    //marginTop: 10,
                    marginHorizontal: 30,
                    marginLeft: switchMerchant ? 31 : 29,
                    marginBottom: 30,
                    alignSelf: 'center',
                    //borderRadius: 5,
                    shadowOpacity: 0,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                    borderRadius: 5,
                  }}>

                    <ScrollView
                      showsVerticalScrollIndicator={false}
                      style={{}}>
                      <View style={{}}>

                        <View style={{ position: 'absolute', alignSelf: 'flex-end', marginTop: 30, zIndex: 10000, }}>
                          {(selectedWorkOrder &&
                            selectedWorkOrder.status === 'PENDING') ?
                            <>
                              <View
                                style={{

                                }}>
                                <TouchableOpacity
                                  style={{
                                    justifyContent: 'center',
                                    flexDirection: 'row',
                                    borderWidth: 1,
                                    borderColor: Colors.primaryColor,
                                    backgroundColor: '#4E9F7D',
                                    borderRadius: 5,
                                    width: switchMerchant ? 160 : 230,
                                    paddingHorizontal: 10,
                                    height: switchMerchant ? 35 : 40,
                                    alignItems: 'center',
                                    shadowOffset: {
                                      width: 0,
                                      height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                    zIndex: -1,
                                    marginRight: 20,
                                    marginBottom: 10,
                                  }}
                                  onPress={async () => {
                                    updateStatusToComplete(selectedWorkOrder);

                                    ////////////////

                                    // 2024-12-19

                                    try {
                                      let orderIdListToProcessed = selectedWorkOrder.woItems.map(woItem => woItem.userOrderId);
                                      let woItemListOriginal = selectedWorkOrder.woItems;

                                      console.log(`woItemListOriginal`);
                                      console.log(woItemListOriginal);

                                      for (let index = 0; index < orderIdListToProcessed.length; index++) {
                                        let currWoItem = woItemListOriginal[index];

                                        console.log(`currWoItem`);
                                        console.log(currWoItem);

                                        let woItemList = [currWoItem];

                                        if (currWoItem.type === 'PO_STOCK') {
                                          var body = {
                                            // poId: poId,
                                            poItems: woItemList.map(woItem => {
                                              return {
                                                ...woItem,
                                                sku: woItem.osiSku,
                                                insertQuantity: woItem.totalUsage,
                                              };
                                            }),
                                            // supplierId: selectedSupplierId,
                                            // status: poStatus,
                                            // outletId: selectedTargetOutletId,
                                            outletId: currOutletId,

                                            // outletName: allOutlets.find(outlet => outlet.uniqueId === currOutletId).name,

                                            merchantId: merchantId,
                                            // remarks: '',
                                          };

                                          // console.log(body);

                                          APILocal.insertInventory({ body: body }).then(async (result) => {
                                            // ApiClient.POST(API.insertInventory, body).then((result) => {
                                            if (result && result.status === 'success') {
                                              // Alert.alert(
                                              //   'Success',
                                              //   'Inventory has been created',
                                              //   [
                                              //     {
                                              //       text: 'OK',
                                              //       onPress: () => {
                                              //         //props.navigation.goBack();
                                              //         setLowStockAlert(true);

                                              //         logEventAnalytics({
                                              //           eventName: ANALYTICS.MODULE_COMPOSITE_INVENTORY_OVERVIEW_ADD_INVENTORY_C_ADD_ALERT,
                                              //           eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_INVENTORY_OVERVIEW_ADD_INVENTORY_C_ADD_ALERT,
                                              //         });
                                              //       },
                                              //     },
                                              //   ],
                                              //   { cancelable: false },
                                              // );

                                              /////////////////////////////////////////////

                                              // 2025-03-16 - consume stocks that linked with this ingredients

                                              for (let index = 0; index < woItemList.length; woItemList++) {
                                                const currWoItem = woItemList[index];

                                                let osi = null;
                                                if (global.outletSupplyItemsDict[currWoItem.osiId]) {
                                                  osi = global.outletSupplyItemsDict[currWoItem.osiId];
                                                }

                                                consumeOutletSupplyItemRecursiveWO(osi, currWoItem, selectedWorkOrder, 1);
                                              }

                                              /////////////////////////////////////////////
                                            } else {
                                              Alert.alert('Error', 'Failed to create inventory');
                                            }
                                          });
                                        }
                                        else {
                                          ////////////////////////////////////////////////
                                          // consume inventory

                                          // console.log(orderIdList[index]);

                                          let userOrderSnapshot = await firebase.firestore()
                                            .collection(Collections.UserOrder)
                                            .where('uniqueId', '==', orderIdListToProcessed[index])
                                            .limit(1)
                                            .get();

                                          let userOrder = null;
                                          if (!userOrderSnapshot.empty) {
                                            userOrder = userOrderSnapshot.docs[0].data();
                                          }

                                          if (userOrder && userOrder.uniqueId) {
                                            // console.log('valid order');

                                            try {
                                              const currCartItem = {
                                                ...selectedWorkOrder.woItems[index].cartItem,
                                                ...selectedWorkOrder.woItems[index],
                                              };

                                              // const currCartItem = userOrder.cartItems[i];

                                              let isValidToProceed = true;
                                              // if (currCartItem.itemId === currWoItem.cartItem.itemId &&
                                              //   currCartItem.cartItemDate === currWoItem.cartItem.cartItemDate
                                              // ) {
                                              //   isValidToProceed = true;
                                              // }
                                              console.log(currCartItem);
                                              console.log('======currcart item======')

                                              //////////////////////////////////////////////////

                                              // product

                                              let osi = null;
                                              if (global.outletSupplyItemsDict[currCartItem.osiId]) {
                                                osi = global.outletSupplyItemsDict[currCartItem.osiId];
                                              }

                                              firebase.firestore()
                                                .collection(Collections.OutletSupplyItem)
                                                .doc(currCartItem.osiId)
                                                .update({
                                                  quantity: firebase.firestore.FieldValue.increment(currCartItem.totalUsage),

                                                  updatedAt: Date.now(),
                                                });

                                              setTimeout(async () => {
                                                recordOutletSupplyItemTransaction({
                                                  outletSupplyItemId: currCartItem.osiId,
                                                  name: currCartItem.osiName,
                                                  updatedAt: Date.now(),
                                                  cn: Collections.OutletItem,
                                                  cnId: currCartItem.itemId,

                                                  cnData: {
                                                    userOrder,
                                                    cartItem: currCartItem,
                                                  },

                                                  am: currCartItem.totalUsage,

                                                  osi,
                                                });
                                                console.log('consume product');
                                                console.log(currCartItem);
                                                await consumeOutletSupplyItemRecursive(currCartItem, currCartItem, userOrder, currCartItem.totalUsage);
                                              },/*  i * j * */ global.currOutlet.ositTime ? global.currOutlet.ositTime : 3000); // wait some time before next call

                                              //////////////////////////////////////////////////

                                              // variant

                                              if (currCartItem.variantAddonChoice &&
                                                currCartItem.variantAddonChoice.choiceId &&
                                                currCartItem.osiId && currCartItem.totalUsage < 0) {
                                                // consume

                                                // check if OutletItemAddOnChoice choiceStockCount is bigger than 0
                                                const outletItemAddOnChoiceSnapshot = await firebase.firestore()
                                                  .collection(Collections.OutletItemAddOnChoice)
                                                  .where('uniqueId', '==', currCartItem.curVariantGroupChoice.choiceId)
                                                  .limit(1)
                                                  .get();

                                                let outletItemAddOnChoice = null;
                                                if (!outletItemAddOnChoiceSnapshot.empty) {
                                                  outletItemAddOnChoice = outletItemAddOnChoiceSnapshot.docs[0].data();
                                                }

                                                let osi = null;
                                                if (global.outletSupplyItemsDict[currCartItem.osiId]) {
                                                  osi = global.outletSupplyItemsDict[currCartItem.osiId];
                                                }

                                                ///////////////////

                                                // if (osi === null) {
                                                //   // retrieve via firestore
                                                // }

                                                ///////////////////

                                                firebase.firestore()
                                                  .collection(Collections.OutletSupplyItem)
                                                  .doc(currCartItem.osiId)
                                                  .update({
                                                    quantity: firebase.firestore.FieldValue.increment(currCartItem.totalUsage),

                                                    updatedAt: Date.now(),
                                                  });

                                                setTimeout(() => {
                                                  recordOutletSupplyItemTransaction({
                                                    outletSupplyItemId: currCartItem.osiId,
                                                    name: currCartItem.osiName,
                                                    updatedAt: Date.now(),
                                                    cn: Collections.OutletItemAddOnChoice,
                                                    cnId: (currCartItem.variantAddonChoice ? currCartItem.variantAddonChoice.choiceId : ''),

                                                    cnData: {
                                                      userOrder,
                                                      cartItem: currCartItem,
                                                      variantGroupChoice: (currCartItem.variantAddonChoice ? currCartItem.variantAddonChoice : null),
                                                      outletItemAddOnChoice
                                                    },

                                                    am: currCartItem.totalUsage,

                                                    osi,
                                                  });
                                                  console.log('consume varaint');
                                                  console.log(currCartItem);
                                                  consumeOutletSupplyItemRecursive(currCartItem, currCartItem, userOrder, currCartItem.totalUsage);
                                                }, /* i * j * zi * */ global.currOutlet.ositTime ? global.currOutlet.ositTime : 3000); // wait some time before next call
                                              }

                                              //////////////////////////////////////////////////

                                              // add-on

                                              if (currCartItem.variantAddonChoice &&
                                                currCartItem.variantAddonChoice.choiceId &&
                                                currCartItem.osiId && currCartItem.totalUsage < 0) {
                                                // check if OutletItemAddOnChoice choiceStockCount is bigger than 0
                                                const outletItemAddOnChoiceSnapshot = await firebase.firestore()
                                                  .collection(Collections.OutletItemAddOnChoice)
                                                  .where('uniqueId', '==', currCartItem.curVariantGroupChoice.choiceId)
                                                  .limit(1)
                                                  .get();

                                                let outletItemAddOnChoice = null;
                                                if (!outletItemAddOnChoiceSnapshot.empty) {
                                                  outletItemAddOnChoice = outletItemAddOnChoiceSnapshot.docs[0].data();
                                                }

                                                let quantity = 1;
                                                for (let x = 0; x < currCartItem.addOns.length; x++) {
                                                  // if (curAddOnGroupChoice.choiceName === currCartItem.addOns[x].choiceNames[0]) {
                                                  if (currCartItem.addOns[x].addOnChoiceIdList && currCartItem.variantAddonChoice.choiceId === currCartItem.addOns[x].addOnChoiceIdList[0]) {
                                                    quantity = currCartItem.addOns[x].quantities[0];
                                                  }
                                                }

                                                let osi = null;
                                                if (global.outletSupplyItemsDict[currCartItem.outletSupplyItemId]) {
                                                  osi = global.outletSupplyItemsDict[currCartItem.outletSupplyItemId];
                                                }

                                                firebase.firestore()
                                                  .collection(Collections.OutletSupplyItem)
                                                  .doc(currCartItem.outletSupplyItemId)
                                                  .update({
                                                    quantity: firebase.firestore.FieldValue.increment(-calculateQuantityUsageAndQuantityWastage(currCartItem) * quantity * currCartItem.quantity),

                                                    updatedAt: Date.now(),
                                                  });

                                                setTimeout(() => {
                                                  recordOutletSupplyItemTransaction({
                                                    outletSupplyItemId: currCartItem.outletSupplyItemId,
                                                    name: currCartItem.name,
                                                    updatedAt: Date.now(),
                                                    cn: Collections.OutletItemAddOnChoice,
                                                    cnId: (currCartItem.variantAddonChoice ? currCartItem.variantAddonChoice.choiceId : ''),

                                                    cnData: {
                                                      userOrder,
                                                      cartItem: currCartItem,
                                                      variantGroupChoice: (currCartItem.variantAddonChoice ? currCartItem.variantAddonChoice : null),
                                                      outletItemAddOnChoice
                                                    },

                                                    cnData: {
                                                      userOrder,
                                                      cartItem: currCartItem,
                                                      addOnGroupChoice: (currCartItem.variantAddonChoice ? currCartItem.variantAddonChoice.choiceId : null),
                                                      addOnGroupChoiceQty: quantity,
                                                      outletItemAddOnChoice,
                                                    },

                                                    am: currCartItem.totalUsage,

                                                    osi,
                                                  });
                                                  console.log('consume addon');
                                                  console.log(currCartItem);
                                                  consumeOutletSupplyItemRecursive(currCartItem, currCartItem, userOrder, currCartItem.totalUsage);
                                                }, /* /* i * j * zi * */ global.currOutlet.ositTime ? global.currOutlet.ositTime : 3000); // wait some time before next call
                                              }

                                              //////////////////////////////////////////////////
                                            }
                                            catch (err) {
                                              console.error('=======inventory consume error=======');
                                              console.error(err);
                                            }
                                          }

                                          ////////////////////////////////////////////////
                                        }
                                      }
                                    }
                                    catch (ex) {
                                      console.error(ex);
                                    }

                                    ///////////////
                                  }}
                                >
                                  <Text style={{
                                    color: Colors.whiteColor,
                                    //marginLeft: 5,
                                    fontSize: switchMerchant ? 10 : 16,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                    {'MARK COMPLETED'}
                                  </Text>
                                </TouchableOpacity>
                              </View>
                            </>
                            :
                            <></>}

                          {(selectedWorkOrder && selectedWorkOrder.status === 'PENDING') ?
                            <></>
                            :
                            <View
                              style={{

                              }}>
                              <TouchableOpacity
                                style={{
                                  justifyContent: 'center',
                                  flexDirection: 'row',
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  backgroundColor: '#4E9F7D',
                                  borderRadius: 5,
                                  width: 120,
                                  paddingHorizontal: 10,
                                  height: switchMerchant ? 35 : 40,
                                  alignItems: 'center',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  zIndex: -1,
                                  marginRight: 20,
                                }}
                                onPress={() => {
                                  updateStatusToComplete(selectedWorkOrder);
                                }}>
                                <Text style={{
                                  color: Colors.whiteColor,
                                  //marginLeft: 5,
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                  UNDO
                                </Text>
                              </TouchableOpacity>
                            </View>
                          }
                        </View>

                        <View style={{ justifyContent: 'center', alignItems: 'center' }}>
                          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                            <Text style={{ alignSelf: "center", marginTop: 30, fontSize: switchMerchant ? 20 : 40, fontWeight: '600' }}>
                              Work Order Details
                            </Text>
                          </View>
                          <Text style={{ alignSelf: "center", fontSize: switchMerchant ? 10 : 16, color: '#adadad' }}>
                            {/* Fill In The Information */}
                          </Text>
                        </View>

                        <View style={{ flexDirection: "row", marginTop: 55, justifyContent: 'space-between', width: '80%', alignSelf: 'center', }}>
                          <View style={{ flexDirection: "row", width: '45%', alignItems: 'center' }}>
                            <Text style={{ width: '42%', fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14, fontWeight: '400' }}>
                              ID
                            </Text>
                            <View style={{ width: '58%', justifyContent: 'space-between', flexDirection: 'row', alignItems: 'center' }}>
                              <View style={{ justifyContent: 'center', borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, height: 35, width: '90%', backgroundColor: Colors.fieldtBgColor, paddingHorizontal: 10 }}>
                                {/* {editMode ?
                                  <TextInput
                                    //editable={false}
                                    style={{
                                      fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14,
                                    }}
                                    placeholder='ID (Max Length 12)'
                                    placeholderStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                    onChangeText={(text) => {
                                      setPoId(text);
                                    }}
                                    maxLength={12}
                                  //value={`ST${poId}`}
                                  />
                                  :
                                  <Text style={{ fontSize: 15, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
                                    {`${poId}`}
                                  </Text>
                                } */}
                                <Text style={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
                                  {selectedWorkOrder.woId ? selectedWorkOrder.woId : 'N/A'}
                                </Text>
                              </View>
                              {/* {selectedStockTransferEdit ?
                                <></>
                                :
                                <TouchableOpacity style={{ marginLeft: 5 }}
                                  onPress={() => {
                                    setEditMode(!editMode);
                                  }}>
                                  <Icon4 name='edit' size={switchMerchant ? 15 : 18} color={Colors.primaryColor} />
                                </TouchableOpacity>
                              } */}
                            </View>
                          </View>

                          <View style={{ flexDirection: "row", width: '50%', alignItems: 'center' }}>
                            <Text style={{ width: '38%', fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14, fontWeight: '400' }}>
                              Source Store
                            </Text>

                            <View style={{ width: '62%', }}>
                              {/* {targetOutletDropdownList.find(item => item.value === selectedSourceOutletId) ?
                                <DropDownPicker
                                  containerStyle={{ height: 35, zIndex: 2 }}
                                  arrowColor={'black'}
                                  arrowSize={20}
                                  arrowStyle={{ fontWeight: 'bold' }}
                                  labelStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}
                                  style={{ width: switchMerchant ? 170 : 220, paddingVertical: 0, backgroundColor: Colors.fieldtBgColor, borderRadius: 10 }}
                                  placeholderStyle={{ color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                  disabled={selectedStockTransferEdit ? true : false}
                                  items={targetOutletDropdownList}
                                  itemStyle={{ justifyContent: 'flex-start', marginLeft: 5, zIndex: 2 }}
                                  placeholder={"Product"}
                                  customTickIcon={(press) => <Ionicon name={"md-checkbox"} color={press ? Colors.fieldtBgColor : Colors.primaryColor} size={25} />}
                                  onChangeItem={(item) => {
                                    setSelectedSourceOutletIdPrev(selectedSourceOutletId);
                                    setSelectedSourceOutletId(item.value);
                                  }}
                                  defaultValue={selectedSourceOutletId}
                                  dropDownMaxHeight={150}
                                  dropDownStyle={{ width: switchMerchant ? 170 : 220, height: 80, backgroundColor: Colors.fieldtBgColor, borderRadius: 10, borderWidth: 1, textAlign: 'left', zIndex: 2 }}
                                />
                                : <></>
                              } */}
                              <Text style={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
                                {selectedWorkOrder.sourceOutletName ? selectedWorkOrder.sourceOutletName : 'N/A'}
                              </Text>
                            </View>
                          </View>
                        </View>
                      </View>

                      <View style={{ flexDirection: "row", marginTop: 10, justifyContent: 'space-between', alignSelf: 'center', marginTop: 50, zIndex: -1, width: '80%' }}>
                        <View style={{ flexDirection: "row", width: '45%', alignItems: 'center' }}>


                          <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14, width: '42%', fontWeight: '400' }}>
                            Created Date
                          </Text>
                          <View style={{ width: '58%', alignItems: 'baseline' }}>
                            <View style={{ paddingHorizontal: 10, borderColor: '#E5E5E5', borderWidth: 1, borderRadius: 5, width: '90.5%', height: 35, justifyContent: 'space-between', backgroundColor: Colors.fieldtBgColor, flexDirection: 'row', alignItems: 'center' }}>
                              <GCalendarGrey width={switchMerchant ? 15 : 20} height={switchMerchant ? 15 : 20} style={{ marginRight: 5, }} />
                              <Text style={{
                                marginRight: '18%',
                                fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14,
                              }}>
                                {/* {selectedStockTransferEdit ?
                                  moment(createdDate).format('DD MMM YYYY') :
                                  moment(date1).format('DD MMM YYYY')
                                } */}
                                {moment(selectedWorkOrder.createdAt).format('DD MMM YYYY')}
                              </Text>
                            </View>
                          </View>

                        </View>

                        <View style={{ flexDirection: "row", width: '50%', alignItems: 'center' }}>
                          <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14, width: '38%', fontWeight: '400' }}>
                            Destination Store
                          </Text>
                          <View style={{ width: '62%' }}>

                            {/* {targetOutletDropdownList.find(item => item.value === selectedTargetOutletId) ?
                              <DropDownPicker
                                containerStyle={{ height: 35, zIndex: 2 }}
                                arrowColor={'black'}
                                arrowSize={20}
                                arrowStyle={{ fontWeight: 'bold' }}
                                labelStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}
                                style={{ width: switchMerchant ? 170 : 220, paddingVertical: 0, backgroundColor: Colors.fieldtBgColor, borderRadius: 10 }}
                                placeholderStyle={{ color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                disabled={selectedStockTransferEdit ? true : false}
                                items={targetOutletDropdownList}
                                itemStyle={{ justifyContent: 'flex-start', marginLeft: 5, zIndex: 2 }}
                                placeholder={"Product"}
                                customTickIcon={(press) => <Ionicon name={"md-checkbox"} color={press ? Colors.fieldtBgColor : Colors.primaryColor} size={25} />}
                                onChangeItem={(item) => {
                                  setSelectedTargetOutletId(item.value);
                                }}
                                defaultValue={selectedTargetOutletId}
                                dropDownMaxHeight={150}
                                dropDownStyle={{ width: switchMerchant ? 170 : 220, height: 80, backgroundColor: Colors.fieldtBgColor, borderRadius: 10, borderWidth: 1, textAlign: 'left', zIndex: 2 }}
                              />
                              : <></>

                            } */}
                            <Text style={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
                              {selectedWorkOrder.targetOutletName.length > 0 ? selectedWorkOrder.targetOutletName[0] : 'N/A'}
                            </Text>
                          </View>

                        </View>
                      </View>

                      <View style={{ flexDirection: "row", justifyContent: "space-between", alignSelf: 'center', marginTop: 50, marginBottom: 40, zIndex: -2, width: '80%' }}>
                        <View style={{ flexDirection: "row", width: '45%', alignItems: 'center' }}>
                          <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14, width: '42%', fontWeight: '400' }}>
                            Shipped Date
                          </Text>
                          {/* <Text style={{ color: '#adadad', marginLeft: 80, fontSize: 16, }}>8/10/2020</Text> */}
                          <View style={{
                            paddingHorizontal: 0,
                            flexDirection: 'row',
                            alignItems: 'center',
                            width: '57%',
                          }}>
                            <View style={{
                              // width: 140,
                              width: '90.5%',
                              height: 35,
                              backgroundColor: Colors.fieldtBgColor,
                              paddingHorizontal: 10,
                              flexDirection: 'row',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                              borderRadius: 5,
                              borderWidth: 1,
                              borderColor: '#E5E5E5',
                            }}>
                              <TouchableOpacity style={{ marginRight: 5, }}
                                disabled={selectedWorkOrder.status === PURCHASE_ORDER_STATUS.COMPLETED}
                                onPress={() => {
                                  setIsDateTimePickerVisible(true);
                                }}>
                                {selectedWorkOrder.status === PURCHASE_ORDER_STATUS.COMPLETED ?
                                  <GCalendarGrey width={switchMerchant ? 15 : 20} height={switchMerchant ? 15 : 20} />
                                  :
                                  <GCalendar width={switchMerchant ? 15 : 20} height={switchMerchant ? 15 : 20} />
                                }
                              </TouchableOpacity>
                              <Text style={{
                                //textAlign: 'left',
                                marginRight: '18%',
                                fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14,
                              }}>
                                {/* {moment(date).format('DD/MM/YYYY')}  */}
                                {date ? moment(date).format('DD MMM YYYY') : 'Shipping Date'}
                              </Text>
                            </View>

                            <DateTimePickerModal
                              isVisible={isDateTimePickerVisible}
                              mode='date'
                              onConfirm={(text) => {
                                setDate(text);
                                setIsDateTimePickerVisible(false);
                              }}
                              onCancel={() => {
                                setIsDateTimePickerVisible(false);
                              }}
                            />
                          </View>
                        </View>
                      </View>
                      <View style={{ borderWidth: 1, borderColor: '#E5E5E5' }} />

                      <View style={{ flexDirection: 'row', justifyContent: 'center', marginBottom: Platform.OS == 'ios' ? 0 : 10 }}>
                        <Text style={{ alignSelf: "center", marginTop: 30, fontSize: switchMerchant ? 15 : 20, fontWeight: 'bold' }}>Stock to Produce</Text>
                      </View>

                      <View
                        style={{
                          backgroundColor: '#ffffff',
                          flexDirection: 'row',
                          paddingVertical: 20,
                          paddingHorizontal: 10,
                          marginTop: 10,
                          borderBottomWidth: StyleSheet.hairlineWidth,
                        }}>
                        <Text style={{ width: '20%', fontSize: switchMerchant ? 10 : 14, fontFamily: 'NunitoSans-Bold', fontWeight: '500' }}>
                          Product Name
                        </Text>
                        <Text style={{ width: '14%', fontSize: switchMerchant ? 10 : 14, fontFamily: 'NunitoSans-Bold', fontWeight: '500', marginLeft: 10, }}>
                          SKU
                        </Text>
                        <Text style={{ width: '9%', fontSize: switchMerchant ? 10 : 14, fontFamily: 'NunitoSans-Bold', fontWeight: '500', }}>
                          Unit
                        </Text>
                        <Text style={{ width: '9%', fontSize: switchMerchant ? 10 : 14, fontFamily: 'NunitoSans-Bold', fontWeight: '500' }}>
                          {/* In Stock */}
                          From Stock
                        </Text>
                        <Text style={{ width: '11%', fontSize: switchMerchant ? 10 : 14, fontFamily: 'NunitoSans-Bold', fontWeight: '500' }}>
                          Produce Qty
                        </Text>
                        <Text style={{ width: '13%', fontSize: switchMerchant ? 10 : 14, fontFamily: 'NunitoSans-Bold', fontWeight: '500' }}>
                          Balance Stock
                        </Text>
                        <Text style={{ width: '10%', fontSize: switchMerchant ? 10 : 14, fontFamily: 'NunitoSans-Bold', fontWeight: '500' }}>
                          Cost (RM)
                        </Text>
                        <Text style={{ width: '12%', fontSize: switchMerchant ? 10 : 14, fontFamily: 'NunitoSans-Bold', fontWeight: '500' }}>
                          Subtotal (RM)
                        </Text>
                      </View>
                      {selectedWorkOrder && selectedWorkOrder.woItems && (
                        <FlatList
                          nestedScrollEnabled={true}
                          showsVerticalScrollIndicator={false}
                          data={selectedWorkOrder.woItems}
                          renderItem={renderWorkOrderItemList}
                          keyExtractor={(item, index) => String(index)}
                        />
                      )}
                      {/* :
                        <View style={{ alignItems: 'center', marginVertical: 20, marginTop: 50 }}>
                          <Text style={{ color: Colors.descriptionColor, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}>
                            No supply items In current store
                          </Text>
                        </View>
                      } */}

                      {/* {outletSupplyItems.length > 0 ?
                        <View style={{ flexDirection: 'row', marginBottom: 20, justifyContent: 'space-between' }}>
                          {!selectedStockTransferEdit ?
                            <TouchableOpacity
                              style={styles.submitText2}
                              onPress={() => {
                                if (outletSupplyItems.length > 0) {
                                  setPoItems([
                                    ...poItems,
                                    {
                                      // supplyItemId: '',
                                      // name: '',
                                      // sku: '',
                                      // quantity: '',
                                      // insertQuantity: 0,
                                      outletSupplyItemId: outletSupplyItems[0].uniqueId,
                                      name: outletSupplyItems[0].name,
                                      sku: outletSupplyItems[0].sku,
                                      unit: outletSupplyItems[0].unit,
                                      skuMerchant: outletSupplyItems[0].skuMerchant,
                                      quantity: outletSupplyItems[0].quantity,
                                      transferQuantity: 0,
                                      balance: 0,
                                      price: outletSupplyItems[0].price,
                                      totalPrice: 0,

                                      supplyItem: supplyItems[0],
                                    },
                                  ]);
                                }
                                else {
                                  Alert.alert('Error', 'No supplier items added')
                                }

                              }}>

                              <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 5, marginLeft: 0 }}>
                                <Icon1 name="plus-circle" size={switchMerchant ? 15 : 20} color={Colors.primaryColor} />
                                <Text style={{ marginLeft: 10, color: Colors.primaryColor, marginBottom: Platform.OS === 'ios' ? 0 : 1, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
                                  Add Product Slot
                                </Text>
                              </View>

                            </TouchableOpacity>
                            :
                            <></>
                          }


                          <View style={{ alignItems: 'flex-end', marginTop: 5, marginRight: 20 }}>
                            <Text style={{ fontWeight: 'bold', fontSize: switchMerchant ? 10 : 16 }}>{poItems.totalPrice}</Text>
                          </View>
                        </View>
                        :
                        null
                      } */}
                      {/* </ScrollView> */}
                      <View style={{ height: 50, }}>
                      </View>
                    </ScrollView>
                  </View>
                  {/* </ScrollView> */}
                </View>
              </View>
            ) : null}
          </View>
        </ScrollView>
      </View>
    </UserIdleWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row'
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItem: {
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    width: Dimensions.get('screen').width * Styles.sideBarWidth,
    // shadowColor: "#000",
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    padding: 16,
    width: Dimensions.get('screen').width * (1 - Styles.sideBarWidth),
  },
  submitText: {
    height: Platform.OS == 'ios' ? Dimensions.get('screen').height * 0.06 : Dimensions.get('screen').height * 0.05,
    paddingVertical: 5,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  submitText1: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    alignSelf: "flex-end",
    marginRight: 20,
    // marginTop: 15,
    height: 40,
    width: 220,
  },
  submitText2: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    alignSelf: "flex-end",
    marginRight: 20,
    marginTop: 15
  },
  /* textInput: {
    width: 300,
    height: '10%',
    padding: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
    paddingTop: 20,
  }, */

  textInput: {
    width: 200,
    height: 40,
    // padding:5,
    backgroundColor: Colors.whiteColor,
    borderRadius: 0,
    marginRight: '35%',
    flexDirection: 'row',
    alignContent: 'center',
    alignItems: 'flex-end',

    shadowOffset: Platform.OS == 'ios' ? {
      width: 0,
      height: 0,
    } : {
      width: 0,
      height: 7,
    },
    shadowOpacity: Platform.OS == 'ios' ? 0 : 0.43,
    shadowRadius: Platform.OS == 'ios' ? 0 : 0.51,
    elevation: 15,
  },
  searchIcon: {
    backgroundColor: Colors.whiteColor,
    height: 40,
    padding: 10,
    shadowOffset: Platform.OS == 'ios' ? {
      width: 0,
      height: 0,
    } : {
      width: 0,
      height: 7,
    },
    shadowOpacity: Platform.OS == 'ios' ? 0 : 0.43,
    shadowRadius: Platform.OS == 'ios' ? 0 : 9.51,

    elevation: 15,
  },
  textInput1: {
    width: 160,
    padding: 5,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
    paddingTop: 5,
  },
  textInput2: {
    width: 100,
    padding: 5,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
    paddingTop: 5,
    textAlign: 'center'
  },
  confirmBox: {
    width: 450,
    height: 450,
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
  textFieldInput: {
    height: 80,
    width: 900,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center'
  },
  headerLeftStyle: {
    width: Dimensions.get('screen').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center'
  },
  modalView: {
    height: Dimensions.get('screen').width * 0.2,
    width: Dimensions.get('screen').width * 0.3,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get('screen').width * 0.03,
    alignItems: 'center',
    justifyContent: 'center'
  },
  modalView1: {
    //height: Dimensions.get('screen').width * 0.2,
    width: Dimensions.get('screen').width * 0.3,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get('screen').width * 0.03,
    alignItems: 'center',
    justifyContent: 'center'
  },
  modalViewImport: {
    height: Dimensions.get('screen').width * 0.6,
    width: Dimensions.get('screen').width * 0.6,
    backgroundColor: Colors.whiteColor,
    borderRadius: Dimensions.get('screen').width * 0.03,
    padding: 20,
    paddingTop: 25,
    //paddingBottom: 30,
  },
  closeButton: {
    position: 'absolute',
    right: Dimensions.get('screen').width * 0.02,
    top: Dimensions.get('screen').width * 0.02,

    elevation: 1000,
    zIndex: 1000,
  },
  modalTitle: {
    alignItems: 'center',
    top: '20%',
    position: 'absolute',
  },
  modalBody: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center'

  },
  modalTitleText: {
    fontFamily: 'NunitoSans-Bold',
    textAlign: 'center',
    fontSize: 20,
  },
  modalDescText: {
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 18,
    color: Colors.fieldtTxtColor
  },
  modalBodyText: {
    flex: 1,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
    width: "20%",
  },
  modalSaveButton: {
    width: Dimensions.get('screen').width * 0.15,
    backgroundColor: Colors.fieldtBgColor,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,

    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,

    marginVertical: 10,
  },
});
export default WorkOrderListScreen;
