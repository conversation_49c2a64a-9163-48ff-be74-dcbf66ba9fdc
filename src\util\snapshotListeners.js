import AsyncStorage from "@react-native-async-storage/async-storage";
import {
    OUTLET_SHIFT_STATUS,
    ROLE_TYPE,
    USER_ORDER_STATUS,
    USER_QUEUE_STATUS,
    USER_RESERVATION_STATUS,
    USER_RING_STATUS,
    REPORT_SORT_FIELD_TYPE_COMPARE,
    REPORT_SORT_FIELD_TYPE_VALUE,
    REPORT_SORT_COMPARE_OPERATOR,
    REPORT_SORT_FIELD_TYPE,
    ORDER_TYPE,
    ACCUMULATOR_ID,
    EMAIL_REPORT_TYPE,
    PAYMENT_SORT_FIELD_TYPE,
    PAYMENT_SORT_FIELD_TYPE_VALUE,
    PAYMENT_SORT_COMPARE_OPERATOR,
    PAYMENT_SORT_FIELD_TYPE_COMPARE,
    TIMEZONE,
    KD_PRINT_EVENT_TYPE,
    KD_PRINT_VARIATION,
    KD_FONT_SIZE,
    ORDER_TYPE_SUB,
} from "../constant/common";
import { Collections } from "../constant/firebase";
import { CommonStore } from "../store/commonStore";
import { MerchantStore } from "../store/merchantStore";
import { OutletStore } from "../store/outletStore";
import { UserStore } from "../store/userStore";
import { naturalCompare, saveEmployeeData, updateOutletAsyncStorage, updatePrintersAsyncStorage } from "./common";
import { storageMMKV } from "./storageMMKV";
import moment from 'moment';

export const startSnapshotListeners = async () => {
    global.emitter.addListener(`${Collections.User}-snapshot`, async (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.User} changed!`);

        if (snapshot && !snapshot.empty) {
            const record = snapshot.docs[0].data();

            var { merchantId } = record;

            // const enteredPinNo = await AsyncStorage.getItem('enteredPinNo');
            const enteredPinNo = storageMMKV.getString('enteredPinNo');

            if (record.pinNo === enteredPinNo) {
                UserStore.update((s) => {
                    s.avatar = record.avatar;
                    s.dob = record.dob;
                    s.email = record.email;
                    s.gender = record.gender;
                    s.name = record.name;
                    s.number = record.number;
                    s.outletId = record.outledId;
                    s.race = record.race;
                    s.state = record.state;
                    s.uniqueName = record.uniqueName;
                    s.updatedAt = record.updatedAt;

                    s.merchantId = record.merchantId;

                    s.privileges = record.privileges;
                    s.screensToBlock = record.screensToBlock ? record.screensToBlock : [];

                    s.pinNo = record.pinNo || '';
                });

                global.privileges_state = record.privileges;
            }
            else {
                // UserStore.update((s) => {
                //   // s.avatar = record.avatar;
                //   // s.dob = record.dob;
                //   // s.email = record.email;
                //   // s.gender = record.gender;
                //   // s.name = record.name;
                //   // s.number = record.number;
                //   // s.outletId = record.outledId;
                //   // s.race = record.race;
                //   // s.state = record.state;
                //   // s.uniqueName = record.uniqueName;
                //   // s.updatedAt = record.updatedAt;

                //   // s.merchantId = record.merchantId;

                //   s.privileges = [];
                // });
            }

            // if (currOutletId === '') {
            //     MerchantStore.update(s => {
            //         s.currOutletId = record.outletId;
            //     });
            // }

            // auto-switch outlet bug fixes
            // MerchantStore.update((s) => {
            //   s.currOutletId = record.outletId;
            // });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.Merchant}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.Merchant} changed!`);

        if (snapshot && !snapshot.empty) {
            const record = snapshot.docs[0].data();

            MerchantStore.update((s) => {
                s.description = record.description;
                s.name = record.name;
                s.shortcode = record.shortcode;
                s.logo = record.logo;
                s.merchantLastUpdated = record.updatedAt;

                s.poNumber = record.poNumber;
                s.poNumberUpdatedAt = record.poNumberUpdatedAt;
                s.poNumberProduct = record.poNumberProduct;
                s.poNumberProductUpdatedAt = record.poNumberProductUpdatedAt;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.OutletItem}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.OutletItem} changed!`);

        // var outletItems = [];
        // var outletItemsDict = {};
        // var outletItemsSkuDict = {};

        var allOutletsItems = [];
        var allOutletsItemsSkuDict = {};

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                // if (record.outletId === currOutletId) {
                //   outletItems.push(record);
                //   outletItemsDict[record.uniqueId] = record;
                //   outletItemsSkuDict[record.sku] = record;
                // }

                allOutletsItems.push(record);

                // if (allOutletsItemsSkuDict[record.sku]) {
                //     allOutletsItemsSkuDict[record.sku].push(record);
                // } else {
                //     allOutletsItemsSkuDict[record.sku] = [record];
                // }
            }
        }

        // outletItems.sort((a, b) => a.name.localeCompare(b.name));

        allOutletsItems.sort((a, b) => a.name.localeCompare(b.name));

        if (snapshot) {
            OutletStore.update((s) => {
                // s.outletItems = outletItems;
                // s.outletItemsDict = outletItemsDict;
                // s.outletItemsSkuDict = outletItemsSkuDict;

                s.allOutletsItems = allOutletsItems;
                // s.allOutletsItemsSkuDict = allOutletsItemsSkuDict;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.OutletItemCategory}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.OutletItemCategory} changed!`);

        // var outletCategories = [];
        // var outletCategoriesDict = {};

        var allOutletsCategories = [];
        var allOutletsCategoriesNameDict = {};
        var allOutletsCategoriesDict = {};

        var allOutletsCategoriesUnique = [];

        if (snapshot && !snapshot.empty) {
            // var selectedOutletItemCategory = {};

            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                // if (record.outletId === currOutletId) {
                //   outletCategories.push(record);
                //   outletCategoriesDict[record.uniqueId] = record;
                // }

                allOutletsCategories.push(record);

                if (allOutletsCategoriesNameDict[record.name]) {
                    allOutletsCategoriesNameDict[record.name].push(record);
                } else {
                    allOutletsCategoriesNameDict[record.name] = [record];
                }

                if (allOutletsCategoriesDict[record.uniqueId]) {
                    allOutletsCategoriesDict[record.uniqueId] = {
                        ...record,
                        printerAreaList: record.printerAreaList ? record.printerAreaList : [],
                    };
                } else {
                    allOutletsCategoriesDict[record.uniqueId] = {
                        ...record,
                        printerAreaList: record.printerAreaList ? record.printerAreaList : [],
                    };
                }

                var isExisted = false;

                for (var j = 0; j < allOutletsCategoriesUnique.length; j++) {
                    if (allOutletsCategoriesUnique[j].name === record.name) {
                        isExisted = true;
                        break;
                    }
                }

                if (!isExisted) {
                    allOutletsCategoriesUnique.push(record);
                }
            }

            allOutletsCategories.sort((a, b) => {
                return naturalCompare(a.name || '', b.name || '');
            });

            allOutletsCategoriesUnique.sort((a, b) => {
                return naturalCompare(a.name || '', b.name || '');
            });

            global.allOutletsCategoriesDict = allOutletsCategoriesDict;

            OutletStore.update((s) => {
                // s.outletCategories = outletCategories;
                // s.outletCategoriesDict = outletCategoriesDict;

                s.allOutletsCategories = allOutletsCategories;
                s.allOutletsCategoriesNameDict = allOutletsCategoriesNameDict;
                s.allOutletsCategoriesDict = allOutletsCategoriesDict;

                s.allOutletsCategoriesUnique = allOutletsCategoriesUnique;
            });

            // CommonStore.update((s) => {
            //   s.selectedOutletItemCategory = outletCategories[0];
            // });
        }

        global.isSnapshotChanging = false;

        global.snapshots[`${Collections.OutletItemCategory}-snapshot`] = null;
    });

    global.emitter.addListener(`${Collections.OutletItemAddOn}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.OutletItemAddOn} changed!`);

        var allOutletsItemAddOn = [];
        var allOutletsItemAddOnDict = {};
        var allOutletsItemAddOnIdDict = {};

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                allOutletsItemAddOn.push(record);

                if (allOutletsItemAddOnDict[record.outletItemId]) {
                    allOutletsItemAddOnDict[record.outletItemId].push(record);
                } else {
                    allOutletsItemAddOnDict[record.outletItemId] = [record];
                }

                allOutletsItemAddOnDict[record.outletItemId] =
                    allOutletsItemAddOnDict[record.outletItemId].sort((a, b) =>
                        a.maxSelect && a.minSelect ? -1 : 1,
                    );

                allOutletsItemAddOnIdDict[record.uniqueId] = record;
            }
        }

        if (snapshot) {
            CommonStore.update((s) => {
                s.allOutletsItemAddOn = allOutletsItemAddOn;
                s.allOutletsItemAddOnDict = allOutletsItemAddOnDict;
                s.allOutletsItemAddOnIdDict = allOutletsItemAddOnIdDict;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.OutletItemAddOnChoice}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.OutletItemAddOnChoice} changed!`);

        var allOutletsItemAddOnChoiceDict = {};
        var allOutletsItemAddOnChoiceIdDict = {};

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                if (allOutletsItemAddOnChoiceDict[record.outletItemAddOnId]) {
                    allOutletsItemAddOnChoiceDict[record.outletItemAddOnId].push(
                        record,
                    );
                } else {
                    allOutletsItemAddOnChoiceDict[record.outletItemAddOnId] = [record];
                }

                allOutletsItemAddOnChoiceIdDict[record.uniqueId] = record;
            }

            // Sort allOutletsItemAddOnChoiceDict and allOutletsItemAddOnChoiceIdDict by price
            // for (const outletItemAddOnId in allOutletsItemAddOnChoiceDict) {
            //     allOutletsItemAddOnChoiceDict[outletItemAddOnId].sort((a, b) => b.price - a.price);
            // }
            // const allOutletsItemAddOnChoiceIdDictValues = Object.values(allOutletsItemAddOnChoiceIdDict);
            // allOutletsItemAddOnChoiceIdDictValues.sort((a, b) => b.price - a.price);
            // allOutletsItemAddOnChoiceIdDict = Object.assign({}, ...allOutletsItemAddOnChoiceIdDictValues.map((record) => ({ [record.uniqueId]: record })));
        }

        if (snapshot) {
            CommonStore.update((s) => {
                s.allOutletsItemAddOnChoiceDict = allOutletsItemAddOnChoiceDict;
                s.allOutletsItemAddOnChoiceIdDict = allOutletsItemAddOnChoiceIdDict;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.OutletOpening}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.OutletOpening} changed!`);

        if (snapshot && !snapshot.empty) {
            var outletsOpeningDict = {};

            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                outletsOpeningDict[record.outletId] = record;
            }

            OutletStore.update((s) => {
                s.outletsOpeningDict = outletsOpeningDict;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.User}-snapshot-allOutletsEmployees`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.User} changed!`);

        var allOutletsEmployees = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                allOutletsEmployees.push(record);
            }
        }

        if (snapshot) {
            OutletStore.update((s) => {
                s.allOutletsEmployees = allOutletsEmployees;
            });

            // 2023-06-10 - Only save the data if in offline mode
            // if (global.outletToggleOfflineMode) {
            //     saveEmployeeData(allOutletsEmployees);
            // }            

            saveEmployeeData(allOutletsEmployees);
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.UserAction}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.UserAction} changed!`);

        var allOutletsEmployeesUserActionsDict = {};

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                allOutletsEmployeesUserActionsDict[record.userId] = record;
            }
        }

        if (snapshot) {
            OutletStore.update((s) => {
                s.allOutletsEmployeesUserActionsDict =
                    allOutletsEmployeesUserActionsDict;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.EmployeeClock}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.UserAction} changed!`);

        var employeeClockDict = {};

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                employeeClockDict[record.firebaseUid] = record;
            }
        }

        if (snapshot) {
            OutletStore.update((s) => {
                s.employeeClockDict = employeeClockDict;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.UserOrder}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        // global.isSnapshotChanging = true;

        // console.log('=================================');
        // console.log(snapshot);
        console.log('=================================');
        console.log(`${Collections.UserOrder} monthly changed!`);
        // console.log(snapshot.size);
        console.log('=================================');

        var allOutletsUserOrdersDoneRealTime = [];
        var allOutletsUserOrdersRealTime = [];

        if (snapshot && !snapshot.empty) {
            // console.log(snapshot);
            // console.log(snapshot.metadata);
            // console.log('test snapshot');

            // const {fromCache} = snapshot.metadata;
            // const {hasPendingWrites} = snapshot.metadata;

            // console.log('fromCache');
            // console.log(fromCache);
            // console.log('hasPendingWrites');
            // console.log(hasPendingWrites);
            // console.log('test log');

            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                if (record.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT) {
                    if (record.combinedOrderList && record.combinedOrderList.length > 0) {
                        // means this order already merged with other orders
                        // // console.log('GONEEEE',record)

                        // allOutletsUserOrdersDoneRealTime.push(record);
                    } else {
                        allOutletsUserOrdersDoneRealTime.push(record);
                    }

                    // if (record.orderStatus === USER_ORDER_STATUS.ORDER_COMPLETED) {
                    //   userOrders.push(record);
                    // }

                    allOutletsUserOrdersRealTime.push(record);

                    ////////////////////////////////////////////////

                    ////////////////////////////////////////////////
                }
            }
        }

        if (snapshot) {
            OutletStore.update((s) => {
                s.allOutletsUserOrdersDoneRealTime = allOutletsUserOrdersDoneRealTime;
                s.allOutletsUserOrdersRealTime = allOutletsUserOrdersRealTime;
            });
        }

        // global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.UserOrderLoyalty}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log('=================================');
        // console.log(snapshot);
        console.log('=================================');
        console.log(`${Collections.UserOrderLoyalty} monthly changed!`);
        // console.log(snapshot.size);
        console.log('=================================');

        if (snapshot && !snapshot.empty) {
            var userOrders = [];
            var allOutletsUserOrders = [];

            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                if (record.combinedOrderList && record.combinedOrderList.length > 0) {
                    // means this order already merged with other orders
                    // // console.log('GONEEEE',record)
                } else {
                    userOrders.push(record);
                }

                // if (record.orderStatus === USER_ORDER_STATUS.ORDER_COMPLETED) {
                //   userOrders.push(record);
                // }

                allOutletsUserOrders.push(record);
            }

            OutletStore.update((s) => {
                s.allOutletsUserOrdersLoyaltyDoneRealTime = userOrders;
                s.allOutletsUserOrdersLoyaltyRealTime = allOutletsUserOrders;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.PreorderPackage}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.PreorderPackage} changed!`);

        var preorderPackages = [];

        if (snapshot && !snapshot.empty) {
            // var selectedOutletItemCategory = {};

            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                preorderPackages.push(record);
            }

            preorderPackages.sort((a, b) => a.name.localeCompare(b.name));
        }

        if (snapshot) {
            OutletStore.update((s) => {
                s.preorderPackages = preorderPackages;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.Supplier}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.Supplier} changed!`);

        var suppliers = [];
        var suppliersDict = {};

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                suppliers.push(record);
                suppliersDict[record.uniqueId] = record;
            }

            suppliers.sort((a, b) => b.updatedAt - a.updatedAt);
        }

        if (snapshot) {
            CommonStore.update((s) => {
                s.suppliers = suppliers;
                s.suppliersDict = suppliersDict;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.SupplierProduct}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.SupplierProduct} changed!`);

        var suppliersProduct = [];
        var suppliersProductDict = {};

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                suppliersProduct.push(record);
                suppliersProductDict[record.uniqueId] = record;
            }

            suppliersProduct.sort((a, b) => b.updatedAt - a.updatedAt);
        }

        if (snapshot) {
            CommonStore.update((s) => {
                s.suppliersProduct = suppliersProduct;
                s.suppliersProductDict = suppliersProductDict;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.SupplyItem}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.SupplyItem} changed!`);

        var supplyItems = [];
        var supplyItemsDict = {};
        var supplyItemsSkuDict = {};

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                supplyItems.push(record);
                supplyItemsDict[record.uniqueId] = record;
                supplyItemsSkuDict[record.sku] = record;
            }
        }

        if (snapshot) {
            CommonStore.update((s) => {
                s.supplyItems = supplyItems;
                s.supplyItemsDict = supplyItemsDict;
                s.supplyItemsSkuDict = supplyItemsSkuDict;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.OutletSupplyItem}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.OutletSupplyItem} changed!`);

        var allOutletsSupplyItems = [];
        var allOutletsSupplyItemsDict = {};
        var allOutletsSupplyItemsSkuDict = {};

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                allOutletsSupplyItems.push(record);
                allOutletsSupplyItemsDict[record.uniqueId] = record;

                if (allOutletsSupplyItemsSkuDict[record.sku] === undefined) {
                    allOutletsSupplyItemsSkuDict[record.sku] = [record];
                } else {
                    allOutletsSupplyItemsSkuDict[record.sku].push(record);
                }
            }
        }

        if (snapshot) {
            CommonStore.update((s) => {
                s.allOutletsSupplyItems = allOutletsSupplyItems;
                s.allOutletsSupplyItemsDict = allOutletsSupplyItemsDict;
                s.allOutletsSupplyItemsSkuDict = allOutletsSupplyItemsSkuDict;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.StockTransfer}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.StockTransfer} changed!`);

        var stockTransfers = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                stockTransfers.push(record);
            }
        }

        stockTransfers.sort((a, b) => b.updatedAt - a.updatedAt);

        if (snapshot) {
            CommonStore.update((s) => {
                s.stockTransfers = stockTransfers;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.StockTransferProduct}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        var stockTransfersProduct = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                stockTransfersProduct.push(record);
            }
        }

        stockTransfersProduct.sort((a, b) => b.updatedAt - a.updatedAt);

        if (snapshot) {
            CommonStore.update((s) => {
                s.stockTransfersProduct = stockTransfersProduct;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.StockTake}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.StockTake} changed!`);

        var stockTakes = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                stockTakes.push(record);
            }
        }

        stockTakes.sort((a, b) => b.updatedAt - a.updatedAt);

        if (snapshot) {
            CommonStore.update((s) => {
                s.stockTakes = stockTakes;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.StockTakeProduct}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.StockTakeProduct} changed!`);

        var stockTakesProduct = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                stockTakesProduct.push(record);
            }
        }

        stockTakesProduct.sort((a, b) => b.updatedAt - a.updatedAt);

        if (snapshot) {
            CommonStore.update((s) => {
                s.stockTakesProduct = stockTakesProduct;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.StockReturnProduct}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.StockReturnProduct} changed!`);

        var stockReturnsProduct = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                stockReturnsProduct.push(record);
            }
        }

        stockReturnsProduct.sort((a, b) => b.updatedAt - a.updatedAt);

        CommonStore.update((s) => {
            s.stockReturnsProduct = stockReturnsProduct;
        });

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.OutletShift}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.OutletShift} changed!`);

        var allOutletShifts = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                allOutletShifts.push(record);
            }
        }

        allOutletShifts.sort((a, b) => b.openDate - a.openDate);

        if (snapshot) {
            OutletStore.update((s) => {
                s.allOutletShifts = allOutletShifts;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.BeerDocketCategory}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.BeerDocketCategory} changed!`);

        var beerDocketCategories = [];
        var beerDocketCategoriesDict = {};

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                beerDocketCategories.push(record);
                beerDocketCategoriesDict[record.uniqueId] = record;
            }
        }

        if (snapshot) {
            OutletStore.update((s) => {
                s.beerDocketCategories = beerDocketCategories;
                s.beerDocketCategoriesDict = beerDocketCategoriesDict;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.BeerDocket}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.BeerDocket} changed!`);

        var beerDockets = [];
        var beerDocketsDict = {};

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                beerDockets.push(record);
                beerDocketsDict[record.uniqueId] = record;
            }
        }

        if (snapshot) {
            OutletStore.update((s) => {
                s.beerDockets = beerDockets;
                s.beerDocketsDict = beerDocketsDict;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.UserBeerDocket}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.UserBeerDocket} changed!`);

        var userBeerDockets = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                userBeerDockets.push(record);
            }
        }

        if (snapshot) {
            OutletStore.update((s) => {
                s.userBeerDockets = userBeerDockets;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.UserOrderBeerDocket}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.UserOrderBeerDocket} changed!`);

        var userOrderBeerDocketUBDIdDict = {};

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                if (
                    record.bdCartItems &&
                    record.bdCartItems[0] &&
                    record.bdCartItems[0].userBeerDocketId
                ) {
                    userOrderBeerDocketUBDIdDict[
                        record.bdCartItems[0].userBeerDocketId
                    ] = record;
                }
            }
        }

        if (snapshot) {
            OutletStore.update((s) => {
                s.userOrderBeerDocketUBDIdDict = userOrderBeerDocketUBDIdDict;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.Promotion}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.Promotion} changed!`);

        var promotions = [];
        var promotionsDict = {};

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                promotions.push(record);
                promotionsDict[record.uniqueId] = record;
            }
        }

        //promotions.sort((a, b) => b.updatedAt - a.updatedAt);
        promotions.sort((a, b) => b.createdAt - a.createdAt);

        if (snapshot) {
            OutletStore.update((s) => {
                s.promotions = promotions;
                s.promotionsDict = promotionsDict;
            });
        }

        global.isSnapshotChanging = false;

        // CommonStore.update(s => {
        //   s.selectedOutletPromotions = promotions;
        // });
    });

    global.emitter.addListener(`${Collections.LoyaltyCampaign}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.LoyaltyCampaign} changed!`);

        var loyaltyCampaigns = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                loyaltyCampaigns.push(record);
            }
        }

        //loyaltyCampaigns.sort((a, b) => b.updatedAt - a.updatedAt);
        loyaltyCampaigns.sort((a, b) => b.createdAt - a.createdAt);

        if (snapshot) {
            OutletStore.update((s) => {
                s.loyaltyCampaigns = loyaltyCampaigns;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.UpsellingCampaign}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.UpsellingCampaign} changed!`);

        var upsellingCampaigns = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                upsellingCampaigns.push(record);
            }
        }

        //upsellingCampaigns.sort((a, b) => b.updatedAt - a.updatedAt);
        upsellingCampaigns.sort((a, b) => b.createdAt - a.createdAt);

        if (snapshot) {
            OutletStore.update((s) => {
                s.upsellingCampaigns = upsellingCampaigns;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.TaggableVoucher}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.TaggableVoucher} changed!`);

        var taggableVouchers = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                taggableVouchers.push(record);
            }
        }

        //loyaltyCampaigns.sort((a, b) => b.updatedAt - a.updatedAt);
        taggableVouchers.sort((a, b) => b.createdAt - a.createdAt);

        if (snapshot) {
            OutletStore.update((s) => {
                s.taggableVouchers = taggableVouchers;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.User}-snapshot-favoriteMerchantIdUsers`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.User} changed!`);

        var favoriteMerchantIdUsers = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                favoriteMerchantIdUsers.push(record);
            }
        }

        if (snapshot) {
            OutletStore.update((s) => {
                s.favoriteMerchantIdUsers = favoriteMerchantIdUsers;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.User}-snapshot-linkedMerchantIdUsers`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.User} changed!`);

        var linkedMerchantIdUsers = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                linkedMerchantIdUsers.push(record);
            }
        }

        if (snapshot) {
            OutletStore.update((s) => {
                s.linkedMerchantIdUsers = linkedMerchantIdUsers;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.CRMUserTag}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.CRMUserTag} changed!`);

        var crmUserTags = [];
        var crmUserTagsDict = {};

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                crmUserTags.push(record);
                crmUserTagsDict[record.uniqueId] = record;
            }
        }

        crmUserTags.sort((a, b) => a.name.localeCompare(b.name));

        if (snapshot) {
            OutletStore.update((s) => {
                s.crmUserTags = crmUserTags;
                s.crmUserTagsDict = crmUserTagsDict;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.CRMSegment}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.CRMSegment} changed!`);

        var crmSegments = [];
        var crmSegmentsDict = {};

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                crmSegments.push(record);
                crmSegmentsDict[record.uniqueId] = record;
            }
        }

        crmSegments.sort((a, b) => a.name.localeCompare(b.name));

        if (snapshot) {
            OutletStore.update((s) => {
                s.crmSegments = crmSegments;
                s.crmSegmentsDict = crmSegmentsDict;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.OutletPaymentMethod}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.OutletPaymentMethod} changed!`);

        var outletPaymentMethods = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                outletPaymentMethods.push(record);
            }
        }

        if (snapshot) {
            OutletStore.update((s) => {
                s.outletPaymentMethods = outletPaymentMethods;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.LoyaltyStamp}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.LoyaltyStamp} changed!`);

        var loyaltyStamps = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                loyaltyStamps.push(record);
            }
        }

        if (snapshot) {
            OutletStore.update((s) => {
                s.loyaltyStamps = loyaltyStamps;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.LoyaltyStampType}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.LoyaltyStamp} changed!`);

        var loyaltyStampsType = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                loyaltyStampsType.push(record);
            }
        }

        if (snapshot) {
            OutletStore.update((s) => {
                s.loyaltyStampsType = loyaltyStampsType;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.TopupCreditType}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.LoyaltyStamp} changed!`);

        var topupCreditTypes = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                topupCreditTypes.push(record);
            }
        }

        if (snapshot) {
            OutletStore.update((s) => {
                s.topupCreditTypes = topupCreditTypes;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.Outlet}-snapshot`, (args) => {
        const {
            snapshot,
            currOutletId,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.Outlet} changed!`);

        if (snapshot && !snapshot.empty) {
            var outlets = [];
            var outletsDict = {};

            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                outlets.push(record);
                outletsDict[record.uniqueId] = record;
            }

            MerchantStore.update((s) => {
                s.allOutlets = outlets;
                s.allOutletsDict = outletsDict;
            });

            if (currOutletId !== '') {
                var currOutletIdTemp = '';
                var currOutletTemp = {
                    uniqueId: '',
                    privileges: [],
                };

                if (outlets.length > 0) {
                    // var firstOutlet = null;
                    // var oldestDate = moment();

                    // for (var i = 0; i < outlets.length; i++) {
                    //     if (moment(outlets[i].createdAt).isBefore(oldestDate)) {
                    //         oldestDate = outlets[i].createdAt;
                    //         firstOutlet = outlets[i];
                    //     }
                    // }

                    // currOutletIdTemp = outlets.uniqueId;
                    currOutletTemp = outlets.find(
                        (outlet) => outlet.uniqueId === currOutletId,
                    );
                }

                if (currOutletTemp && currOutletTemp.uniqueId) {
                    MerchantStore.update((s) => {
                        // s.currOutletId = currOutletIdTemp;
                        s.currOutlet = currOutletTemp;

                        // s.isMasterAccount = currOutletTemp.isMasterAccount !== undefined ? currOutletTemp.isMasterAccount : true;
                    });

                    global.outletKdEventTypes = currOutletTemp.kdPrintEventTypes !== undefined ? currOutletTemp.kdPrintEventTypes : [
                        KD_PRINT_EVENT_TYPE.DELIVER,
                        KD_PRINT_EVENT_TYPE.REJECT,
                        KD_PRINT_EVENT_TYPE.UNDO_DELIVER,
                        KD_PRINT_EVENT_TYPE.UNDO_REJECT,
                        KD_PRINT_EVENT_TYPE.SWITCH_TABLE,
                    ];

                    global.outletKdVariation = currOutletTemp.kdPrintVariation !== undefined ? currOutletTemp.kdPrintVariation : KD_PRINT_VARIATION.SUMMARY;

                    global.outletKdFontSize = currOutletTemp.kdFontSize !== undefined ? currOutletTemp.kdFontSize : KD_FONT_SIZE.NORMAL;

                    global.outletKdHeaderFontSize = currOutletTemp.kdHeaderFontSize !== undefined ? currOutletTemp.kdHeaderFontSize : KD_FONT_SIZE.EXTRA_LARGE;

                    global.outletToggleDisableAutoPrint = currOutletTemp.toggleDisableAutoPrint ? currOutletTemp.toggleDisableAutoPrint : false;

                    global.outletToggleDisablePrintingAlert = currOutletTemp.toggleDisablePrintingAlert ? currOutletTemp.toggleDisablePrintingAlert : false;

                    global.outletAutoPrintPaySlip = currOutletTemp.autoPrintPaySlip !== undefined ? currOutletTemp.autoPrintPaySlip : true;

                    updateOutletAsyncStorage(currOutletTemp);
                }
            }
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.OutletSupplyItem}-snapshot-outletSupplyItems`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        console.log('=================================');
        console.log(`${Collections.OutletSupplyItem} changed!`);
        // console.log(snapshot.size);
        console.log('=================================');

        var outletSupplyItems = [];
        var outletSupplyItemsDict = {};
        var outletSupplyItemsSkuDict = {};

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                outletSupplyItems.push(record);
                outletSupplyItemsDict[record.uniqueId] = record;
                outletSupplyItemsSkuDict[record.sku] = record;
            }
        }

        if (snapshot) {
            CommonStore.update((s) => {
                s.outletSupplyItems = outletSupplyItems;
                s.outletSupplyItemsDict = outletSupplyItemsDict;
                s.outletSupplyItemsSkuDict = outletSupplyItemsSkuDict;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.PurchaseOrder}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        console.log('=================================');
        console.log(`${Collections.PurchaseOrder} changed!`);
        // console.log(snapshot.size);
        console.log('=================================');

        var purchaseOrders = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                purchaseOrders.push(record);
            }
        }

        purchaseOrders.sort((a, b) => b.updatedAt - a.updatedAt);

        if (snapshot) {
            CommonStore.update((s) => {
                s.purchaseOrders = purchaseOrders;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.PurchaseOrderProduct}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        console.log('=================================');
        console.log(`${Collections.PurchaseOrderProduct} changed!`);
        // console.log(snapshot.size);
        console.log('=================================');

        var purchaseOrdersProduct = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                purchaseOrdersProduct.push(record);
            }
        }

        purchaseOrdersProduct.sort((a, b) => b.updatedAt - a.updatedAt);

        if (snapshot) {
            CommonStore.update((s) => {
                s.purchaseOrdersProduct = purchaseOrdersProduct;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.OutletShift}-snapshot-currOutletShift`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        console.log('=================================');
        console.log(`${Collections.OutletShift} changed!`);
        // console.log(snapshot.size);
        console.log('=================================');

        var currOutletShift = {};
        var currOutletShiftStatus = OUTLET_SHIFT_STATUS.CLOSED;

        if (snapshot && !snapshot.empty) {
            currOutletShift = snapshot.docs[0].data();

            if (currOutletShift.closeDate === null) {
                currOutletShiftStatus = OUTLET_SHIFT_STATUS.OPENED;
            } else {
                currOutletShiftStatus = OUTLET_SHIFT_STATUS.CLOSED;
            }
        }

        if (snapshot) {
            OutletStore.update((s) => {
                s.currOutletShift = currOutletShift;
                s.currOutletShiftStatus = currOutletShiftStatus;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.OutletPrinter}-snapshot`, async (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        console.log('=================================');
        console.log(`${Collections.OutletPrinter} changed!`);
        // console.log(snapshot.size);
        console.log('=================================');

        var outletPrinters = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                var newIp = record.ip;

                // if (record.ip === '***********8') {
                //     newIp = '*************';
                // }
                // if (record.ip === '***********9') {
                //     newIp = '*************';
                // }
                // if (record.ip === '************') {
                //     newIp = '*************';
                // }

                // outletPrinters.push(record);

                outletPrinters.push({
                    ...record,

                    // ip: '*************',

                    // ip: record.ip === '***********'
                    ip: newIp,
                });
            }
        }

        if (snapshot) {
            OutletStore.update((s) => {
                s.outletPrinters = outletPrinters;
            });

            await updatePrintersAsyncStorage(outletPrinters);
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.LoyaltyTier}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.LoyaltyTier} changed!`);

        var loyaltyTier = {};

        if (snapshot && !snapshot.empty) {
            loyaltyTier = snapshot.docs[0].data();
        }

        if (snapshot) {
            OutletStore.update((s) => {
                s.loyaltyTier = loyaltyTier;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.OutletOrderNumber}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.OutletOrderNumber} changed!`);

        var currOutletOrderNumber = {};

        if (snapshot && !snapshot.empty) {
            currOutletOrderNumber = snapshot.docs[0].data();
        }

        if (snapshot) {
            CommonStore.update((s) => {
                s.currOutletOrderNumber = currOutletOrderNumber;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.UserTaggableVoucher}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        console.log('=================================');
        console.log(`${Collections.UserTaggableVoucher} changed!`);
        // console.log(snapshot.size);
        console.log('=================================');

        var userTaggableVouchers = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                userTaggableVouchers.push(record);
            }
        }

        if (snapshot) {
            OutletStore.update((s) => {
                s.userTaggableVouchers = userTaggableVouchers;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.CRMUser}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.CRMUser} changed!`);

        var crmUsersRaw = [];
        //var crmUsersDict = {};

        if (snapshot && !snapshot.empty) {
            var crmUsersDict = {};

            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                crmUsersRaw.push(record);
                // crmUsersDict[record.uniqueId] = record;
            }
        }

        if (snapshot) {
            OutletStore.update((s) => {
                s.crmUsersRaw = crmUsersRaw;
                // s.crmUsersDict = crmUsersDict;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.UserOrder}-snapshot-selectedCustomerOrders`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        console.log('=================================');
        console.log(`${Collections.UserOrder} customer userId changed!`);
        // console.log(snapshot.size);
        console.log('=================================');

        var selectedCustomerOrders = [];
        var selectedCustomerDineInOrders = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                if (
                    record.combinedOrderList &&
                    record.combinedOrderList.length > 0
                ) {
                    // means this order already merged with other orders
                } else if (
                    record.orderStatus !==
                    USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT &&
                    record.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER
                ) {
                    if (record.orderType === ORDER_TYPE.DINEIN) {
                        selectedCustomerDineInOrders.push(record);
                    }

                    selectedCustomerOrders.push(record);
                }
            }
        }

        selectedCustomerDineInOrders.sort((a, b) => b.orderDate - a.orderDate);
        selectedCustomerOrders.sort((a, b) => b.orderDate - a.orderDate);

        if (snapshot) {
            OutletStore.update((s) => {
                s.selectedCustomerDineInOrders = selectedCustomerDineInOrders;
                s.selectedCustomerOrders = selectedCustomerOrders;
            });
        }

        CommonStore.update((s) => {
            s.isLoading = false;
        });

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.UserAddress}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.UserAddress} changed!`);

        var selectedCustomerAddresses = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                selectedCustomerAddresses.push(record);
            }
        }

        if (snapshot) {
            OutletStore.update((s) => {
                s.selectedCustomerAddresses = selectedCustomerAddresses;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.UserVoucherRedemption}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.UserAddress} changed!`);

        var selectedCustomerVoucherRedemptions = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                selectedCustomerVoucherRedemptions = record.redemptions;
            }
        }

        if (snapshot) {
            OutletStore.update((s) => {
                s.selectedCustomerVoucherRedemptions =
                    selectedCustomerVoucherRedemptions;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.UserBeerDocket}-snapshot-selectedCustomerUserBeerDocketsUserId`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.UserBeerDocket} changed!`);

        var selectedCustomerUserBeerDockets = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                selectedCustomerUserBeerDockets.push(record);
            }
        }

        selectedCustomerUserBeerDockets.sort(
            (a, b) => b.createdAt - a.createdAt,
        );

        if (snapshot) {
            OutletStore.update((s) => {
                s.selectedCustomerUserBeerDocketsUserId = selectedCustomerUserBeerDockets;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.UserOrder}-snapshot-selectedCustomerOrders-phone`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        console.log('=================================');
        console.log(`${Collections.UserOrder} customer userPhone changed!`);
        // console.log(snapshot.size);
        console.log('=================================');

        var selectedCustomerOrders = [];
        var selectedCustomerDineInOrders = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                if (
                    record.combinedOrderList &&
                    record.combinedOrderList.length > 0
                ) {
                    // means this order already merged with other orders
                } else if (
                    record.orderStatus !==
                    USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT &&
                    record.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER
                ) {
                    if (record.orderType === ORDER_TYPE.DINEIN) {
                        selectedCustomerDineInOrders.push(record);
                    }

                    selectedCustomerOrders.push(record);
                }
            }
        }

        selectedCustomerDineInOrders.sort((a, b) => b.orderDate - a.orderDate);
        selectedCustomerOrders.sort((a, b) => b.orderDate - a.orderDate);

        if (snapshot) {
            OutletStore.update((s) => {
                s.selectedCustomerDineInOrders = selectedCustomerDineInOrders;
                s.selectedCustomerOrders = selectedCustomerOrders;
            });
        }

        CommonStore.update((s) => {
            s.isLoading = false;
        });

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.LoyaltyCampaignCreditTransaction}-snapshot-selectedCustomerLCCTransactionsEmail`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.LoyaltyCampaignCreditTransaction} changed!`);

        var selectedCustomerLCCTransactionsEmail = [];
        var selectedCustomerLCCBalanceEmail = 0;

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                selectedCustomerLCCTransactionsEmail.push(record);
                selectedCustomerLCCBalanceEmail += record.amount;
            }
        }

        selectedCustomerLCCTransactionsEmail.sort(
            (a, b) => b.createdAt - a.createdAt,
        );

        if (snapshot) {
            OutletStore.update((s) => {
                s.selectedCustomerLCCTransactionsEmail =
                    selectedCustomerLCCTransactionsEmail;
                s.selectedCustomerLCCBalanceEmail = selectedCustomerLCCBalanceEmail;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.UserPointsTransaction}-snapshot-selectedCustomerPointsTransactionsEmail`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.LoyaltyCampaignCreditTransaction} changed!`);

        var selectedCustomerPointsTransactionsEmail = [];
        var selectedCustomerPointsBalanceEmail = 0;

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                selectedCustomerPointsTransactionsEmail.push(record);
                selectedCustomerPointsBalanceEmail += record.amount;
            }
        }

        selectedCustomerPointsTransactionsEmail.sort(
            (a, b) => b.createdAt - a.createdAt,
        );

        if (snapshot) {
            OutletStore.update((s) => {
                s.selectedCustomerPointsTransactionsEmail =
                    selectedCustomerPointsTransactionsEmail;
                s.selectedCustomerPointsBalanceEmail = selectedCustomerPointsBalanceEmail;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.UserBeerDocket}-snapshot-selectedCustomerUserBeerDocketsEmail`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.UserBeerDocket} changed!`);

        var selectedCustomerUserBeerDockets = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                selectedCustomerUserBeerDockets.push(record);
            }
        }

        selectedCustomerUserBeerDockets.sort(
            (a, b) => b.createdAt - a.createdAt,
        );

        if (snapshot) {
            OutletStore.update((s) => {
                s.selectedCustomerUserBeerDocketsEmail = selectedCustomerUserBeerDockets;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.LoyaltyCampaignCreditTransaction}-snapshot-selectedCustomerLCCTransactionsPhone`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.LoyaltyCampaignCreditTransaction} changed!`);

        var selectedCustomerLCCTransactionsPhone = [];
        var selectedCustomerLCCBalancePhone = 0;

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                selectedCustomerLCCTransactionsPhone.push(record);
                selectedCustomerLCCBalancePhone += record.amount;
            }
        }

        selectedCustomerLCCTransactionsPhone.sort(
            (a, b) => b.createdAt - a.createdAt,
        );

        if (snapshot) {
            OutletStore.update((s) => {
                s.selectedCustomerLCCTransactionsPhone =
                    selectedCustomerLCCTransactionsPhone;
                s.selectedCustomerLCCBalancePhone = selectedCustomerLCCBalancePhone;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.UserPointsTransaction}-snapshot-selectedCustomerPointsTransactionsPhone`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.LoyaltyCampaignCreditTransaction} changed!`);

        var selectedCustomerPointsTransactionsPhone = [];
        var selectedCustomerPointsBalancePhone = 0;

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                selectedCustomerPointsTransactionsPhone.push(record);
                selectedCustomerPointsBalancePhone += record.amount;
            }
        }

        selectedCustomerPointsTransactionsPhone.sort(
            (a, b) => b.createdAt - a.createdAt,
        );

        if (snapshot) {
            OutletStore.update((s) => {
                s.selectedCustomerPointsTransactionsPhone =
                    selectedCustomerPointsTransactionsPhone;
                s.selectedCustomerPointsBalancePhone = selectedCustomerPointsBalancePhone;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.UserLoyaltyCampaign}-snapshot-selectedCustomerUserLoyaltyCampaigns`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.UserLoyaltyCampaign} changed!`);

        var selectedCustomerUserLoyaltyCampaigns = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                selectedCustomerUserLoyaltyCampaigns.push(record);
            }
        }

        if (snapshot) {
            OutletStore.update((s) => {
                s.selectedCustomerUserLoyaltyCampaigns =
                    selectedCustomerUserLoyaltyCampaigns;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.UserTaggableVoucher}-snapshot-selectedCustomerUserTaggableVouchersView`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.UserTaggableVoucher} changed!`);

        var selectedCustomerUserTaggableVouchers = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                selectedCustomerUserTaggableVouchers.push(record);
            }
        }

        if (snapshot) {
            OutletStore.update((s) => {
                s.selectedCustomerUserTaggableVouchersView =
                    selectedCustomerUserTaggableVouchers;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.UserLoyaltyStamp}-snapshot-selectedCustomerUserLoyaltyStamps`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.UserTaggableVoucher} changed!`);

        var selectedCustomerUserLoyaltyStamps = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                selectedCustomerUserLoyaltyStamps.push(record);
            }
        }

        if (snapshot) {
            OutletStore.update((s) => {
                s.selectedCustomerUserLoyaltyStamps =
                    selectedCustomerUserLoyaltyStamps;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.Segment}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.Segment} changed!`);

        if (snapshot && !snapshot.empty) {
            var segments = [];

            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                segments.push(record);
            }

            CommonStore.update((s) => {
                s.segments = segments;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.Accumulator}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.Accumulator} changed!`);

        if (snapshot && !snapshot.empty) {
            const record = snapshot.docs[0].data();

            CommonStore.update((s) => {
                s.accumulator = record;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.User}-snapshot-listenToUserChangesWaiter`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.User} changed!`);

        if (snapshot && !snapshot.empty) {
            const record = snapshot.docs[0].data();

            merchantId = record.merchantId;

            UserStore.update((s) => {
                s.avatar = record.avatar;
                s.dob = record.dob;
                s.email = record.email;
                s.gender = record.gender;
                s.name = record.name;
                s.number = record.number;
                s.outletId = record.outledId;
                s.race = record.race;
                s.state = record.state;
                s.uniqueName = record.uniqueName;
                s.updatedAt = record.updatedAt;

                // s.role = record.role;
                s.merchantId = record.merchantId;

                s.privileges = record.privileges;
                s.screensToBlock = record.screensToBlock ? record.screensToBlock : [];

                s.pinNo = record.pinNo || ''; // 2023-01-06 - could be missing changess
            });

            global.privileges_state = record.privileges;

            // auto-switch outlet bug fixes
            // MerchantStore.update((s) => {
            //   s.currOutletId = record.outletId;
            // });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.Merchant}-snapshot-listenToUserChangesWaiter`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.Merchant} changed!`);

        if (snapshot && !snapshot.empty) {
            const record = snapshot.docs[0].data();

            MerchantStore.update((s) => {
                s.description = record.description;
                s.name = record.name;
                s.shortcode = record.shortcode;
                s.logo = record.logo;
                s.merchantLastUpdated = record.merchantLastUpdated;

                s.poNumber = record.poNumber;
                s.poNumberUpdatedAt = record.poNumberUpdatedAt;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.UserOrder}-snapshot-userOrders`, (args) => {
        const {
            snapshot,
        } = args;

        // global.isSnapshotChanging = true;

        // console.log(`${Collections.UserOrder} changed!`);
        // console.log(snapshot.size);

        console.log('=================================');
        console.log(`${Collections.UserOrder} active changed!`);
        // console.log(snapshot.size);
        console.log('=================================');

        var userOrders = [];
        var userOrdersDict = {};
        var userOrdersTableDict = {};

        var userOrdersPrinting = [];

        // var userOrdersAllStatus = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                if (!record.isReservationOrder) {
                    // currently filtered out cancelled orders
                    if (
                        record.orderStatus !==
                        USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT &&
                        record.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER
                    ) {
                        userOrders.push(record);
                        // userOrdersDict[record.uniqueId] = record;

                        if (record.tableId && record.tableId.length > 0) {
                            if (userOrdersTableDict[record.tableId] === undefined) {
                                userOrdersTableDict[record.tableId] = [record];
                            } else {
                                userOrdersTableDict[record.tableId] = [
                                    ...userOrdersTableDict[record.tableId],
                                    record,
                                ];
                            }
                        }

                        if (
                            record.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED ||
                            record.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED
                        ) {
                            userOrdersPrinting.push(record);
                        }
                    }

                    // userOrdersAllStatus.push(record);
                }
            }

            // // console.log('userOrders');
            // // console.log(userOrders);
            // // console.log('userOrdersTableDict');
            // // console.log(userOrdersTableDict);
        }

        // userOrdersAllStatus.sort((a, b) => b.updatedAt - a.updatedAt);

        if (snapshot) {
            OutletStore.update((s) => {
                s.userOrdersNormal = userOrders;
                // s.userOrdersDict = userOrdersDict;
                s.userOrdersTableDictNormal = userOrdersTableDict;

                s.userOrdersPrintingNormal = userOrdersPrinting;

                // s.userOrdersAllStatus = userOrdersAllStatus;
            });
        }

        // global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.UserOrder}-snapshot-reservation`, (args) => {
        const {
            snapshot,
        } = args;

        // global.isSnapshotChanging = true;

        // console.log(`${Collections.UserOrder} changed!`);
        // console.log(snapshot.size);

        console.log('=================================');
        console.log(`${Collections.UserOrder} reservation active changed!`);
        // console.log(snapshot.size);
        console.log('=================================');

        var userOrders = [];
        var userOrdersDict = {};
        var userOrdersTableDict = {};

        var userOrdersPrinting = [];

        // var userOrdersAllStatus = [];

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                // currently filtered out cancelled orders
                if (
                    record.orderStatus !==
                    USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT &&
                    record.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER
                ) {
                    userOrders.push(record);
                    // userOrdersDict[record.uniqueId] = record;

                    if (record.tableId && record.tableId.length > 0) {
                        if (userOrdersTableDict[record.tableId] === undefined) {
                            userOrdersTableDict[record.tableId] = [record];
                        } else {
                            userOrdersTableDict[record.tableId] = [
                                ...userOrdersTableDict[record.tableId],
                                record,
                            ];
                        }
                    }

                    if (
                        record.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED ||
                        record.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED
                    ) {
                        userOrdersPrinting.push(record);
                    }
                }

                // userOrdersAllStatus.push(record);
            }

            // // console.log('userOrders');
            // // console.log(userOrders);
            // // console.log('userOrdersTableDict');
            // // console.log(userOrdersTableDict);
        }

        // userOrdersAllStatus.sort((a, b) => b.updatedAt - a.updatedAt);

        if (snapshot) {
            OutletStore.update((s) => {
                s.userOrdersReservation = userOrders;
                // s.userOrdersDict = userOrdersDict;
                s.userOrdersTableDictReservation = userOrdersTableDict;

                s.userOrdersPrintingReservation = userOrdersPrinting;

                // s.userOrdersAllStatus = userOrdersAllStatus;
            });
        }

        // global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.OutletSection}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.OutletSection} changed!`);

        var outletSections = [];
        var outletSectionsDict = {};

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                outletSections.push(record);
                outletSectionsDict[record.uniqueId] = record;
            }

            // console.log('outletSections');
            // console.log(outletSections);
            // console.log('outletSectionsDict');
            // console.log(outletSectionsDict);
        }

        if (snapshot) {
            OutletStore.update((s) => {
                s.outletSections = outletSections;
                s.outletSectionsDict = outletSectionsDict;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.OutletTable}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.OutletTable} changed!`);

        var outletTables = [];
        var outletTablesDict = {};
        var outletTablesCodeDict = {};

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                outletTables.push(record);
                outletTablesDict[record.uniqueId] = record;
                // outletTablesCodeDict[record.code] = record;
            }

            // console.log('outletTables');
            // console.log(outletTables);
            // console.log('outletTablesDict');
            // console.log(outletTablesDict);
        }

        outletTables.sort((a, b) => a.createdAt - b.createdAt);

        if (snapshot) {
            OutletStore.update((s) => {
                s.outletTables = outletTables;
                s.outletTablesDict = outletTablesDict;
                // s.outletTablesCodeDict = outletTablesCodeDict;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.OutletTableCombination}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.OutletTableCombination} changed!`);

        var outletTableCombinations = [];
        // var outletTableCombinationsDict = {};

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                outletTableCombinations.push(record);
                // outletTableCombinationsDict[record.uniqueId] = record;
            }
        }

        outletTableCombinations.sort((a, b) => a.createdAt - b.createdAt);

        if (snapshot) {
            OutletStore.update((s) => {
                s.outletTableCombinations = outletTableCombinations;
                // s.outletTableCombinationsDict = outletTableCombinationsDict;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.UserReservation}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        console.log('=================================');
        console.log(`${Collections.UserReservation} changed!`);
        // console.log(snapshot.size);
        console.log('=================================');

        var userReservations = [];
        var userReservationsDict = {};
        var userReservationsUserIdDict = {};

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                // if (record.status !== USER_RESERVATION_STATUS.CANCELED) {
                //     userReservations.push(record);
                //     userReservationsDict[record.uniqueId] = record;

                //     if (userReservationsUserIdDict[record.userId]) {
                //         userReservationsUserIdDict[record.userId].push(record);
                //     }
                //     else {
                //         userReservationsUserIdDict[record.userId] = [
                //             record,
                //         ];
                //     }
                // }

                userReservations.push(record);
                // userReservationsDict[record.uniqueId] = record;

                if (userReservationsUserIdDict[record.userId]) {
                    userReservationsUserIdDict[record.userId].push(record);
                } else {
                    userReservationsUserIdDict[record.userId] = [record];
                }
            }
        }

        // userReservations.sort((a, b) => b.reservationTime - a.reservationTime);
        userReservations.sort((a, b) => b.createdAt - a.createdAt);

        if (snapshot) {
            OutletStore.update((s) => {
                s.userReservations = userReservations;
                // s.userReservationsDict = userReservationsDict;
                s.userReservationsUserIdDict = userReservationsUserIdDict;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.UserReservationWaitList}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        // console.log(`${Collections.UserReservationWaitList} changed!`);

        var userReservationsWaitList = [];
        var userReservationsWaitListDict = {};
        var userReservationsWaitListUserIdDict = {};

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                // if (record.status !== USER_RESERVATION_STATUS.CANCELED) {
                //     userReservations.push(record);
                //     userReservationsDict[record.uniqueId] = record;

                //     if (userReservationsUserIdDict[record.userId]) {
                //         userReservationsUserIdDict[record.userId].push(record);
                //     }
                //     else {
                //         userReservationsUserIdDict[record.userId] = [
                //             record,
                //         ];
                //     }
                // }

                userReservationsWaitList.push(record);
                // userReservationsWaitListDict[record.uniqueId] = record;

                if (userReservationsWaitListUserIdDict[record.userId]) {
                    userReservationsWaitListUserIdDict[record.userId].push(record);
                } else {
                    userReservationsWaitListUserIdDict[record.userId] = [record];
                }
            }
        }

        // userReservations.sort((a, b) => b.reservationTime - a.reservationTime);
        userReservationsWaitList.sort((a, b) => b.createdAt - a.createdAt);

        if (snapshot) {
            OutletStore.update((s) => {
                s.userReservationsWaitList = userReservationsWaitList;
                // s.userReservationsWaitListDict = userReservationsWaitListDict;
                s.userReservationsWaitListUserIdDict =
                    userReservationsWaitListUserIdDict;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.UserQueue}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        console.log('=================================');
        console.log(`${Collections.UserQueue} changed!`);
        // console.log(snapshot.size);
        console.log('=================================');

        var userQueues = [];
        var userQueuesDict = {};

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                // if (record.status !== USER_QUEUE_STATUS.CANCELED &&
                //     record.status !== USER_QUEUE_STATUS.NO_SHOW) {
                //     userQueues.push(record);
                //     userQueuesDict[record.uniqueId] = record;
                // }

                userQueues.push(record);
                // userQueuesDict[record.uniqueId] = record;
            }
        }

        userQueues.sort((a, b) => a.createdAt - b.createdAt);

        if (snapshot) {
            OutletStore.update((s) => {
                s.userQueues = userQueues;
                // s.userQueuesDict = userQueuesDict;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.UserRing}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        console.log('=================================');
        console.log(`${Collections.UserRing} changed!`);
        // console.log(snapshot.size);
        console.log('=================================');

        var userRings = [];
        var userRingsDict = {};

        if (snapshot && !snapshot.empty) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                userRings.push(record);
                // userRingsDict[record.uniqueId] = record;
            }
        }

        userRings.sort((a, b) => b.updatedAt - a.updatedAt);

        if (snapshot) {
            OutletStore.update((s) => {
                s.userRings = userRings;
                // s.userRingsDict = userRingsDict;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.UserOrder}-snapshot-historical`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        console.log('=================================');
        console.log(`${Collections.UserOrder} historical changed!`);
        // console.log(snapshot.size);
        console.log('=================================');

        var userOrdersTemp = [];
        var allOutletsUserOrdersTemp = [];

        if (snapshot) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                if (record.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT) {
                    if (record.combinedOrderList &&
                        record.combinedOrderList.length > 0) {
                    }
                    else {
                        userOrdersTemp.push(record);
                    }

                    allOutletsUserOrdersTemp.push(record);
                }
            }
        }

        // OutletStore.update(s => {
        //     s.allOutletsUserOrdersDoneCache = userOrdersTemp;
        //     s.allOutletsUserOrdersCache = allOutletsUserOrdersTemp;
        // });

        if (snapshot) {
            OutletStore.update(s => {
                s.allOutletsUserOrdersDoneCache = userOrdersTemp;
                s.allOutletsUserOrdersCache = allOutletsUserOrdersTemp;
            });
        }

        global.isSnapshotChanging = false;
    });

    global.emitter.addListener(`${Collections.RazerPayoutTransaction}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        console.log('=================================');
        console.log(`${Collections.RazerPayoutTransaction} changed!`);
        // console.log(snapshot.size);
        console.log('=================================');

        var payoutTransactions = [];

        if (snapshot) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                payoutTransactions.push({
                    ...record,

                    transactionDate: moment(record.createdAt).add(-1, 'day').valueOf(),

                    // 2023-05-03 - for amended fields
                    ...record.remarks >= 'v3' && {
                        userOrdersFigures: (record.userOrdersFigures ? record.userOrdersFigures : []).filter(order => {
                            // if (order.orderId === '4da8c7a0-9008-45c3-80ab-4ea602ba07f6') {
                            //     console.log('hit');
                            // }

                            if (order.userOrderPriceBeforeCommission <= 0) {
                                if (order.iro) {
                                    return true;
                                }
                                else if (order.colf && order.colf.length > 0) {
                                    return true;
                                }
                                else {
                                    // are those orders that already joined as bill

                                    return false;
                                }
                            }
                            else {
                                return true;
                            }
                        }).map(order => ({
                            ...order,

                            cartItems: order.cartItems.map(cartItem => ({
                                ...cartItem,

                                itemId: cartItem.id,
                                quantity: cartItem.qty,
                                // price: cartItem.price,
                                discount: cartItem.disc,
                                upsellingCampaignId: cartItem.upsellId,

                                itemCostPrice: cartItem.icp,
                                priceBackup: cartItem.pbu,

                                addOns: cartItem.addons,

                                isFreeItem: cartItem.ifi,
                                priceOriginal: cartItem.po,

                                promotionId: cartItem.pi,
                                discountPromotions: cartItem.dp,
                            })),

                            cartItemsCancelled: (order.cic ? order.cic : []).map(cartItem => ({
                                ...cartItem,

                                itemId: cartItem.id,
                                quantity: cartItem.qty,
                                // price: cartItem.price,
                                discount: cartItem.disc,
                                upsellingCampaignId: cartItem.upsellId,

                                itemCostPrice: cartItem.icp,
                                priceBackup: cartItem.pbu,

                                addOns: cartItem.addons,

                                isFreeItem: cartItem.ifi,
                                priceOriginal: cartItem.po,

                                promotionId: cartItem.pi,
                                discountPromotions: cartItem.dp,
                            })),

                            uniqueId: order.orderId,
                            orderId: order.orderIdHuman ? order.orderIdHuman : '',
                            outletId: record.outletId,
                            finalPrice:
                                (
                                    (
                                        order.iro
                                        &&
                                        order.ro

                                    )
                                    ||
                                    (
                                        order.commissionFeeFinal > 0
                                        &&
                                        !order.iro
                                    )
                                    ||
                                    (
                                        order.commissionFeeFinal <= 0
                                        &&
                                        !order.iro
                                    )
                                )
                                    ?
                                    order.userOrderPriceBeforeCommission
                                    :
                                    ((Math.round(Math.max(order.fpb) * 20) / 20)),

                            finalPriceBefore: order.fpb,
                            finalPriceBeforeBackup: order.fpbbu,
                            finalPriceBackup: order.fpbu,
                            discount: order.disc,
                            discountBackup: order.discbu,
                            isRefundOrder: order.iro,

                            isRefundOnline: order.ro ? order.ro : false, // added in 2023-07-29 support
                            combinedOrderListFrom: order.colf ? order.colf : [], // added in 2023-07-29 support

                            waiterName: order.wn,

                            completedDate: order.cd,

                            promotionIdList: order.pidl,
                            promoCodePromotionIdList: order.pcpidl,
                            cartPromotionIdList: order.cpidl,

                            taggableVoucherId: order.tvi,

                            paymentDetails: {
                                channel: order.pdChannel,
                            },

                            orderTypeSub: order.ots ? order.ots : ORDER_TYPE_SUB.NORMAL,
                        })),
                    }
                });
            }
        }

        // OutletStore.update(s => {
        //     s.allOutletsUserOrdersDoneCache = userOrdersTemp;
        //     s.allOutletsUserOrdersCache = allOutletsUserOrdersTemp;
        // });

        payoutTransactions.sort((a, b) => a.createdAt - b.createdAt);

        if (snapshot) {
            OutletStore.update(s => {
                s.payoutTransactions = payoutTransactions;
            });
        }

        global.isSnapshotChanging = false;
    });
    global.emitter.addListener(`${Collections.RazerPayoutTransactionExtend}-snapshot`, (args) => {
        const {
            snapshot,
        } = args;

        global.isSnapshotChanging = true;

        console.log('=================================');
        console.log(`${Collections.RazerPayoutTransactionExtend} changed!`);
        // console.log(snapshot.size);
        console.log('=================================');

        var payoutTransactionsExtend = [];

        if (snapshot) {
            for (var i = 0; i < snapshot.size; i++) {
                const record = snapshot.docs[i].data();

                payoutTransactionsExtend.push({
                    ...record,

                    transactionDate: moment(record.createdAt).add(-1, 'day').valueOf(),

                    // 2023-05-03 - for amended fields
                    ...record.remarks >= 'v3' && {
                        userOrdersFigures: (record.userOrdersFigures ? record.userOrdersFigures : []).filter(order => {
                            // if (order.orderId === '4da8c7a0-9008-45c3-80ab-4ea602ba07f6') {
                            //     console.log('hit');
                            // }

                            if (order.userOrderPriceBeforeCommission <= 0) {
                                if (order.iro) {
                                    return true;
                                }
                                else if (order.colf && order.colf.length > 0) {
                                    return true;
                                }
                                else {
                                    // are those orders that already joined as bill

                                    return false;
                                }
                            }
                            else {
                                return true;
                            }
                        }).map(order => ({
                            ...order,

                            cartItems: (order.cartItems ? order.cartItems : []).map(cartItem => ({
                                ...cartItem,

                                itemId: cartItem.id,
                                quantity: cartItem.qty,
                                // price: cartItem.price,
                                discount: cartItem.disc,
                                upsellingCampaignId: cartItem.upsellId,

                                itemCostPrice: cartItem.icp,
                                priceBackup: cartItem.pbu,

                                addOns: cartItem.addons,

                                isFreeItem: cartItem.ifi,
                                priceOriginal: cartItem.po,

                                promotionId: cartItem.pi,
                                discountPromotions: cartItem.dp,
                            })),

                            cartItemsCancelled: (order.cic ? order.cic : []).map(cartItem => ({
                                ...cartItem,

                                itemId: cartItem.id,
                                quantity: cartItem.qty,
                                // price: cartItem.price,
                                discount: cartItem.disc,
                                upsellingCampaignId: cartItem.upsellId,

                                itemCostPrice: cartItem.icp,
                                priceBackup: cartItem.pbu,

                                addOns: cartItem.addons,

                                isFreeItem: cartItem.ifi,
                                priceOriginal: cartItem.po,

                                promotionId: cartItem.pi,
                                discountPromotions: cartItem.dp,
                            })),

                            uniqueId: order.orderId,
                            orderId: order.orderIdHuman ? order.orderIdHuman : '',
                            outletId: record.outletId,
                            finalPrice:
                                (
                                    (
                                        order.iro
                                        &&
                                        order.ro

                                    )
                                    ||
                                    (
                                        order.commissionFeeFinal > 0
                                        &&
                                        !order.iro
                                    )
                                    ||
                                    (
                                        order.commissionFeeFinal <= 0
                                        &&
                                        !order.iro
                                    )
                                )
                                    ?
                                    order.userOrderPriceBeforeCommission
                                    :
                                    ((Math.round(Math.max(order.fpb) * 20) / 20)),

                            finalPriceBefore: order.fpb,
                            finalPriceBeforeBackup: order.fpbbu,
                            finalPriceBackup: order.fpbu,
                            discount: order.disc,
                            discountBackup: order.discbu,
                            isRefundOrder: order.iro,

                            isRefundOnline: order.ro ? order.ro : false, // added in 2023-07-29 support
                            combinedOrderListFrom: order.colf ? order.colf : [], // added in 2023-07-29 support

                            waiterName: order.wn,

                            completedDate: order.cd,

                            promotionIdList: order.pidl,
                            promoCodePromotionIdList: order.pcpidl,
                            cartPromotionIdList: order.cpidl,

                            taggableVoucherId: order.tvi,

                            paymentDetails: {
                                channel: order.pdChannel,
                            },

                            orderTypeSub: order.ots ? order.ots : ORDER_TYPE_SUB.NORMAL,
                        })),
                    }
                });
            }
        }

        // OutletStore.update(s => {
        //     s.allOutletsUserOrdersDoneCache = userOrdersTemp;
        //     s.allOutletsUserOrdersCache = allOutletsUserOrdersTemp;
        // });

        payoutTransactionsExtend.sort((a, b) => a.createdAt - b.createdAt);

        if (snapshot) {
            OutletStore.update(s => {
                s.payoutTransactionsExtend = payoutTransactionsExtend;
            });
        }

        global.isSnapshotChanging = false;
    });
};
