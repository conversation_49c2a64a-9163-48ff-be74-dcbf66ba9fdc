const webpack = require('webpack');
const TerserPlugin = require('terser-webpack-plugin');

const config = {
  plugins: [],
  optimization: {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          compress: {
            drop_console: true,
            drop_debugger: true,
            pure_funcs: ['console.log']
          }
        }
      })
    ]
  }
};

module.exports = (env, argv) => {
  config.plugins.push(new webpack.DefinePlugin({
    __DEV__: JSON.stringify(argv.mode !== 'production'),
  }));

  return config;
};